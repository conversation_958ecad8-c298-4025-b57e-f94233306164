{"info": {"_postman_id": "a149f0f3-352c-49fb-8669-447a3a5c78a4", "name": "Taysir", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "13166667", "_collection_link": "https://grimwolf.postman.co/workspace/Shared-Workspace~7d1fb684-f085-471d-8550-e4e442ef5afb/collection/13166667-a149f0f3-352c-49fb-8669-447a3a5c78a4?action=share&source=collection_link&creator=13166667"}, "item": [{"name": "authenticate", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"username\": \"admin\",\n    \"password\": \"admin\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8080/api/authenticate", "host": ["localhost"], "port": "8080", "path": ["api", "authenticate"]}}, "response": []}, {"name": "attachment", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"id\": \"\",\n    \"copyId\": \"\",\n    \"lblAttachment\": \"\",\n    \"idDocAttachment\": \"\",\n    \"sizeAttachement\": \"\",\n    \"filenameattachment\": \"\",\n    \"userattachment\": \"\",\n    \"fullname\": \"\",\n    \"iddecision\": \"\",\n    \"idtemplate\": \"\",\n    \"datejcattachment\": \"\",\n    \"datehjrattachment\": \"\",\n    \"idtransfer\": \"\",\n    \"idcorresp\": \"\",\n    \"ordering\": 0,\n    \"levelattachement\": \"\",\n    \"idreq\": \"\",\n    \"orderingscan\": 0,\n    \"iddocext\": \"\",\n    \"idleave\": \"\",\n    \"configLevel\": \"\",\n    \"filepathdoc\": \"\",\n    \"typeAtach\": \"\",\n    \"idDirectOrder\": \"\",\n    \"uid\": \"\",\n    \"version\": 0\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "localhost:8080/api/attachements/att?toList=&ccList=&transId=&correspId=&sendwithmail=", "host": ["localhost"], "port": "8080", "path": ["api", "attachements", "att"], "query": [{"key": "toList", "value": ""}, {"key": "ccList", "value": ""}, {"key": "transId", "value": ""}, {"key": "correspId", "value": ""}, {"key": "sendwithmail", "value": ""}]}}, "response": []}, {"name": "delete subscription", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "DELETE", "header": [], "url": {"raw": "http://localhost:8080/api/subscriptions/7fd045a8-24c5-4c76-8c07-e12a14cb626b", "protocol": "http", "host": ["localhost"], "port": "8080", "path": ["api", "subscriptions", "7fd045a8-24c5-4c76-8c07-e12a14cb626b"]}}, "response": []}, {"name": "create subscription", "request": {"auth": {"type": "<PERSON><PERSON><PERSON>"}, "method": "POST", "header": [], "url": {"raw": "https://e47fc55907c9.ngrok-free.app/api/subscriptions?userId=ad01c942-a24b-4af5-ba9b-1939f227081f", "protocol": "https", "host": ["e47fc55907c9", "ngrok-free", "app"], "path": ["api", "subscriptions"], "query": [{"key": "userId", "value": "ad01c942-a24b-4af5-ba9b-1939f227081f"}]}}, "response": []}]}