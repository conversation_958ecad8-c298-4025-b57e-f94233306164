{"info": {"_postman_id": "e0e03110-71c8-4ff7-8fb8-f2a7a2b8b1d4", "name": "Microsoft Graph", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json", "_exporter_id": "13166667", "_collection_link": "https://grimwolf.postman.co/workspace/Shared-Workspace~7d1fb684-f085-471d-8550-e4e442ef5afb/collection/13166667-e0e03110-71c8-4ff7-8fb8-f2a7a2b8b1d4?action=share&source=collection_link&creator=13166667"}, "item": [{"name": "get mails", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "clientSecret", "value": "****************************************", "type": "string"}, {"key": "clientId", "value": "0c45619b-508e-4b62-ab28-44cd2dba6312", "type": "string"}, {"key": "accessTokenUrl", "value": "https://login.microsoftonline.com/02a0ba1f-49da-4a0d-80c5-0c5f3784e72b/oauth2/v2.0/token", "type": "string"}, {"key": "authUrl", "value": "https://login.microsoftonline.com/02a0ba1f-49da-4a0d-80c5-0c5f3784e72b/oauth2/v2.0/authorize", "type": "string"}, {"key": "scope", "value": "https://graph.microsoft.com/.default", "type": "string"}, {"key": "password", "value": "D@leel24!", "type": "string"}, {"key": "username", "value": "admin", "type": "string"}, {"key": "client_authentication", "value": "header", "type": "string"}, {"key": "redirect_uri", "value": "https://oauth.psmn.io/v1/callback", "type": "string"}, {"key": "useBrowser", "value": false, "type": "boolean"}, {"key": "tokenName", "value": "token", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://graph.microsoft.com/v1.0/users/ad01c942-a24b-4af5-ba9b-1939f227081f/mailFolders/Inbox/messages", "protocol": "https", "host": ["graph", "microsoft", "com"], "path": ["v1.0", "users", "ad01c942-a24b-4af5-ba9b-1939f227081f", "mailFolders", "Inbox", "messages"]}}, "response": []}, {"name": "get subscriptions", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "clientSecret", "value": "****************************************", "type": "string"}, {"key": "clientId", "value": "0c45619b-508e-4b62-ab28-44cd2dba6312", "type": "string"}, {"key": "accessTokenUrl", "value": "https://login.microsoftonline.com/02a0ba1f-49da-4a0d-80c5-0c5f3784e72b/oauth2/v2.0/token", "type": "string"}, {"key": "authUrl", "value": "https://login.microsoftonline.com/02a0ba1f-49da-4a0d-80c5-0c5f3784e72b/oauth2/v2.0/authorize", "type": "string"}, {"key": "scope", "value": "https://graph.microsoft.com/.default", "type": "string"}, {"key": "password", "value": "D@leel24!", "type": "string"}, {"key": "username", "value": "admin", "type": "string"}, {"key": "client_authentication", "value": "header", "type": "string"}, {"key": "redirect_uri", "value": "https://oauth.psmn.io/v1/callback", "type": "string"}, {"key": "useBrowser", "value": false, "type": "boolean"}, {"key": "tokenName", "value": "token", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "GET", "header": [], "url": {"raw": "https://graph.microsoft.com/v1.0/subscriptions", "protocol": "https", "host": ["graph", "microsoft", "com"], "path": ["v1.0", "subscriptions"]}}, "response": []}, {"name": "create subscription", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "clientSecret", "value": "****************************************", "type": "string"}, {"key": "clientId", "value": "0c45619b-508e-4b62-ab28-44cd2dba6312", "type": "string"}, {"key": "accessTokenUrl", "value": "https://login.microsoftonline.com/02a0ba1f-49da-4a0d-80c5-0c5f3784e72b/oauth2/v2.0/token", "type": "string"}, {"key": "authUrl", "value": "https://login.microsoftonline.com/02a0ba1f-49da-4a0d-80c5-0c5f3784e72b/oauth2/v2.0/authorize", "type": "string"}, {"key": "scope", "value": "https://graph.microsoft.com/.default", "type": "string"}, {"key": "password", "value": "D@leel24!", "type": "string"}, {"key": "username", "value": "admin", "type": "string"}, {"key": "client_authentication", "value": "header", "type": "string"}, {"key": "redirect_uri", "value": "https://oauth.psmn.io/v1/callback", "type": "string"}, {"key": "useBrowser", "value": false, "type": "boolean"}, {"key": "tokenName", "value": "token", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "POST", "header": [], "body": {"mode": "raw", "raw": "{\n    \"changeType\": \"created\",\n    \"expirationDateTime\": \"2025-07-16T23:00:00.000Z\",\n    \"notificationUrl\": \"https://e47fc55907c9.ngrok-free.app/api/notifications\",\n    \"resource\": \"/users/ad01c942-a24b-4af5-ba9b-1939f227081f/messages\"\n}", "options": {"raw": {"language": "json"}}}, "url": {"raw": "https://graph.microsoft.com/v1.0/subscriptions", "protocol": "https", "host": ["graph", "microsoft", "com"], "path": ["v1.0", "subscriptions"]}}, "response": []}, {"name": "delete subscription", "request": {"auth": {"type": "oauth2", "oauth2": [{"key": "grant_type", "value": "client_credentials", "type": "string"}, {"key": "clientSecret", "value": "****************************************", "type": "string"}, {"key": "clientId", "value": "0c45619b-508e-4b62-ab28-44cd2dba6312", "type": "string"}, {"key": "accessTokenUrl", "value": "https://login.microsoftonline.com/02a0ba1f-49da-4a0d-80c5-0c5f3784e72b/oauth2/v2.0/token", "type": "string"}, {"key": "authUrl", "value": "https://login.microsoftonline.com/02a0ba1f-49da-4a0d-80c5-0c5f3784e72b/oauth2/v2.0/authorize", "type": "string"}, {"key": "scope", "value": "https://graph.microsoft.com/.default", "type": "string"}, {"key": "password", "value": "D@leel24!", "type": "string"}, {"key": "username", "value": "admin", "type": "string"}, {"key": "client_authentication", "value": "header", "type": "string"}, {"key": "redirect_uri", "value": "https://oauth.psmn.io/v1/callback", "type": "string"}, {"key": "useBrowser", "value": false, "type": "boolean"}, {"key": "tokenName", "value": "token", "type": "string"}, {"key": "addTokenTo", "value": "header", "type": "string"}]}, "method": "DELETE", "header": [], "url": {"raw": "https://graph.microsoft.com/v1.0/subscriptions/2bf5c16d-297c-4432-9356-926281655262", "protocol": "https", "host": ["graph", "microsoft", "com"], "path": ["v1.0", "subscriptions", "2bf5c16d-297c-4432-9356-926281655262"]}}, "response": []}]}