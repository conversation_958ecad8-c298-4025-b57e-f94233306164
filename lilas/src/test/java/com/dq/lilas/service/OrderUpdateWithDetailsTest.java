package com.dq.lilas.service;

import com.dq.lilas.domain.Order;
import com.dq.lilas.domain.OrderDetails;
import com.dq.lilas.domain.enumeration.OrderStatus;
import com.dq.lilas.repository.CompanyRepository;
import com.dq.lilas.repository.DailyBatchesRepository;
import com.dq.lilas.repository.GmsBrandsRepository;
import com.dq.lilas.repository.GmsClientsRepository;
import com.dq.lilas.repository.OrderDetailsRepository;
import com.dq.lilas.repository.OrderRepository;
import com.dq.lilas.service.dto.OrderDTO;
import com.dq.lilas.service.dto.OrderDetailsDTO;
import com.dq.lilas.service.impl.OrderServiceImpl;
import com.dq.lilas.service.mapper.OrderMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Optional;
import java.util.Set;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

/**
 * Test class for the new update services in OrderService.
 * Tests updateWithDetails() and bulkUpdateOrderDetails() methods.
 */
@ExtendWith(MockitoExtension.class)
class OrderUpdateWithDetailsTest {

    @Mock
    private OrderRepository orderRepository;

    @Mock
    private OrderDetailsRepository orderDetailsRepository;

    @Mock
    private OrderMapper orderMapper;

    @Mock
    private CompanyRepository companyRepository;

    @Mock
    private GmsClientsRepository gmsClientsRepository;

    @Mock
    private GmsBrandsRepository gmsBrandsRepository;

    @Mock
    private DailyBatchesRepository dailyBatchesRepository;

    private OrderServiceImpl orderService;

    @BeforeEach
    void setUp() {
        orderService = new OrderServiceImpl(
            orderRepository,
            orderDetailsRepository,
            orderMapper,
            companyRepository,
            gmsClientsRepository,
            gmsBrandsRepository,
            dailyBatchesRepository
        );
    }

    @Test
    void testUpdateWithDetails_Success() {
        // Given
        Long orderId = 1L;
        OrderDTO orderDTO = createTestOrderDTO(orderId);
        Order existingOrder = createTestOrder(orderId);
        Order updatedOrder = createTestOrder(orderId);
        
        // Mock repository calls
        when(orderRepository.findById(orderId)).thenReturn(Optional.of(existingOrder));
        when(orderRepository.save(any(Order.class))).thenReturn(updatedOrder);
        when(orderDetailsRepository.save(any(OrderDetails.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(orderMapper.toDto(any(Order.class))).thenReturn(orderDTO);
        doNothing().when(orderMapper).partialUpdate(any(Order.class), any(OrderDTO.class));

        // When
        OrderDTO result = orderService.updateWithDetails(orderDTO);

        // Then
        assertNotNull(result);
        assertEquals(orderId, result.getId());
        verify(orderRepository, times(2)).findById(orderId); // Once for validation, once for reload
        verify(orderRepository).save(any(Order.class));
        verify(orderDetailsRepository, atLeastOnce()).save(any(OrderDetails.class));
    }

    @Test
    void testUpdateWithDetails_OrderNotFound() {
        // Given
        Long orderId = 1L;
        OrderDTO orderDTO = createTestOrderDTO(orderId);
        
        when(orderRepository.findById(orderId)).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            orderService.updateWithDetails(orderDTO);
        });
        
        assertTrue(exception.getMessage().contains("Order not found"));
        verify(orderRepository).findById(orderId);
        verify(orderRepository, never()).save(any(Order.class));
    }

    @Test
    void testUpdateWithDetails_NullOrderId() {
        // Given
        OrderDTO orderDTO = createTestOrderDTO(null);

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            orderService.updateWithDetails(orderDTO);
        });
        
        assertTrue(exception.getMessage().contains("Order ID cannot be null"));
        verify(orderRepository, never()).findById(anyLong());
    }

    @Test
    void testBulkUpdateOrderDetails_Success() {
        // Given
        Long orderId = 1L;
        Set<OrderDetailsDTO> orderDetailsSet = createTestOrderDetailsSet();
        Order existingOrder = createTestOrder(orderId);
        OrderDTO expectedResult = createTestOrderDTO(orderId);
        
        when(orderRepository.findById(orderId)).thenReturn(Optional.of(existingOrder));
        when(orderDetailsRepository.save(any(OrderDetails.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(orderMapper.toDto(any(Order.class))).thenReturn(expectedResult);

        // When
        OrderDTO result = orderService.bulkUpdateOrderDetails(orderId, orderDetailsSet);

        // Then
        assertNotNull(result);
        assertEquals(orderId, result.getId());
        verify(orderRepository, times(2)).findById(orderId); // Once for validation, once for reload
        verify(orderDetailsRepository, atLeastOnce()).save(any(OrderDetails.class));
    }

    @Test
    void testBulkUpdateOrderDetails_OrderNotFound() {
        // Given
        Long orderId = 1L;
        Set<OrderDetailsDTO> orderDetailsSet = createTestOrderDetailsSet();
        
        when(orderRepository.findById(orderId)).thenReturn(Optional.empty());

        // When & Then
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            orderService.bulkUpdateOrderDetails(orderId, orderDetailsSet);
        });
        
        assertTrue(exception.getMessage().contains("Order not found"));
        verify(orderRepository).findById(orderId);
        verify(orderDetailsRepository, never()).save(any(OrderDetails.class));
    }

    @Test
    void testBulkUpdateOrderDetails_NullOrderId() {
        // Given
        Set<OrderDetailsDTO> orderDetailsSet = createTestOrderDetailsSet();

        // When & Then
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> {
            orderService.bulkUpdateOrderDetails(null, orderDetailsSet);
        });
        
        assertTrue(exception.getMessage().contains("Order ID cannot be null"));
        verify(orderRepository, never()).findById(anyLong());
    }

    // Helper methods
    private OrderDTO createTestOrderDTO(Long id) {
        OrderDTO orderDTO = new OrderDTO();
        orderDTO.setId(id);
        orderDTO.setOrderNumber("TEST-001");
        orderDTO.setStatus(OrderStatus.WAITING);
        orderDTO.setOrderDate(LocalDate.now());
        orderDTO.setCompany("Test Company");
        orderDTO.setOrderDetails(createTestOrderDetailsSet());
        return orderDTO;
    }

    private Order createTestOrder(Long id) {
        Order order = new Order();
        order.setId(id);
        order.setOrderNumber("TEST-001");
        order.setStatus(OrderStatus.WAITING);
        order.setOrderDate(LocalDate.now());
        order.setCompany("Test Company");
        order.setOrderDetails(new HashSet<>());
        return order;
    }

    private Set<OrderDetailsDTO> createTestOrderDetailsSet() {
        Set<OrderDetailsDTO> details = new HashSet<>();
        
        OrderDetailsDTO detail1 = new OrderDetailsDTO();
        detail1.setId(1L);
        detail1.setQuantity(BigDecimal.valueOf(10));
        detail1.setUnitPrice(25.50);
        detail1.setProductName("Test Product 1");
        detail1.setInternalCode("PROD-001");
        
        OrderDetailsDTO detail2 = new OrderDetailsDTO();
        detail2.setId(2L);
        detail2.setQuantity(BigDecimal.valueOf(5));
        detail2.setUnitPrice(15.75);
        detail2.setProductName("Test Product 2");
        detail2.setInternalCode("PROD-002");
        
        details.add(detail1);
        details.add(detail2);
        
        return details;
    }
}
