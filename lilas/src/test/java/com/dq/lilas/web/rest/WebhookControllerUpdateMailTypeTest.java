package com.dq.lilas.web.rest;

import com.dq.lilas.domain.enumeration.MailType;
import com.dq.lilas.service.MailsService;
import com.dq.lilas.service.dto.MailsDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.Optional;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Test class for the updateMailType method in WebhookController.
 * This test verifies that the mail type update functionality works correctly.
 */
@ExtendWith(MockitoExtension.class)
class WebhookControllerUpdateMailTypeTest {

    @Mock
    private MailsService mailsService;

    private WebhookController webhookController;

    @BeforeEach
    void setUp() {
        // Create a minimal WebhookController instance for testing
        webhookController = new WebhookController(
            null, // messageService - not needed for this test
            null, // props - not needed for this test
            mailsService,
            null, // attachementService - not needed for this test
            null, // orderService - not needed for this test
            null, // restTemplate - not needed for this test
            null  // objectMapper - not needed for this test
        );
    }

    @Test
    void testUpdateMailType_Success() throws Exception {
        // Given
        MailsDTO mail = new MailsDTO();
        mail.setId(1L);
        mail.setMailType(MailType.NOT_BC);

        MailsDTO updatedMail = new MailsDTO();
        updatedMail.setId(1L);
        updatedMail.setMailType(MailType.BC);

        when(mailsService.partialUpdate(any(MailsDTO.class))).thenReturn(Optional.of(updatedMail));

        // When
        invokeUpdateMailType(mail, MailType.BC);

        // Then
        verify(mailsService).partialUpdate(any(MailsDTO.class));
        // Verify that the local mail object was updated
        assert mail.getMailType() == MailType.BC;
    }

    @Test
    void testUpdateMailType_NullMail() throws Exception {
        // When
        invokeUpdateMailType(null, MailType.BC);

        // Then
        verify(mailsService, never()).partialUpdate(any(MailsDTO.class));
    }

    @Test
    void testUpdateMailType_NullMailId() throws Exception {
        // Given
        MailsDTO mail = new MailsDTO();
        mail.setId(null);

        // When
        invokeUpdateMailType(mail, MailType.BC);

        // Then
        verify(mailsService, never()).partialUpdate(any(MailsDTO.class));
    }

    @Test
    void testUpdateMailType_NullMailType() throws Exception {
        // Given
        MailsDTO mail = new MailsDTO();
        mail.setId(1L);

        // When
        invokeUpdateMailType(mail, null);

        // Then
        verify(mailsService, never()).partialUpdate(any(MailsDTO.class));
    }

    @Test
    void testUpdateMailType_SameMailType() throws Exception {
        // Given
        MailsDTO mail = new MailsDTO();
        mail.setId(1L);
        mail.setMailType(MailType.BC);

        // When
        invokeUpdateMailType(mail, MailType.BC);

        // Then
        verify(mailsService, never()).partialUpdate(any(MailsDTO.class));
    }

    @Test
    void testUpdateMailType_ServiceReturnsEmpty() throws Exception {
        // Given
        MailsDTO mail = new MailsDTO();
        mail.setId(1L);
        mail.setMailType(MailType.NOT_BC);

        when(mailsService.partialUpdate(any(MailsDTO.class))).thenReturn(Optional.empty());

        // When
        invokeUpdateMailType(mail, MailType.BC);

        // Then
        verify(mailsService).partialUpdate(any(MailsDTO.class));
        // Verify that the local mail object was NOT updated
        assert mail.getMailType() == MailType.NOT_BC;
    }

    /**
     * Helper method to invoke the private updateMailType method using reflection
     */
    private void invokeUpdateMailType(MailsDTO mail, MailType mailType) throws Exception {
        Method updateMailTypeMethod = WebhookController.class.getDeclaredMethod("updateMailType", MailsDTO.class, MailType.class);
        updateMailTypeMethod.setAccessible(true);
        updateMailTypeMethod.invoke(webhookController, mail, mailType);
    }
}
