package com.dq.lilas.rest;

import static com.dq.lilas.domain.UnitlangAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Unitlang;
import com.dq.lilas.repository.UnitlangRepository;
import com.dq.lilas.service.dto.UnitlangDTO;
import com.dq.lilas.service.mapper.UnitlangMapper;
import com.dq.lilas.web.rest.UnitlangResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link UnitlangResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class UnitlangResourceIT {

    private static final String DEFAULT_ADDRESS = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS = "BBBBBBBBBB";

    private static final String DEFAULT_NBR = "AAAAAAAAAA";
    private static final String UPDATED_NBR = "BBBBBBBBBB";

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_LANG = "AAAAA";
    private static final String UPDATED_LANG = "BBBBB";

    private static final String DEFAULT_ABRV = "AAAAAAAAAA";
    private static final String UPDATED_ABRV = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/unitlangs";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private UnitlangRepository unitlangRepository;

    @Autowired
    private UnitlangMapper unitlangMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restUnitlangMockMvc;

    private Unitlang unitlang;

    private Unitlang insertedUnitlang;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Unitlang createEntity() {
        return new Unitlang().address(DEFAULT_ADDRESS).nbr(DEFAULT_NBR).name(DEFAULT_NAME).lang(DEFAULT_LANG).abrv(DEFAULT_ABRV);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Unitlang createUpdatedEntity() {
        return new Unitlang().address(UPDATED_ADDRESS).nbr(UPDATED_NBR).name(UPDATED_NAME).lang(UPDATED_LANG).abrv(UPDATED_ABRV);
    }

    @BeforeEach
    void initTest() {
        unitlang = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedUnitlang != null) {
            unitlangRepository.delete(insertedUnitlang);
            insertedUnitlang = null;
        }
    }

    @Test
    @Transactional
    void createUnitlang() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Unitlang
        UnitlangDTO unitlangDTO = unitlangMapper.toDto(unitlang);
        var returnedUnitlangDTO = om.readValue(
            restUnitlangMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(unitlangDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            UnitlangDTO.class
        );

        // Validate the Unitlang in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedUnitlang = unitlangMapper.toEntity(returnedUnitlangDTO);
        assertUnitlangUpdatableFieldsEquals(returnedUnitlang, getPersistedUnitlang(returnedUnitlang));

        insertedUnitlang = returnedUnitlang;
    }

    @Test
    @Transactional
    void createUnitlangWithExistingId() throws Exception {
        // Create the Unitlang with an existing ID
        unitlang.setId(1L);
        UnitlangDTO unitlangDTO = unitlangMapper.toDto(unitlang);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restUnitlangMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(unitlangDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Unitlang in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkNameIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        unitlang.setName(null);

        // Create the Unitlang, which fails.
        UnitlangDTO unitlangDTO = unitlangMapper.toDto(unitlang);

        restUnitlangMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(unitlangDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkLangIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        unitlang.setLang(null);

        // Create the Unitlang, which fails.
        UnitlangDTO unitlangDTO = unitlangMapper.toDto(unitlang);

        restUnitlangMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(unitlangDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllUnitlangs() throws Exception {
        // Initialize the database
        insertedUnitlang = unitlangRepository.saveAndFlush(unitlang);

        // Get all the unitlangList
        restUnitlangMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(unitlang.getId().intValue())))
            .andExpect(jsonPath("$.[*].address").value(hasItem(DEFAULT_ADDRESS)))
            .andExpect(jsonPath("$.[*].nbr").value(hasItem(DEFAULT_NBR)))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].lang").value(hasItem(DEFAULT_LANG)))
            .andExpect(jsonPath("$.[*].abrv").value(hasItem(DEFAULT_ABRV)));
    }

    @Test
    @Transactional
    void getUnitlang() throws Exception {
        // Initialize the database
        insertedUnitlang = unitlangRepository.saveAndFlush(unitlang);

        // Get the unitlang
        restUnitlangMockMvc
            .perform(get(ENTITY_API_URL_ID, unitlang.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(unitlang.getId().intValue()))
            .andExpect(jsonPath("$.address").value(DEFAULT_ADDRESS))
            .andExpect(jsonPath("$.nbr").value(DEFAULT_NBR))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.lang").value(DEFAULT_LANG))
            .andExpect(jsonPath("$.abrv").value(DEFAULT_ABRV));
    }

    @Test
    @Transactional
    void getNonExistingUnitlang() throws Exception {
        // Get the unitlang
        restUnitlangMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingUnitlang() throws Exception {
        // Initialize the database
        insertedUnitlang = unitlangRepository.saveAndFlush(unitlang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the unitlang
        Unitlang updatedUnitlang = unitlangRepository.findById(unitlang.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedUnitlang are not directly saved in db
        em.detach(updatedUnitlang);
        updatedUnitlang.address(UPDATED_ADDRESS).nbr(UPDATED_NBR).name(UPDATED_NAME).lang(UPDATED_LANG).abrv(UPDATED_ABRV);
        UnitlangDTO unitlangDTO = unitlangMapper.toDto(updatedUnitlang);

        restUnitlangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, unitlangDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(unitlangDTO))
            )
            .andExpect(status().isOk());

        // Validate the Unitlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedUnitlangToMatchAllProperties(updatedUnitlang);
    }

    @Test
    @Transactional
    void putNonExistingUnitlang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        unitlang.setId(longCount.incrementAndGet());

        // Create the Unitlang
        UnitlangDTO unitlangDTO = unitlangMapper.toDto(unitlang);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restUnitlangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, unitlangDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(unitlangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Unitlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchUnitlang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        unitlang.setId(longCount.incrementAndGet());

        // Create the Unitlang
        UnitlangDTO unitlangDTO = unitlangMapper.toDto(unitlang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUnitlangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(unitlangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Unitlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamUnitlang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        unitlang.setId(longCount.incrementAndGet());

        // Create the Unitlang
        UnitlangDTO unitlangDTO = unitlangMapper.toDto(unitlang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUnitlangMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(unitlangDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Unitlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateUnitlangWithPatch() throws Exception {
        // Initialize the database
        insertedUnitlang = unitlangRepository.saveAndFlush(unitlang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the unitlang using partial update
        Unitlang partialUpdatedUnitlang = new Unitlang();
        partialUpdatedUnitlang.setId(unitlang.getId());

        restUnitlangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedUnitlang.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedUnitlang))
            )
            .andExpect(status().isOk());

        // Validate the Unitlang in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertUnitlangUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedUnitlang, unitlang), getPersistedUnitlang(unitlang));
    }

    @Test
    @Transactional
    void fullUpdateUnitlangWithPatch() throws Exception {
        // Initialize the database
        insertedUnitlang = unitlangRepository.saveAndFlush(unitlang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the unitlang using partial update
        Unitlang partialUpdatedUnitlang = new Unitlang();
        partialUpdatedUnitlang.setId(unitlang.getId());

        partialUpdatedUnitlang.address(UPDATED_ADDRESS).nbr(UPDATED_NBR).name(UPDATED_NAME).lang(UPDATED_LANG).abrv(UPDATED_ABRV);

        restUnitlangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedUnitlang.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedUnitlang))
            )
            .andExpect(status().isOk());

        // Validate the Unitlang in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertUnitlangUpdatableFieldsEquals(partialUpdatedUnitlang, getPersistedUnitlang(partialUpdatedUnitlang));
    }

    @Test
    @Transactional
    void patchNonExistingUnitlang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        unitlang.setId(longCount.incrementAndGet());

        // Create the Unitlang
        UnitlangDTO unitlangDTO = unitlangMapper.toDto(unitlang);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restUnitlangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, unitlangDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(unitlangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Unitlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchUnitlang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        unitlang.setId(longCount.incrementAndGet());

        // Create the Unitlang
        UnitlangDTO unitlangDTO = unitlangMapper.toDto(unitlang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUnitlangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(unitlangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Unitlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamUnitlang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        unitlang.setId(longCount.incrementAndGet());

        // Create the Unitlang
        UnitlangDTO unitlangDTO = unitlangMapper.toDto(unitlang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUnitlangMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(unitlangDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Unitlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteUnitlang() throws Exception {
        // Initialize the database
        insertedUnitlang = unitlangRepository.saveAndFlush(unitlang);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the unitlang
        restUnitlangMockMvc
            .perform(delete(ENTITY_API_URL_ID, unitlang.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return unitlangRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Unitlang getPersistedUnitlang(Unitlang unitlang) {
        return unitlangRepository.findById(unitlang.getId()).orElseThrow();
    }

    protected void assertPersistedUnitlangToMatchAllProperties(Unitlang expectedUnitlang) {
        assertUnitlangAllPropertiesEquals(expectedUnitlang, getPersistedUnitlang(expectedUnitlang));
    }

    protected void assertPersistedUnitlangToMatchUpdatableProperties(Unitlang expectedUnitlang) {
        assertUnitlangAllUpdatablePropertiesEquals(expectedUnitlang, getPersistedUnitlang(expectedUnitlang));
    }
}
