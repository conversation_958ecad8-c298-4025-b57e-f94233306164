package com.dq.lilas.rest;

import static com.dq.lilas.domain.UnitAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Unit;
import com.dq.lilas.repository.UnitRepository;
import com.dq.lilas.service.dto.UnitDTO;
import com.dq.lilas.service.mapper.UnitMapper;
import com.dq.lilas.web.rest.UnitResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link UnitResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class UnitResourceIT {

    private static final String DEFAULT_PARENTID = "AAAAAAAAAA";
    private static final String UPDATED_PARENTID = "BBBBBBBBBB";

    private static final String DEFAULT_TEL = "AAAAAAAAAA";
    private static final String UPDATED_TEL = "BBBBBBBBBB";

    private static final String DEFAULT_FAX = "AAAAAAAAAA";
    private static final String UPDATED_FAX = "BBBBBBBBBB";

    private static final Integer DEFAULT_ORDER = 1;
    private static final Integer UPDATED_ORDER = 2;

    private static final String DEFAULT_DEANSTATUS = "A";
    private static final String UPDATED_DEANSTATUS = "B";

    private static final String DEFAULT_ADDRESS = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS = "BBBBBBBBBB";

    private static final String DEFAULT_NBR = "AAAAAAAAAA";
    private static final String UPDATED_NBR = "BBBBBBBBBB";

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_LANG = "AAAAA";
    private static final String UPDATED_LANG = "BBBBB";

    private static final String DEFAULT_ABBREVIATED = "AAAAAAAAAA";
    private static final String UPDATED_ABBREVIATED = "BBBBBBBBBB";

    private static final String DEFAULT_MAIL = "AAAAAAAAAA";
    private static final String UPDATED_MAIL = "BBBBBBBBBB";

    private static final String DEFAULT_CHECKENABLED = "AAAAAAAAAA";
    private static final String UPDATED_CHECKENABLED = "BBBBBBBBBB";

    private static final String DEFAULT_ACTIVATE = "AAAAAAAAAA";
    private static final String UPDATED_ACTIVATE = "BBBBBBBBBB";

    private static final String DEFAULT_ACTIVE = "AAAAAAAAAA";
    private static final String UPDATED_ACTIVE = "BBBBBBBBBB";

    private static final String DEFAULT_LEVEL = "AAAAAAAAAA";
    private static final String UPDATED_LEVEL = "BBBBBBBBBB";

    private static final String DEFAULT_GRP = "AAAAAAAAAA";
    private static final String UPDATED_GRP = "BBBBBBBBBB";

    private static final String DEFAULT_COMPID = "AAAAAAAAAA";
    private static final String UPDATED_COMPID = "BBBBBBBBBB";

    private static final String DEFAULT_BRANCH = "AAAAAAAAAA";
    private static final String UPDATED_BRANCH = "BBBBBBBBBB";

    private static final String DEFAULT_REGOFFICE = "AAAAAAAAAA";
    private static final String UPDATED_REGOFFICE = "BBBBBBBBBB";

    private static final String DEFAULT_UNITGROUP = "AAAAAAAAAA";
    private static final String UPDATED_UNITGROUP = "BBBBBBBBBB";

    private static final String DEFAULT_GRDPARENT = "AAAAAAAAAA";
    private static final String UPDATED_GRDPARENT = "BBBBBBBBBB";

    private static final String DEFAULT_STATUS = "A";
    private static final String UPDATED_STATUS = "B";

    private static final String DEFAULT_CATEGORY = "AAAAAAAAAA";
    private static final String UPDATED_CATEGORY = "BBBBBBBBBB";

    private static final String DEFAULT_FUNCTION_UNIT = "AAAAAAAAAA";
    private static final String UPDATED_FUNCTION_UNIT = "BBBBBBBBBB";

    private static final String DEFAULT_POSITION = "AAAAAAAAAA";
    private static final String UPDATED_POSITION = "BBBBBBBBBB";

    private static final String DEFAULT_SECTION = "AAAAAAAAAA";
    private static final String UPDATED_SECTION = "BBBBBBBBBB";

    private static final String DEFAULT_CATEGDIR = "AAAAAAAAAA";
    private static final String UPDATED_CATEGDIR = "BBBBBBBBBB";

    private static final String DEFAULT_CATEG_UNIT = "AAAAAAAAAA";
    private static final String UPDATED_CATEG_UNIT = "BBBBBBBBBB";

    private static final String DEFAULT_FUNCTION = "AAAAAAAAAA";
    private static final String UPDATED_FUNCTION = "BBBBBBBBBB";

    private static final String DEFAULT_RESPONSIBLE = "AAAAAAAAAA";
    private static final String UPDATED_RESPONSIBLE = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/units";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private UnitRepository unitRepository;

    @Autowired
    private UnitMapper unitMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restUnitMockMvc;

    private Unit unit;

    private Unit insertedUnit;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Unit createEntity() {
        return new Unit()
            .parentid(DEFAULT_PARENTID)
            .tel(DEFAULT_TEL)
            .fax(DEFAULT_FAX)
            .order(DEFAULT_ORDER)
            .deanstatus(DEFAULT_DEANSTATUS)
            .address(DEFAULT_ADDRESS)
            .nbr(DEFAULT_NBR)
            .name(DEFAULT_NAME)
            .lang(DEFAULT_LANG)
            .abbreviated(DEFAULT_ABBREVIATED)
            .mail(DEFAULT_MAIL)
            .checkenabled(DEFAULT_CHECKENABLED)
            .activate(DEFAULT_ACTIVATE)
            .active(DEFAULT_ACTIVE)
            .level(DEFAULT_LEVEL)
            .grp(DEFAULT_GRP)
            .compid(DEFAULT_COMPID)
            .branch(DEFAULT_BRANCH)
            .regoffice(DEFAULT_REGOFFICE)
            .unitgroup(DEFAULT_UNITGROUP)
            .grdparent(DEFAULT_GRDPARENT)
            .status(DEFAULT_STATUS)
            .category(DEFAULT_CATEGORY)
            .functionUnit(DEFAULT_FUNCTION_UNIT)
            .position(DEFAULT_POSITION)
            .section(DEFAULT_SECTION)
            .categdir(DEFAULT_CATEGDIR)
            .categUnit(DEFAULT_CATEG_UNIT)
            .function(DEFAULT_FUNCTION)
            .responsible(DEFAULT_RESPONSIBLE);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Unit createUpdatedEntity() {
        return new Unit()
            .parentid(UPDATED_PARENTID)
            .tel(UPDATED_TEL)
            .fax(UPDATED_FAX)
            .order(UPDATED_ORDER)
            .deanstatus(UPDATED_DEANSTATUS)
            .address(UPDATED_ADDRESS)
            .nbr(UPDATED_NBR)
            .name(UPDATED_NAME)
            .lang(UPDATED_LANG)
            .abbreviated(UPDATED_ABBREVIATED)
            .mail(UPDATED_MAIL)
            .checkenabled(UPDATED_CHECKENABLED)
            .activate(UPDATED_ACTIVATE)
            .active(UPDATED_ACTIVE)
            .level(UPDATED_LEVEL)
            .grp(UPDATED_GRP)
            .compid(UPDATED_COMPID)
            .branch(UPDATED_BRANCH)
            .regoffice(UPDATED_REGOFFICE)
            .unitgroup(UPDATED_UNITGROUP)
            .grdparent(UPDATED_GRDPARENT)
            .status(UPDATED_STATUS)
            .category(UPDATED_CATEGORY)
            .functionUnit(UPDATED_FUNCTION_UNIT)
            .position(UPDATED_POSITION)
            .section(UPDATED_SECTION)
            .categdir(UPDATED_CATEGDIR)
            .categUnit(UPDATED_CATEG_UNIT)
            .function(UPDATED_FUNCTION)
            .responsible(UPDATED_RESPONSIBLE);
    }

    @BeforeEach
    void initTest() {
        unit = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedUnit != null) {
            unitRepository.delete(insertedUnit);
            insertedUnit = null;
        }
    }

    @Test
    @Transactional
    void createUnit() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Unit
        UnitDTO unitDTO = unitMapper.toDto(unit);
        var returnedUnitDTO = om.readValue(
            restUnitMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(unitDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            UnitDTO.class
        );

        // Validate the Unit in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedUnit = unitMapper.toEntity(returnedUnitDTO);
        assertUnitUpdatableFieldsEquals(returnedUnit, getPersistedUnit(returnedUnit));

        insertedUnit = returnedUnit;
    }

    @Test
    @Transactional
    void createUnitWithExistingId() throws Exception {
        // Create the Unit with an existing ID
        unit.setId(1L);
        UnitDTO unitDTO = unitMapper.toDto(unit);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restUnitMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(unitDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Unit in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllUnits() throws Exception {
        // Initialize the database
        insertedUnit = unitRepository.saveAndFlush(unit);

        // Get all the unitList
        restUnitMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(unit.getId().intValue())))
            .andExpect(jsonPath("$.[*].parentid").value(hasItem(DEFAULT_PARENTID)))
            .andExpect(jsonPath("$.[*].tel").value(hasItem(DEFAULT_TEL)))
            .andExpect(jsonPath("$.[*].fax").value(hasItem(DEFAULT_FAX)))
            .andExpect(jsonPath("$.[*].order").value(hasItem(DEFAULT_ORDER)))
            .andExpect(jsonPath("$.[*].deanstatus").value(hasItem(DEFAULT_DEANSTATUS)))
            .andExpect(jsonPath("$.[*].address").value(hasItem(DEFAULT_ADDRESS)))
            .andExpect(jsonPath("$.[*].nbr").value(hasItem(DEFAULT_NBR)))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].lang").value(hasItem(DEFAULT_LANG)))
            .andExpect(jsonPath("$.[*].abbreviated").value(hasItem(DEFAULT_ABBREVIATED)))
            .andExpect(jsonPath("$.[*].mail").value(hasItem(DEFAULT_MAIL)))
            .andExpect(jsonPath("$.[*].checkenabled").value(hasItem(DEFAULT_CHECKENABLED)))
            .andExpect(jsonPath("$.[*].activate").value(hasItem(DEFAULT_ACTIVATE)))
            .andExpect(jsonPath("$.[*].active").value(hasItem(DEFAULT_ACTIVE)))
            .andExpect(jsonPath("$.[*].level").value(hasItem(DEFAULT_LEVEL)))
            .andExpect(jsonPath("$.[*].grp").value(hasItem(DEFAULT_GRP)))
            .andExpect(jsonPath("$.[*].compid").value(hasItem(DEFAULT_COMPID)))
            .andExpect(jsonPath("$.[*].branch").value(hasItem(DEFAULT_BRANCH)))
            .andExpect(jsonPath("$.[*].regoffice").value(hasItem(DEFAULT_REGOFFICE)))
            .andExpect(jsonPath("$.[*].unitgroup").value(hasItem(DEFAULT_UNITGROUP)))
            .andExpect(jsonPath("$.[*].grdparent").value(hasItem(DEFAULT_GRDPARENT)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS)))
            .andExpect(jsonPath("$.[*].category").value(hasItem(DEFAULT_CATEGORY)))
            .andExpect(jsonPath("$.[*].functionUnit").value(hasItem(DEFAULT_FUNCTION_UNIT)))
            .andExpect(jsonPath("$.[*].position").value(hasItem(DEFAULT_POSITION)))
            .andExpect(jsonPath("$.[*].section").value(hasItem(DEFAULT_SECTION)))
            .andExpect(jsonPath("$.[*].categdir").value(hasItem(DEFAULT_CATEGDIR)))
            .andExpect(jsonPath("$.[*].categUnit").value(hasItem(DEFAULT_CATEG_UNIT)))
            .andExpect(jsonPath("$.[*].function").value(hasItem(DEFAULT_FUNCTION)))
            .andExpect(jsonPath("$.[*].responsible").value(hasItem(DEFAULT_RESPONSIBLE)));
    }

    @Test
    @Transactional
    void getUnit() throws Exception {
        // Initialize the database
        insertedUnit = unitRepository.saveAndFlush(unit);

        // Get the unit
        restUnitMockMvc
            .perform(get(ENTITY_API_URL_ID, unit.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(unit.getId().intValue()))
            .andExpect(jsonPath("$.parentid").value(DEFAULT_PARENTID))
            .andExpect(jsonPath("$.tel").value(DEFAULT_TEL))
            .andExpect(jsonPath("$.fax").value(DEFAULT_FAX))
            .andExpect(jsonPath("$.order").value(DEFAULT_ORDER))
            .andExpect(jsonPath("$.deanstatus").value(DEFAULT_DEANSTATUS))
            .andExpect(jsonPath("$.address").value(DEFAULT_ADDRESS))
            .andExpect(jsonPath("$.nbr").value(DEFAULT_NBR))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.lang").value(DEFAULT_LANG))
            .andExpect(jsonPath("$.abbreviated").value(DEFAULT_ABBREVIATED))
            .andExpect(jsonPath("$.mail").value(DEFAULT_MAIL))
            .andExpect(jsonPath("$.checkenabled").value(DEFAULT_CHECKENABLED))
            .andExpect(jsonPath("$.activate").value(DEFAULT_ACTIVATE))
            .andExpect(jsonPath("$.active").value(DEFAULT_ACTIVE))
            .andExpect(jsonPath("$.level").value(DEFAULT_LEVEL))
            .andExpect(jsonPath("$.grp").value(DEFAULT_GRP))
            .andExpect(jsonPath("$.compid").value(DEFAULT_COMPID))
            .andExpect(jsonPath("$.branch").value(DEFAULT_BRANCH))
            .andExpect(jsonPath("$.regoffice").value(DEFAULT_REGOFFICE))
            .andExpect(jsonPath("$.unitgroup").value(DEFAULT_UNITGROUP))
            .andExpect(jsonPath("$.grdparent").value(DEFAULT_GRDPARENT))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS))
            .andExpect(jsonPath("$.category").value(DEFAULT_CATEGORY))
            .andExpect(jsonPath("$.functionUnit").value(DEFAULT_FUNCTION_UNIT))
            .andExpect(jsonPath("$.position").value(DEFAULT_POSITION))
            .andExpect(jsonPath("$.section").value(DEFAULT_SECTION))
            .andExpect(jsonPath("$.categdir").value(DEFAULT_CATEGDIR))
            .andExpect(jsonPath("$.categUnit").value(DEFAULT_CATEG_UNIT))
            .andExpect(jsonPath("$.function").value(DEFAULT_FUNCTION))
            .andExpect(jsonPath("$.responsible").value(DEFAULT_RESPONSIBLE));
    }

    @Test
    @Transactional
    void getNonExistingUnit() throws Exception {
        // Get the unit
        restUnitMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingUnit() throws Exception {
        // Initialize the database
        insertedUnit = unitRepository.saveAndFlush(unit);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the unit
        Unit updatedUnit = unitRepository.findById(unit.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedUnit are not directly saved in db
        em.detach(updatedUnit);
        updatedUnit
            .parentid(UPDATED_PARENTID)
            .tel(UPDATED_TEL)
            .fax(UPDATED_FAX)
            .order(UPDATED_ORDER)
            .deanstatus(UPDATED_DEANSTATUS)
            .address(UPDATED_ADDRESS)
            .nbr(UPDATED_NBR)
            .name(UPDATED_NAME)
            .lang(UPDATED_LANG)
            .abbreviated(UPDATED_ABBREVIATED)
            .mail(UPDATED_MAIL)
            .checkenabled(UPDATED_CHECKENABLED)
            .activate(UPDATED_ACTIVATE)
            .active(UPDATED_ACTIVE)
            .level(UPDATED_LEVEL)
            .grp(UPDATED_GRP)
            .compid(UPDATED_COMPID)
            .branch(UPDATED_BRANCH)
            .regoffice(UPDATED_REGOFFICE)
            .unitgroup(UPDATED_UNITGROUP)
            .grdparent(UPDATED_GRDPARENT)
            .status(UPDATED_STATUS)
            .category(UPDATED_CATEGORY)
            .functionUnit(UPDATED_FUNCTION_UNIT)
            .position(UPDATED_POSITION)
            .section(UPDATED_SECTION)
            .categdir(UPDATED_CATEGDIR)
            .categUnit(UPDATED_CATEG_UNIT)
            .function(UPDATED_FUNCTION)
            .responsible(UPDATED_RESPONSIBLE);
        UnitDTO unitDTO = unitMapper.toDto(updatedUnit);

        restUnitMockMvc
            .perform(put(ENTITY_API_URL_ID, unitDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(unitDTO)))
            .andExpect(status().isOk());

        // Validate the Unit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedUnitToMatchAllProperties(updatedUnit);
    }

    @Test
    @Transactional
    void putNonExistingUnit() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        unit.setId(longCount.incrementAndGet());

        // Create the Unit
        UnitDTO unitDTO = unitMapper.toDto(unit);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restUnitMockMvc
            .perform(put(ENTITY_API_URL_ID, unitDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(unitDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Unit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchUnit() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        unit.setId(longCount.incrementAndGet());

        // Create the Unit
        UnitDTO unitDTO = unitMapper.toDto(unit);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUnitMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(unitDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Unit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamUnit() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        unit.setId(longCount.incrementAndGet());

        // Create the Unit
        UnitDTO unitDTO = unitMapper.toDto(unit);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUnitMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(unitDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Unit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateUnitWithPatch() throws Exception {
        // Initialize the database
        insertedUnit = unitRepository.saveAndFlush(unit);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the unit using partial update
        Unit partialUpdatedUnit = new Unit();
        partialUpdatedUnit.setId(unit.getId());

        partialUpdatedUnit
            .tel(UPDATED_TEL)
            .order(UPDATED_ORDER)
            .nbr(UPDATED_NBR)
            .name(UPDATED_NAME)
            .lang(UPDATED_LANG)
            .abbreviated(UPDATED_ABBREVIATED)
            .mail(UPDATED_MAIL)
            .grp(UPDATED_GRP)
            .unitgroup(UPDATED_UNITGROUP)
            .category(UPDATED_CATEGORY)
            .categdir(UPDATED_CATEGDIR)
            .categUnit(UPDATED_CATEG_UNIT)
            .function(UPDATED_FUNCTION);

        restUnitMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedUnit.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedUnit))
            )
            .andExpect(status().isOk());

        // Validate the Unit in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertUnitUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedUnit, unit), getPersistedUnit(unit));
    }

    @Test
    @Transactional
    void fullUpdateUnitWithPatch() throws Exception {
        // Initialize the database
        insertedUnit = unitRepository.saveAndFlush(unit);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the unit using partial update
        Unit partialUpdatedUnit = new Unit();
        partialUpdatedUnit.setId(unit.getId());

        partialUpdatedUnit
            .parentid(UPDATED_PARENTID)
            .tel(UPDATED_TEL)
            .fax(UPDATED_FAX)
            .order(UPDATED_ORDER)
            .deanstatus(UPDATED_DEANSTATUS)
            .address(UPDATED_ADDRESS)
            .nbr(UPDATED_NBR)
            .name(UPDATED_NAME)
            .lang(UPDATED_LANG)
            .abbreviated(UPDATED_ABBREVIATED)
            .mail(UPDATED_MAIL)
            .checkenabled(UPDATED_CHECKENABLED)
            .activate(UPDATED_ACTIVATE)
            .active(UPDATED_ACTIVE)
            .level(UPDATED_LEVEL)
            .grp(UPDATED_GRP)
            .compid(UPDATED_COMPID)
            .branch(UPDATED_BRANCH)
            .regoffice(UPDATED_REGOFFICE)
            .unitgroup(UPDATED_UNITGROUP)
            .grdparent(UPDATED_GRDPARENT)
            .status(UPDATED_STATUS)
            .category(UPDATED_CATEGORY)
            .functionUnit(UPDATED_FUNCTION_UNIT)
            .position(UPDATED_POSITION)
            .section(UPDATED_SECTION)
            .categdir(UPDATED_CATEGDIR)
            .categUnit(UPDATED_CATEG_UNIT)
            .function(UPDATED_FUNCTION)
            .responsible(UPDATED_RESPONSIBLE);

        restUnitMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedUnit.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedUnit))
            )
            .andExpect(status().isOk());

        // Validate the Unit in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertUnitUpdatableFieldsEquals(partialUpdatedUnit, getPersistedUnit(partialUpdatedUnit));
    }

    @Test
    @Transactional
    void patchNonExistingUnit() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        unit.setId(longCount.incrementAndGet());

        // Create the Unit
        UnitDTO unitDTO = unitMapper.toDto(unit);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restUnitMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, unitDTO.getId()).contentType("application/merge-patch+json").content(om.writeValueAsBytes(unitDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Unit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchUnit() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        unit.setId(longCount.incrementAndGet());

        // Create the Unit
        UnitDTO unitDTO = unitMapper.toDto(unit);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUnitMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(unitDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Unit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamUnit() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        unit.setId(longCount.incrementAndGet());

        // Create the Unit
        UnitDTO unitDTO = unitMapper.toDto(unit);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUnitMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(unitDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Unit in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteUnit() throws Exception {
        // Initialize the database
        insertedUnit = unitRepository.saveAndFlush(unit);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the unit
        restUnitMockMvc
            .perform(delete(ENTITY_API_URL_ID, unit.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return unitRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Unit getPersistedUnit(Unit unit) {
        return unitRepository.findById(unit.getId()).orElseThrow();
    }

    protected void assertPersistedUnitToMatchAllProperties(Unit expectedUnit) {
        assertUnitAllPropertiesEquals(expectedUnit, getPersistedUnit(expectedUnit));
    }

    protected void assertPersistedUnitToMatchUpdatableProperties(Unit expectedUnit) {
        assertUnitAllUpdatablePropertiesEquals(expectedUnit, getPersistedUnit(expectedUnit));
    }
}
