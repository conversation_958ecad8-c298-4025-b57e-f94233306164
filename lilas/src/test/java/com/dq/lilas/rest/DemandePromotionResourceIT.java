package com.dq.lilas.rest;

import static com.dq.lilas.domain.DemandePromotionAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.DemandePromotion;
import com.dq.lilas.repository.DemandePromotionRepository;
import com.dq.lilas.service.dto.DemandePromotionDTO;
import com.dq.lilas.service.mapper.DemandePromotionMapper;
import com.dq.lilas.web.rest.DemandePromotionResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link DemandePromotionResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class DemandePromotionResourceIT {

    private static final String DEFAULT_CODE_CLIENT = "AAAAAAAAAA";
    private static final String UPDATED_CODE_CLIENT = "BBBBBBBBBB";

    private static final String DEFAULT_ENSEIGNE = "AAAAAAAAAA";
    private static final String UPDATED_ENSEIGNE = "BBBBBBBBBB";

    private static final String DEFAULT_ACTION = "AAAAAAAAAA";
    private static final String UPDATED_ACTION = "BBBBBBBBBB";

    private static final Instant DEFAULT_PERIOD_PROMOTION_START = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_PERIOD_PROMOTION_START = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_PERIOD_PROMOTION_END = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_PERIOD_PROMOTION_END = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_PERIOD_FACTURATION_START = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_PERIOD_FACTURATION_START = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_PERIOD_FACTURATION_END = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_PERIOD_FACTURATION_END = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String ENTITY_API_URL = "/api/demande-promotions";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private DemandePromotionRepository demandePromotionRepository;

    @Autowired
    private DemandePromotionMapper demandePromotionMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restDemandePromotionMockMvc;

    private DemandePromotion demandePromotion;

    private DemandePromotion insertedDemandePromotion;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static DemandePromotion createEntity() {
        return new DemandePromotion()
            .codeClient(DEFAULT_CODE_CLIENT)
            .enseigne(DEFAULT_ENSEIGNE)
            .action(DEFAULT_ACTION)
            .periodPromotionStart(DEFAULT_PERIOD_PROMOTION_START)
            .periodPromotionEnd(DEFAULT_PERIOD_PROMOTION_END)
            .periodFacturationStart(DEFAULT_PERIOD_FACTURATION_START)
            .periodFacturationEnd(DEFAULT_PERIOD_FACTURATION_END);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static DemandePromotion createUpdatedEntity() {
        return new DemandePromotion()
            .codeClient(UPDATED_CODE_CLIENT)
            .enseigne(UPDATED_ENSEIGNE)
            .action(UPDATED_ACTION)
            .periodPromotionStart(UPDATED_PERIOD_PROMOTION_START)
            .periodPromotionEnd(UPDATED_PERIOD_PROMOTION_END)
            .periodFacturationStart(UPDATED_PERIOD_FACTURATION_START)
            .periodFacturationEnd(UPDATED_PERIOD_FACTURATION_END);
    }

    @BeforeEach
    void initTest() {
        demandePromotion = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedDemandePromotion != null) {
            demandePromotionRepository.delete(insertedDemandePromotion);
            insertedDemandePromotion = null;
        }
    }

    @Test
    @Transactional
    void createDemandePromotion() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the DemandePromotion
        DemandePromotionDTO demandePromotionDTO = demandePromotionMapper.toDto(demandePromotion);
        var returnedDemandePromotionDTO = om.readValue(
            restDemandePromotionMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(demandePromotionDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            DemandePromotionDTO.class
        );

        // Validate the DemandePromotion in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedDemandePromotion = demandePromotionMapper.toEntity(returnedDemandePromotionDTO);
        assertDemandePromotionUpdatableFieldsEquals(returnedDemandePromotion, getPersistedDemandePromotion(returnedDemandePromotion));

        insertedDemandePromotion = returnedDemandePromotion;
    }

    @Test
    @Transactional
    void createDemandePromotionWithExistingId() throws Exception {
        // Create the DemandePromotion with an existing ID
        demandePromotion.setId(1L);
        DemandePromotionDTO demandePromotionDTO = demandePromotionMapper.toDto(demandePromotion);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restDemandePromotionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(demandePromotionDTO)))
            .andExpect(status().isBadRequest());

        // Validate the DemandePromotion in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllDemandePromotions() throws Exception {
        // Initialize the database
        insertedDemandePromotion = demandePromotionRepository.saveAndFlush(demandePromotion);

        // Get all the demandePromotionList
        restDemandePromotionMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(demandePromotion.getId().intValue())))
            .andExpect(jsonPath("$.[*].codeClient").value(hasItem(DEFAULT_CODE_CLIENT)))
            .andExpect(jsonPath("$.[*].enseigne").value(hasItem(DEFAULT_ENSEIGNE)))
            .andExpect(jsonPath("$.[*].action").value(hasItem(DEFAULT_ACTION)))
            .andExpect(jsonPath("$.[*].periodPromotionStart").value(hasItem(DEFAULT_PERIOD_PROMOTION_START.toString())))
            .andExpect(jsonPath("$.[*].periodPromotionEnd").value(hasItem(DEFAULT_PERIOD_PROMOTION_END.toString())))
            .andExpect(jsonPath("$.[*].periodFacturationStart").value(hasItem(DEFAULT_PERIOD_FACTURATION_START.toString())))
            .andExpect(jsonPath("$.[*].periodFacturationEnd").value(hasItem(DEFAULT_PERIOD_FACTURATION_END.toString())));
    }

    @Test
    @Transactional
    void getDemandePromotion() throws Exception {
        // Initialize the database
        insertedDemandePromotion = demandePromotionRepository.saveAndFlush(demandePromotion);

        // Get the demandePromotion
        restDemandePromotionMockMvc
            .perform(get(ENTITY_API_URL_ID, demandePromotion.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(demandePromotion.getId().intValue()))
            .andExpect(jsonPath("$.codeClient").value(DEFAULT_CODE_CLIENT))
            .andExpect(jsonPath("$.enseigne").value(DEFAULT_ENSEIGNE))
            .andExpect(jsonPath("$.action").value(DEFAULT_ACTION))
            .andExpect(jsonPath("$.periodPromotionStart").value(DEFAULT_PERIOD_PROMOTION_START.toString()))
            .andExpect(jsonPath("$.periodPromotionEnd").value(DEFAULT_PERIOD_PROMOTION_END.toString()))
            .andExpect(jsonPath("$.periodFacturationStart").value(DEFAULT_PERIOD_FACTURATION_START.toString()))
            .andExpect(jsonPath("$.periodFacturationEnd").value(DEFAULT_PERIOD_FACTURATION_END.toString()));
    }

    @Test
    @Transactional
    void getNonExistingDemandePromotion() throws Exception {
        // Get the demandePromotion
        restDemandePromotionMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingDemandePromotion() throws Exception {
        // Initialize the database
        insertedDemandePromotion = demandePromotionRepository.saveAndFlush(demandePromotion);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the demandePromotion
        DemandePromotion updatedDemandePromotion = demandePromotionRepository.findById(demandePromotion.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedDemandePromotion are not directly saved in db
        em.detach(updatedDemandePromotion);
        updatedDemandePromotion
            .codeClient(UPDATED_CODE_CLIENT)
            .enseigne(UPDATED_ENSEIGNE)
            .action(UPDATED_ACTION)
            .periodPromotionStart(UPDATED_PERIOD_PROMOTION_START)
            .periodPromotionEnd(UPDATED_PERIOD_PROMOTION_END)
            .periodFacturationStart(UPDATED_PERIOD_FACTURATION_START)
            .periodFacturationEnd(UPDATED_PERIOD_FACTURATION_END);
        DemandePromotionDTO demandePromotionDTO = demandePromotionMapper.toDto(updatedDemandePromotion);

        restDemandePromotionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, demandePromotionDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(demandePromotionDTO))
            )
            .andExpect(status().isOk());

        // Validate the DemandePromotion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedDemandePromotionToMatchAllProperties(updatedDemandePromotion);
    }

    @Test
    @Transactional
    void putNonExistingDemandePromotion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        demandePromotion.setId(longCount.incrementAndGet());

        // Create the DemandePromotion
        DemandePromotionDTO demandePromotionDTO = demandePromotionMapper.toDto(demandePromotion);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restDemandePromotionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, demandePromotionDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(demandePromotionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the DemandePromotion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchDemandePromotion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        demandePromotion.setId(longCount.incrementAndGet());

        // Create the DemandePromotion
        DemandePromotionDTO demandePromotionDTO = demandePromotionMapper.toDto(demandePromotion);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDemandePromotionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(demandePromotionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the DemandePromotion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamDemandePromotion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        demandePromotion.setId(longCount.incrementAndGet());

        // Create the DemandePromotion
        DemandePromotionDTO demandePromotionDTO = demandePromotionMapper.toDto(demandePromotion);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDemandePromotionMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(demandePromotionDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the DemandePromotion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateDemandePromotionWithPatch() throws Exception {
        // Initialize the database
        insertedDemandePromotion = demandePromotionRepository.saveAndFlush(demandePromotion);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the demandePromotion using partial update
        DemandePromotion partialUpdatedDemandePromotion = new DemandePromotion();
        partialUpdatedDemandePromotion.setId(demandePromotion.getId());

        partialUpdatedDemandePromotion
            .codeClient(UPDATED_CODE_CLIENT)
            .action(UPDATED_ACTION)
            .periodPromotionEnd(UPDATED_PERIOD_PROMOTION_END)
            .periodFacturationStart(UPDATED_PERIOD_FACTURATION_START);

        restDemandePromotionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedDemandePromotion.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedDemandePromotion))
            )
            .andExpect(status().isOk());

        // Validate the DemandePromotion in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertDemandePromotionUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedDemandePromotion, demandePromotion),
            getPersistedDemandePromotion(demandePromotion)
        );
    }

    @Test
    @Transactional
    void fullUpdateDemandePromotionWithPatch() throws Exception {
        // Initialize the database
        insertedDemandePromotion = demandePromotionRepository.saveAndFlush(demandePromotion);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the demandePromotion using partial update
        DemandePromotion partialUpdatedDemandePromotion = new DemandePromotion();
        partialUpdatedDemandePromotion.setId(demandePromotion.getId());

        partialUpdatedDemandePromotion
            .codeClient(UPDATED_CODE_CLIENT)
            .enseigne(UPDATED_ENSEIGNE)
            .action(UPDATED_ACTION)
            .periodPromotionStart(UPDATED_PERIOD_PROMOTION_START)
            .periodPromotionEnd(UPDATED_PERIOD_PROMOTION_END)
            .periodFacturationStart(UPDATED_PERIOD_FACTURATION_START)
            .periodFacturationEnd(UPDATED_PERIOD_FACTURATION_END);

        restDemandePromotionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedDemandePromotion.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedDemandePromotion))
            )
            .andExpect(status().isOk());

        // Validate the DemandePromotion in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertDemandePromotionUpdatableFieldsEquals(
            partialUpdatedDemandePromotion,
            getPersistedDemandePromotion(partialUpdatedDemandePromotion)
        );
    }

    @Test
    @Transactional
    void patchNonExistingDemandePromotion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        demandePromotion.setId(longCount.incrementAndGet());

        // Create the DemandePromotion
        DemandePromotionDTO demandePromotionDTO = demandePromotionMapper.toDto(demandePromotion);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restDemandePromotionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, demandePromotionDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(demandePromotionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the DemandePromotion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchDemandePromotion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        demandePromotion.setId(longCount.incrementAndGet());

        // Create the DemandePromotion
        DemandePromotionDTO demandePromotionDTO = demandePromotionMapper.toDto(demandePromotion);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDemandePromotionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(demandePromotionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the DemandePromotion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamDemandePromotion() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        demandePromotion.setId(longCount.incrementAndGet());

        // Create the DemandePromotion
        DemandePromotionDTO demandePromotionDTO = demandePromotionMapper.toDto(demandePromotion);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDemandePromotionMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(demandePromotionDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the DemandePromotion in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteDemandePromotion() throws Exception {
        // Initialize the database
        insertedDemandePromotion = demandePromotionRepository.saveAndFlush(demandePromotion);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the demandePromotion
        restDemandePromotionMockMvc
            .perform(delete(ENTITY_API_URL_ID, demandePromotion.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return demandePromotionRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected DemandePromotion getPersistedDemandePromotion(DemandePromotion demandePromotion) {
        return demandePromotionRepository.findById(demandePromotion.getId()).orElseThrow();
    }

    protected void assertPersistedDemandePromotionToMatchAllProperties(DemandePromotion expectedDemandePromotion) {
        assertDemandePromotionAllPropertiesEquals(expectedDemandePromotion, getPersistedDemandePromotion(expectedDemandePromotion));
    }

    protected void assertPersistedDemandePromotionToMatchUpdatableProperties(DemandePromotion expectedDemandePromotion) {
        assertDemandePromotionAllUpdatablePropertiesEquals(
            expectedDemandePromotion,
            getPersistedDemandePromotion(expectedDemandePromotion)
        );
    }
}
