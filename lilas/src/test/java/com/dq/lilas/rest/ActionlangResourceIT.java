package com.dq.lilas.rest;

import static com.dq.lilas.domain.ActionlangAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Actionlang;
import com.dq.lilas.repository.ActionlangRepository;
import com.dq.lilas.service.dto.ActionlangDTO;
import com.dq.lilas.service.mapper.ActionlangMapper;
import com.dq.lilas.web.rest.ActionlangResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ActionlangResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ActionlangResourceIT {

    private static final String DEFAULT_APP_TYPE = "AAAAAAAAAA";
    private static final String UPDATED_APP_TYPE = "BBBBBBBBBB";

    private static final String DEFAULT_LANG = "AAAAA";
    private static final String UPDATED_LANG = "BBBBB";

    private static final String DEFAULT_ABRV = "AAAAAAAAAA";
    private static final String UPDATED_ABRV = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/actionlangs";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ActionlangRepository actionlangRepository;

    @Autowired
    private ActionlangMapper actionlangMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restActionlangMockMvc;

    private Actionlang actionlang;

    private Actionlang insertedActionlang;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Actionlang createEntity() {
        return new Actionlang().appType(DEFAULT_APP_TYPE).lang(DEFAULT_LANG).abrv(DEFAULT_ABRV);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Actionlang createUpdatedEntity() {
        return new Actionlang().appType(UPDATED_APP_TYPE).lang(UPDATED_LANG).abrv(UPDATED_ABRV);
    }

    @BeforeEach
    void initTest() {
        actionlang = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedActionlang != null) {
            actionlangRepository.delete(insertedActionlang);
            insertedActionlang = null;
        }
    }

    @Test
    @Transactional
    void createActionlang() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Actionlang
        ActionlangDTO actionlangDTO = actionlangMapper.toDto(actionlang);
        var returnedActionlangDTO = om.readValue(
            restActionlangMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(actionlangDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ActionlangDTO.class
        );

        // Validate the Actionlang in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedActionlang = actionlangMapper.toEntity(returnedActionlangDTO);
        assertActionlangUpdatableFieldsEquals(returnedActionlang, getPersistedActionlang(returnedActionlang));

        insertedActionlang = returnedActionlang;
    }

    @Test
    @Transactional
    void createActionlangWithExistingId() throws Exception {
        // Create the Actionlang with an existing ID
        actionlang.setId(1L);
        ActionlangDTO actionlangDTO = actionlangMapper.toDto(actionlang);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restActionlangMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(actionlangDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Actionlang in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllActionlangs() throws Exception {
        // Initialize the database
        insertedActionlang = actionlangRepository.saveAndFlush(actionlang);

        // Get all the actionlangList
        restActionlangMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(actionlang.getId().intValue())))
            .andExpect(jsonPath("$.[*].appType").value(hasItem(DEFAULT_APP_TYPE)))
            .andExpect(jsonPath("$.[*].lang").value(hasItem(DEFAULT_LANG)))
            .andExpect(jsonPath("$.[*].abrv").value(hasItem(DEFAULT_ABRV)));
    }

    @Test
    @Transactional
    void getActionlang() throws Exception {
        // Initialize the database
        insertedActionlang = actionlangRepository.saveAndFlush(actionlang);

        // Get the actionlang
        restActionlangMockMvc
            .perform(get(ENTITY_API_URL_ID, actionlang.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(actionlang.getId().intValue()))
            .andExpect(jsonPath("$.appType").value(DEFAULT_APP_TYPE))
            .andExpect(jsonPath("$.lang").value(DEFAULT_LANG))
            .andExpect(jsonPath("$.abrv").value(DEFAULT_ABRV));
    }

    @Test
    @Transactional
    void getNonExistingActionlang() throws Exception {
        // Get the actionlang
        restActionlangMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingActionlang() throws Exception {
        // Initialize the database
        insertedActionlang = actionlangRepository.saveAndFlush(actionlang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the actionlang
        Actionlang updatedActionlang = actionlangRepository.findById(actionlang.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedActionlang are not directly saved in db
        em.detach(updatedActionlang);
        updatedActionlang.appType(UPDATED_APP_TYPE).lang(UPDATED_LANG).abrv(UPDATED_ABRV);
        ActionlangDTO actionlangDTO = actionlangMapper.toDto(updatedActionlang);

        restActionlangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, actionlangDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(actionlangDTO))
            )
            .andExpect(status().isOk());

        // Validate the Actionlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedActionlangToMatchAllProperties(updatedActionlang);
    }

    @Test
    @Transactional
    void putNonExistingActionlang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        actionlang.setId(longCount.incrementAndGet());

        // Create the Actionlang
        ActionlangDTO actionlangDTO = actionlangMapper.toDto(actionlang);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restActionlangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, actionlangDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(actionlangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Actionlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchActionlang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        actionlang.setId(longCount.incrementAndGet());

        // Create the Actionlang
        ActionlangDTO actionlangDTO = actionlangMapper.toDto(actionlang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restActionlangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(actionlangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Actionlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamActionlang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        actionlang.setId(longCount.incrementAndGet());

        // Create the Actionlang
        ActionlangDTO actionlangDTO = actionlangMapper.toDto(actionlang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restActionlangMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(actionlangDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Actionlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateActionlangWithPatch() throws Exception {
        // Initialize the database
        insertedActionlang = actionlangRepository.saveAndFlush(actionlang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the actionlang using partial update
        Actionlang partialUpdatedActionlang = new Actionlang();
        partialUpdatedActionlang.setId(actionlang.getId());

        partialUpdatedActionlang.abrv(UPDATED_ABRV);

        restActionlangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedActionlang.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedActionlang))
            )
            .andExpect(status().isOk());

        // Validate the Actionlang in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertActionlangUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedActionlang, actionlang),
            getPersistedActionlang(actionlang)
        );
    }

    @Test
    @Transactional
    void fullUpdateActionlangWithPatch() throws Exception {
        // Initialize the database
        insertedActionlang = actionlangRepository.saveAndFlush(actionlang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the actionlang using partial update
        Actionlang partialUpdatedActionlang = new Actionlang();
        partialUpdatedActionlang.setId(actionlang.getId());

        partialUpdatedActionlang.appType(UPDATED_APP_TYPE).lang(UPDATED_LANG).abrv(UPDATED_ABRV);

        restActionlangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedActionlang.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedActionlang))
            )
            .andExpect(status().isOk());

        // Validate the Actionlang in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertActionlangUpdatableFieldsEquals(partialUpdatedActionlang, getPersistedActionlang(partialUpdatedActionlang));
    }

    @Test
    @Transactional
    void patchNonExistingActionlang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        actionlang.setId(longCount.incrementAndGet());

        // Create the Actionlang
        ActionlangDTO actionlangDTO = actionlangMapper.toDto(actionlang);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restActionlangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, actionlangDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(actionlangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Actionlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchActionlang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        actionlang.setId(longCount.incrementAndGet());

        // Create the Actionlang
        ActionlangDTO actionlangDTO = actionlangMapper.toDto(actionlang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restActionlangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(actionlangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Actionlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamActionlang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        actionlang.setId(longCount.incrementAndGet());

        // Create the Actionlang
        ActionlangDTO actionlangDTO = actionlangMapper.toDto(actionlang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restActionlangMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(actionlangDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Actionlang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteActionlang() throws Exception {
        // Initialize the database
        insertedActionlang = actionlangRepository.saveAndFlush(actionlang);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the actionlang
        restActionlangMockMvc
            .perform(delete(ENTITY_API_URL_ID, actionlang.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return actionlangRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Actionlang getPersistedActionlang(Actionlang actionlang) {
        return actionlangRepository.findById(actionlang.getId()).orElseThrow();
    }

    protected void assertPersistedActionlangToMatchAllProperties(Actionlang expectedActionlang) {
        assertActionlangAllPropertiesEquals(expectedActionlang, getPersistedActionlang(expectedActionlang));
    }

    protected void assertPersistedActionlangToMatchUpdatableProperties(Actionlang expectedActionlang) {
        assertActionlangAllUpdatablePropertiesEquals(expectedActionlang, getPersistedActionlang(expectedActionlang));
    }
}
