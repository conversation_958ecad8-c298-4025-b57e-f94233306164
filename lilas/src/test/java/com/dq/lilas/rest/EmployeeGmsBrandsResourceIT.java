package com.dq.lilas.rest;

import static com.dq.lilas.domain.EmployeeGmsBrandsAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.EmployeeGmsBrands;
import com.dq.lilas.repository.EmployeeGmsBrandsRepository;
import com.dq.lilas.service.dto.EmployeeGmsBrandsDTO;
import com.dq.lilas.service.mapper.EmployeeGmsBrandsMapper;
import com.dq.lilas.web.rest.EmployeeGmsBrandsResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link EmployeeGmsBrandsResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class EmployeeGmsBrandsResourceIT {

    private static final Instant DEFAULT_ASSIGNMENT_DATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_ASSIGNMENT_DATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Boolean DEFAULT_STATUS = false;
    private static final Boolean UPDATED_STATUS = true;

    private static final String ENTITY_API_URL = "/api/employee-gms-brands";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private EmployeeGmsBrandsRepository employeeGmsBrandsRepository;

    @Autowired
    private EmployeeGmsBrandsMapper employeeGmsBrandsMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restEmployeeGmsBrandsMockMvc;

    private EmployeeGmsBrands employeeGmsBrands;

    private EmployeeGmsBrands insertedEmployeeGmsBrands;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static EmployeeGmsBrands createEntity() {
        return new EmployeeGmsBrands().assignmentDate(DEFAULT_ASSIGNMENT_DATE).status(DEFAULT_STATUS);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static EmployeeGmsBrands createUpdatedEntity() {
        return new EmployeeGmsBrands().assignmentDate(UPDATED_ASSIGNMENT_DATE).status(UPDATED_STATUS);
    }

    @BeforeEach
    void initTest() {
        employeeGmsBrands = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedEmployeeGmsBrands != null) {
            employeeGmsBrandsRepository.delete(insertedEmployeeGmsBrands);
            insertedEmployeeGmsBrands = null;
        }
    }

    @Test
    @Transactional
    void createEmployeeGmsBrands() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the EmployeeGmsBrands
        EmployeeGmsBrandsDTO employeeGmsBrandsDTO = employeeGmsBrandsMapper.toDto(employeeGmsBrands);
        var returnedEmployeeGmsBrandsDTO = om.readValue(
            restEmployeeGmsBrandsMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeGmsBrandsDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            EmployeeGmsBrandsDTO.class
        );

        // Validate the EmployeeGmsBrands in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedEmployeeGmsBrands = employeeGmsBrandsMapper.toEntity(returnedEmployeeGmsBrandsDTO);
        assertEmployeeGmsBrandsUpdatableFieldsEquals(returnedEmployeeGmsBrands, getPersistedEmployeeGmsBrands(returnedEmployeeGmsBrands));

        insertedEmployeeGmsBrands = returnedEmployeeGmsBrands;
    }

    @Test
    @Transactional
    void createEmployeeGmsBrandsWithExistingId() throws Exception {
        // Create the EmployeeGmsBrands with an existing ID
        employeeGmsBrands.setId(1L);
        EmployeeGmsBrandsDTO employeeGmsBrandsDTO = employeeGmsBrandsMapper.toDto(employeeGmsBrands);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restEmployeeGmsBrandsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeGmsBrandsDTO)))
            .andExpect(status().isBadRequest());

        // Validate the EmployeeGmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllEmployeeGmsBrands() throws Exception {
        // Initialize the database
        insertedEmployeeGmsBrands = employeeGmsBrandsRepository.saveAndFlush(employeeGmsBrands);

        // Get all the employeeGmsBrandsList
        restEmployeeGmsBrandsMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(employeeGmsBrands.getId().intValue())))
            .andExpect(jsonPath("$.[*].assignmentDate").value(hasItem(DEFAULT_ASSIGNMENT_DATE.toString())))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS)));
    }

    @Test
    @Transactional
    void getEmployeeGmsBrands() throws Exception {
        // Initialize the database
        insertedEmployeeGmsBrands = employeeGmsBrandsRepository.saveAndFlush(employeeGmsBrands);

        // Get the employeeGmsBrands
        restEmployeeGmsBrandsMockMvc
            .perform(get(ENTITY_API_URL_ID, employeeGmsBrands.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(employeeGmsBrands.getId().intValue()))
            .andExpect(jsonPath("$.assignmentDate").value(DEFAULT_ASSIGNMENT_DATE.toString()))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS));
    }

    @Test
    @Transactional
    void getNonExistingEmployeeGmsBrands() throws Exception {
        // Get the employeeGmsBrands
        restEmployeeGmsBrandsMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingEmployeeGmsBrands() throws Exception {
        // Initialize the database
        insertedEmployeeGmsBrands = employeeGmsBrandsRepository.saveAndFlush(employeeGmsBrands);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeeGmsBrands
        EmployeeGmsBrands updatedEmployeeGmsBrands = employeeGmsBrandsRepository.findById(employeeGmsBrands.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedEmployeeGmsBrands are not directly saved in db
        em.detach(updatedEmployeeGmsBrands);
        updatedEmployeeGmsBrands.assignmentDate(UPDATED_ASSIGNMENT_DATE).status(UPDATED_STATUS);
        EmployeeGmsBrandsDTO employeeGmsBrandsDTO = employeeGmsBrandsMapper.toDto(updatedEmployeeGmsBrands);

        restEmployeeGmsBrandsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, employeeGmsBrandsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeeGmsBrandsDTO))
            )
            .andExpect(status().isOk());

        // Validate the EmployeeGmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedEmployeeGmsBrandsToMatchAllProperties(updatedEmployeeGmsBrands);
    }

    @Test
    @Transactional
    void putNonExistingEmployeeGmsBrands() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeGmsBrands.setId(longCount.incrementAndGet());

        // Create the EmployeeGmsBrands
        EmployeeGmsBrandsDTO employeeGmsBrandsDTO = employeeGmsBrandsMapper.toDto(employeeGmsBrands);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restEmployeeGmsBrandsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, employeeGmsBrandsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeeGmsBrandsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeGmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchEmployeeGmsBrands() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeGmsBrands.setId(longCount.incrementAndGet());

        // Create the EmployeeGmsBrands
        EmployeeGmsBrandsDTO employeeGmsBrandsDTO = employeeGmsBrandsMapper.toDto(employeeGmsBrands);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeeGmsBrandsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeeGmsBrandsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeGmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamEmployeeGmsBrands() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeGmsBrands.setId(longCount.incrementAndGet());

        // Create the EmployeeGmsBrands
        EmployeeGmsBrandsDTO employeeGmsBrandsDTO = employeeGmsBrandsMapper.toDto(employeeGmsBrands);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeeGmsBrandsMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeGmsBrandsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the EmployeeGmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateEmployeeGmsBrandsWithPatch() throws Exception {
        // Initialize the database
        insertedEmployeeGmsBrands = employeeGmsBrandsRepository.saveAndFlush(employeeGmsBrands);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeeGmsBrands using partial update
        EmployeeGmsBrands partialUpdatedEmployeeGmsBrands = new EmployeeGmsBrands();
        partialUpdatedEmployeeGmsBrands.setId(employeeGmsBrands.getId());

        restEmployeeGmsBrandsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedEmployeeGmsBrands.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedEmployeeGmsBrands))
            )
            .andExpect(status().isOk());

        // Validate the EmployeeGmsBrands in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertEmployeeGmsBrandsUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedEmployeeGmsBrands, employeeGmsBrands),
            getPersistedEmployeeGmsBrands(employeeGmsBrands)
        );
    }

    @Test
    @Transactional
    void fullUpdateEmployeeGmsBrandsWithPatch() throws Exception {
        // Initialize the database
        insertedEmployeeGmsBrands = employeeGmsBrandsRepository.saveAndFlush(employeeGmsBrands);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeeGmsBrands using partial update
        EmployeeGmsBrands partialUpdatedEmployeeGmsBrands = new EmployeeGmsBrands();
        partialUpdatedEmployeeGmsBrands.setId(employeeGmsBrands.getId());

        partialUpdatedEmployeeGmsBrands.assignmentDate(UPDATED_ASSIGNMENT_DATE).status(UPDATED_STATUS);

        restEmployeeGmsBrandsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedEmployeeGmsBrands.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedEmployeeGmsBrands))
            )
            .andExpect(status().isOk());

        // Validate the EmployeeGmsBrands in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertEmployeeGmsBrandsUpdatableFieldsEquals(
            partialUpdatedEmployeeGmsBrands,
            getPersistedEmployeeGmsBrands(partialUpdatedEmployeeGmsBrands)
        );
    }

    @Test
    @Transactional
    void patchNonExistingEmployeeGmsBrands() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeGmsBrands.setId(longCount.incrementAndGet());

        // Create the EmployeeGmsBrands
        EmployeeGmsBrandsDTO employeeGmsBrandsDTO = employeeGmsBrandsMapper.toDto(employeeGmsBrands);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restEmployeeGmsBrandsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, employeeGmsBrandsDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(employeeGmsBrandsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeGmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchEmployeeGmsBrands() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeGmsBrands.setId(longCount.incrementAndGet());

        // Create the EmployeeGmsBrands
        EmployeeGmsBrandsDTO employeeGmsBrandsDTO = employeeGmsBrandsMapper.toDto(employeeGmsBrands);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeeGmsBrandsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(employeeGmsBrandsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeGmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamEmployeeGmsBrands() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeGmsBrands.setId(longCount.incrementAndGet());

        // Create the EmployeeGmsBrands
        EmployeeGmsBrandsDTO employeeGmsBrandsDTO = employeeGmsBrandsMapper.toDto(employeeGmsBrands);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeeGmsBrandsMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(employeeGmsBrandsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the EmployeeGmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteEmployeeGmsBrands() throws Exception {
        // Initialize the database
        insertedEmployeeGmsBrands = employeeGmsBrandsRepository.saveAndFlush(employeeGmsBrands);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the employeeGmsBrands
        restEmployeeGmsBrandsMockMvc
            .perform(delete(ENTITY_API_URL_ID, employeeGmsBrands.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return employeeGmsBrandsRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected EmployeeGmsBrands getPersistedEmployeeGmsBrands(EmployeeGmsBrands employeeGmsBrands) {
        return employeeGmsBrandsRepository.findById(employeeGmsBrands.getId()).orElseThrow();
    }

    protected void assertPersistedEmployeeGmsBrandsToMatchAllProperties(EmployeeGmsBrands expectedEmployeeGmsBrands) {
        assertEmployeeGmsBrandsAllPropertiesEquals(expectedEmployeeGmsBrands, getPersistedEmployeeGmsBrands(expectedEmployeeGmsBrands));
    }

    protected void assertPersistedEmployeeGmsBrandsToMatchUpdatableProperties(EmployeeGmsBrands expectedEmployeeGmsBrands) {
        assertEmployeeGmsBrandsAllUpdatablePropertiesEquals(
            expectedEmployeeGmsBrands,
            getPersistedEmployeeGmsBrands(expectedEmployeeGmsBrands)
        );
    }
}
