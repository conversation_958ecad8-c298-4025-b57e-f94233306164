package com.dq.lilas.rest;

import static com.dq.lilas.domain.AttachementAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Attachement;
import com.dq.lilas.repository.AttachementRepository;
import com.dq.lilas.service.dto.AttachementDTO;
import com.dq.lilas.service.mapper.AttachementMapper;
import com.dq.lilas.web.rest.AttachementResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link AttachementResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class AttachementResourceIT {

    private static final String DEFAULT_COPY_ID = "AAAAAAAAAA";
    private static final String UPDATED_COPY_ID = "BBBBBBBBBB";

    private static final String DEFAULT_LBL_ATTACHMENT = "AAAAAAAAAA";
    private static final String UPDATED_LBL_ATTACHMENT = "BBBBBBBBBB";

    private static final String DEFAULT_ID_DOC_ATTACHMENT = "AAAAAAAAAA";
    private static final String UPDATED_ID_DOC_ATTACHMENT = "BBBBBBBBBB";

    private static final String DEFAULT_SIZE_ATTACHEMENT = "AAAAAAAAAA";
    private static final String UPDATED_SIZE_ATTACHEMENT = "BBBBBBBBBB";

    private static final String DEFAULT_FILENAMEATTACHMENT = "AAAAAAAAAA";
    private static final String UPDATED_FILENAMEATTACHMENT = "BBBBBBBBBB";

    private static final String DEFAULT_USERATTACHMENT = "AAAAAAAAAA";
    private static final String UPDATED_USERATTACHMENT = "BBBBBBBBBB";

    private static final String DEFAULT_IDDECISION = "AAAAAAAAAA";
    private static final String UPDATED_IDDECISION = "BBBBBBBBBB";

    private static final String DEFAULT_IDTEMPLATE = "AAAAAAAAAA";
    private static final String UPDATED_IDTEMPLATE = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEJCATTACHMENT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCATTACHMENT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_DATEHJRATTACHMENT = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEHJRATTACHMENT = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_IDTRANSFER = "AAAAAAAAAA";
    private static final String UPDATED_IDTRANSFER = "BBBBBBBBBB";

    private static final String DEFAULT_IDCORRESP = "AAAAAAAAAA";
    private static final String UPDATED_IDCORRESP = "BBBBBBBBBB";

    private static final Double DEFAULT_ORDERING = 1D;
    private static final Double UPDATED_ORDERING = 2D;

    private static final String DEFAULT_LEVELATTACHEMENT = "AAAAAAAAAA";
    private static final String UPDATED_LEVELATTACHEMENT = "BBBBBBBBBB";

    private static final String DEFAULT_IDREQ = "AAAAAAAAAA";
    private static final String UPDATED_IDREQ = "BBBBBBBBBB";

    private static final Double DEFAULT_ORDERINGSCAN = 1D;
    private static final Double UPDATED_ORDERINGSCAN = 2D;

    private static final String DEFAULT_IDDOCEXT = "AAAAAAAAAA";
    private static final String UPDATED_IDDOCEXT = "BBBBBBBBBB";

    private static final String DEFAULT_IDLEAVE = "AAAAAAAAAA";
    private static final String UPDATED_IDLEAVE = "BBBBBBBBBB";

    private static final String DEFAULT_CONFIG_LEVEL = "AAAAAAAAAA";
    private static final String UPDATED_CONFIG_LEVEL = "BBBBBBBBBB";

    private static final String DEFAULT_TYPE_ATACH = "AAAAAAAAAA";
    private static final String UPDATED_TYPE_ATACH = "BBBBBBBBBB";

    private static final String DEFAULT_ID_DIRECT_ORDER = "AAAAAAAAAA";
    private static final String UPDATED_ID_DIRECT_ORDER = "BBBBBBBBBB";

    private static final String DEFAULT_PATH_FILE = "AAAAAAAAAA";
    private static final String UPDATED_PATH_FILE = "BBBBBBBBBB";

    private static final Integer DEFAULT_VERSION = 1;
    private static final Integer UPDATED_VERSION = 2;

    private static final String ENTITY_API_URL = "/api/attachements";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private AttachementRepository attachementRepository;

    @Autowired
    private AttachementMapper attachementMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restAttachementMockMvc;

    private Attachement attachement;

    private Attachement insertedAttachement;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Attachement createEntity() {
        return new Attachement()
            .copyId(DEFAULT_COPY_ID)
            .lblAttachment(DEFAULT_LBL_ATTACHMENT)
            .idDocAttachment(DEFAULT_ID_DOC_ATTACHMENT)
            .sizeAttachement(DEFAULT_SIZE_ATTACHEMENT)
            .filenameattachment(DEFAULT_FILENAMEATTACHMENT)
            .userattachment(DEFAULT_USERATTACHMENT)
            .iddecision(DEFAULT_IDDECISION)
            .idtemplate(DEFAULT_IDTEMPLATE)
            .datejcattachment(DEFAULT_DATEJCATTACHMENT)
            .datehjrattachment(DEFAULT_DATEHJRATTACHMENT)
            .idtransfer(DEFAULT_IDTRANSFER)
            .idcorresp(DEFAULT_IDCORRESP)
            .ordering(DEFAULT_ORDERING)
            .levelattachement(DEFAULT_LEVELATTACHEMENT)
            .idreq(DEFAULT_IDREQ)
            .orderingscan(DEFAULT_ORDERINGSCAN)
            .iddocext(DEFAULT_IDDOCEXT)
            .idleave(DEFAULT_IDLEAVE)
            .configLevel(DEFAULT_CONFIG_LEVEL)
            .typeAtach(DEFAULT_TYPE_ATACH)
            .idDirectOrder(DEFAULT_ID_DIRECT_ORDER)
            .pathFile(DEFAULT_PATH_FILE)
            .version(DEFAULT_VERSION);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Attachement createUpdatedEntity() {
        return new Attachement()
            .copyId(UPDATED_COPY_ID)
            .lblAttachment(UPDATED_LBL_ATTACHMENT)
            .idDocAttachment(UPDATED_ID_DOC_ATTACHMENT)
            .sizeAttachement(UPDATED_SIZE_ATTACHEMENT)
            .filenameattachment(UPDATED_FILENAMEATTACHMENT)
            .userattachment(UPDATED_USERATTACHMENT)
            .iddecision(UPDATED_IDDECISION)
            .idtemplate(UPDATED_IDTEMPLATE)
            .datejcattachment(UPDATED_DATEJCATTACHMENT)
            .datehjrattachment(UPDATED_DATEHJRATTACHMENT)
            .idtransfer(UPDATED_IDTRANSFER)
            .idcorresp(UPDATED_IDCORRESP)
            .ordering(UPDATED_ORDERING)
            .levelattachement(UPDATED_LEVELATTACHEMENT)
            .idreq(UPDATED_IDREQ)
            .orderingscan(UPDATED_ORDERINGSCAN)
            .iddocext(UPDATED_IDDOCEXT)
            .idleave(UPDATED_IDLEAVE)
            .configLevel(UPDATED_CONFIG_LEVEL)
            .typeAtach(UPDATED_TYPE_ATACH)
            .idDirectOrder(UPDATED_ID_DIRECT_ORDER)
            .pathFile(UPDATED_PATH_FILE)
            .version(UPDATED_VERSION);
    }

    @BeforeEach
    void initTest() {
        attachement = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedAttachement != null) {
            attachementRepository.delete(insertedAttachement);
            insertedAttachement = null;
        }
    }

    @Test
    @Transactional
    void createAttachement() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Attachement
        AttachementDTO attachementDTO = attachementMapper.toDto(attachement);
        var returnedAttachementDTO = om.readValue(
            restAttachementMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(attachementDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            AttachementDTO.class
        );

        // Validate the Attachement in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedAttachement = attachementMapper.toEntity(returnedAttachementDTO);
        assertAttachementUpdatableFieldsEquals(returnedAttachement, getPersistedAttachement(returnedAttachement));

        insertedAttachement = returnedAttachement;
    }

    @Test
    @Transactional
    void createAttachementWithExistingId() throws Exception {
        // Create the Attachement with an existing ID
        attachement.setId(1L);
        AttachementDTO attachementDTO = attachementMapper.toDto(attachement);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restAttachementMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(attachementDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Attachement in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllAttachements() throws Exception {
        // Initialize the database
        insertedAttachement = attachementRepository.saveAndFlush(attachement);

        // Get all the attachementList
        restAttachementMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(attachement.getId().intValue())))
            .andExpect(jsonPath("$.[*].copyId").value(hasItem(DEFAULT_COPY_ID)))
            .andExpect(jsonPath("$.[*].lblAttachment").value(hasItem(DEFAULT_LBL_ATTACHMENT)))
            .andExpect(jsonPath("$.[*].idDocAttachment").value(hasItem(DEFAULT_ID_DOC_ATTACHMENT)))
            .andExpect(jsonPath("$.[*].sizeAttachement").value(hasItem(DEFAULT_SIZE_ATTACHEMENT)))
            .andExpect(jsonPath("$.[*].filenameattachment").value(hasItem(DEFAULT_FILENAMEATTACHMENT)))
            .andExpect(jsonPath("$.[*].userattachment").value(hasItem(DEFAULT_USERATTACHMENT)))
            .andExpect(jsonPath("$.[*].iddecision").value(hasItem(DEFAULT_IDDECISION)))
            .andExpect(jsonPath("$.[*].idtemplate").value(hasItem(DEFAULT_IDTEMPLATE)))
            .andExpect(jsonPath("$.[*].datejcattachment").value(hasItem(DEFAULT_DATEJCATTACHMENT.toString())))
            .andExpect(jsonPath("$.[*].datehjrattachment").value(hasItem(DEFAULT_DATEHJRATTACHMENT.toString())))
            .andExpect(jsonPath("$.[*].idtransfer").value(hasItem(DEFAULT_IDTRANSFER)))
            .andExpect(jsonPath("$.[*].idcorresp").value(hasItem(DEFAULT_IDCORRESP)))
            .andExpect(jsonPath("$.[*].ordering").value(hasItem(DEFAULT_ORDERING)))
            .andExpect(jsonPath("$.[*].levelattachement").value(hasItem(DEFAULT_LEVELATTACHEMENT)))
            .andExpect(jsonPath("$.[*].idreq").value(hasItem(DEFAULT_IDREQ)))
            .andExpect(jsonPath("$.[*].orderingscan").value(hasItem(DEFAULT_ORDERINGSCAN)))
            .andExpect(jsonPath("$.[*].iddocext").value(hasItem(DEFAULT_IDDOCEXT)))
            .andExpect(jsonPath("$.[*].idleave").value(hasItem(DEFAULT_IDLEAVE)))
            .andExpect(jsonPath("$.[*].configLevel").value(hasItem(DEFAULT_CONFIG_LEVEL)))
            .andExpect(jsonPath("$.[*].typeAtach").value(hasItem(DEFAULT_TYPE_ATACH)))
            .andExpect(jsonPath("$.[*].idDirectOrder").value(hasItem(DEFAULT_ID_DIRECT_ORDER)))
            .andExpect(jsonPath("$.[*].pathFile").value(hasItem(DEFAULT_PATH_FILE)))
            .andExpect(jsonPath("$.[*].version").value(hasItem(DEFAULT_VERSION)));
    }

    @Test
    @Transactional
    void getAttachement() throws Exception {
        // Initialize the database
        insertedAttachement = attachementRepository.saveAndFlush(attachement);

        // Get the attachement
        restAttachementMockMvc
            .perform(get(ENTITY_API_URL_ID, attachement.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(attachement.getId().intValue()))
            .andExpect(jsonPath("$.copyId").value(DEFAULT_COPY_ID))
            .andExpect(jsonPath("$.lblAttachment").value(DEFAULT_LBL_ATTACHMENT))
            .andExpect(jsonPath("$.idDocAttachment").value(DEFAULT_ID_DOC_ATTACHMENT))
            .andExpect(jsonPath("$.sizeAttachement").value(DEFAULT_SIZE_ATTACHEMENT))
            .andExpect(jsonPath("$.filenameattachment").value(DEFAULT_FILENAMEATTACHMENT))
            .andExpect(jsonPath("$.userattachment").value(DEFAULT_USERATTACHMENT))
            .andExpect(jsonPath("$.iddecision").value(DEFAULT_IDDECISION))
            .andExpect(jsonPath("$.idtemplate").value(DEFAULT_IDTEMPLATE))
            .andExpect(jsonPath("$.datejcattachment").value(DEFAULT_DATEJCATTACHMENT.toString()))
            .andExpect(jsonPath("$.datehjrattachment").value(DEFAULT_DATEHJRATTACHMENT.toString()))
            .andExpect(jsonPath("$.idtransfer").value(DEFAULT_IDTRANSFER))
            .andExpect(jsonPath("$.idcorresp").value(DEFAULT_IDCORRESP))
            .andExpect(jsonPath("$.ordering").value(DEFAULT_ORDERING))
            .andExpect(jsonPath("$.levelattachement").value(DEFAULT_LEVELATTACHEMENT))
            .andExpect(jsonPath("$.idreq").value(DEFAULT_IDREQ))
            .andExpect(jsonPath("$.orderingscan").value(DEFAULT_ORDERINGSCAN))
            .andExpect(jsonPath("$.iddocext").value(DEFAULT_IDDOCEXT))
            .andExpect(jsonPath("$.idleave").value(DEFAULT_IDLEAVE))
            .andExpect(jsonPath("$.configLevel").value(DEFAULT_CONFIG_LEVEL))
            .andExpect(jsonPath("$.typeAtach").value(DEFAULT_TYPE_ATACH))
            .andExpect(jsonPath("$.idDirectOrder").value(DEFAULT_ID_DIRECT_ORDER))
            .andExpect(jsonPath("$.pathFile").value(DEFAULT_PATH_FILE))
            .andExpect(jsonPath("$.version").value(DEFAULT_VERSION));
    }

    @Test
    @Transactional
    void getNonExistingAttachement() throws Exception {
        // Get the attachement
        restAttachementMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingAttachement() throws Exception {
        // Initialize the database
        insertedAttachement = attachementRepository.saveAndFlush(attachement);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the attachement
        Attachement updatedAttachement = attachementRepository.findById(attachement.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedAttachement are not directly saved in db
        em.detach(updatedAttachement);
        updatedAttachement
            .copyId(UPDATED_COPY_ID)
            .lblAttachment(UPDATED_LBL_ATTACHMENT)
            .idDocAttachment(UPDATED_ID_DOC_ATTACHMENT)
            .sizeAttachement(UPDATED_SIZE_ATTACHEMENT)
            .filenameattachment(UPDATED_FILENAMEATTACHMENT)
            .userattachment(UPDATED_USERATTACHMENT)
            .iddecision(UPDATED_IDDECISION)
            .idtemplate(UPDATED_IDTEMPLATE)
            .datejcattachment(UPDATED_DATEJCATTACHMENT)
            .datehjrattachment(UPDATED_DATEHJRATTACHMENT)
            .idtransfer(UPDATED_IDTRANSFER)
            .idcorresp(UPDATED_IDCORRESP)
            .ordering(UPDATED_ORDERING)
            .levelattachement(UPDATED_LEVELATTACHEMENT)
            .idreq(UPDATED_IDREQ)
            .orderingscan(UPDATED_ORDERINGSCAN)
            .iddocext(UPDATED_IDDOCEXT)
            .idleave(UPDATED_IDLEAVE)
            .configLevel(UPDATED_CONFIG_LEVEL)
            .typeAtach(UPDATED_TYPE_ATACH)
            .idDirectOrder(UPDATED_ID_DIRECT_ORDER)
            .pathFile(UPDATED_PATH_FILE)
            .version(UPDATED_VERSION);
        AttachementDTO attachementDTO = attachementMapper.toDto(updatedAttachement);

        restAttachementMockMvc
            .perform(
                put(ENTITY_API_URL_ID, attachementDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(attachementDTO))
            )
            .andExpect(status().isOk());

        // Validate the Attachement in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedAttachementToMatchAllProperties(updatedAttachement);
    }

    @Test
    @Transactional
    void putNonExistingAttachement() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        attachement.setId(longCount.incrementAndGet());

        // Create the Attachement
        AttachementDTO attachementDTO = attachementMapper.toDto(attachement);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAttachementMockMvc
            .perform(
                put(ENTITY_API_URL_ID, attachementDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(attachementDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Attachement in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchAttachement() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        attachement.setId(longCount.incrementAndGet());

        // Create the Attachement
        AttachementDTO attachementDTO = attachementMapper.toDto(attachement);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAttachementMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(attachementDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Attachement in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamAttachement() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        attachement.setId(longCount.incrementAndGet());

        // Create the Attachement
        AttachementDTO attachementDTO = attachementMapper.toDto(attachement);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAttachementMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(attachementDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Attachement in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateAttachementWithPatch() throws Exception {
        // Initialize the database
        insertedAttachement = attachementRepository.saveAndFlush(attachement);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the attachement using partial update
        Attachement partialUpdatedAttachement = new Attachement();
        partialUpdatedAttachement.setId(attachement.getId());

        partialUpdatedAttachement
            .copyId(UPDATED_COPY_ID)
            .lblAttachment(UPDATED_LBL_ATTACHMENT)
            .idDocAttachment(UPDATED_ID_DOC_ATTACHMENT)
            .filenameattachment(UPDATED_FILENAMEATTACHMENT)
            .userattachment(UPDATED_USERATTACHMENT)
            .iddecision(UPDATED_IDDECISION)
            .idtemplate(UPDATED_IDTEMPLATE)
            .datehjrattachment(UPDATED_DATEHJRATTACHMENT)
            .idtransfer(UPDATED_IDTRANSFER)
            .ordering(UPDATED_ORDERING)
            .idreq(UPDATED_IDREQ)
            .orderingscan(UPDATED_ORDERINGSCAN)
            .iddocext(UPDATED_IDDOCEXT)
            .idleave(UPDATED_IDLEAVE)
            .configLevel(UPDATED_CONFIG_LEVEL)
            .typeAtach(UPDATED_TYPE_ATACH);

        restAttachementMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAttachement.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAttachement))
            )
            .andExpect(status().isOk());

        // Validate the Attachement in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAttachementUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedAttachement, attachement),
            getPersistedAttachement(attachement)
        );
    }

    @Test
    @Transactional
    void fullUpdateAttachementWithPatch() throws Exception {
        // Initialize the database
        insertedAttachement = attachementRepository.saveAndFlush(attachement);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the attachement using partial update
        Attachement partialUpdatedAttachement = new Attachement();
        partialUpdatedAttachement.setId(attachement.getId());

        partialUpdatedAttachement
            .copyId(UPDATED_COPY_ID)
            .lblAttachment(UPDATED_LBL_ATTACHMENT)
            .idDocAttachment(UPDATED_ID_DOC_ATTACHMENT)
            .sizeAttachement(UPDATED_SIZE_ATTACHEMENT)
            .filenameattachment(UPDATED_FILENAMEATTACHMENT)
            .userattachment(UPDATED_USERATTACHMENT)
            .iddecision(UPDATED_IDDECISION)
            .idtemplate(UPDATED_IDTEMPLATE)
            .datejcattachment(UPDATED_DATEJCATTACHMENT)
            .datehjrattachment(UPDATED_DATEHJRATTACHMENT)
            .idtransfer(UPDATED_IDTRANSFER)
            .idcorresp(UPDATED_IDCORRESP)
            .ordering(UPDATED_ORDERING)
            .levelattachement(UPDATED_LEVELATTACHEMENT)
            .idreq(UPDATED_IDREQ)
            .orderingscan(UPDATED_ORDERINGSCAN)
            .iddocext(UPDATED_IDDOCEXT)
            .idleave(UPDATED_IDLEAVE)
            .configLevel(UPDATED_CONFIG_LEVEL)
            .typeAtach(UPDATED_TYPE_ATACH)
            .idDirectOrder(UPDATED_ID_DIRECT_ORDER)
            .pathFile(UPDATED_PATH_FILE)
            .version(UPDATED_VERSION);

        restAttachementMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAttachement.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAttachement))
            )
            .andExpect(status().isOk());

        // Validate the Attachement in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertAttachementUpdatableFieldsEquals(partialUpdatedAttachement, getPersistedAttachement(partialUpdatedAttachement));
    }

    @Test
    @Transactional
    void patchNonExistingAttachement() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        attachement.setId(longCount.incrementAndGet());

        // Create the Attachement
        AttachementDTO attachementDTO = attachementMapper.toDto(attachement);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restAttachementMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, attachementDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(attachementDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Attachement in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchAttachement() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        attachement.setId(longCount.incrementAndGet());

        // Create the Attachement
        AttachementDTO attachementDTO = attachementMapper.toDto(attachement);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAttachementMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(attachementDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Attachement in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamAttachement() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        attachement.setId(longCount.incrementAndGet());

        // Create the Attachement
        AttachementDTO attachementDTO = attachementMapper.toDto(attachement);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restAttachementMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(attachementDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Attachement in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteAttachement() throws Exception {
        // Initialize the database
        insertedAttachement = attachementRepository.saveAndFlush(attachement);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the attachement
        restAttachementMockMvc
            .perform(delete(ENTITY_API_URL_ID, attachement.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return attachementRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Attachement getPersistedAttachement(Attachement attachement) {
        return attachementRepository.findById(attachement.getId()).orElseThrow();
    }

    protected void assertPersistedAttachementToMatchAllProperties(Attachement expectedAttachement) {
        assertAttachementAllPropertiesEquals(expectedAttachement, getPersistedAttachement(expectedAttachement));
    }

    protected void assertPersistedAttachementToMatchUpdatableProperties(Attachement expectedAttachement) {
        assertAttachementAllUpdatablePropertiesEquals(expectedAttachement, getPersistedAttachement(expectedAttachement));
    }
}
