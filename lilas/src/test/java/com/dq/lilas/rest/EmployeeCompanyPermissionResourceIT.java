package com.dq.lilas.rest;

import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static com.dq.lilas.domain.EmployeeCompanyPermissionAsserts.*;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.mockito.Mockito.*;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.EmployeeCompanyPermission;
import com.dq.lilas.repository.EmployeeCompanyPermissionRepository;
import com.dq.lilas.service.EmployeeCompanyPermissionService;
import com.dq.lilas.service.dto.EmployeeCompanyPermissionDTO;
import com.dq.lilas.service.mapper.EmployeeCompanyPermissionMapper;
import com.dq.lilas.web.rest.EmployeeCompanyPermissionResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.ArrayList;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link EmployeeCompanyPermissionResource} REST controller.
 */
@IntegrationTest
@ExtendWith(MockitoExtension.class)
@AutoConfigureMockMvc
@WithMockUser
class EmployeeCompanyPermissionResourceIT {

    private static final String ENTITY_API_URL = "/api/user-company-permissions";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private EmployeeCompanyPermissionRepository employeeCompanyPermissionRepository;

    @Mock
    private EmployeeCompanyPermissionRepository employeeCompanyPermissionRepositoryMock;

    @Autowired
    private EmployeeCompanyPermissionMapper employeeCompanyPermissionMapper;

    @Mock
    private EmployeeCompanyPermissionService employeeCompanyPermissionServiceMock;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restUserCompanyPermissionMockMvc;

    private EmployeeCompanyPermission employeeCompanyPermission;

    private EmployeeCompanyPermission insertedUserCompanyPermission;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static EmployeeCompanyPermission createEntity() {
        return new EmployeeCompanyPermission();
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static EmployeeCompanyPermission createUpdatedEntity() {
        return new EmployeeCompanyPermission();
    }

    @BeforeEach
    void initTest() {
        employeeCompanyPermission = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedUserCompanyPermission != null) {
            employeeCompanyPermissionRepository.delete(insertedUserCompanyPermission);
            insertedUserCompanyPermission = null;
        }
    }

    @Test
    @Transactional
    void createEmployeeCompanyPermission() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the EmployeeCompanyPermission
        EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO = employeeCompanyPermissionMapper.toDto(employeeCompanyPermission);
        var returnedUserCompanyPermissionDTO = om.readValue(
            restUserCompanyPermissionMockMvc
                .perform(
                    post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeCompanyPermissionDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            EmployeeCompanyPermissionDTO.class
        );

        // Validate the EmployeeCompanyPermission in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedUserCompanyPermission = employeeCompanyPermissionMapper.toEntity(returnedUserCompanyPermissionDTO);
        assertEmployeeCompanyPermissionUpdatableFieldsEquals(
            returnedUserCompanyPermission,
            getPersistedEmployeeCompanyPermission(returnedUserCompanyPermission)
        );

        insertedUserCompanyPermission = returnedUserCompanyPermission;
    }

    @Test
    @Transactional
    void createEmployeeCompanyPermissionWithExistingId() throws Exception {
        // Create the EmployeeCompanyPermission with an existing ID
        employeeCompanyPermission.setId(1L);
        EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO = employeeCompanyPermissionMapper.toDto(employeeCompanyPermission);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restUserCompanyPermissionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeCompanyPermissionDTO)))
            .andExpect(status().isBadRequest());

        // Validate the EmployeeCompanyPermission in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllUserCompanyPermissions() throws Exception {
        // Initialize the database
        insertedUserCompanyPermission = employeeCompanyPermissionRepository.saveAndFlush(employeeCompanyPermission);

        // Get all the userCompanyPermissionList
        restUserCompanyPermissionMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(employeeCompanyPermission.getId().intValue())));
    }

    @SuppressWarnings({ "unchecked" })
    void getAllUserCompanyPermissionsWithEagerRelationshipsIsEnabled() throws Exception {
        when(employeeCompanyPermissionServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl<>(new ArrayList<>()));

        restUserCompanyPermissionMockMvc.perform(get(ENTITY_API_URL + "?eagerload=true")).andExpect(status().isOk());

        verify(employeeCompanyPermissionServiceMock, times(1)).findAllWithEagerRelationships(any());
    }

    @SuppressWarnings({ "unchecked" })
    void getAllUserCompanyPermissionsWithEagerRelationshipsIsNotEnabled() throws Exception {
        when(employeeCompanyPermissionServiceMock.findAllWithEagerRelationships(any())).thenReturn(new PageImpl<>(new ArrayList<>()));

        restUserCompanyPermissionMockMvc.perform(get(ENTITY_API_URL + "?eagerload=false")).andExpect(status().isOk());
        verify(employeeCompanyPermissionRepositoryMock, times(1)).findAll(any(Pageable.class));
    }

    @Test
    @Transactional
    void getEmployeeCompanyPermission() throws Exception {
        // Initialize the database
        insertedUserCompanyPermission = employeeCompanyPermissionRepository.saveAndFlush(employeeCompanyPermission);

        // Get the employeeCompanyPermission
        restUserCompanyPermissionMockMvc
            .perform(get(ENTITY_API_URL_ID, employeeCompanyPermission.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(employeeCompanyPermission.getId().intValue()));
    }

    @Test
    @Transactional
    void getNonExistingUserCompanyPermission() throws Exception {
        // Get the employeeCompanyPermission
        restUserCompanyPermissionMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingUserCompanyPermission() throws Exception {
        // Initialize the database
        insertedUserCompanyPermission = employeeCompanyPermissionRepository.saveAndFlush(employeeCompanyPermission);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeeCompanyPermission
        EmployeeCompanyPermission updatedUserCompanyPermission = employeeCompanyPermissionRepository
            .findById(employeeCompanyPermission.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedUserCompanyPermission are not directly saved in db
        em.detach(updatedUserCompanyPermission);
        EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO = employeeCompanyPermissionMapper.toDto(updatedUserCompanyPermission);

        restUserCompanyPermissionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, employeeCompanyPermissionDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeeCompanyPermissionDTO))
            )
            .andExpect(status().isOk());

        // Validate the EmployeeCompanyPermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedUserCompanyPermissionToMatchAllProperties(updatedUserCompanyPermission);
    }

    @Test
    @Transactional
    void putNonExistingUserCompanyPermission() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeCompanyPermission.setId(longCount.incrementAndGet());

        // Create the EmployeeCompanyPermission
        EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO = employeeCompanyPermissionMapper.toDto(employeeCompanyPermission);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restUserCompanyPermissionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, employeeCompanyPermissionDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeeCompanyPermissionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeCompanyPermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchUserCompanyPermission() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeCompanyPermission.setId(longCount.incrementAndGet());

        // Create the EmployeeCompanyPermission
        EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO = employeeCompanyPermissionMapper.toDto(employeeCompanyPermission);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserCompanyPermissionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeeCompanyPermissionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeCompanyPermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamUserCompanyPermission() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeCompanyPermission.setId(longCount.incrementAndGet());

        // Create the EmployeeCompanyPermission
        EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO = employeeCompanyPermissionMapper.toDto(employeeCompanyPermission);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserCompanyPermissionMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeeCompanyPermissionDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the EmployeeCompanyPermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateUserCompanyPermissionWithPatch() throws Exception {
        // Initialize the database
        insertedUserCompanyPermission = employeeCompanyPermissionRepository.saveAndFlush(employeeCompanyPermission);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeeCompanyPermission using partial update
        EmployeeCompanyPermission partialUpdatedUserCompanyPermission = new EmployeeCompanyPermission();
        partialUpdatedUserCompanyPermission.setId(employeeCompanyPermission.getId());

        restUserCompanyPermissionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedUserCompanyPermission.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedUserCompanyPermission))
            )
            .andExpect(status().isOk());

        // Validate the EmployeeCompanyPermission in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertEmployeeCompanyPermissionUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedUserCompanyPermission, employeeCompanyPermission),
            getPersistedEmployeeCompanyPermission(employeeCompanyPermission)
        );
    }

    @Test
    @Transactional
    void fullUpdateUserCompanyPermissionWithPatch() throws Exception {
        // Initialize the database
        insertedUserCompanyPermission = employeeCompanyPermissionRepository.saveAndFlush(employeeCompanyPermission);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeeCompanyPermission using partial update
        EmployeeCompanyPermission partialUpdatedUserCompanyPermission = new EmployeeCompanyPermission();
        partialUpdatedUserCompanyPermission.setId(employeeCompanyPermission.getId());

        restUserCompanyPermissionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedUserCompanyPermission.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedUserCompanyPermission))
            )
            .andExpect(status().isOk());

        // Validate the EmployeeCompanyPermission in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertEmployeeCompanyPermissionUpdatableFieldsEquals(
            partialUpdatedUserCompanyPermission,
            getPersistedEmployeeCompanyPermission(partialUpdatedUserCompanyPermission)
        );
    }

    @Test
    @Transactional
    void patchNonExistingUserCompanyPermission() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeCompanyPermission.setId(longCount.incrementAndGet());

        // Create the EmployeeCompanyPermission
        EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO = employeeCompanyPermissionMapper.toDto(employeeCompanyPermission);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restUserCompanyPermissionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, employeeCompanyPermissionDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(employeeCompanyPermissionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeCompanyPermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchUserCompanyPermission() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeCompanyPermission.setId(longCount.incrementAndGet());

        // Create the EmployeeCompanyPermission
        EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO = employeeCompanyPermissionMapper.toDto(employeeCompanyPermission);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserCompanyPermissionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(employeeCompanyPermissionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmployeeCompanyPermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamUserCompanyPermission() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeeCompanyPermission.setId(longCount.incrementAndGet());

        // Create the EmployeeCompanyPermission
        EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO = employeeCompanyPermissionMapper.toDto(employeeCompanyPermission);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restUserCompanyPermissionMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(employeeCompanyPermissionDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the EmployeeCompanyPermission in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteUserCompanyPermission() throws Exception {
        // Initialize the database
        insertedUserCompanyPermission = employeeCompanyPermissionRepository.saveAndFlush(employeeCompanyPermission);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the employeeCompanyPermission
        restUserCompanyPermissionMockMvc
            .perform(delete(ENTITY_API_URL_ID, employeeCompanyPermission.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return employeeCompanyPermissionRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected EmployeeCompanyPermission getPersistedEmployeeCompanyPermission(EmployeeCompanyPermission employeeCompanyPermission) {
        return employeeCompanyPermissionRepository.findById(employeeCompanyPermission.getId()).orElseThrow();
    }

    protected void assertPersistedUserCompanyPermissionToMatchAllProperties(EmployeeCompanyPermission expectedUserCompanyPermission) {
        assertEmployeeCompanyPermissionAllPropertiesEquals(
            expectedUserCompanyPermission,
            getPersistedEmployeeCompanyPermission(expectedUserCompanyPermission)
        );
    }

    protected void assertPersistedUserCompanyPermissionToMatchUpdatableProperties(EmployeeCompanyPermission expectedUserCompanyPermission) {
        assertEmployeeCompanyPermissionAllUpdatablePropertiesEquals(
            expectedUserCompanyPermission,
            getPersistedEmployeeCompanyPermission(expectedUserCompanyPermission)
        );
    }
}
