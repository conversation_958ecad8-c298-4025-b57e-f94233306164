package com.dq.lilas.rest;

import static com.dq.lilas.domain.MailsAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Mails;
import com.dq.lilas.repository.MailsRepository;
import com.dq.lilas.service.dto.MailsDTO;
import com.dq.lilas.service.mapper.MailsMapper;
import com.dq.lilas.web.rest.MailsResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link MailsResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class MailsResourceIT {

    private static final Instant DEFAULT_MAILDATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_MAILDATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_SUBJECT = "AAAAAAAAAA";
    private static final String UPDATED_SUBJECT = "BBBBBBBBBB";

    private static final String DEFAULT_MAILBODY = "AAAAAAAAAA";
    private static final String UPDATED_MAILBODY = "BBBBBBBBBB";

    private static final String DEFAULT_RECIPIENT = "AAAAAAAAAA";
    private static final String UPDATED_RECIPIENT = "BBBBBBBBBB";

    private static final String DEFAULT_SENDER = "AAAAAAAAAA";
    private static final String UPDATED_SENDER = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/mails";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private MailsRepository mailsRepository;

    @Autowired
    private MailsMapper mailsMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restMailsMockMvc;

    private Mails mails;

    private Mails insertedMails;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Mails createEntity() {
        return new Mails()
            .maildate(DEFAULT_MAILDATE)
            .subject(DEFAULT_SUBJECT)
            .mailbody(DEFAULT_MAILBODY)
            .recipient(DEFAULT_RECIPIENT)
            .sender(DEFAULT_SENDER);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Mails createUpdatedEntity() {
        return new Mails()
            .maildate(UPDATED_MAILDATE)
            .subject(UPDATED_SUBJECT)
            .mailbody(UPDATED_MAILBODY)
            .recipient(UPDATED_RECIPIENT)
            .sender(UPDATED_SENDER);
    }

    @BeforeEach
    void initTest() {
        mails = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedMails != null) {
            mailsRepository.delete(insertedMails);
            insertedMails = null;
        }
    }

    @Test
    @Transactional
    void createMails() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Mails
        MailsDTO mailsDTO = mailsMapper.toDto(mails);
        var returnedMailsDTO = om.readValue(
            restMailsMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(mailsDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            MailsDTO.class
        );

        // Validate the Mails in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedMails = mailsMapper.toEntity(returnedMailsDTO);
        assertMailsUpdatableFieldsEquals(returnedMails, getPersistedMails(returnedMails));

        insertedMails = returnedMails;
    }

    @Test
    @Transactional
    void createMailsWithExistingId() throws Exception {
        // Create the Mails with an existing ID
        mails.setId(1L);
        MailsDTO mailsDTO = mailsMapper.toDto(mails);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restMailsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(mailsDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Mails in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllMails() throws Exception {
        // Initialize the database
        insertedMails = mailsRepository.saveAndFlush(mails);

        // Get all the mailsList
        restMailsMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(mails.getId().intValue())))
            .andExpect(jsonPath("$.[*].maildate").value(hasItem(DEFAULT_MAILDATE.toString())))
            .andExpect(jsonPath("$.[*].subject").value(hasItem(DEFAULT_SUBJECT)))
            .andExpect(jsonPath("$.[*].mailbody").value(hasItem(DEFAULT_MAILBODY)))
            .andExpect(jsonPath("$.[*].recipient").value(hasItem(DEFAULT_RECIPIENT)))
            .andExpect(jsonPath("$.[*].sender").value(hasItem(DEFAULT_SENDER)));
    }

    @Test
    @Transactional
    void getMails() throws Exception {
        // Initialize the database
        insertedMails = mailsRepository.saveAndFlush(mails);

        // Get the mails
        restMailsMockMvc
            .perform(get(ENTITY_API_URL_ID, mails.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(mails.getId().intValue()))
            .andExpect(jsonPath("$.maildate").value(DEFAULT_MAILDATE.toString()))
            .andExpect(jsonPath("$.subject").value(DEFAULT_SUBJECT))
            .andExpect(jsonPath("$.mailbody").value(DEFAULT_MAILBODY))
            .andExpect(jsonPath("$.recipient").value(DEFAULT_RECIPIENT))
            .andExpect(jsonPath("$.sender").value(DEFAULT_SENDER));
    }

    @Test
    @Transactional
    void getNonExistingMails() throws Exception {
        // Get the mails
        restMailsMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingMails() throws Exception {
        // Initialize the database
        insertedMails = mailsRepository.saveAndFlush(mails);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the mails
        Mails updatedMails = mailsRepository.findById(mails.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedMails are not directly saved in db
        em.detach(updatedMails);
        updatedMails
            .maildate(UPDATED_MAILDATE)
            .subject(UPDATED_SUBJECT)
            .mailbody(UPDATED_MAILBODY)
            .recipient(UPDATED_RECIPIENT)
            .sender(UPDATED_SENDER);
        MailsDTO mailsDTO = mailsMapper.toDto(updatedMails);

        restMailsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, mailsDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(mailsDTO))
            )
            .andExpect(status().isOk());

        // Validate the Mails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedMailsToMatchAllProperties(updatedMails);
    }

    @Test
    @Transactional
    void putNonExistingMails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        mails.setId(longCount.incrementAndGet());

        // Create the Mails
        MailsDTO mailsDTO = mailsMapper.toDto(mails);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restMailsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, mailsDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(mailsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Mails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchMails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        mails.setId(longCount.incrementAndGet());

        // Create the Mails
        MailsDTO mailsDTO = mailsMapper.toDto(mails);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restMailsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(mailsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Mails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamMails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        mails.setId(longCount.incrementAndGet());

        // Create the Mails
        MailsDTO mailsDTO = mailsMapper.toDto(mails);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restMailsMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(mailsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Mails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateMailsWithPatch() throws Exception {
        // Initialize the database
        insertedMails = mailsRepository.saveAndFlush(mails);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the mails using partial update
        Mails partialUpdatedMails = new Mails();
        partialUpdatedMails.setId(mails.getId());

        partialUpdatedMails.maildate(UPDATED_MAILDATE).mailbody(UPDATED_MAILBODY).sender(UPDATED_SENDER);

        restMailsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedMails.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedMails))
            )
            .andExpect(status().isOk());

        // Validate the Mails in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertMailsUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedMails, mails), getPersistedMails(mails));
    }

    @Test
    @Transactional
    void fullUpdateMailsWithPatch() throws Exception {
        // Initialize the database
        insertedMails = mailsRepository.saveAndFlush(mails);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the mails using partial update
        Mails partialUpdatedMails = new Mails();
        partialUpdatedMails.setId(mails.getId());

        partialUpdatedMails
            .maildate(UPDATED_MAILDATE)
            .subject(UPDATED_SUBJECT)
            .mailbody(UPDATED_MAILBODY)
            .recipient(UPDATED_RECIPIENT)
            .sender(UPDATED_SENDER);

        restMailsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedMails.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedMails))
            )
            .andExpect(status().isOk());

        // Validate the Mails in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertMailsUpdatableFieldsEquals(partialUpdatedMails, getPersistedMails(partialUpdatedMails));
    }

    @Test
    @Transactional
    void patchNonExistingMails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        mails.setId(longCount.incrementAndGet());

        // Create the Mails
        MailsDTO mailsDTO = mailsMapper.toDto(mails);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restMailsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, mailsDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(mailsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Mails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchMails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        mails.setId(longCount.incrementAndGet());

        // Create the Mails
        MailsDTO mailsDTO = mailsMapper.toDto(mails);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restMailsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(mailsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Mails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamMails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        mails.setId(longCount.incrementAndGet());

        // Create the Mails
        MailsDTO mailsDTO = mailsMapper.toDto(mails);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restMailsMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(mailsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Mails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteMails() throws Exception {
        // Initialize the database
        insertedMails = mailsRepository.saveAndFlush(mails);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the mails
        restMailsMockMvc
            .perform(delete(ENTITY_API_URL_ID, mails.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return mailsRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Mails getPersistedMails(Mails mails) {
        return mailsRepository.findById(mails.getId()).orElseThrow();
    }

    protected void assertPersistedMailsToMatchAllProperties(Mails expectedMails) {
        assertMailsAllPropertiesEquals(expectedMails, getPersistedMails(expectedMails));
    }

    protected void assertPersistedMailsToMatchUpdatableProperties(Mails expectedMails) {
        assertMailsAllUpdatablePropertiesEquals(expectedMails, getPersistedMails(expectedMails));
    }
}
