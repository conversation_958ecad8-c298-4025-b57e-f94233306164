package com.dq.lilas.rest;

import static com.dq.lilas.domain.ProductsListAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.ProductsList;
import com.dq.lilas.repository.ProductsListRepository;
import com.dq.lilas.service.dto.ProductsListDTO;
import com.dq.lilas.service.mapper.ProductsListMapper;
import com.dq.lilas.web.rest.ProductsListResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ProductsListResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ProductsListResourceIT {

    private static final String DEFAULT_INTERNAL_CODE = "AAAAAAAAAA";
    private static final String UPDATED_INTERNAL_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_BARCODE = "AAAAAAAAAA";
    private static final String UPDATED_BARCODE = "BBBBBBBBBB";

    private static final String DEFAULT_QAD_NAME = "AAAAAAAAAA";
    private static final String UPDATED_QAD_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_CATEGORY = "AAAAAAAAAA";
    private static final String UPDATED_CATEGORY = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/products-lists";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ProductsListRepository productsListRepository;

    @Autowired
    private ProductsListMapper productsListMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restProductsListMockMvc;

    private ProductsList productsList;

    private ProductsList insertedProductsList;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProductsList createEntity() {
        return new ProductsList()
            .internalCode(DEFAULT_INTERNAL_CODE)
            .barcode(DEFAULT_BARCODE)
            .qadName(DEFAULT_QAD_NAME)
            .category(DEFAULT_CATEGORY);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static ProductsList createUpdatedEntity() {
        return new ProductsList()
            .internalCode(UPDATED_INTERNAL_CODE)
            .barcode(UPDATED_BARCODE)
            .qadName(UPDATED_QAD_NAME)
            .category(UPDATED_CATEGORY);
    }

    @BeforeEach
    void initTest() {
        productsList = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedProductsList != null) {
            productsListRepository.delete(insertedProductsList);
            insertedProductsList = null;
        }
    }

    @Test
    @Transactional
    void createProductsList() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the ProductsList
        ProductsListDTO productsListDTO = productsListMapper.toDto(productsList);
        var returnedProductsListDTO = om.readValue(
            restProductsListMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(productsListDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ProductsListDTO.class
        );

        // Validate the ProductsList in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedProductsList = productsListMapper.toEntity(returnedProductsListDTO);
        assertProductsListUpdatableFieldsEquals(returnedProductsList, getPersistedProductsList(returnedProductsList));

        insertedProductsList = returnedProductsList;
    }

    @Test
    @Transactional
    void createProductsListWithExistingId() throws Exception {
        // Create the ProductsList with an existing ID
        productsList.setId(1L);
        ProductsListDTO productsListDTO = productsListMapper.toDto(productsList);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restProductsListMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(productsListDTO)))
            .andExpect(status().isBadRequest());

        // Validate the ProductsList in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllProductsLists() throws Exception {
        // Initialize the database
        insertedProductsList = productsListRepository.saveAndFlush(productsList);

        // Get all the productsListList
        restProductsListMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(productsList.getId().intValue())))
            .andExpect(jsonPath("$.[*].internalCode").value(hasItem(DEFAULT_INTERNAL_CODE)))
            .andExpect(jsonPath("$.[*].barcode").value(hasItem(DEFAULT_BARCODE)))
            .andExpect(jsonPath("$.[*].qadName").value(hasItem(DEFAULT_QAD_NAME)))
            .andExpect(jsonPath("$.[*].category").value(hasItem(DEFAULT_CATEGORY)));
    }

    @Test
    @Transactional
    void getProductsList() throws Exception {
        // Initialize the database
        insertedProductsList = productsListRepository.saveAndFlush(productsList);

        // Get the productsList
        restProductsListMockMvc
            .perform(get(ENTITY_API_URL_ID, productsList.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(productsList.getId().intValue()))
            .andExpect(jsonPath("$.internalCode").value(DEFAULT_INTERNAL_CODE))
            .andExpect(jsonPath("$.barcode").value(DEFAULT_BARCODE))
            .andExpect(jsonPath("$.qadName").value(DEFAULT_QAD_NAME))
            .andExpect(jsonPath("$.category").value(DEFAULT_CATEGORY));
    }

    @Test
    @Transactional
    void getNonExistingProductsList() throws Exception {
        // Get the productsList
        restProductsListMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingProductsList() throws Exception {
        // Initialize the database
        insertedProductsList = productsListRepository.saveAndFlush(productsList);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the productsList
        ProductsList updatedProductsList = productsListRepository.findById(productsList.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedProductsList are not directly saved in db
        em.detach(updatedProductsList);
        updatedProductsList
            .internalCode(UPDATED_INTERNAL_CODE)
            .barcode(UPDATED_BARCODE)
            .qadName(UPDATED_QAD_NAME)
            .category(UPDATED_CATEGORY);
        ProductsListDTO productsListDTO = productsListMapper.toDto(updatedProductsList);

        restProductsListMockMvc
            .perform(
                put(ENTITY_API_URL_ID, productsListDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(productsListDTO))
            )
            .andExpect(status().isOk());

        // Validate the ProductsList in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedProductsListToMatchAllProperties(updatedProductsList);
    }

    @Test
    @Transactional
    void putNonExistingProductsList() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        productsList.setId(longCount.incrementAndGet());

        // Create the ProductsList
        ProductsListDTO productsListDTO = productsListMapper.toDto(productsList);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProductsListMockMvc
            .perform(
                put(ENTITY_API_URL_ID, productsListDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(productsListDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProductsList in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchProductsList() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        productsList.setId(longCount.incrementAndGet());

        // Create the ProductsList
        ProductsListDTO productsListDTO = productsListMapper.toDto(productsList);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProductsListMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(productsListDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProductsList in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamProductsList() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        productsList.setId(longCount.incrementAndGet());

        // Create the ProductsList
        ProductsListDTO productsListDTO = productsListMapper.toDto(productsList);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProductsListMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(productsListDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProductsList in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateProductsListWithPatch() throws Exception {
        // Initialize the database
        insertedProductsList = productsListRepository.saveAndFlush(productsList);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the productsList using partial update
        ProductsList partialUpdatedProductsList = new ProductsList();
        partialUpdatedProductsList.setId(productsList.getId());

        restProductsListMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProductsList.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProductsList))
            )
            .andExpect(status().isOk());

        // Validate the ProductsList in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProductsListUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedProductsList, productsList),
            getPersistedProductsList(productsList)
        );
    }

    @Test
    @Transactional
    void fullUpdateProductsListWithPatch() throws Exception {
        // Initialize the database
        insertedProductsList = productsListRepository.saveAndFlush(productsList);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the productsList using partial update
        ProductsList partialUpdatedProductsList = new ProductsList();
        partialUpdatedProductsList.setId(productsList.getId());

        partialUpdatedProductsList
            .internalCode(UPDATED_INTERNAL_CODE)
            .barcode(UPDATED_BARCODE)
            .qadName(UPDATED_QAD_NAME)
            .category(UPDATED_CATEGORY);

        restProductsListMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedProductsList.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedProductsList))
            )
            .andExpect(status().isOk());

        // Validate the ProductsList in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertProductsListUpdatableFieldsEquals(partialUpdatedProductsList, getPersistedProductsList(partialUpdatedProductsList));
    }

    @Test
    @Transactional
    void patchNonExistingProductsList() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        productsList.setId(longCount.incrementAndGet());

        // Create the ProductsList
        ProductsListDTO productsListDTO = productsListMapper.toDto(productsList);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restProductsListMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, productsListDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(productsListDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProductsList in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchProductsList() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        productsList.setId(longCount.incrementAndGet());

        // Create the ProductsList
        ProductsListDTO productsListDTO = productsListMapper.toDto(productsList);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProductsListMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(productsListDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the ProductsList in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamProductsList() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        productsList.setId(longCount.incrementAndGet());

        // Create the ProductsList
        ProductsListDTO productsListDTO = productsListMapper.toDto(productsList);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restProductsListMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(productsListDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the ProductsList in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteProductsList() throws Exception {
        // Initialize the database
        insertedProductsList = productsListRepository.saveAndFlush(productsList);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the productsList
        restProductsListMockMvc
            .perform(delete(ENTITY_API_URL_ID, productsList.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return productsListRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected ProductsList getPersistedProductsList(ProductsList productsList) {
        return productsListRepository.findById(productsList.getId()).orElseThrow();
    }

    protected void assertPersistedProductsListToMatchAllProperties(ProductsList expectedProductsList) {
        assertProductsListAllPropertiesEquals(expectedProductsList, getPersistedProductsList(expectedProductsList));
    }

    protected void assertPersistedProductsListToMatchUpdatableProperties(ProductsList expectedProductsList) {
        assertProductsListAllUpdatablePropertiesEquals(expectedProductsList, getPersistedProductsList(expectedProductsList));
    }
}
