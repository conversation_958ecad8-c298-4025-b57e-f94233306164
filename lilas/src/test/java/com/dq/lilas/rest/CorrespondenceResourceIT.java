package com.dq.lilas.rest;

import static com.dq.lilas.domain.CorrespondenceAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static com.dq.lilas.rest.TestUtil.sameInstant;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Correspondence;
import com.dq.lilas.repository.CorrespondenceRepository;
import com.dq.lilas.service.dto.CorrespondenceDTO;
import com.dq.lilas.service.mapper.CorrespondenceMapper;
import com.dq.lilas.web.rest.CorrespondenceResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.ZoneId;
import java.time.ZoneOffset;
import java.time.ZonedDateTime;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link CorrespondenceResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class CorrespondenceResourceIT {

    private static final String DEFAULT_ORDERNBR = "AAAAAAAAAA";
    private static final String UPDATED_ORDERNBR = "BBBBBBBBBB";

    private static final String DEFAULT_NUMCOPY = "AAAAAAAAAA";
    private static final String UPDATED_NUMCOPY = "BBBBBBBBBB";

    private static final String DEFAULT_DOCLINK = "AAAAAAAAAA";
    private static final String UPDATED_DOCLINK = "BBBBBBBBBB";

    private static final String DEFAULT_PAGENBR = "AAAAAAAAAA";
    private static final String UPDATED_PAGENBR = "BBBBBBBBBB";

    private static final String DEFAULT_HIGHLEVEL = "AAAAA";
    private static final String UPDATED_HIGHLEVEL = "BBBBB";

    private static final String DEFAULT_CONFIDENTIEL = "AAAAAAAAAA";
    private static final String UPDATED_CONFIDENTIEL = "BBBBBBBBBB";

    private static final String DEFAULT_PRIORITY = "AAAAA";
    private static final String UPDATED_PRIORITY = "BBBBB";

    private static final String DEFAULT_OBS = "AAAAAAAAAA";
    private static final String UPDATED_OBS = "BBBBBBBBBB";

    private static final String DEFAULT_CATEGORY = "AAAAA";
    private static final String UPDATED_CATEGORY = "BBBBB";

    private static final String DEFAULT_STATUS = "AAAAA";
    private static final String UPDATED_STATUS = "BBBBB";

    private static final ZonedDateTime DEFAULT_DATEJC = ZonedDateTime.ofInstant(Instant.ofEpochMilli(0L), ZoneOffset.UTC);
    private static final ZonedDateTime UPDATED_DATEJC = ZonedDateTime.now(ZoneId.systemDefault()).withNano(0);

    private static final ZonedDateTime DEFAULT_DATEJCSEND = ZonedDateTime.ofInstant(Instant.ofEpochMilli(0L), ZoneOffset.UTC);
    private static final ZonedDateTime UPDATED_DATEJCSEND = ZonedDateTime.now(ZoneId.systemDefault()).withNano(0);

    private static final String DEFAULT_TYPERECEIVE = "A";
    private static final String UPDATED_TYPERECEIVE = "B";

    private static final String DEFAULT_TYPECOPY = "AAAAAAAAAA";
    private static final String UPDATED_TYPECOPY = "BBBBBBBBBB";

    private static final String DEFAULT_REFSND = "AAAAAAAAAA";
    private static final String UPDATED_REFSND = "BBBBBBBBBB";

    private static final String DEFAULT_TEXT = "AAAAAAAAAA";
    private static final String UPDATED_TEXT = "BBBBBBBBBB";

    private static final String DEFAULT_DOCYEAR = "AAAA";
    private static final String UPDATED_DOCYEAR = "BBBB";

    private static final String DEFAULT_DATEHJRSAVE = "AAAAAAAAAA";
    private static final String UPDATED_DATEHJRSAVE = "BBBBBBBBBB";

    private static final ZonedDateTime DEFAULT_DATEJCSAVE = ZonedDateTime.ofInstant(Instant.ofEpochMilli(0L), ZoneOffset.UTC);
    private static final ZonedDateTime UPDATED_DATEJCSAVE = ZonedDateTime.now(ZoneId.systemDefault()).withNano(0);

    private static final String DEFAULT_OLD_ORDER_NUMBER = "AAAAAAAAAA";
    private static final String UPDATED_OLD_ORDER_NUMBER = "BBBBBBBBBB";

    private static final String DEFAULT_SUBJECT = "AAAAAAAAAA";
    private static final String UPDATED_SUBJECT = "BBBBBBBBBB";

    private static final String DEFAULT_ATTACH = "AAAAAAAAAA";
    private static final String UPDATED_ATTACH = "BBBBBBBBBB";

    private static final String DEFAULT_ORDERNBRADRSBOOK = "AAAAAAAAAA";
    private static final String UPDATED_ORDERNBRADRSBOOK = "BBBBBBBBBB";

    private static final Integer DEFAULT_SEQKEYADRSBOOK = 1;
    private static final Integer UPDATED_SEQKEYADRSBOOK = 2;

    private static final String DEFAULT_USERADRSBOOK = "AAAAAAAAAA";
    private static final String UPDATED_USERADRSBOOK = "BBBBBBBBBB";

    private static final String DEFAULT_CITYZENNCARD = "AAAAAAAAAA";
    private static final String UPDATED_CITYZENNCARD = "BBBBBBBBBB";

    private static final String DEFAULT_CITYZENPHONE = "AAAAAAAAAA";
    private static final String UPDATED_CITYZENPHONE = "BBBBBBBBBB";

    private static final String DEFAULT_SMS = "A";
    private static final String UPDATED_SMS = "B";

    private static final String DEFAULT_INCIDENT = "AAAAA";
    private static final String UPDATED_INCIDENT = "BBBBB";

    private static final String DEFAULT_CHECK_FAVORITE = "A";
    private static final String UPDATED_CHECK_FAVORITE = "B";

    private static final String DEFAULT_COMPANY_ID = "AAAAAAAAAA";
    private static final String UPDATED_COMPANY_ID = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/correspondences";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private CorrespondenceRepository correspondenceRepository;

    @Autowired
    private CorrespondenceMapper correspondenceMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restCorrespondenceMockMvc;

    private Correspondence correspondence;

    private Correspondence insertedCorrespondence;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Correspondence createEntity() {
        return new Correspondence()
            .ordernbr(DEFAULT_ORDERNBR)
            .numcopy(DEFAULT_NUMCOPY)
            .doclink(DEFAULT_DOCLINK)
            .pagenbr(DEFAULT_PAGENBR)
            .highlevel(DEFAULT_HIGHLEVEL)
            .confidentiel(DEFAULT_CONFIDENTIEL)
            .priority(DEFAULT_PRIORITY)
            .obs(DEFAULT_OBS)
            .category(DEFAULT_CATEGORY)
            .status(DEFAULT_STATUS)
            .datejc(DEFAULT_DATEJC)
            .datejcsend(DEFAULT_DATEJCSEND)
            .typereceive(DEFAULT_TYPERECEIVE)
            .typecopy(DEFAULT_TYPECOPY)
            .refsnd(DEFAULT_REFSND)
            .text(DEFAULT_TEXT)
            .docyear(DEFAULT_DOCYEAR)
            .datehjrsave(DEFAULT_DATEHJRSAVE)
            .datejcsave(DEFAULT_DATEJCSAVE)
            .oldOrderNumber(DEFAULT_OLD_ORDER_NUMBER)
            .subject(DEFAULT_SUBJECT)
            .attach(DEFAULT_ATTACH)
            .ordernbradrsbook(DEFAULT_ORDERNBRADRSBOOK)
            .seqkeyadrsbook(DEFAULT_SEQKEYADRSBOOK)
            .useradrsbook(DEFAULT_USERADRSBOOK)
            .cityzenncard(DEFAULT_CITYZENNCARD)
            .cityzenphone(DEFAULT_CITYZENPHONE)
            .sms(DEFAULT_SMS)
            .incident(DEFAULT_INCIDENT)
            .checkFavorite(DEFAULT_CHECK_FAVORITE)
            .companyId(DEFAULT_COMPANY_ID);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Correspondence createUpdatedEntity() {
        return new Correspondence()
            .ordernbr(UPDATED_ORDERNBR)
            .numcopy(UPDATED_NUMCOPY)
            .doclink(UPDATED_DOCLINK)
            .pagenbr(UPDATED_PAGENBR)
            .highlevel(UPDATED_HIGHLEVEL)
            .confidentiel(UPDATED_CONFIDENTIEL)
            .priority(UPDATED_PRIORITY)
            .obs(UPDATED_OBS)
            .category(UPDATED_CATEGORY)
            .status(UPDATED_STATUS)
            .datejc(UPDATED_DATEJC)
            .datejcsend(UPDATED_DATEJCSEND)
            .typereceive(UPDATED_TYPERECEIVE)
            .typecopy(UPDATED_TYPECOPY)
            .refsnd(UPDATED_REFSND)
            .text(UPDATED_TEXT)
            .docyear(UPDATED_DOCYEAR)
            .datehjrsave(UPDATED_DATEHJRSAVE)
            .datejcsave(UPDATED_DATEJCSAVE)
            .oldOrderNumber(UPDATED_OLD_ORDER_NUMBER)
            .subject(UPDATED_SUBJECT)
            .attach(UPDATED_ATTACH)
            .ordernbradrsbook(UPDATED_ORDERNBRADRSBOOK)
            .seqkeyadrsbook(UPDATED_SEQKEYADRSBOOK)
            .useradrsbook(UPDATED_USERADRSBOOK)
            .cityzenncard(UPDATED_CITYZENNCARD)
            .cityzenphone(UPDATED_CITYZENPHONE)
            .sms(UPDATED_SMS)
            .incident(UPDATED_INCIDENT)
            .checkFavorite(UPDATED_CHECK_FAVORITE)
            .companyId(UPDATED_COMPANY_ID);
    }

    @BeforeEach
    void initTest() {
        correspondence = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedCorrespondence != null) {
            correspondenceRepository.delete(insertedCorrespondence);
            insertedCorrespondence = null;
        }
    }

    @Test
    @Transactional
    void createCorrespondence() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Correspondence
        CorrespondenceDTO correspondenceDTO = correspondenceMapper.toDto(correspondence);
        var returnedCorrespondenceDTO = om.readValue(
            restCorrespondenceMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(correspondenceDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            CorrespondenceDTO.class
        );

        // Validate the Correspondence in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedCorrespondence = correspondenceMapper.toEntity(returnedCorrespondenceDTO);
        assertCorrespondenceUpdatableFieldsEquals(returnedCorrespondence, getPersistedCorrespondence(returnedCorrespondence));

        insertedCorrespondence = returnedCorrespondence;
    }

    @Test
    @Transactional
    void createCorrespondenceWithExistingId() throws Exception {
        // Create the Correspondence with an existing ID
        correspondence.setId(1L);
        CorrespondenceDTO correspondenceDTO = correspondenceMapper.toDto(correspondence);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restCorrespondenceMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(correspondenceDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Correspondence in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void checkCategoryIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        correspondence.setCategory(null);

        // Create the Correspondence, which fails.
        CorrespondenceDTO correspondenceDTO = correspondenceMapper.toDto(correspondence);

        restCorrespondenceMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(correspondenceDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void checkSubjectIsRequired() throws Exception {
        long databaseSizeBeforeTest = getRepositoryCount();
        // set the field null
        correspondence.setSubject(null);

        // Create the Correspondence, which fails.
        CorrespondenceDTO correspondenceDTO = correspondenceMapper.toDto(correspondence);

        restCorrespondenceMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(correspondenceDTO)))
            .andExpect(status().isBadRequest());

        assertSameRepositoryCount(databaseSizeBeforeTest);
    }

    @Test
    @Transactional
    void getAllCorrespondences() throws Exception {
        // Initialize the database
        insertedCorrespondence = correspondenceRepository.saveAndFlush(correspondence);

        // Get all the correspondenceList
        restCorrespondenceMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(correspondence.getId().intValue())))
            .andExpect(jsonPath("$.[*].ordernbr").value(hasItem(DEFAULT_ORDERNBR)))
            .andExpect(jsonPath("$.[*].numcopy").value(hasItem(DEFAULT_NUMCOPY)))
            .andExpect(jsonPath("$.[*].doclink").value(hasItem(DEFAULT_DOCLINK)))
            .andExpect(jsonPath("$.[*].pagenbr").value(hasItem(DEFAULT_PAGENBR)))
            .andExpect(jsonPath("$.[*].highlevel").value(hasItem(DEFAULT_HIGHLEVEL)))
            .andExpect(jsonPath("$.[*].confidentiel").value(hasItem(DEFAULT_CONFIDENTIEL)))
            .andExpect(jsonPath("$.[*].priority").value(hasItem(DEFAULT_PRIORITY)))
            .andExpect(jsonPath("$.[*].obs").value(hasItem(DEFAULT_OBS)))
            .andExpect(jsonPath("$.[*].category").value(hasItem(DEFAULT_CATEGORY)))
            .andExpect(jsonPath("$.[*].status").value(hasItem(DEFAULT_STATUS)))
            .andExpect(jsonPath("$.[*].datejc").value(hasItem(sameInstant(DEFAULT_DATEJC))))
            .andExpect(jsonPath("$.[*].datejcsend").value(hasItem(sameInstant(DEFAULT_DATEJCSEND))))
            .andExpect(jsonPath("$.[*].typereceive").value(hasItem(DEFAULT_TYPERECEIVE)))
            .andExpect(jsonPath("$.[*].typecopy").value(hasItem(DEFAULT_TYPECOPY)))
            .andExpect(jsonPath("$.[*].refsnd").value(hasItem(DEFAULT_REFSND)))
            .andExpect(jsonPath("$.[*].text").value(hasItem(DEFAULT_TEXT)))
            .andExpect(jsonPath("$.[*].docyear").value(hasItem(DEFAULT_DOCYEAR)))
            .andExpect(jsonPath("$.[*].datehjrsave").value(hasItem(DEFAULT_DATEHJRSAVE)))
            .andExpect(jsonPath("$.[*].datejcsave").value(hasItem(sameInstant(DEFAULT_DATEJCSAVE))))
            .andExpect(jsonPath("$.[*].oldOrderNumber").value(hasItem(DEFAULT_OLD_ORDER_NUMBER)))
            .andExpect(jsonPath("$.[*].subject").value(hasItem(DEFAULT_SUBJECT)))
            .andExpect(jsonPath("$.[*].attach").value(hasItem(DEFAULT_ATTACH)))
            .andExpect(jsonPath("$.[*].ordernbradrsbook").value(hasItem(DEFAULT_ORDERNBRADRSBOOK)))
            .andExpect(jsonPath("$.[*].seqkeyadrsbook").value(hasItem(DEFAULT_SEQKEYADRSBOOK)))
            .andExpect(jsonPath("$.[*].useradrsbook").value(hasItem(DEFAULT_USERADRSBOOK)))
            .andExpect(jsonPath("$.[*].cityzenncard").value(hasItem(DEFAULT_CITYZENNCARD)))
            .andExpect(jsonPath("$.[*].cityzenphone").value(hasItem(DEFAULT_CITYZENPHONE)))
            .andExpect(jsonPath("$.[*].sms").value(hasItem(DEFAULT_SMS)))
            .andExpect(jsonPath("$.[*].incident").value(hasItem(DEFAULT_INCIDENT)))
            .andExpect(jsonPath("$.[*].checkFavorite").value(hasItem(DEFAULT_CHECK_FAVORITE)))
            .andExpect(jsonPath("$.[*].companyId").value(hasItem(DEFAULT_COMPANY_ID)));
    }

    @Test
    @Transactional
    void getCorrespondence() throws Exception {
        // Initialize the database
        insertedCorrespondence = correspondenceRepository.saveAndFlush(correspondence);

        // Get the correspondence
        restCorrespondenceMockMvc
            .perform(get(ENTITY_API_URL_ID, correspondence.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(correspondence.getId().intValue()))
            .andExpect(jsonPath("$.ordernbr").value(DEFAULT_ORDERNBR))
            .andExpect(jsonPath("$.numcopy").value(DEFAULT_NUMCOPY))
            .andExpect(jsonPath("$.doclink").value(DEFAULT_DOCLINK))
            .andExpect(jsonPath("$.pagenbr").value(DEFAULT_PAGENBR))
            .andExpect(jsonPath("$.highlevel").value(DEFAULT_HIGHLEVEL))
            .andExpect(jsonPath("$.confidentiel").value(DEFAULT_CONFIDENTIEL))
            .andExpect(jsonPath("$.priority").value(DEFAULT_PRIORITY))
            .andExpect(jsonPath("$.obs").value(DEFAULT_OBS))
            .andExpect(jsonPath("$.category").value(DEFAULT_CATEGORY))
            .andExpect(jsonPath("$.status").value(DEFAULT_STATUS))
            .andExpect(jsonPath("$.datejc").value(sameInstant(DEFAULT_DATEJC)))
            .andExpect(jsonPath("$.datejcsend").value(sameInstant(DEFAULT_DATEJCSEND)))
            .andExpect(jsonPath("$.typereceive").value(DEFAULT_TYPERECEIVE))
            .andExpect(jsonPath("$.typecopy").value(DEFAULT_TYPECOPY))
            .andExpect(jsonPath("$.refsnd").value(DEFAULT_REFSND))
            .andExpect(jsonPath("$.text").value(DEFAULT_TEXT))
            .andExpect(jsonPath("$.docyear").value(DEFAULT_DOCYEAR))
            .andExpect(jsonPath("$.datehjrsave").value(DEFAULT_DATEHJRSAVE))
            .andExpect(jsonPath("$.datejcsave").value(sameInstant(DEFAULT_DATEJCSAVE)))
            .andExpect(jsonPath("$.oldOrderNumber").value(DEFAULT_OLD_ORDER_NUMBER))
            .andExpect(jsonPath("$.subject").value(DEFAULT_SUBJECT))
            .andExpect(jsonPath("$.attach").value(DEFAULT_ATTACH))
            .andExpect(jsonPath("$.ordernbradrsbook").value(DEFAULT_ORDERNBRADRSBOOK))
            .andExpect(jsonPath("$.seqkeyadrsbook").value(DEFAULT_SEQKEYADRSBOOK))
            .andExpect(jsonPath("$.useradrsbook").value(DEFAULT_USERADRSBOOK))
            .andExpect(jsonPath("$.cityzenncard").value(DEFAULT_CITYZENNCARD))
            .andExpect(jsonPath("$.cityzenphone").value(DEFAULT_CITYZENPHONE))
            .andExpect(jsonPath("$.sms").value(DEFAULT_SMS))
            .andExpect(jsonPath("$.incident").value(DEFAULT_INCIDENT))
            .andExpect(jsonPath("$.checkFavorite").value(DEFAULT_CHECK_FAVORITE))
            .andExpect(jsonPath("$.companyId").value(DEFAULT_COMPANY_ID));
    }

    @Test
    @Transactional
    void getNonExistingCorrespondence() throws Exception {
        // Get the correspondence
        restCorrespondenceMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingCorrespondence() throws Exception {
        // Initialize the database
        insertedCorrespondence = correspondenceRepository.saveAndFlush(correspondence);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the correspondence
        Correspondence updatedCorrespondence = correspondenceRepository.findById(correspondence.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedCorrespondence are not directly saved in db
        em.detach(updatedCorrespondence);
        updatedCorrespondence
            .ordernbr(UPDATED_ORDERNBR)
            .numcopy(UPDATED_NUMCOPY)
            .doclink(UPDATED_DOCLINK)
            .pagenbr(UPDATED_PAGENBR)
            .highlevel(UPDATED_HIGHLEVEL)
            .confidentiel(UPDATED_CONFIDENTIEL)
            .priority(UPDATED_PRIORITY)
            .obs(UPDATED_OBS)
            .category(UPDATED_CATEGORY)
            .status(UPDATED_STATUS)
            .datejc(UPDATED_DATEJC)
            .datejcsend(UPDATED_DATEJCSEND)
            .typereceive(UPDATED_TYPERECEIVE)
            .typecopy(UPDATED_TYPECOPY)
            .refsnd(UPDATED_REFSND)
            .text(UPDATED_TEXT)
            .docyear(UPDATED_DOCYEAR)
            .datehjrsave(UPDATED_DATEHJRSAVE)
            .datejcsave(UPDATED_DATEJCSAVE)
            .oldOrderNumber(UPDATED_OLD_ORDER_NUMBER)
            .subject(UPDATED_SUBJECT)
            .attach(UPDATED_ATTACH)
            .ordernbradrsbook(UPDATED_ORDERNBRADRSBOOK)
            .seqkeyadrsbook(UPDATED_SEQKEYADRSBOOK)
            .useradrsbook(UPDATED_USERADRSBOOK)
            .cityzenncard(UPDATED_CITYZENNCARD)
            .cityzenphone(UPDATED_CITYZENPHONE)
            .sms(UPDATED_SMS)
            .incident(UPDATED_INCIDENT)
            .checkFavorite(UPDATED_CHECK_FAVORITE)
            .companyId(UPDATED_COMPANY_ID);
        CorrespondenceDTO correspondenceDTO = correspondenceMapper.toDto(updatedCorrespondence);

        restCorrespondenceMockMvc
            .perform(
                put(ENTITY_API_URL_ID, correspondenceDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(correspondenceDTO))
            )
            .andExpect(status().isOk());

        // Validate the Correspondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedCorrespondenceToMatchAllProperties(updatedCorrespondence);
    }

    @Test
    @Transactional
    void putNonExistingCorrespondence() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        correspondence.setId(longCount.incrementAndGet());

        // Create the Correspondence
        CorrespondenceDTO correspondenceDTO = correspondenceMapper.toDto(correspondence);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restCorrespondenceMockMvc
            .perform(
                put(ENTITY_API_URL_ID, correspondenceDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(correspondenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Correspondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchCorrespondence() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        correspondence.setId(longCount.incrementAndGet());

        // Create the Correspondence
        CorrespondenceDTO correspondenceDTO = correspondenceMapper.toDto(correspondence);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restCorrespondenceMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(correspondenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Correspondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamCorrespondence() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        correspondence.setId(longCount.incrementAndGet());

        // Create the Correspondence
        CorrespondenceDTO correspondenceDTO = correspondenceMapper.toDto(correspondence);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restCorrespondenceMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(correspondenceDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Correspondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateCorrespondenceWithPatch() throws Exception {
        // Initialize the database
        insertedCorrespondence = correspondenceRepository.saveAndFlush(correspondence);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the correspondence using partial update
        Correspondence partialUpdatedCorrespondence = new Correspondence();
        partialUpdatedCorrespondence.setId(correspondence.getId());

        partialUpdatedCorrespondence
            .ordernbr(UPDATED_ORDERNBR)
            .numcopy(UPDATED_NUMCOPY)
            .pagenbr(UPDATED_PAGENBR)
            .priority(UPDATED_PRIORITY)
            .status(UPDATED_STATUS)
            .datejc(UPDATED_DATEJC)
            .typereceive(UPDATED_TYPERECEIVE)
            .typecopy(UPDATED_TYPECOPY)
            .text(UPDATED_TEXT)
            .datejcsave(UPDATED_DATEJCSAVE)
            .oldOrderNumber(UPDATED_OLD_ORDER_NUMBER)
            .ordernbradrsbook(UPDATED_ORDERNBRADRSBOOK)
            .cityzenphone(UPDATED_CITYZENPHONE)
            .sms(UPDATED_SMS)
            .incident(UPDATED_INCIDENT)
            .checkFavorite(UPDATED_CHECK_FAVORITE)
            .companyId(UPDATED_COMPANY_ID);

        restCorrespondenceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedCorrespondence.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedCorrespondence))
            )
            .andExpect(status().isOk());

        // Validate the Correspondence in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertCorrespondenceUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedCorrespondence, correspondence),
            getPersistedCorrespondence(correspondence)
        );
    }

    @Test
    @Transactional
    void fullUpdateCorrespondenceWithPatch() throws Exception {
        // Initialize the database
        insertedCorrespondence = correspondenceRepository.saveAndFlush(correspondence);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the correspondence using partial update
        Correspondence partialUpdatedCorrespondence = new Correspondence();
        partialUpdatedCorrespondence.setId(correspondence.getId());

        partialUpdatedCorrespondence
            .ordernbr(UPDATED_ORDERNBR)
            .numcopy(UPDATED_NUMCOPY)
            .doclink(UPDATED_DOCLINK)
            .pagenbr(UPDATED_PAGENBR)
            .highlevel(UPDATED_HIGHLEVEL)
            .confidentiel(UPDATED_CONFIDENTIEL)
            .priority(UPDATED_PRIORITY)
            .obs(UPDATED_OBS)
            .category(UPDATED_CATEGORY)
            .status(UPDATED_STATUS)
            .datejc(UPDATED_DATEJC)
            .datejcsend(UPDATED_DATEJCSEND)
            .typereceive(UPDATED_TYPERECEIVE)
            .typecopy(UPDATED_TYPECOPY)
            .refsnd(UPDATED_REFSND)
            .text(UPDATED_TEXT)
            .docyear(UPDATED_DOCYEAR)
            .datehjrsave(UPDATED_DATEHJRSAVE)
            .datejcsave(UPDATED_DATEJCSAVE)
            .oldOrderNumber(UPDATED_OLD_ORDER_NUMBER)
            .subject(UPDATED_SUBJECT)
            .attach(UPDATED_ATTACH)
            .ordernbradrsbook(UPDATED_ORDERNBRADRSBOOK)
            .seqkeyadrsbook(UPDATED_SEQKEYADRSBOOK)
            .useradrsbook(UPDATED_USERADRSBOOK)
            .cityzenncard(UPDATED_CITYZENNCARD)
            .cityzenphone(UPDATED_CITYZENPHONE)
            .sms(UPDATED_SMS)
            .incident(UPDATED_INCIDENT)
            .checkFavorite(UPDATED_CHECK_FAVORITE)
            .companyId(UPDATED_COMPANY_ID);

        restCorrespondenceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedCorrespondence.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedCorrespondence))
            )
            .andExpect(status().isOk());

        // Validate the Correspondence in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertCorrespondenceUpdatableFieldsEquals(partialUpdatedCorrespondence, getPersistedCorrespondence(partialUpdatedCorrespondence));
    }

    @Test
    @Transactional
    void patchNonExistingCorrespondence() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        correspondence.setId(longCount.incrementAndGet());

        // Create the Correspondence
        CorrespondenceDTO correspondenceDTO = correspondenceMapper.toDto(correspondence);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restCorrespondenceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, correspondenceDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(correspondenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Correspondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchCorrespondence() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        correspondence.setId(longCount.incrementAndGet());

        // Create the Correspondence
        CorrespondenceDTO correspondenceDTO = correspondenceMapper.toDto(correspondence);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restCorrespondenceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(correspondenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Correspondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamCorrespondence() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        correspondence.setId(longCount.incrementAndGet());

        // Create the Correspondence
        CorrespondenceDTO correspondenceDTO = correspondenceMapper.toDto(correspondence);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restCorrespondenceMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(correspondenceDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Correspondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteCorrespondence() throws Exception {
        // Initialize the database
        insertedCorrespondence = correspondenceRepository.saveAndFlush(correspondence);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the correspondence
        restCorrespondenceMockMvc
            .perform(delete(ENTITY_API_URL_ID, correspondence.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return correspondenceRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Correspondence getPersistedCorrespondence(Correspondence correspondence) {
        return correspondenceRepository.findById(correspondence.getId()).orElseThrow();
    }

    protected void assertPersistedCorrespondenceToMatchAllProperties(Correspondence expectedCorrespondence) {
        assertCorrespondenceAllPropertiesEquals(expectedCorrespondence, getPersistedCorrespondence(expectedCorrespondence));
    }

    protected void assertPersistedCorrespondenceToMatchUpdatableProperties(Correspondence expectedCorrespondence) {
        assertCorrespondenceAllUpdatablePropertiesEquals(expectedCorrespondence, getPersistedCorrespondence(expectedCorrespondence));
    }
}
