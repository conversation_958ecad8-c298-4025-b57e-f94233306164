package com.dq.lilas.rest;

import static com.dq.lilas.domain.GmsClientsAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.GmsClients;
import com.dq.lilas.repository.GmsClientsRepository;
import com.dq.lilas.service.dto.GmsClientsDTO;
import com.dq.lilas.service.mapper.GmsClientsMapper;
import com.dq.lilas.web.rest.GmsClientsResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link GmsClientsResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class GmsClientsResourceIT {

    private static final String DEFAULT_CLIENT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_CLIENT_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_CODE = "AAAAAAAAAA";
    private static final String UPDATED_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_CLASSE = "AAAAAAAAAA";
    private static final String UPDATED_CLASSE = "BBBBBBBBBB";

    private static final String DEFAULT_VILLE = "AAAAAAAAAA";
    private static final String UPDATED_VILLE = "BBBBBBBBBB";

    private static final Instant DEFAULT_CREATION_DATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_CREATION_DATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_UPDATE_DATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_UPDATE_DATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_FISCALE_ID = "AAAAAAAAAA";
    private static final String UPDATED_FISCALE_ID = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/gms-clients";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private GmsClientsRepository gmsClientsRepository;

    @Autowired
    private GmsClientsMapper gmsClientsMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restGmsClientsMockMvc;

    private GmsClients gmsClients;

    private GmsClients insertedGmsClients;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static GmsClients createEntity() {
        return new GmsClients()
            .clientName(DEFAULT_CLIENT_NAME)
            .code(DEFAULT_CODE)
            .classe(DEFAULT_CLASSE)
            .ville(DEFAULT_VILLE)
            .creationDate(DEFAULT_CREATION_DATE)
            .updateDate(DEFAULT_UPDATE_DATE)
            .fiscaleId(DEFAULT_FISCALE_ID);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static GmsClients createUpdatedEntity() {
        return new GmsClients()
            .clientName(UPDATED_CLIENT_NAME)
            .code(UPDATED_CODE)
            .classe(UPDATED_CLASSE)
            .ville(UPDATED_VILLE)
            .creationDate(UPDATED_CREATION_DATE)
            .updateDate(UPDATED_UPDATE_DATE)
            .fiscaleId(UPDATED_FISCALE_ID);
    }

    @BeforeEach
    void initTest() {
        gmsClients = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedGmsClients != null) {
            gmsClientsRepository.delete(insertedGmsClients);
            insertedGmsClients = null;
        }
    }

    @Test
    @Transactional
    void createGmsClients() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the GmsClients
        GmsClientsDTO gmsClientsDTO = gmsClientsMapper.toDto(gmsClients);
        var returnedGmsClientsDTO = om.readValue(
            restGmsClientsMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(gmsClientsDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            GmsClientsDTO.class
        );

        // Validate the GmsClients in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedGmsClients = gmsClientsMapper.toEntity(returnedGmsClientsDTO);
        assertGmsClientsUpdatableFieldsEquals(returnedGmsClients, getPersistedGmsClients(returnedGmsClients));

        insertedGmsClients = returnedGmsClients;
    }

    @Test
    @Transactional
    void createGmsClientsWithExistingId() throws Exception {
        // Create the GmsClients with an existing ID
        gmsClients.setId(1L);
        GmsClientsDTO gmsClientsDTO = gmsClientsMapper.toDto(gmsClients);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restGmsClientsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(gmsClientsDTO)))
            .andExpect(status().isBadRequest());

        // Validate the GmsClients in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllGmsClients() throws Exception {
        // Initialize the database
        insertedGmsClients = gmsClientsRepository.saveAndFlush(gmsClients);

        // Get all the gmsClientsList
        restGmsClientsMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(gmsClients.getId().intValue())))
            .andExpect(jsonPath("$.[*].clientName").value(hasItem(DEFAULT_CLIENT_NAME)))
            .andExpect(jsonPath("$.[*].code").value(hasItem(DEFAULT_CODE)))
            .andExpect(jsonPath("$.[*].classe").value(hasItem(DEFAULT_CLASSE)))
            .andExpect(jsonPath("$.[*].ville").value(hasItem(DEFAULT_VILLE)))
            .andExpect(jsonPath("$.[*].creationDate").value(hasItem(DEFAULT_CREATION_DATE.toString())))
            .andExpect(jsonPath("$.[*].updateDate").value(hasItem(DEFAULT_UPDATE_DATE.toString())));
    }

    @Test
    @Transactional
    void getGmsClients() throws Exception {
        // Initialize the database
        insertedGmsClients = gmsClientsRepository.saveAndFlush(gmsClients);

        // Get the gmsClients
        restGmsClientsMockMvc
            .perform(get(ENTITY_API_URL_ID, gmsClients.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(gmsClients.getId().intValue()))
            .andExpect(jsonPath("$.clientName").value(DEFAULT_CLIENT_NAME))
            .andExpect(jsonPath("$.code").value(DEFAULT_CODE))
            .andExpect(jsonPath("$.classe").value(DEFAULT_CLASSE))
            .andExpect(jsonPath("$.ville").value(DEFAULT_VILLE))
            .andExpect(jsonPath("$.creationDate").value(DEFAULT_CREATION_DATE.toString()))
            .andExpect(jsonPath("$.updateDate").value(DEFAULT_UPDATE_DATE.toString()));
    }

    @Test
    @Transactional
    void getNonExistingGmsClients() throws Exception {
        // Get the gmsClients
        restGmsClientsMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingGmsClients() throws Exception {
        // Initialize the database
        insertedGmsClients = gmsClientsRepository.saveAndFlush(gmsClients);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the gmsClients
        GmsClients updatedGmsClients = gmsClientsRepository.findById(gmsClients.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedGmsClients are not directly saved in db
        em.detach(updatedGmsClients);
        updatedGmsClients
            .clientName(UPDATED_CLIENT_NAME)
            .code(UPDATED_CODE)
            .classe(UPDATED_CLASSE)
            .ville(UPDATED_VILLE)
            .creationDate(UPDATED_CREATION_DATE)
            .updateDate(UPDATED_UPDATE_DATE);
        GmsClientsDTO gmsClientsDTO = gmsClientsMapper.toDto(updatedGmsClients);

        restGmsClientsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, gmsClientsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(gmsClientsDTO))
            )
            .andExpect(status().isOk());

        // Validate the GmsClients in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedGmsClientsToMatchAllProperties(updatedGmsClients);
    }

    @Test
    @Transactional
    void putNonExistingGmsClients() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        gmsClients.setId(longCount.incrementAndGet());

        // Create the GmsClients
        GmsClientsDTO gmsClientsDTO = gmsClientsMapper.toDto(gmsClients);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restGmsClientsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, gmsClientsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(gmsClientsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the GmsClients in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchGmsClients() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        gmsClients.setId(longCount.incrementAndGet());

        // Create the GmsClients
        GmsClientsDTO gmsClientsDTO = gmsClientsMapper.toDto(gmsClients);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restGmsClientsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(gmsClientsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the GmsClients in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamGmsClients() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        gmsClients.setId(longCount.incrementAndGet());

        // Create the GmsClients
        GmsClientsDTO gmsClientsDTO = gmsClientsMapper.toDto(gmsClients);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restGmsClientsMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(gmsClientsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the GmsClients in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateGmsClientsWithPatch() throws Exception {
        // Initialize the database
        insertedGmsClients = gmsClientsRepository.saveAndFlush(gmsClients);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the gmsClients using partial update
        GmsClients partialUpdatedGmsClients = new GmsClients();
        partialUpdatedGmsClients.setId(gmsClients.getId());

        partialUpdatedGmsClients
            .clientName(UPDATED_CLIENT_NAME)
            .classe(UPDATED_CLASSE)
            .ville(UPDATED_VILLE)
            .updateDate(UPDATED_UPDATE_DATE);

        restGmsClientsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedGmsClients.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedGmsClients))
            )
            .andExpect(status().isOk());

        // Validate the GmsClients in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertGmsClientsUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedGmsClients, gmsClients),
            getPersistedGmsClients(gmsClients)
        );
    }

    @Test
    @Transactional
    void fullUpdateGmsClientsWithPatch() throws Exception {
        // Initialize the database
        insertedGmsClients = gmsClientsRepository.saveAndFlush(gmsClients);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the gmsClients using partial update
        GmsClients partialUpdatedGmsClients = new GmsClients();
        partialUpdatedGmsClients.setId(gmsClients.getId());

        partialUpdatedGmsClients
            .clientName(UPDATED_CLIENT_NAME)
            .code(UPDATED_CODE)
            .classe(UPDATED_CLASSE)
            .ville(UPDATED_VILLE)
            .creationDate(UPDATED_CREATION_DATE)
            .updateDate(UPDATED_UPDATE_DATE);

        restGmsClientsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedGmsClients.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedGmsClients))
            )
            .andExpect(status().isOk());

        // Validate the GmsClients in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertGmsClientsUpdatableFieldsEquals(partialUpdatedGmsClients, getPersistedGmsClients(partialUpdatedGmsClients));
    }

    @Test
    @Transactional
    void patchNonExistingGmsClients() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        gmsClients.setId(longCount.incrementAndGet());

        // Create the GmsClients
        GmsClientsDTO gmsClientsDTO = gmsClientsMapper.toDto(gmsClients);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restGmsClientsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, gmsClientsDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(gmsClientsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the GmsClients in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchGmsClients() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        gmsClients.setId(longCount.incrementAndGet());

        // Create the GmsClients
        GmsClientsDTO gmsClientsDTO = gmsClientsMapper.toDto(gmsClients);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restGmsClientsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(gmsClientsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the GmsClients in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamGmsClients() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        gmsClients.setId(longCount.incrementAndGet());

        // Create the GmsClients
        GmsClientsDTO gmsClientsDTO = gmsClientsMapper.toDto(gmsClients);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restGmsClientsMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(gmsClientsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the GmsClients in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteGmsClients() throws Exception {
        // Initialize the database
        insertedGmsClients = gmsClientsRepository.saveAndFlush(gmsClients);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the gmsClients
        restGmsClientsMockMvc
            .perform(delete(ENTITY_API_URL_ID, gmsClients.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return gmsClientsRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected GmsClients getPersistedGmsClients(GmsClients gmsClients) {
        return gmsClientsRepository.findById(gmsClients.getId()).orElseThrow();
    }

    protected void assertPersistedGmsClientsToMatchAllProperties(GmsClients expectedGmsClients) {
        assertGmsClientsAllPropertiesEquals(expectedGmsClients, getPersistedGmsClients(expectedGmsClients));
    }

    protected void assertPersistedGmsClientsToMatchUpdatableProperties(GmsClients expectedGmsClients) {
        assertGmsClientsAllUpdatablePropertiesEquals(expectedGmsClients, getPersistedGmsClients(expectedGmsClients));
    }
}
