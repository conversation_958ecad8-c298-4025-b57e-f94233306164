package com.dq.lilas.rest;

import static com.dq.lilas.domain.JoblangAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Joblang;
import com.dq.lilas.repository.JoblangRepository;
import com.dq.lilas.service.dto.JoblangDTO;
import com.dq.lilas.service.mapper.JoblangMapper;
import com.dq.lilas.web.rest.JoblangResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link JoblangResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class JoblangResourceIT {

    private static final String DEFAULT_LBL = "AAAAAAAAAA";
    private static final String UPDATED_LBL = "BBBBBBBBBB";

    private static final String DEFAULT_LANG = "AAAAA";
    private static final String UPDATED_LANG = "BBBBB";

    private static final String DEFAULT_ABR = "AAAAAAAAAA";
    private static final String UPDATED_ABR = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/joblangs";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private JoblangRepository joblangRepository;

    @Autowired
    private JoblangMapper joblangMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restJoblangMockMvc;

    private Joblang joblang;

    private Joblang insertedJoblang;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Joblang createEntity() {
        return new Joblang().lbl(DEFAULT_LBL).lang(DEFAULT_LANG).abr(DEFAULT_ABR);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Joblang createUpdatedEntity() {
        return new Joblang().lbl(UPDATED_LBL).lang(UPDATED_LANG).abr(UPDATED_ABR);
    }

    @BeforeEach
    void initTest() {
        joblang = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedJoblang != null) {
            joblangRepository.delete(insertedJoblang);
            insertedJoblang = null;
        }
    }

    @Test
    @Transactional
    void createJoblang() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Joblang
        JoblangDTO joblangDTO = joblangMapper.toDto(joblang);
        var returnedJoblangDTO = om.readValue(
            restJoblangMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(joblangDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            JoblangDTO.class
        );

        // Validate the Joblang in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedJoblang = joblangMapper.toEntity(returnedJoblangDTO);
        assertJoblangUpdatableFieldsEquals(returnedJoblang, getPersistedJoblang(returnedJoblang));

        insertedJoblang = returnedJoblang;
    }

    @Test
    @Transactional
    void createJoblangWithExistingId() throws Exception {
        // Create the Joblang with an existing ID
        joblang.setId(1L);
        JoblangDTO joblangDTO = joblangMapper.toDto(joblang);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restJoblangMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(joblangDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Joblang in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllJoblangs() throws Exception {
        // Initialize the database
        insertedJoblang = joblangRepository.saveAndFlush(joblang);

        // Get all the joblangList
        restJoblangMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(joblang.getId().intValue())))
            .andExpect(jsonPath("$.[*].lbl").value(hasItem(DEFAULT_LBL)))
            .andExpect(jsonPath("$.[*].lang").value(hasItem(DEFAULT_LANG)))
            .andExpect(jsonPath("$.[*].abr").value(hasItem(DEFAULT_ABR)));
    }

    @Test
    @Transactional
    void getJoblang() throws Exception {
        // Initialize the database
        insertedJoblang = joblangRepository.saveAndFlush(joblang);

        // Get the joblang
        restJoblangMockMvc
            .perform(get(ENTITY_API_URL_ID, joblang.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(joblang.getId().intValue()))
            .andExpect(jsonPath("$.lbl").value(DEFAULT_LBL))
            .andExpect(jsonPath("$.lang").value(DEFAULT_LANG))
            .andExpect(jsonPath("$.abr").value(DEFAULT_ABR));
    }

    @Test
    @Transactional
    void getNonExistingJoblang() throws Exception {
        // Get the joblang
        restJoblangMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingJoblang() throws Exception {
        // Initialize the database
        insertedJoblang = joblangRepository.saveAndFlush(joblang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the joblang
        Joblang updatedJoblang = joblangRepository.findById(joblang.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedJoblang are not directly saved in db
        em.detach(updatedJoblang);
        updatedJoblang.lbl(UPDATED_LBL).lang(UPDATED_LANG).abr(UPDATED_ABR);
        JoblangDTO joblangDTO = joblangMapper.toDto(updatedJoblang);

        restJoblangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, joblangDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(joblangDTO))
            )
            .andExpect(status().isOk());

        // Validate the Joblang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedJoblangToMatchAllProperties(updatedJoblang);
    }

    @Test
    @Transactional
    void putNonExistingJoblang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        joblang.setId(longCount.incrementAndGet());

        // Create the Joblang
        JoblangDTO joblangDTO = joblangMapper.toDto(joblang);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restJoblangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, joblangDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(joblangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Joblang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchJoblang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        joblang.setId(longCount.incrementAndGet());

        // Create the Joblang
        JoblangDTO joblangDTO = joblangMapper.toDto(joblang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restJoblangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(joblangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Joblang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamJoblang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        joblang.setId(longCount.incrementAndGet());

        // Create the Joblang
        JoblangDTO joblangDTO = joblangMapper.toDto(joblang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restJoblangMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(joblangDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Joblang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateJoblangWithPatch() throws Exception {
        // Initialize the database
        insertedJoblang = joblangRepository.saveAndFlush(joblang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the joblang using partial update
        Joblang partialUpdatedJoblang = new Joblang();
        partialUpdatedJoblang.setId(joblang.getId());

        partialUpdatedJoblang.lbl(UPDATED_LBL).lang(UPDATED_LANG);

        restJoblangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedJoblang.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedJoblang))
            )
            .andExpect(status().isOk());

        // Validate the Joblang in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertJoblangUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedJoblang, joblang), getPersistedJoblang(joblang));
    }

    @Test
    @Transactional
    void fullUpdateJoblangWithPatch() throws Exception {
        // Initialize the database
        insertedJoblang = joblangRepository.saveAndFlush(joblang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the joblang using partial update
        Joblang partialUpdatedJoblang = new Joblang();
        partialUpdatedJoblang.setId(joblang.getId());

        partialUpdatedJoblang.lbl(UPDATED_LBL).lang(UPDATED_LANG).abr(UPDATED_ABR);

        restJoblangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedJoblang.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedJoblang))
            )
            .andExpect(status().isOk());

        // Validate the Joblang in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertJoblangUpdatableFieldsEquals(partialUpdatedJoblang, getPersistedJoblang(partialUpdatedJoblang));
    }

    @Test
    @Transactional
    void patchNonExistingJoblang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        joblang.setId(longCount.incrementAndGet());

        // Create the Joblang
        JoblangDTO joblangDTO = joblangMapper.toDto(joblang);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restJoblangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, joblangDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(joblangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Joblang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchJoblang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        joblang.setId(longCount.incrementAndGet());

        // Create the Joblang
        JoblangDTO joblangDTO = joblangMapper.toDto(joblang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restJoblangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(joblangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Joblang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamJoblang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        joblang.setId(longCount.incrementAndGet());

        // Create the Joblang
        JoblangDTO joblangDTO = joblangMapper.toDto(joblang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restJoblangMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(joblangDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Joblang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteJoblang() throws Exception {
        // Initialize the database
        insertedJoblang = joblangRepository.saveAndFlush(joblang);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the joblang
        restJoblangMockMvc
            .perform(delete(ENTITY_API_URL_ID, joblang.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return joblangRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Joblang getPersistedJoblang(Joblang joblang) {
        return joblangRepository.findById(joblang.getId()).orElseThrow();
    }

    protected void assertPersistedJoblangToMatchAllProperties(Joblang expectedJoblang) {
        assertJoblangAllPropertiesEquals(expectedJoblang, getPersistedJoblang(expectedJoblang));
    }

    protected void assertPersistedJoblangToMatchUpdatableProperties(Joblang expectedJoblang) {
        assertJoblangAllUpdatablePropertiesEquals(expectedJoblang, getPersistedJoblang(expectedJoblang));
    }
}
