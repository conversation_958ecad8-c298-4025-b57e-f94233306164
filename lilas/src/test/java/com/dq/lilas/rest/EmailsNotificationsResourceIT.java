package com.dq.lilas.rest;

import static com.dq.lilas.domain.EmailsNotificationsAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.EmailsNotifications;
import com.dq.lilas.repository.EmailsNotificationsRepository;
import com.dq.lilas.service.dto.EmailsNotificationsDTO;
import com.dq.lilas.service.mapper.EmailsNotificationsMapper;
import com.dq.lilas.web.rest.EmailsNotificationsResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Base64;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link EmailsNotificationsResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class EmailsNotificationsResourceIT {

    private static final String DEFAULT_SUBJECT = "AAAAAAAAAA";
    private static final String UPDATED_SUBJECT = "BBBBBBBBBB";

    private static final Instant DEFAULT_NOTIFICATION_DATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_NOTIFICATION_DATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final byte[] DEFAULT_BODY = TestUtil.createByteArray(1, "0");
    private static final byte[] UPDATED_BODY = TestUtil.createByteArray(1, "1");
    private static final String DEFAULT_BODY_CONTENT_TYPE = "image/jpg";
    private static final String UPDATED_BODY_CONTENT_TYPE = "image/png";

    private static final Long DEFAULT_RECIPIENT = 1L;
    private static final Long UPDATED_RECIPIENT = 2L;

    private static final String DEFAULT_SENDER = "AAAAAAAAAA";
    private static final String UPDATED_SENDER = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/emails-notifications";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private EmailsNotificationsRepository emailsNotificationsRepository;

    @Autowired
    private EmailsNotificationsMapper emailsNotificationsMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restEmailsNotificationsMockMvc;

    private EmailsNotifications emailsNotifications;

    private EmailsNotifications insertedEmailsNotifications;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static EmailsNotifications createEntity() {
        return new EmailsNotifications()
            .subject(DEFAULT_SUBJECT)
            .notificationDate(DEFAULT_NOTIFICATION_DATE)
            .body(DEFAULT_BODY)
            .bodyContentType(DEFAULT_BODY_CONTENT_TYPE)
            .recipient(DEFAULT_RECIPIENT)
            .sender(DEFAULT_SENDER);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static EmailsNotifications createUpdatedEntity() {
        return new EmailsNotifications()
            .subject(UPDATED_SUBJECT)
            .notificationDate(UPDATED_NOTIFICATION_DATE)
            .body(UPDATED_BODY)
            .bodyContentType(UPDATED_BODY_CONTENT_TYPE)
            .recipient(UPDATED_RECIPIENT)
            .sender(UPDATED_SENDER);
    }

    @BeforeEach
    void initTest() {
        emailsNotifications = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedEmailsNotifications != null) {
            emailsNotificationsRepository.delete(insertedEmailsNotifications);
            insertedEmailsNotifications = null;
        }
    }

    @Test
    @Transactional
    void createEmailsNotifications() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the EmailsNotifications
        EmailsNotificationsDTO emailsNotificationsDTO = emailsNotificationsMapper.toDto(emailsNotifications);
        var returnedEmailsNotificationsDTO = om.readValue(
            restEmailsNotificationsMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(emailsNotificationsDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            EmailsNotificationsDTO.class
        );

        // Validate the EmailsNotifications in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedEmailsNotifications = emailsNotificationsMapper.toEntity(returnedEmailsNotificationsDTO);
        assertEmailsNotificationsUpdatableFieldsEquals(
            returnedEmailsNotifications,
            getPersistedEmailsNotifications(returnedEmailsNotifications)
        );

        insertedEmailsNotifications = returnedEmailsNotifications;
    }

    @Test
    @Transactional
    void createEmailsNotificationsWithExistingId() throws Exception {
        // Create the EmailsNotifications with an existing ID
        emailsNotifications.setId(1L);
        EmailsNotificationsDTO emailsNotificationsDTO = emailsNotificationsMapper.toDto(emailsNotifications);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restEmailsNotificationsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(emailsNotificationsDTO)))
            .andExpect(status().isBadRequest());

        // Validate the EmailsNotifications in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllEmailsNotifications() throws Exception {
        // Initialize the database
        insertedEmailsNotifications = emailsNotificationsRepository.saveAndFlush(emailsNotifications);

        // Get all the emailsNotificationsList
        restEmailsNotificationsMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(emailsNotifications.getId().intValue())))
            .andExpect(jsonPath("$.[*].subject").value(hasItem(DEFAULT_SUBJECT)))
            .andExpect(jsonPath("$.[*].notificationDate").value(hasItem(DEFAULT_NOTIFICATION_DATE.toString())))
            .andExpect(jsonPath("$.[*].bodyContentType").value(hasItem(DEFAULT_BODY_CONTENT_TYPE)))
            .andExpect(jsonPath("$.[*].body").value(hasItem(Base64.getEncoder().encodeToString(DEFAULT_BODY))))
            .andExpect(jsonPath("$.[*].recipient").value(hasItem(DEFAULT_RECIPIENT.intValue())))
            .andExpect(jsonPath("$.[*].sender").value(hasItem(DEFAULT_SENDER)));
    }

    @Test
    @Transactional
    void getEmailsNotifications() throws Exception {
        // Initialize the database
        insertedEmailsNotifications = emailsNotificationsRepository.saveAndFlush(emailsNotifications);

        // Get the emailsNotifications
        restEmailsNotificationsMockMvc
            .perform(get(ENTITY_API_URL_ID, emailsNotifications.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(emailsNotifications.getId().intValue()))
            .andExpect(jsonPath("$.subject").value(DEFAULT_SUBJECT))
            .andExpect(jsonPath("$.notificationDate").value(DEFAULT_NOTIFICATION_DATE.toString()))
            .andExpect(jsonPath("$.bodyContentType").value(DEFAULT_BODY_CONTENT_TYPE))
            .andExpect(jsonPath("$.body").value(Base64.getEncoder().encodeToString(DEFAULT_BODY)))
            .andExpect(jsonPath("$.recipient").value(DEFAULT_RECIPIENT.intValue()))
            .andExpect(jsonPath("$.sender").value(DEFAULT_SENDER));
    }

    @Test
    @Transactional
    void getNonExistingEmailsNotifications() throws Exception {
        // Get the emailsNotifications
        restEmailsNotificationsMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingEmailsNotifications() throws Exception {
        // Initialize the database
        insertedEmailsNotifications = emailsNotificationsRepository.saveAndFlush(emailsNotifications);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the emailsNotifications
        EmailsNotifications updatedEmailsNotifications = emailsNotificationsRepository.findById(emailsNotifications.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedEmailsNotifications are not directly saved in db
        em.detach(updatedEmailsNotifications);
        updatedEmailsNotifications
            .subject(UPDATED_SUBJECT)
            .notificationDate(UPDATED_NOTIFICATION_DATE)
            .body(UPDATED_BODY)
            .bodyContentType(UPDATED_BODY_CONTENT_TYPE)
            .recipient(UPDATED_RECIPIENT)
            .sender(UPDATED_SENDER);
        EmailsNotificationsDTO emailsNotificationsDTO = emailsNotificationsMapper.toDto(updatedEmailsNotifications);

        restEmailsNotificationsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, emailsNotificationsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(emailsNotificationsDTO))
            )
            .andExpect(status().isOk());

        // Validate the EmailsNotifications in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedEmailsNotificationsToMatchAllProperties(updatedEmailsNotifications);
    }

    @Test
    @Transactional
    void putNonExistingEmailsNotifications() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        emailsNotifications.setId(longCount.incrementAndGet());

        // Create the EmailsNotifications
        EmailsNotificationsDTO emailsNotificationsDTO = emailsNotificationsMapper.toDto(emailsNotifications);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restEmailsNotificationsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, emailsNotificationsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(emailsNotificationsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmailsNotifications in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchEmailsNotifications() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        emailsNotifications.setId(longCount.incrementAndGet());

        // Create the EmailsNotifications
        EmailsNotificationsDTO emailsNotificationsDTO = emailsNotificationsMapper.toDto(emailsNotifications);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmailsNotificationsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(emailsNotificationsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmailsNotifications in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamEmailsNotifications() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        emailsNotifications.setId(longCount.incrementAndGet());

        // Create the EmailsNotifications
        EmailsNotificationsDTO emailsNotificationsDTO = emailsNotificationsMapper.toDto(emailsNotifications);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmailsNotificationsMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(emailsNotificationsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the EmailsNotifications in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateEmailsNotificationsWithPatch() throws Exception {
        // Initialize the database
        insertedEmailsNotifications = emailsNotificationsRepository.saveAndFlush(emailsNotifications);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the emailsNotifications using partial update
        EmailsNotifications partialUpdatedEmailsNotifications = new EmailsNotifications();
        partialUpdatedEmailsNotifications.setId(emailsNotifications.getId());

        partialUpdatedEmailsNotifications
            .notificationDate(UPDATED_NOTIFICATION_DATE)
            .body(UPDATED_BODY)
            .bodyContentType(UPDATED_BODY_CONTENT_TYPE);

        restEmailsNotificationsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedEmailsNotifications.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedEmailsNotifications))
            )
            .andExpect(status().isOk());

        // Validate the EmailsNotifications in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertEmailsNotificationsUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedEmailsNotifications, emailsNotifications),
            getPersistedEmailsNotifications(emailsNotifications)
        );
    }

    @Test
    @Transactional
    void fullUpdateEmailsNotificationsWithPatch() throws Exception {
        // Initialize the database
        insertedEmailsNotifications = emailsNotificationsRepository.saveAndFlush(emailsNotifications);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the emailsNotifications using partial update
        EmailsNotifications partialUpdatedEmailsNotifications = new EmailsNotifications();
        partialUpdatedEmailsNotifications.setId(emailsNotifications.getId());

        partialUpdatedEmailsNotifications
            .subject(UPDATED_SUBJECT)
            .notificationDate(UPDATED_NOTIFICATION_DATE)
            .body(UPDATED_BODY)
            .bodyContentType(UPDATED_BODY_CONTENT_TYPE)
            .recipient(UPDATED_RECIPIENT)
            .sender(UPDATED_SENDER);

        restEmailsNotificationsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedEmailsNotifications.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedEmailsNotifications))
            )
            .andExpect(status().isOk());

        // Validate the EmailsNotifications in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertEmailsNotificationsUpdatableFieldsEquals(
            partialUpdatedEmailsNotifications,
            getPersistedEmailsNotifications(partialUpdatedEmailsNotifications)
        );
    }

    @Test
    @Transactional
    void patchNonExistingEmailsNotifications() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        emailsNotifications.setId(longCount.incrementAndGet());

        // Create the EmailsNotifications
        EmailsNotificationsDTO emailsNotificationsDTO = emailsNotificationsMapper.toDto(emailsNotifications);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restEmailsNotificationsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, emailsNotificationsDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(emailsNotificationsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmailsNotifications in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchEmailsNotifications() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        emailsNotifications.setId(longCount.incrementAndGet());

        // Create the EmailsNotifications
        EmailsNotificationsDTO emailsNotificationsDTO = emailsNotificationsMapper.toDto(emailsNotifications);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmailsNotificationsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(emailsNotificationsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the EmailsNotifications in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamEmailsNotifications() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        emailsNotifications.setId(longCount.incrementAndGet());

        // Create the EmailsNotifications
        EmailsNotificationsDTO emailsNotificationsDTO = emailsNotificationsMapper.toDto(emailsNotifications);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmailsNotificationsMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(emailsNotificationsDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the EmailsNotifications in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteEmailsNotifications() throws Exception {
        // Initialize the database
        insertedEmailsNotifications = emailsNotificationsRepository.saveAndFlush(emailsNotifications);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the emailsNotifications
        restEmailsNotificationsMockMvc
            .perform(delete(ENTITY_API_URL_ID, emailsNotifications.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return emailsNotificationsRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected EmailsNotifications getPersistedEmailsNotifications(EmailsNotifications emailsNotifications) {
        return emailsNotificationsRepository.findById(emailsNotifications.getId()).orElseThrow();
    }

    protected void assertPersistedEmailsNotificationsToMatchAllProperties(EmailsNotifications expectedEmailsNotifications) {
        assertEmailsNotificationsAllPropertiesEquals(
            expectedEmailsNotifications,
            getPersistedEmailsNotifications(expectedEmailsNotifications)
        );
    }

    protected void assertPersistedEmailsNotificationsToMatchUpdatableProperties(EmailsNotifications expectedEmailsNotifications) {
        assertEmailsNotificationsAllUpdatablePropertiesEquals(
            expectedEmailsNotifications,
            getPersistedEmailsNotifications(expectedEmailsNotifications)
        );
    }
}
