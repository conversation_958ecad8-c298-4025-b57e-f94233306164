package com.dq.lilas.rest;

import static com.dq.lilas.domain.ActionAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Action;
import com.dq.lilas.repository.ActionRepository;
import com.dq.lilas.service.dto.ActionDTO;
import com.dq.lilas.service.mapper.ActionMapper;
import com.dq.lilas.web.rest.ActionResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link ActionResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class ActionResourceIT {

    private static final String DEFAULT_REGULATION = "A";
    private static final String UPDATED_REGULATION = "B";

    private static final String DEFAULT_CIRCULAR = "A";
    private static final String UPDATED_CIRCULAR = "B";

    private static final Integer DEFAULT_APP_ORDER = 1;
    private static final Integer UPDATED_APP_ORDER = 2;

    private static final String DEFAULT_STATUT = "A";
    private static final String UPDATED_STATUT = "B";

    private static final String ENTITY_API_URL = "/api/actions";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private ActionRepository actionRepository;

    @Autowired
    private ActionMapper actionMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restActionMockMvc;

    private Action action;

    private Action insertedAction;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Action createEntity() {
        return new Action().regulation(DEFAULT_REGULATION).circular(DEFAULT_CIRCULAR).appOrder(DEFAULT_APP_ORDER).statut(DEFAULT_STATUT);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Action createUpdatedEntity() {
        return new Action().regulation(UPDATED_REGULATION).circular(UPDATED_CIRCULAR).appOrder(UPDATED_APP_ORDER).statut(UPDATED_STATUT);
    }

    @BeforeEach
    void initTest() {
        action = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedAction != null) {
            actionRepository.delete(insertedAction);
            insertedAction = null;
        }
    }

    @Test
    @Transactional
    void createAction() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Action
        ActionDTO actionDTO = actionMapper.toDto(action);
        var returnedActionDTO = om.readValue(
            restActionMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(actionDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            ActionDTO.class
        );

        // Validate the Action in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedAction = actionMapper.toEntity(returnedActionDTO);
        assertActionUpdatableFieldsEquals(returnedAction, getPersistedAction(returnedAction));

        insertedAction = returnedAction;
    }

    @Test
    @Transactional
    void createActionWithExistingId() throws Exception {
        // Create the Action with an existing ID
        action.setId(1L);
        ActionDTO actionDTO = actionMapper.toDto(action);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restActionMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(actionDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Action in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllActions() throws Exception {
        // Initialize the database
        insertedAction = actionRepository.saveAndFlush(action);

        // Get all the actionList
        restActionMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(action.getId().intValue())))
            .andExpect(jsonPath("$.[*].regulation").value(hasItem(DEFAULT_REGULATION)))
            .andExpect(jsonPath("$.[*].circular").value(hasItem(DEFAULT_CIRCULAR)))
            .andExpect(jsonPath("$.[*].appOrder").value(hasItem(DEFAULT_APP_ORDER)))
            .andExpect(jsonPath("$.[*].statut").value(hasItem(DEFAULT_STATUT)));
    }

    @Test
    @Transactional
    void getAction() throws Exception {
        // Initialize the database
        insertedAction = actionRepository.saveAndFlush(action);

        // Get the action
        restActionMockMvc
            .perform(get(ENTITY_API_URL_ID, action.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(action.getId().intValue()))
            .andExpect(jsonPath("$.regulation").value(DEFAULT_REGULATION))
            .andExpect(jsonPath("$.circular").value(DEFAULT_CIRCULAR))
            .andExpect(jsonPath("$.appOrder").value(DEFAULT_APP_ORDER))
            .andExpect(jsonPath("$.statut").value(DEFAULT_STATUT));
    }

    @Test
    @Transactional
    void getNonExistingAction() throws Exception {
        // Get the action
        restActionMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingAction() throws Exception {
        // Initialize the database
        insertedAction = actionRepository.saveAndFlush(action);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the action
        Action updatedAction = actionRepository.findById(action.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedAction are not directly saved in db
        em.detach(updatedAction);
        updatedAction.regulation(UPDATED_REGULATION).circular(UPDATED_CIRCULAR).appOrder(UPDATED_APP_ORDER).statut(UPDATED_STATUT);
        ActionDTO actionDTO = actionMapper.toDto(updatedAction);

        restActionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, actionDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(actionDTO))
            )
            .andExpect(status().isOk());

        // Validate the Action in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedActionToMatchAllProperties(updatedAction);
    }

    @Test
    @Transactional
    void putNonExistingAction() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        action.setId(longCount.incrementAndGet());

        // Create the Action
        ActionDTO actionDTO = actionMapper.toDto(action);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restActionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, actionDTO.getId()).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(actionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Action in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchAction() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        action.setId(longCount.incrementAndGet());

        // Create the Action
        ActionDTO actionDTO = actionMapper.toDto(action);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restActionMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(actionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Action in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamAction() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        action.setId(longCount.incrementAndGet());

        // Create the Action
        ActionDTO actionDTO = actionMapper.toDto(action);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restActionMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(actionDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Action in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateActionWithPatch() throws Exception {
        // Initialize the database
        insertedAction = actionRepository.saveAndFlush(action);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the action using partial update
        Action partialUpdatedAction = new Action();
        partialUpdatedAction.setId(action.getId());

        restActionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAction.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAction))
            )
            .andExpect(status().isOk());

        // Validate the Action in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertActionUpdatableFieldsEquals(createUpdateProxyForBean(partialUpdatedAction, action), getPersistedAction(action));
    }

    @Test
    @Transactional
    void fullUpdateActionWithPatch() throws Exception {
        // Initialize the database
        insertedAction = actionRepository.saveAndFlush(action);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the action using partial update
        Action partialUpdatedAction = new Action();
        partialUpdatedAction.setId(action.getId());

        partialUpdatedAction.regulation(UPDATED_REGULATION).circular(UPDATED_CIRCULAR).appOrder(UPDATED_APP_ORDER).statut(UPDATED_STATUT);

        restActionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedAction.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedAction))
            )
            .andExpect(status().isOk());

        // Validate the Action in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertActionUpdatableFieldsEquals(partialUpdatedAction, getPersistedAction(partialUpdatedAction));
    }

    @Test
    @Transactional
    void patchNonExistingAction() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        action.setId(longCount.incrementAndGet());

        // Create the Action
        ActionDTO actionDTO = actionMapper.toDto(action);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restActionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, actionDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(actionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Action in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchAction() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        action.setId(longCount.incrementAndGet());

        // Create the Action
        ActionDTO actionDTO = actionMapper.toDto(action);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restActionMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(actionDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Action in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamAction() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        action.setId(longCount.incrementAndGet());

        // Create the Action
        ActionDTO actionDTO = actionMapper.toDto(action);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restActionMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(actionDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Action in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteAction() throws Exception {
        // Initialize the database
        insertedAction = actionRepository.saveAndFlush(action);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the action
        restActionMockMvc
            .perform(delete(ENTITY_API_URL_ID, action.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return actionRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Action getPersistedAction(Action action) {
        return actionRepository.findById(action.getId()).orElseThrow();
    }

    protected void assertPersistedActionToMatchAllProperties(Action expectedAction) {
        assertActionAllPropertiesEquals(expectedAction, getPersistedAction(expectedAction));
    }

    protected void assertPersistedActionToMatchUpdatableProperties(Action expectedAction) {
        assertActionAllUpdatablePropertiesEquals(expectedAction, getPersistedAction(expectedAction));
    }
}
