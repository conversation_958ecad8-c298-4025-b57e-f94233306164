package com.dq.lilas.rest;

import static com.dq.lilas.domain.TypecorrespondencelangAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Typecorrespondencelang;
import com.dq.lilas.repository.TypecorrespondencelangRepository;
import com.dq.lilas.service.dto.TypecorrespondencelangDTO;
import com.dq.lilas.service.mapper.TypecorrespondencelangMapper;
import com.dq.lilas.web.rest.TypecorrespondencelangResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link TypecorrespondencelangResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class TypecorrespondencelangResourceIT {

    private static final String DEFAULT_LBL = "AAAAAAAAAA";
    private static final String UPDATED_LBL = "BBBBBBBBBB";

    private static final String DEFAULT_LANG = "AAAAAAAAAA";
    private static final String UPDATED_LANG = "BBBBBBBBBB";

    private static final String DEFAULT_ABBREVIATED = "AAAAAAAAAA";
    private static final String UPDATED_ABBREVIATED = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/typecorrespondencelangs";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private TypecorrespondencelangRepository typecorrespondencelangRepository;

    @Autowired
    private TypecorrespondencelangMapper typecorrespondencelangMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restTypecorrespondencelangMockMvc;

    private Typecorrespondencelang typecorrespondencelang;

    private Typecorrespondencelang insertedTypecorrespondencelang;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Typecorrespondencelang createEntity() {
        return new Typecorrespondencelang().lbl(DEFAULT_LBL).lang(DEFAULT_LANG).abbreviated(DEFAULT_ABBREVIATED);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Typecorrespondencelang createUpdatedEntity() {
        return new Typecorrespondencelang().lbl(UPDATED_LBL).lang(UPDATED_LANG).abbreviated(UPDATED_ABBREVIATED);
    }

    @BeforeEach
    void initTest() {
        typecorrespondencelang = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedTypecorrespondencelang != null) {
            typecorrespondencelangRepository.delete(insertedTypecorrespondencelang);
            insertedTypecorrespondencelang = null;
        }
    }

    @Test
    @Transactional
    void createTypecorrespondencelang() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Typecorrespondencelang
        TypecorrespondencelangDTO typecorrespondencelangDTO = typecorrespondencelangMapper.toDto(typecorrespondencelang);
        var returnedTypecorrespondencelangDTO = om.readValue(
            restTypecorrespondencelangMockMvc
                .perform(
                    post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(typecorrespondencelangDTO))
                )
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            TypecorrespondencelangDTO.class
        );

        // Validate the Typecorrespondencelang in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedTypecorrespondencelang = typecorrespondencelangMapper.toEntity(returnedTypecorrespondencelangDTO);
        assertTypecorrespondencelangUpdatableFieldsEquals(
            returnedTypecorrespondencelang,
            getPersistedTypecorrespondencelang(returnedTypecorrespondencelang)
        );

        insertedTypecorrespondencelang = returnedTypecorrespondencelang;
    }

    @Test
    @Transactional
    void createTypecorrespondencelangWithExistingId() throws Exception {
        // Create the Typecorrespondencelang with an existing ID
        typecorrespondencelang.setId(1L);
        TypecorrespondencelangDTO typecorrespondencelangDTO = typecorrespondencelangMapper.toDto(typecorrespondencelang);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restTypecorrespondencelangMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(typecorrespondencelangDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Typecorrespondencelang in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllTypecorrespondencelangs() throws Exception {
        // Initialize the database
        insertedTypecorrespondencelang = typecorrespondencelangRepository.saveAndFlush(typecorrespondencelang);

        // Get all the typecorrespondencelangList
        restTypecorrespondencelangMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(typecorrespondencelang.getId().intValue())))
            .andExpect(jsonPath("$.[*].lbl").value(hasItem(DEFAULT_LBL)))
            .andExpect(jsonPath("$.[*].lang").value(hasItem(DEFAULT_LANG)))
            .andExpect(jsonPath("$.[*].abbreviated").value(hasItem(DEFAULT_ABBREVIATED)));
    }

    @Test
    @Transactional
    void getTypecorrespondencelang() throws Exception {
        // Initialize the database
        insertedTypecorrespondencelang = typecorrespondencelangRepository.saveAndFlush(typecorrespondencelang);

        // Get the typecorrespondencelang
        restTypecorrespondencelangMockMvc
            .perform(get(ENTITY_API_URL_ID, typecorrespondencelang.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(typecorrespondencelang.getId().intValue()))
            .andExpect(jsonPath("$.lbl").value(DEFAULT_LBL))
            .andExpect(jsonPath("$.lang").value(DEFAULT_LANG))
            .andExpect(jsonPath("$.abbreviated").value(DEFAULT_ABBREVIATED));
    }

    @Test
    @Transactional
    void getNonExistingTypecorrespondencelang() throws Exception {
        // Get the typecorrespondencelang
        restTypecorrespondencelangMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingTypecorrespondencelang() throws Exception {
        // Initialize the database
        insertedTypecorrespondencelang = typecorrespondencelangRepository.saveAndFlush(typecorrespondencelang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the typecorrespondencelang
        Typecorrespondencelang updatedTypecorrespondencelang = typecorrespondencelangRepository
            .findById(typecorrespondencelang.getId())
            .orElseThrow();
        // Disconnect from session so that the updates on updatedTypecorrespondencelang are not directly saved in db
        em.detach(updatedTypecorrespondencelang);
        updatedTypecorrespondencelang.lbl(UPDATED_LBL).lang(UPDATED_LANG).abbreviated(UPDATED_ABBREVIATED);
        TypecorrespondencelangDTO typecorrespondencelangDTO = typecorrespondencelangMapper.toDto(updatedTypecorrespondencelang);

        restTypecorrespondencelangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, typecorrespondencelangDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(typecorrespondencelangDTO))
            )
            .andExpect(status().isOk());

        // Validate the Typecorrespondencelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedTypecorrespondencelangToMatchAllProperties(updatedTypecorrespondencelang);
    }

    @Test
    @Transactional
    void putNonExistingTypecorrespondencelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        typecorrespondencelang.setId(longCount.incrementAndGet());

        // Create the Typecorrespondencelang
        TypecorrespondencelangDTO typecorrespondencelangDTO = typecorrespondencelangMapper.toDto(typecorrespondencelang);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTypecorrespondencelangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, typecorrespondencelangDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(typecorrespondencelangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Typecorrespondencelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchTypecorrespondencelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        typecorrespondencelang.setId(longCount.incrementAndGet());

        // Create the Typecorrespondencelang
        TypecorrespondencelangDTO typecorrespondencelangDTO = typecorrespondencelangMapper.toDto(typecorrespondencelang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTypecorrespondencelangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(typecorrespondencelangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Typecorrespondencelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamTypecorrespondencelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        typecorrespondencelang.setId(longCount.incrementAndGet());

        // Create the Typecorrespondencelang
        TypecorrespondencelangDTO typecorrespondencelangDTO = typecorrespondencelangMapper.toDto(typecorrespondencelang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTypecorrespondencelangMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(typecorrespondencelangDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Typecorrespondencelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateTypecorrespondencelangWithPatch() throws Exception {
        // Initialize the database
        insertedTypecorrespondencelang = typecorrespondencelangRepository.saveAndFlush(typecorrespondencelang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the typecorrespondencelang using partial update
        Typecorrespondencelang partialUpdatedTypecorrespondencelang = new Typecorrespondencelang();
        partialUpdatedTypecorrespondencelang.setId(typecorrespondencelang.getId());

        partialUpdatedTypecorrespondencelang.lang(UPDATED_LANG);

        restTypecorrespondencelangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTypecorrespondencelang.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTypecorrespondencelang))
            )
            .andExpect(status().isOk());

        // Validate the Typecorrespondencelang in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTypecorrespondencelangUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedTypecorrespondencelang, typecorrespondencelang),
            getPersistedTypecorrespondencelang(typecorrespondencelang)
        );
    }

    @Test
    @Transactional
    void fullUpdateTypecorrespondencelangWithPatch() throws Exception {
        // Initialize the database
        insertedTypecorrespondencelang = typecorrespondencelangRepository.saveAndFlush(typecorrespondencelang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the typecorrespondencelang using partial update
        Typecorrespondencelang partialUpdatedTypecorrespondencelang = new Typecorrespondencelang();
        partialUpdatedTypecorrespondencelang.setId(typecorrespondencelang.getId());

        partialUpdatedTypecorrespondencelang.lbl(UPDATED_LBL).lang(UPDATED_LANG).abbreviated(UPDATED_ABBREVIATED);

        restTypecorrespondencelangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTypecorrespondencelang.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTypecorrespondencelang))
            )
            .andExpect(status().isOk());

        // Validate the Typecorrespondencelang in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTypecorrespondencelangUpdatableFieldsEquals(
            partialUpdatedTypecorrespondencelang,
            getPersistedTypecorrespondencelang(partialUpdatedTypecorrespondencelang)
        );
    }

    @Test
    @Transactional
    void patchNonExistingTypecorrespondencelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        typecorrespondencelang.setId(longCount.incrementAndGet());

        // Create the Typecorrespondencelang
        TypecorrespondencelangDTO typecorrespondencelangDTO = typecorrespondencelangMapper.toDto(typecorrespondencelang);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTypecorrespondencelangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, typecorrespondencelangDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(typecorrespondencelangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Typecorrespondencelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchTypecorrespondencelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        typecorrespondencelang.setId(longCount.incrementAndGet());

        // Create the Typecorrespondencelang
        TypecorrespondencelangDTO typecorrespondencelangDTO = typecorrespondencelangMapper.toDto(typecorrespondencelang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTypecorrespondencelangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(typecorrespondencelangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Typecorrespondencelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamTypecorrespondencelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        typecorrespondencelang.setId(longCount.incrementAndGet());

        // Create the Typecorrespondencelang
        TypecorrespondencelangDTO typecorrespondencelangDTO = typecorrespondencelangMapper.toDto(typecorrespondencelang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTypecorrespondencelangMockMvc
            .perform(
                patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(typecorrespondencelangDTO))
            )
            .andExpect(status().isMethodNotAllowed());

        // Validate the Typecorrespondencelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteTypecorrespondencelang() throws Exception {
        // Initialize the database
        insertedTypecorrespondencelang = typecorrespondencelangRepository.saveAndFlush(typecorrespondencelang);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the typecorrespondencelang
        restTypecorrespondencelangMockMvc
            .perform(delete(ENTITY_API_URL_ID, typecorrespondencelang.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return typecorrespondencelangRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Typecorrespondencelang getPersistedTypecorrespondencelang(Typecorrespondencelang typecorrespondencelang) {
        return typecorrespondencelangRepository.findById(typecorrespondencelang.getId()).orElseThrow();
    }

    protected void assertPersistedTypecorrespondencelangToMatchAllProperties(Typecorrespondencelang expectedTypecorrespondencelang) {
        assertTypecorrespondencelangAllPropertiesEquals(
            expectedTypecorrespondencelang,
            getPersistedTypecorrespondencelang(expectedTypecorrespondencelang)
        );
    }

    protected void assertPersistedTypecorrespondencelangToMatchUpdatableProperties(Typecorrespondencelang expectedTypecorrespondencelang) {
        assertTypecorrespondencelangAllUpdatablePropertiesEquals(
            expectedTypecorrespondencelang,
            getPersistedTypecorrespondencelang(expectedTypecorrespondencelang)
        );
    }
}
