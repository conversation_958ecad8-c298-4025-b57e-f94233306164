package com.dq.lilas.rest;

import static com.dq.lilas.domain.DeliverymodelangAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Deliverymodelang;
import com.dq.lilas.repository.DeliverymodelangRepository;
import com.dq.lilas.service.dto.DeliverymodelangDTO;
import com.dq.lilas.service.mapper.DeliverymodelangMapper;
import com.dq.lilas.web.rest.DeliverymodelangResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link DeliverymodelangResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class DeliverymodelangResourceIT {

    private static final String DEFAULT_LBL = "AAAAAAAAAA";
    private static final String UPDATED_LBL = "BBBBBBBBBB";

    private static final String DEFAULT_LANG = "AAAAA";
    private static final String UPDATED_LANG = "BBBBB";

    private static final String DEFAULT_ABRV = "AAAAAAAAAA";
    private static final String UPDATED_ABRV = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/deliverymodelangs";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private DeliverymodelangRepository deliverymodelangRepository;

    @Autowired
    private DeliverymodelangMapper deliverymodelangMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restDeliverymodelangMockMvc;

    private Deliverymodelang deliverymodelang;

    private Deliverymodelang insertedDeliverymodelang;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Deliverymodelang createEntity() {
        return new Deliverymodelang().lbl(DEFAULT_LBL).lang(DEFAULT_LANG).abrv(DEFAULT_ABRV);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Deliverymodelang createUpdatedEntity() {
        return new Deliverymodelang().lbl(UPDATED_LBL).lang(UPDATED_LANG).abrv(UPDATED_ABRV);
    }

    @BeforeEach
    void initTest() {
        deliverymodelang = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedDeliverymodelang != null) {
            deliverymodelangRepository.delete(insertedDeliverymodelang);
            insertedDeliverymodelang = null;
        }
    }

    @Test
    @Transactional
    void createDeliverymodelang() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Deliverymodelang
        DeliverymodelangDTO deliverymodelangDTO = deliverymodelangMapper.toDto(deliverymodelang);
        var returnedDeliverymodelangDTO = om.readValue(
            restDeliverymodelangMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(deliverymodelangDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            DeliverymodelangDTO.class
        );

        // Validate the Deliverymodelang in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedDeliverymodelang = deliverymodelangMapper.toEntity(returnedDeliverymodelangDTO);
        assertDeliverymodelangUpdatableFieldsEquals(returnedDeliverymodelang, getPersistedDeliverymodelang(returnedDeliverymodelang));

        insertedDeliverymodelang = returnedDeliverymodelang;
    }

    @Test
    @Transactional
    void createDeliverymodelangWithExistingId() throws Exception {
        // Create the Deliverymodelang with an existing ID
        deliverymodelang.setId(1L);
        DeliverymodelangDTO deliverymodelangDTO = deliverymodelangMapper.toDto(deliverymodelang);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restDeliverymodelangMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(deliverymodelangDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Deliverymodelang in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllDeliverymodelangs() throws Exception {
        // Initialize the database
        insertedDeliverymodelang = deliverymodelangRepository.saveAndFlush(deliverymodelang);

        // Get all the deliverymodelangList
        restDeliverymodelangMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(deliverymodelang.getId().intValue())))
            .andExpect(jsonPath("$.[*].lbl").value(hasItem(DEFAULT_LBL)))
            .andExpect(jsonPath("$.[*].lang").value(hasItem(DEFAULT_LANG)))
            .andExpect(jsonPath("$.[*].abrv").value(hasItem(DEFAULT_ABRV)));
    }

    @Test
    @Transactional
    void getDeliverymodelang() throws Exception {
        // Initialize the database
        insertedDeliverymodelang = deliverymodelangRepository.saveAndFlush(deliverymodelang);

        // Get the deliverymodelang
        restDeliverymodelangMockMvc
            .perform(get(ENTITY_API_URL_ID, deliverymodelang.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(deliverymodelang.getId().intValue()))
            .andExpect(jsonPath("$.lbl").value(DEFAULT_LBL))
            .andExpect(jsonPath("$.lang").value(DEFAULT_LANG))
            .andExpect(jsonPath("$.abrv").value(DEFAULT_ABRV));
    }

    @Test
    @Transactional
    void getNonExistingDeliverymodelang() throws Exception {
        // Get the deliverymodelang
        restDeliverymodelangMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingDeliverymodelang() throws Exception {
        // Initialize the database
        insertedDeliverymodelang = deliverymodelangRepository.saveAndFlush(deliverymodelang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the deliverymodelang
        Deliverymodelang updatedDeliverymodelang = deliverymodelangRepository.findById(deliverymodelang.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedDeliverymodelang are not directly saved in db
        em.detach(updatedDeliverymodelang);
        updatedDeliverymodelang.lbl(UPDATED_LBL).lang(UPDATED_LANG).abrv(UPDATED_ABRV);
        DeliverymodelangDTO deliverymodelangDTO = deliverymodelangMapper.toDto(updatedDeliverymodelang);

        restDeliverymodelangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, deliverymodelangDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(deliverymodelangDTO))
            )
            .andExpect(status().isOk());

        // Validate the Deliverymodelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedDeliverymodelangToMatchAllProperties(updatedDeliverymodelang);
    }

    @Test
    @Transactional
    void putNonExistingDeliverymodelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        deliverymodelang.setId(longCount.incrementAndGet());

        // Create the Deliverymodelang
        DeliverymodelangDTO deliverymodelangDTO = deliverymodelangMapper.toDto(deliverymodelang);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restDeliverymodelangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, deliverymodelangDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(deliverymodelangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Deliverymodelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchDeliverymodelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        deliverymodelang.setId(longCount.incrementAndGet());

        // Create the Deliverymodelang
        DeliverymodelangDTO deliverymodelangDTO = deliverymodelangMapper.toDto(deliverymodelang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDeliverymodelangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(deliverymodelangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Deliverymodelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamDeliverymodelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        deliverymodelang.setId(longCount.incrementAndGet());

        // Create the Deliverymodelang
        DeliverymodelangDTO deliverymodelangDTO = deliverymodelangMapper.toDto(deliverymodelang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDeliverymodelangMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(deliverymodelangDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Deliverymodelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateDeliverymodelangWithPatch() throws Exception {
        // Initialize the database
        insertedDeliverymodelang = deliverymodelangRepository.saveAndFlush(deliverymodelang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the deliverymodelang using partial update
        Deliverymodelang partialUpdatedDeliverymodelang = new Deliverymodelang();
        partialUpdatedDeliverymodelang.setId(deliverymodelang.getId());

        partialUpdatedDeliverymodelang.lang(UPDATED_LANG);

        restDeliverymodelangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedDeliverymodelang.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedDeliverymodelang))
            )
            .andExpect(status().isOk());

        // Validate the Deliverymodelang in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertDeliverymodelangUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedDeliverymodelang, deliverymodelang),
            getPersistedDeliverymodelang(deliverymodelang)
        );
    }

    @Test
    @Transactional
    void fullUpdateDeliverymodelangWithPatch() throws Exception {
        // Initialize the database
        insertedDeliverymodelang = deliverymodelangRepository.saveAndFlush(deliverymodelang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the deliverymodelang using partial update
        Deliverymodelang partialUpdatedDeliverymodelang = new Deliverymodelang();
        partialUpdatedDeliverymodelang.setId(deliverymodelang.getId());

        partialUpdatedDeliverymodelang.lbl(UPDATED_LBL).lang(UPDATED_LANG).abrv(UPDATED_ABRV);

        restDeliverymodelangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedDeliverymodelang.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedDeliverymodelang))
            )
            .andExpect(status().isOk());

        // Validate the Deliverymodelang in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertDeliverymodelangUpdatableFieldsEquals(
            partialUpdatedDeliverymodelang,
            getPersistedDeliverymodelang(partialUpdatedDeliverymodelang)
        );
    }

    @Test
    @Transactional
    void patchNonExistingDeliverymodelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        deliverymodelang.setId(longCount.incrementAndGet());

        // Create the Deliverymodelang
        DeliverymodelangDTO deliverymodelangDTO = deliverymodelangMapper.toDto(deliverymodelang);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restDeliverymodelangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, deliverymodelangDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(deliverymodelangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Deliverymodelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchDeliverymodelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        deliverymodelang.setId(longCount.incrementAndGet());

        // Create the Deliverymodelang
        DeliverymodelangDTO deliverymodelangDTO = deliverymodelangMapper.toDto(deliverymodelang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDeliverymodelangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(deliverymodelangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Deliverymodelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamDeliverymodelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        deliverymodelang.setId(longCount.incrementAndGet());

        // Create the Deliverymodelang
        DeliverymodelangDTO deliverymodelangDTO = deliverymodelangMapper.toDto(deliverymodelang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDeliverymodelangMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(deliverymodelangDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Deliverymodelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteDeliverymodelang() throws Exception {
        // Initialize the database
        insertedDeliverymodelang = deliverymodelangRepository.saveAndFlush(deliverymodelang);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the deliverymodelang
        restDeliverymodelangMockMvc
            .perform(delete(ENTITY_API_URL_ID, deliverymodelang.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return deliverymodelangRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Deliverymodelang getPersistedDeliverymodelang(Deliverymodelang deliverymodelang) {
        return deliverymodelangRepository.findById(deliverymodelang.getId()).orElseThrow();
    }

    protected void assertPersistedDeliverymodelangToMatchAllProperties(Deliverymodelang expectedDeliverymodelang) {
        assertDeliverymodelangAllPropertiesEquals(expectedDeliverymodelang, getPersistedDeliverymodelang(expectedDeliverymodelang));
    }

    protected void assertPersistedDeliverymodelangToMatchUpdatableProperties(Deliverymodelang expectedDeliverymodelang) {
        assertDeliverymodelangAllUpdatablePropertiesEquals(
            expectedDeliverymodelang,
            getPersistedDeliverymodelang(expectedDeliverymodelang)
        );
    }
}
