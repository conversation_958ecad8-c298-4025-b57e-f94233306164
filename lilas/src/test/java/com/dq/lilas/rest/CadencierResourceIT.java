package com.dq.lilas.rest;

import static com.dq.lilas.domain.CadencierAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Cadencier;
import com.dq.lilas.repository.CadencierRepository;
import com.dq.lilas.service.dto.CadencierDTO;
import com.dq.lilas.service.mapper.CadencierMapper;
import com.dq.lilas.web.rest.CadencierResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link CadencierResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class CadencierResourceIT {

    private static final Integer DEFAULT_ORDER_DAY = 1;
    private static final Integer UPDATED_ORDER_DAY = 2;

    private static final Integer DEFAULT_DELIVERY_DAY = 1;
    private static final Integer UPDATED_DELIVERY_DAY = 2;

    private static final String DEFAULT_REGION = "AAAAAAAAAA";
    private static final String UPDATED_REGION = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/cadenciers";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private CadencierRepository cadencierRepository;

    @Autowired
    private CadencierMapper cadencierMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restCadencierMockMvc;

    private Cadencier cadencier;

    private Cadencier insertedCadencier;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Cadencier createEntity() {
        return new Cadencier().orderDay(DEFAULT_ORDER_DAY).deliveryDay(DEFAULT_DELIVERY_DAY).region(DEFAULT_REGION);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Cadencier createUpdatedEntity() {
        return new Cadencier().orderDay(UPDATED_ORDER_DAY).deliveryDay(UPDATED_DELIVERY_DAY).region(UPDATED_REGION);
    }

    @BeforeEach
    void initTest() {
        cadencier = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedCadencier != null) {
            cadencierRepository.delete(insertedCadencier);
            insertedCadencier = null;
        }
    }

    @Test
    @Transactional
    void createCadencier() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Cadencier
        CadencierDTO cadencierDTO = cadencierMapper.toDto(cadencier);
        var returnedCadencierDTO = om.readValue(
            restCadencierMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(cadencierDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            CadencierDTO.class
        );

        // Validate the Cadencier in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedCadencier = cadencierMapper.toEntity(returnedCadencierDTO);
        assertCadencierUpdatableFieldsEquals(returnedCadencier, getPersistedCadencier(returnedCadencier));

        insertedCadencier = returnedCadencier;
    }

    @Test
    @Transactional
    void createCadencierWithExistingId() throws Exception {
        // Create the Cadencier with an existing ID
        cadencier.setId(1L);
        CadencierDTO cadencierDTO = cadencierMapper.toDto(cadencier);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restCadencierMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(cadencierDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Cadencier in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllCadenciers() throws Exception {
        // Initialize the database
        insertedCadencier = cadencierRepository.saveAndFlush(cadencier);

        // Get all the cadencierList
        restCadencierMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(cadencier.getId().intValue())))
            .andExpect(jsonPath("$.[*].orderDay").value(hasItem(DEFAULT_ORDER_DAY)))
            .andExpect(jsonPath("$.[*].deliveryDay").value(hasItem(DEFAULT_DELIVERY_DAY)))
            .andExpect(jsonPath("$.[*].region").value(hasItem(DEFAULT_REGION)));
    }

    @Test
    @Transactional
    void getCadencier() throws Exception {
        // Initialize the database
        insertedCadencier = cadencierRepository.saveAndFlush(cadencier);

        // Get the cadencier
        restCadencierMockMvc
            .perform(get(ENTITY_API_URL_ID, cadencier.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(cadencier.getId().intValue()))
            .andExpect(jsonPath("$.orderDay").value(DEFAULT_ORDER_DAY))
            .andExpect(jsonPath("$.deliveryDay").value(DEFAULT_DELIVERY_DAY))
            .andExpect(jsonPath("$.region").value(DEFAULT_REGION));
    }

    @Test
    @Transactional
    void getNonExistingCadencier() throws Exception {
        // Get the cadencier
        restCadencierMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingCadencier() throws Exception {
        // Initialize the database
        insertedCadencier = cadencierRepository.saveAndFlush(cadencier);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the cadencier
        Cadencier updatedCadencier = cadencierRepository.findById(cadencier.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedCadencier are not directly saved in db
        em.detach(updatedCadencier);
        updatedCadencier.orderDay(UPDATED_ORDER_DAY).deliveryDay(UPDATED_DELIVERY_DAY).region(UPDATED_REGION);
        CadencierDTO cadencierDTO = cadencierMapper.toDto(updatedCadencier);

        restCadencierMockMvc
            .perform(
                put(ENTITY_API_URL_ID, cadencierDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(cadencierDTO))
            )
            .andExpect(status().isOk());

        // Validate the Cadencier in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedCadencierToMatchAllProperties(updatedCadencier);
    }

    @Test
    @Transactional
    void putNonExistingCadencier() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        cadencier.setId(longCount.incrementAndGet());

        // Create the Cadencier
        CadencierDTO cadencierDTO = cadencierMapper.toDto(cadencier);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restCadencierMockMvc
            .perform(
                put(ENTITY_API_URL_ID, cadencierDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(cadencierDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Cadencier in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchCadencier() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        cadencier.setId(longCount.incrementAndGet());

        // Create the Cadencier
        CadencierDTO cadencierDTO = cadencierMapper.toDto(cadencier);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restCadencierMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(cadencierDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Cadencier in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamCadencier() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        cadencier.setId(longCount.incrementAndGet());

        // Create the Cadencier
        CadencierDTO cadencierDTO = cadencierMapper.toDto(cadencier);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restCadencierMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(cadencierDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Cadencier in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateCadencierWithPatch() throws Exception {
        // Initialize the database
        insertedCadencier = cadencierRepository.saveAndFlush(cadencier);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the cadencier using partial update
        Cadencier partialUpdatedCadencier = new Cadencier();
        partialUpdatedCadencier.setId(cadencier.getId());

        partialUpdatedCadencier.orderDay(UPDATED_ORDER_DAY).region(UPDATED_REGION);

        restCadencierMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedCadencier.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedCadencier))
            )
            .andExpect(status().isOk());

        // Validate the Cadencier in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertCadencierUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedCadencier, cadencier),
            getPersistedCadencier(cadencier)
        );
    }

    @Test
    @Transactional
    void fullUpdateCadencierWithPatch() throws Exception {
        // Initialize the database
        insertedCadencier = cadencierRepository.saveAndFlush(cadencier);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the cadencier using partial update
        Cadencier partialUpdatedCadencier = new Cadencier();
        partialUpdatedCadencier.setId(cadencier.getId());

        partialUpdatedCadencier.orderDay(UPDATED_ORDER_DAY).deliveryDay(UPDATED_DELIVERY_DAY).region(UPDATED_REGION);

        restCadencierMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedCadencier.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedCadencier))
            )
            .andExpect(status().isOk());

        // Validate the Cadencier in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertCadencierUpdatableFieldsEquals(partialUpdatedCadencier, getPersistedCadencier(partialUpdatedCadencier));
    }

    @Test
    @Transactional
    void patchNonExistingCadencier() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        cadencier.setId(longCount.incrementAndGet());

        // Create the Cadencier
        CadencierDTO cadencierDTO = cadencierMapper.toDto(cadencier);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restCadencierMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, cadencierDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(cadencierDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Cadencier in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchCadencier() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        cadencier.setId(longCount.incrementAndGet());

        // Create the Cadencier
        CadencierDTO cadencierDTO = cadencierMapper.toDto(cadencier);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restCadencierMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(cadencierDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Cadencier in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamCadencier() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        cadencier.setId(longCount.incrementAndGet());

        // Create the Cadencier
        CadencierDTO cadencierDTO = cadencierMapper.toDto(cadencier);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restCadencierMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(cadencierDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Cadencier in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteCadencier() throws Exception {
        // Initialize the database
        insertedCadencier = cadencierRepository.saveAndFlush(cadencier);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the cadencier
        restCadencierMockMvc
            .perform(delete(ENTITY_API_URL_ID, cadencier.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return cadencierRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Cadencier getPersistedCadencier(Cadencier cadencier) {
        return cadencierRepository.findById(cadencier.getId()).orElseThrow();
    }

    protected void assertPersistedCadencierToMatchAllProperties(Cadencier expectedCadencier) {
        assertCadencierAllPropertiesEquals(expectedCadencier, getPersistedCadencier(expectedCadencier));
    }

    protected void assertPersistedCadencierToMatchUpdatableProperties(Cadencier expectedCadencier) {
        assertCadencierAllUpdatablePropertiesEquals(expectedCadencier, getPersistedCadencier(expectedCadencier));
    }
}
