package com.dq.lilas.rest;

import static com.dq.lilas.domain.EmployeelangAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Employeelang;
import com.dq.lilas.repository.EmployeelangRepository;
import com.dq.lilas.service.dto.EmployeelangDTO;
import com.dq.lilas.service.mapper.EmployeelangMapper;
import com.dq.lilas.web.rest.EmployeelangResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link EmployeelangResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class EmployeelangResourceIT {

    private static final String DEFAULT_FULLNAME = "AAAAAAAAAA";
    private static final String UPDATED_FULLNAME = "BBBBBBBBBB";

    private static final String DEFAULT_ADDRESS = "AAAAAAAAAA";
    private static final String UPDATED_ADDRESS = "BBBBBBBBBB";

    private static final String DEFAULT_LANG = "AAAAA";
    private static final String UPDATED_LANG = "BBBBB";

    private static final String DEFAULT_MATRICULE = "AAAAAAAAAA";
    private static final String UPDATED_MATRICULE = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/employeelangs";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private EmployeelangRepository employeelangRepository;

    @Autowired
    private EmployeelangMapper employeelangMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restEmployeelangMockMvc;

    private Employeelang employeelang;

    private Employeelang insertedEmployeelang;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Employeelang createEntity() {
        return new Employeelang().fullname(DEFAULT_FULLNAME).address(DEFAULT_ADDRESS).lang(DEFAULT_LANG).matricule(DEFAULT_MATRICULE);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Employeelang createUpdatedEntity() {
        return new Employeelang().fullname(UPDATED_FULLNAME).address(UPDATED_ADDRESS).lang(UPDATED_LANG).matricule(UPDATED_MATRICULE);
    }

    @BeforeEach
    void initTest() {
        employeelang = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedEmployeelang != null) {
            employeelangRepository.delete(insertedEmployeelang);
            insertedEmployeelang = null;
        }
    }

    @Test
    @Transactional
    void createEmployeelang() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Employeelang
        EmployeelangDTO employeelangDTO = employeelangMapper.toDto(employeelang);
        var returnedEmployeelangDTO = om.readValue(
            restEmployeelangMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeelangDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            EmployeelangDTO.class
        );

        // Validate the Employeelang in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedEmployeelang = employeelangMapper.toEntity(returnedEmployeelangDTO);
        assertEmployeelangUpdatableFieldsEquals(returnedEmployeelang, getPersistedEmployeelang(returnedEmployeelang));

        insertedEmployeelang = returnedEmployeelang;
    }

    @Test
    @Transactional
    void createEmployeelangWithExistingId() throws Exception {
        // Create the Employeelang with an existing ID
        employeelang.setId(1L);
        EmployeelangDTO employeelangDTO = employeelangMapper.toDto(employeelang);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restEmployeelangMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeelangDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Employeelang in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllEmployeelangs() throws Exception {
        // Initialize the database
        insertedEmployeelang = employeelangRepository.saveAndFlush(employeelang);

        // Get all the employeelangList
        restEmployeelangMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(employeelang.getId().intValue())))
            .andExpect(jsonPath("$.[*].fullname").value(hasItem(DEFAULT_FULLNAME)))
            .andExpect(jsonPath("$.[*].address").value(hasItem(DEFAULT_ADDRESS)))
            .andExpect(jsonPath("$.[*].lang").value(hasItem(DEFAULT_LANG)))
            .andExpect(jsonPath("$.[*].matricule").value(hasItem(DEFAULT_MATRICULE)));
    }

    @Test
    @Transactional
    void getEmployeelang() throws Exception {
        // Initialize the database
        insertedEmployeelang = employeelangRepository.saveAndFlush(employeelang);

        // Get the employeelang
        restEmployeelangMockMvc
            .perform(get(ENTITY_API_URL_ID, employeelang.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(employeelang.getId().intValue()))
            .andExpect(jsonPath("$.fullname").value(DEFAULT_FULLNAME))
            .andExpect(jsonPath("$.address").value(DEFAULT_ADDRESS))
            .andExpect(jsonPath("$.lang").value(DEFAULT_LANG))
            .andExpect(jsonPath("$.matricule").value(DEFAULT_MATRICULE));
    }

    @Test
    @Transactional
    void getNonExistingEmployeelang() throws Exception {
        // Get the employeelang
        restEmployeelangMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingEmployeelang() throws Exception {
        // Initialize the database
        insertedEmployeelang = employeelangRepository.saveAndFlush(employeelang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeelang
        Employeelang updatedEmployeelang = employeelangRepository.findById(employeelang.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedEmployeelang are not directly saved in db
        em.detach(updatedEmployeelang);
        updatedEmployeelang.fullname(UPDATED_FULLNAME).address(UPDATED_ADDRESS).lang(UPDATED_LANG).matricule(UPDATED_MATRICULE);
        EmployeelangDTO employeelangDTO = employeelangMapper.toDto(updatedEmployeelang);

        restEmployeelangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, employeelangDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeelangDTO))
            )
            .andExpect(status().isOk());

        // Validate the Employeelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedEmployeelangToMatchAllProperties(updatedEmployeelang);
    }

    @Test
    @Transactional
    void putNonExistingEmployeelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeelang.setId(longCount.incrementAndGet());

        // Create the Employeelang
        EmployeelangDTO employeelangDTO = employeelangMapper.toDto(employeelang);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restEmployeelangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, employeelangDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeelangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Employeelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchEmployeelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeelang.setId(longCount.incrementAndGet());

        // Create the Employeelang
        EmployeelangDTO employeelangDTO = employeelangMapper.toDto(employeelang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeelangMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(employeelangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Employeelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamEmployeelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeelang.setId(longCount.incrementAndGet());

        // Create the Employeelang
        EmployeelangDTO employeelangDTO = employeelangMapper.toDto(employeelang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeelangMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(employeelangDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Employeelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateEmployeelangWithPatch() throws Exception {
        // Initialize the database
        insertedEmployeelang = employeelangRepository.saveAndFlush(employeelang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeelang using partial update
        Employeelang partialUpdatedEmployeelang = new Employeelang();
        partialUpdatedEmployeelang.setId(employeelang.getId());

        partialUpdatedEmployeelang.address(UPDATED_ADDRESS);

        restEmployeelangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedEmployeelang.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedEmployeelang))
            )
            .andExpect(status().isOk());

        // Validate the Employeelang in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertEmployeelangUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedEmployeelang, employeelang),
            getPersistedEmployeelang(employeelang)
        );
    }

    @Test
    @Transactional
    void fullUpdateEmployeelangWithPatch() throws Exception {
        // Initialize the database
        insertedEmployeelang = employeelangRepository.saveAndFlush(employeelang);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the employeelang using partial update
        Employeelang partialUpdatedEmployeelang = new Employeelang();
        partialUpdatedEmployeelang.setId(employeelang.getId());

        partialUpdatedEmployeelang.fullname(UPDATED_FULLNAME).address(UPDATED_ADDRESS).lang(UPDATED_LANG).matricule(UPDATED_MATRICULE);

        restEmployeelangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedEmployeelang.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedEmployeelang))
            )
            .andExpect(status().isOk());

        // Validate the Employeelang in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertEmployeelangUpdatableFieldsEquals(partialUpdatedEmployeelang, getPersistedEmployeelang(partialUpdatedEmployeelang));
    }

    @Test
    @Transactional
    void patchNonExistingEmployeelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeelang.setId(longCount.incrementAndGet());

        // Create the Employeelang
        EmployeelangDTO employeelangDTO = employeelangMapper.toDto(employeelang);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restEmployeelangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, employeelangDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(employeelangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Employeelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchEmployeelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeelang.setId(longCount.incrementAndGet());

        // Create the Employeelang
        EmployeelangDTO employeelangDTO = employeelangMapper.toDto(employeelang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeelangMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(employeelangDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Employeelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamEmployeelang() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        employeelang.setId(longCount.incrementAndGet());

        // Create the Employeelang
        EmployeelangDTO employeelangDTO = employeelangMapper.toDto(employeelang);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restEmployeelangMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(employeelangDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Employeelang in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteEmployeelang() throws Exception {
        // Initialize the database
        insertedEmployeelang = employeelangRepository.saveAndFlush(employeelang);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the employeelang
        restEmployeelangMockMvc
            .perform(delete(ENTITY_API_URL_ID, employeelang.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return employeelangRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Employeelang getPersistedEmployeelang(Employeelang employeelang) {
        return employeelangRepository.findById(employeelang.getId()).orElseThrow();
    }

    protected void assertPersistedEmployeelangToMatchAllProperties(Employeelang expectedEmployeelang) {
        assertEmployeelangAllPropertiesEquals(expectedEmployeelang, getPersistedEmployeelang(expectedEmployeelang));
    }

    protected void assertPersistedEmployeelangToMatchUpdatableProperties(Employeelang expectedEmployeelang) {
        assertEmployeelangAllUpdatablePropertiesEquals(expectedEmployeelang, getPersistedEmployeelang(expectedEmployeelang));
    }
}
