package com.dq.lilas.rest;

import static com.dq.lilas.domain.OrderDetailsAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static com.dq.lilas.rest.TestUtil.sameNumber;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.OrderDetails;
import com.dq.lilas.repository.OrderDetailsRepository;
import com.dq.lilas.service.dto.OrderDetailsDTO;
import com.dq.lilas.service.mapper.OrderDetailsMapper;
import com.dq.lilas.web.rest.OrderDetailsResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.math.BigDecimal;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link OrderDetailsResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class OrderDetailsResourceIT {

    private static final Long DEFAULT_QUANTITY = 1L;
    private static final Long UPDATED_QUANTITY = 2L;

    private static final Long DEFAULT_UPDATED_QTY = 1L;
    private static final Long UPDATED_UPDATED_QTY = 2L;

    private static final Integer DEFAULT_DISCOUNT = 1;
    private static final Integer UPDATED_DISCOUNT = 2;

    private static final Integer DEFAULT_UPDATED_DISCOUNT = 1;
    private static final Integer UPDATED_UPDATED_DISCOUNT = 2;

    private static final Double DEFAULT_UNIT_PRICE = 1D;
    private static final Double UPDATED_UNIT_PRICE = 2D;

    private static final Double DEFAULT_UPDATED_UNIT_PRICE = 1D;
    private static final Double UPDATED_UPDATED_UNIT_PRICE = 2D;

    private static final String DEFAULT_ORDER_LINE_JSON = "AAAAAAAAAA";
    private static final String UPDATED_ORDER_LINE_JSON = "BBBBBBBBBB";

    private static final Boolean DEFAULT_DISCOUNT_STATUS = false;
    private static final Boolean UPDATED_DISCOUNT_STATUS = true;

    private static final Boolean DEFAULT_PRICE_STATUS = false;
    private static final Boolean UPDATED_PRICE_STATUS = true;

    private static final Boolean DEFAULT_QUANTITY_STATUS = false;
    private static final Boolean UPDATED_QUANTITY_STATUS = true;

    private static final Boolean DEFAULT_PRODUCT_STATUS = false;
    private static final Boolean UPDATED_PRODUCT_STATUS = true;

    private static final Boolean DEFAULT_AVAILABILITY = false;
    private static final Boolean UPDATED_AVAILABILITY = true;

    private static final Boolean DEFAULT_VALID_CONDITIONS = false;
    private static final Boolean UPDATED_VALID_CONDITIONS = true;

    private static final Boolean DEFAULT_INJECTED = false;
    private static final Boolean UPDATED_INJECTED = true;

    private static final String DEFAULT_BARCODE = "AAAAAAAAAA";
    private static final String UPDATED_BARCODE = "BBBBBBBBBB";

    private static final String DEFAULT_UPDATED_BARCODE = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_BARCODE = "BBBBBBBBBB";

    private static final String DEFAULT_INTERNAL_CODE = "AAAAAAAAAA";
    private static final String UPDATED_INTERNAL_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_UPDATED_INTERNAL_CODE = "AAAAAAAAAA";
    private static final String UPDATED_UPDATED_INTERNAL_CODE = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/order-details";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private OrderDetailsRepository orderDetailsRepository;

    @Autowired
    private OrderDetailsMapper orderDetailsMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restOrderDetailsMockMvc;

    private OrderDetails orderDetails;

    private OrderDetails insertedOrderDetails;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static OrderDetails createEntity() {
        return new OrderDetails()
            .quantity(DEFAULT_QUANTITY)
            .updatedQty(DEFAULT_UPDATED_QTY)
            .discount(DEFAULT_DISCOUNT)
            .updatedDiscount(DEFAULT_UPDATED_DISCOUNT)
            .unitPrice(DEFAULT_UNIT_PRICE)
            .updatedUnitPrice(DEFAULT_UPDATED_UNIT_PRICE)
            .orderLineJson(DEFAULT_ORDER_LINE_JSON)
            .availability(DEFAULT_AVAILABILITY)
            // Note: validConditions and injected fields have been removed from the entity
            .barcode(DEFAULT_BARCODE)
            .updatedBarcode(DEFAULT_UPDATED_BARCODE)
            .internalCode(DEFAULT_INTERNAL_CODE)
            .updatedInternalCode(DEFAULT_UPDATED_INTERNAL_CODE)
            .discountStatus(DEFAULT_DISCOUNT_STATUS)
            .priceStatus(DEFAULT_PRICE_STATUS)
            .quantityStatus(DEFAULT_QUANTITY_STATUS)
            .productStatus(DEFAULT_PRODUCT_STATUS);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static OrderDetails createUpdatedEntity() {
        return new OrderDetails()
            .quantity(UPDATED_QUANTITY)
            .updatedQty(UPDATED_UPDATED_QTY)
            .discount(UPDATED_DISCOUNT)
            .updatedDiscount(UPDATED_UPDATED_DISCOUNT)
            .unitPrice(UPDATED_UNIT_PRICE)
            .updatedUnitPrice(UPDATED_UPDATED_UNIT_PRICE)
            .orderLineJson(UPDATED_ORDER_LINE_JSON)
            .availability(UPDATED_AVAILABILITY)
            // Note: validConditions and injected fields have been removed from the entity
            .barcode(UPDATED_BARCODE)
            .updatedBarcode(UPDATED_UPDATED_BARCODE)
            .internalCode(UPDATED_INTERNAL_CODE)
            .updatedInternalCode(UPDATED_UPDATED_INTERNAL_CODE)
            .discountStatus(UPDATED_DISCOUNT_STATUS)
            .priceStatus(UPDATED_PRICE_STATUS)
            .quantityStatus(UPDATED_QUANTITY_STATUS)
            .productStatus(UPDATED_PRODUCT_STATUS);
    }

    @BeforeEach
    void initTest() {
        orderDetails = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedOrderDetails != null) {
            orderDetailsRepository.delete(insertedOrderDetails);
            insertedOrderDetails = null;
        }
    }

    @Test
    @Transactional
    void createOrderDetails() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the OrderDetails
        OrderDetailsDTO orderDetailsDTO = orderDetailsMapper.toDto(orderDetails);
        var returnedOrderDetailsDTO = om.readValue(
            restOrderDetailsMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orderDetailsDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            OrderDetailsDTO.class
        );

        // Validate the OrderDetails in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedOrderDetails = orderDetailsMapper.toEntity(returnedOrderDetailsDTO);
        assertOrderDetailsUpdatableFieldsEquals(returnedOrderDetails, getPersistedOrderDetails(returnedOrderDetails));

        insertedOrderDetails = returnedOrderDetails;
    }

    @Test
    @Transactional
    void createOrderDetailsWithExistingId() throws Exception {
        // Create the OrderDetails with an existing ID
        orderDetails.setId(1L);
        OrderDetailsDTO orderDetailsDTO = orderDetailsMapper.toDto(orderDetails);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restOrderDetailsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orderDetailsDTO)))
            .andExpect(status().isBadRequest());

        // Validate the OrderDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllOrderDetails() throws Exception {
        // Initialize the database
        insertedOrderDetails = orderDetailsRepository.saveAndFlush(orderDetails);

        // Get all the orderDetailsList
        restOrderDetailsMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(orderDetails.getId().intValue())))
            .andExpect(jsonPath("$.[*].quantity").value(hasItem(sameNumber(BigDecimal.valueOf(DEFAULT_QUANTITY)))))
            .andExpect(jsonPath("$.[*].updatedQty").value(hasItem(sameNumber(BigDecimal.valueOf(DEFAULT_UPDATED_QTY)))))
            .andExpect(jsonPath("$.[*].discount").value(hasItem(DEFAULT_DISCOUNT)))
            .andExpect(jsonPath("$.[*].updatedDiscount").value(hasItem(DEFAULT_UPDATED_DISCOUNT)))
            .andExpect(jsonPath("$.[*].unitPrice").value(hasItem(DEFAULT_UNIT_PRICE)))
            .andExpect(jsonPath("$.[*].updatedUnitPrice").value(hasItem(DEFAULT_UPDATED_UNIT_PRICE)))
            .andExpect(jsonPath("$.[*].orderLineJson").value(hasItem(DEFAULT_ORDER_LINE_JSON)))
            .andExpect(jsonPath("$.[*].availability").value(hasItem(DEFAULT_AVAILABILITY)))
            .andExpect(jsonPath("$.[*].validConditions").value(hasItem(DEFAULT_VALID_CONDITIONS)))
            .andExpect(jsonPath("$.[*].injected").value(hasItem(DEFAULT_INJECTED)))
            .andExpect(jsonPath("$.[*].barcode").value(hasItem(DEFAULT_BARCODE)))
            .andExpect(jsonPath("$.[*].updatedBarcode").value(hasItem(DEFAULT_UPDATED_BARCODE)))
            .andExpect(jsonPath("$.[*].internalCode").value(hasItem(DEFAULT_INTERNAL_CODE)))
            .andExpect(jsonPath("$.[*].updatedInternalCode").value(hasItem(DEFAULT_UPDATED_INTERNAL_CODE)))
            .andExpect(jsonPath("$.[*].discountStatus").value(hasItem(DEFAULT_DISCOUNT_STATUS)))
            .andExpect(jsonPath("$.[*].priceStatus").value(hasItem(DEFAULT_PRICE_STATUS)))
            .andExpect(jsonPath("$.[*].quantityStatus").value(hasItem(DEFAULT_QUANTITY_STATUS)))
            .andExpect(jsonPath("$.[*].productStatus").value(hasItem(DEFAULT_PRODUCT_STATUS)));
    }

    @Test
    @Transactional
    void getOrderDetails() throws Exception {
        // Initialize the database
        insertedOrderDetails = orderDetailsRepository.saveAndFlush(orderDetails);

        // Get the orderDetails
        restOrderDetailsMockMvc
            .perform(get(ENTITY_API_URL_ID, orderDetails.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(orderDetails.getId().intValue()))
            .andExpect(jsonPath("$.quantity").value(sameNumber(BigDecimal.valueOf(DEFAULT_QUANTITY))))
            .andExpect(jsonPath("$.updatedQty").value(sameNumber(BigDecimal.valueOf(DEFAULT_UPDATED_QTY))))
            .andExpect(jsonPath("$.discount").value(DEFAULT_DISCOUNT))
            .andExpect(jsonPath("$.updatedDiscount").value(DEFAULT_UPDATED_DISCOUNT))
            .andExpect(jsonPath("$.unitPrice").value(DEFAULT_UNIT_PRICE))
            .andExpect(jsonPath("$.updatedUnitPrice").value(DEFAULT_UPDATED_UNIT_PRICE))
            .andExpect(jsonPath("$.orderLineJson").value(DEFAULT_ORDER_LINE_JSON))
            .andExpect(jsonPath("$.availability").value(DEFAULT_AVAILABILITY))
            .andExpect(jsonPath("$.validConditions").value(DEFAULT_VALID_CONDITIONS))
            .andExpect(jsonPath("$.injected").value(DEFAULT_INJECTED))
            .andExpect(jsonPath("$.barcode").value(DEFAULT_BARCODE))
            .andExpect(jsonPath("$.updatedBarcode").value(DEFAULT_UPDATED_BARCODE))
            .andExpect(jsonPath("$.internalCode").value(DEFAULT_INTERNAL_CODE))
            .andExpect(jsonPath("$.updatedInternalCode").value(DEFAULT_UPDATED_INTERNAL_CODE))
            .andExpect(jsonPath("$.discountStatus").value(DEFAULT_DISCOUNT_STATUS))
            .andExpect(jsonPath("$.priceStatus").value(DEFAULT_PRICE_STATUS))
            .andExpect(jsonPath("$.quantityStatus").value(DEFAULT_QUANTITY_STATUS))
            .andExpect(jsonPath("$.productStatus").value(DEFAULT_PRODUCT_STATUS));
    }

    @Test
    @Transactional
    void getNonExistingOrderDetails() throws Exception {
        // Get the orderDetails
        restOrderDetailsMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingOrderDetails() throws Exception {
        // Initialize the database
        insertedOrderDetails = orderDetailsRepository.saveAndFlush(orderDetails);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the orderDetails
        OrderDetails updatedOrderDetails = orderDetailsRepository.findById(orderDetails.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedOrderDetails are not directly saved in db
        em.detach(updatedOrderDetails);
        updatedOrderDetails
            .quantity(UPDATED_QUANTITY)
            .updatedQty(UPDATED_UPDATED_QTY)
            .discount(UPDATED_DISCOUNT)
            .updatedDiscount(UPDATED_UPDATED_DISCOUNT)
            .unitPrice(UPDATED_UNIT_PRICE)
            .updatedUnitPrice(UPDATED_UPDATED_UNIT_PRICE)
            .orderLineJson(UPDATED_ORDER_LINE_JSON)
            .availability(UPDATED_AVAILABILITY)
            // Note: validConditions and injected fields have been removed from the entity
            .barcode(UPDATED_BARCODE)
            .updatedBarcode(UPDATED_UPDATED_BARCODE)
            .internalCode(UPDATED_INTERNAL_CODE)
            .updatedInternalCode(UPDATED_UPDATED_INTERNAL_CODE)
            .discountStatus(UPDATED_DISCOUNT_STATUS)
            .priceStatus(UPDATED_PRICE_STATUS)
            .quantityStatus(UPDATED_QUANTITY_STATUS)
            .productStatus(UPDATED_PRODUCT_STATUS);
        OrderDetailsDTO orderDetailsDTO = orderDetailsMapper.toDto(updatedOrderDetails);

        restOrderDetailsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, orderDetailsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(orderDetailsDTO))
            )
            .andExpect(status().isOk());

        // Validate the OrderDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedOrderDetailsToMatchAllProperties(updatedOrderDetails);
    }

    @Test
    @Transactional
    void putNonExistingOrderDetails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        orderDetails.setId(longCount.incrementAndGet());

        // Create the OrderDetails
        OrderDetailsDTO orderDetailsDTO = orderDetailsMapper.toDto(orderDetails);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restOrderDetailsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, orderDetailsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(orderDetailsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the OrderDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchOrderDetails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        orderDetails.setId(longCount.incrementAndGet());

        // Create the OrderDetails
        OrderDetailsDTO orderDetailsDTO = orderDetailsMapper.toDto(orderDetails);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restOrderDetailsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(orderDetailsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the OrderDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamOrderDetails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        orderDetails.setId(longCount.incrementAndGet());

        // Create the OrderDetails
        OrderDetailsDTO orderDetailsDTO = orderDetailsMapper.toDto(orderDetails);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restOrderDetailsMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(orderDetailsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the OrderDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateOrderDetailsWithPatch() throws Exception {
        // Initialize the database
        insertedOrderDetails = orderDetailsRepository.saveAndFlush(orderDetails);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the orderDetails using partial update
        OrderDetails partialUpdatedOrderDetails = new OrderDetails();
        partialUpdatedOrderDetails.setId(orderDetails.getId());

        partialUpdatedOrderDetails
            .updatedDiscount(UPDATED_UPDATED_DISCOUNT)
            .unitPrice(UPDATED_UNIT_PRICE)
            .updatedUnitPrice(UPDATED_UPDATED_UNIT_PRICE)
            .orderLineJson(UPDATED_ORDER_LINE_JSON)
            .availability(UPDATED_AVAILABILITY)
            // Note: injected field has been removed from the entity
            .barcode(UPDATED_BARCODE)
            .updatedBarcode(UPDATED_UPDATED_BARCODE)
            .updatedInternalCode(UPDATED_UPDATED_INTERNAL_CODE)
            .priceStatus(UPDATED_PRICE_STATUS)
            .quantityStatus(UPDATED_QUANTITY_STATUS)
            .productStatus(UPDATED_PRODUCT_STATUS);

        restOrderDetailsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedOrderDetails.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedOrderDetails))
            )
            .andExpect(status().isOk());

        // Validate the OrderDetails in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertOrderDetailsUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedOrderDetails, orderDetails),
            getPersistedOrderDetails(orderDetails)
        );
    }

    @Test
    @Transactional
    void fullUpdateOrderDetailsWithPatch() throws Exception {
        // Initialize the database
        insertedOrderDetails = orderDetailsRepository.saveAndFlush(orderDetails);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the orderDetails using partial update
        OrderDetails partialUpdatedOrderDetails = new OrderDetails();
        partialUpdatedOrderDetails.setId(orderDetails.getId());

        partialUpdatedOrderDetails
            .quantity(UPDATED_QUANTITY)
            .updatedQty(UPDATED_UPDATED_QTY)
            .discount(UPDATED_DISCOUNT)
            .updatedDiscount(UPDATED_UPDATED_DISCOUNT)
            .unitPrice(UPDATED_UNIT_PRICE)
            .updatedUnitPrice(UPDATED_UPDATED_UNIT_PRICE)
            .orderLineJson(UPDATED_ORDER_LINE_JSON)
            .availability(UPDATED_AVAILABILITY)
            // Note: validConditions and injected fields have been removed from the entity
            .barcode(UPDATED_BARCODE)
            .updatedBarcode(UPDATED_UPDATED_BARCODE)
            .internalCode(UPDATED_INTERNAL_CODE)
            .updatedInternalCode(UPDATED_UPDATED_INTERNAL_CODE)
            .discountStatus(UPDATED_DISCOUNT_STATUS)
            .priceStatus(UPDATED_PRICE_STATUS)
            .quantityStatus(UPDATED_QUANTITY_STATUS)
            .productStatus(UPDATED_PRODUCT_STATUS);

        restOrderDetailsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedOrderDetails.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedOrderDetails))
            )
            .andExpect(status().isOk());

        // Validate the OrderDetails in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertOrderDetailsUpdatableFieldsEquals(partialUpdatedOrderDetails, getPersistedOrderDetails(partialUpdatedOrderDetails));
    }

    @Test
    @Transactional
    void patchNonExistingOrderDetails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        orderDetails.setId(longCount.incrementAndGet());

        // Create the OrderDetails
        OrderDetailsDTO orderDetailsDTO = orderDetailsMapper.toDto(orderDetails);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restOrderDetailsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, orderDetailsDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(orderDetailsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the OrderDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchOrderDetails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        orderDetails.setId(longCount.incrementAndGet());

        // Create the OrderDetails
        OrderDetailsDTO orderDetailsDTO = orderDetailsMapper.toDto(orderDetails);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restOrderDetailsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(orderDetailsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the OrderDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamOrderDetails() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        orderDetails.setId(longCount.incrementAndGet());

        // Create the OrderDetails
        OrderDetailsDTO orderDetailsDTO = orderDetailsMapper.toDto(orderDetails);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restOrderDetailsMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(orderDetailsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the OrderDetails in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteOrderDetails() throws Exception {
        // Initialize the database
        insertedOrderDetails = orderDetailsRepository.saveAndFlush(orderDetails);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the orderDetails
        restOrderDetailsMockMvc
            .perform(delete(ENTITY_API_URL_ID, orderDetails.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return orderDetailsRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected OrderDetails getPersistedOrderDetails(OrderDetails orderDetails) {
        return orderDetailsRepository.findById(orderDetails.getId()).orElseThrow();
    }

    protected void assertPersistedOrderDetailsToMatchAllProperties(OrderDetails expectedOrderDetails) {
        assertOrderDetailsAllPropertiesEquals(expectedOrderDetails, getPersistedOrderDetails(expectedOrderDetails));
    }

    protected void assertPersistedOrderDetailsToMatchUpdatableProperties(OrderDetails expectedOrderDetails) {
        assertOrderDetailsAllUpdatablePropertiesEquals(expectedOrderDetails, getPersistedOrderDetails(expectedOrderDetails));
    }
}
