package com.dq.lilas.rest;

import static com.dq.lilas.domain.GmsBrandsAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.GmsBrands;
import com.dq.lilas.repository.GmsBrandsRepository;
import com.dq.lilas.service.dto.GmsBrandsDTO;
import com.dq.lilas.service.mapper.GmsBrandsMapper;
import com.dq.lilas.web.rest.GmsBrandsResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link GmsBrandsResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class GmsBrandsResourceIT {

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String DEFAULT_CLASSE = "AAAAAAAAAA";
    private static final String UPDATED_CLASSE = "BBBBBBBBBB";

    private static final String DEFAULT_ICON_PATH = "AAAAAAAAAA";
    private static final String UPDATED_ICON_PATH = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/gms-brands";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private GmsBrandsRepository gmsBrandsRepository;

    @Autowired
    private GmsBrandsMapper gmsBrandsMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restGmsBrandsMockMvc;

    private GmsBrands gmsBrands;

    private GmsBrands insertedGmsBrands;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static GmsBrands createEntity() {
        return new GmsBrands().name(DEFAULT_NAME).classe(DEFAULT_CLASSE).iconPath(DEFAULT_ICON_PATH);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static GmsBrands createUpdatedEntity() {
        return new GmsBrands().name(UPDATED_NAME).classe(UPDATED_CLASSE).iconPath(UPDATED_ICON_PATH);
    }

    @BeforeEach
    void initTest() {
        gmsBrands = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedGmsBrands != null) {
            gmsBrandsRepository.delete(insertedGmsBrands);
            insertedGmsBrands = null;
        }
    }

    @Test
    @Transactional
    void createGmsBrands() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the GmsBrands
        GmsBrandsDTO gmsBrandsDTO = gmsBrandsMapper.toDto(gmsBrands);
        var returnedGmsBrandsDTO = om.readValue(
            restGmsBrandsMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(gmsBrandsDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            GmsBrandsDTO.class
        );

        // Validate the GmsBrands in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedGmsBrands = gmsBrandsMapper.toEntity(returnedGmsBrandsDTO);
        assertGmsBrandsUpdatableFieldsEquals(returnedGmsBrands, getPersistedGmsBrands(returnedGmsBrands));

        insertedGmsBrands = returnedGmsBrands;
    }

    @Test
    @Transactional
    void createGmsBrandsWithExistingId() throws Exception {
        // Create the GmsBrands with an existing ID
        gmsBrands.setId(1L);
        GmsBrandsDTO gmsBrandsDTO = gmsBrandsMapper.toDto(gmsBrands);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restGmsBrandsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(gmsBrandsDTO)))
            .andExpect(status().isBadRequest());

        // Validate the GmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllGmsBrands() throws Exception {
        // Initialize the database
        insertedGmsBrands = gmsBrandsRepository.saveAndFlush(gmsBrands);

        // Get all the gmsBrandsList
        restGmsBrandsMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(gmsBrands.getId().intValue())))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)))
            .andExpect(jsonPath("$.[*].classe").value(hasItem(DEFAULT_CLASSE)))
            .andExpect(jsonPath("$.[*].iconPath").value(hasItem(DEFAULT_ICON_PATH)));
    }

    @Test
    @Transactional
    void getGmsBrands() throws Exception {
        // Initialize the database
        insertedGmsBrands = gmsBrandsRepository.saveAndFlush(gmsBrands);

        // Get the gmsBrands
        restGmsBrandsMockMvc
            .perform(get(ENTITY_API_URL_ID, gmsBrands.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(gmsBrands.getId().intValue()))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME))
            .andExpect(jsonPath("$.classe").value(DEFAULT_CLASSE))
            .andExpect(jsonPath("$.iconPath").value(DEFAULT_ICON_PATH));
    }

    @Test
    @Transactional
    void getNonExistingGmsBrands() throws Exception {
        // Get the gmsBrands
        restGmsBrandsMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingGmsBrands() throws Exception {
        // Initialize the database
        insertedGmsBrands = gmsBrandsRepository.saveAndFlush(gmsBrands);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the gmsBrands
        GmsBrands updatedGmsBrands = gmsBrandsRepository.findById(gmsBrands.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedGmsBrands are not directly saved in db
        em.detach(updatedGmsBrands);
        updatedGmsBrands.name(UPDATED_NAME).classe(UPDATED_CLASSE).iconPath(UPDATED_ICON_PATH);
        GmsBrandsDTO gmsBrandsDTO = gmsBrandsMapper.toDto(updatedGmsBrands);

        restGmsBrandsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, gmsBrandsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(gmsBrandsDTO))
            )
            .andExpect(status().isOk());

        // Validate the GmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedGmsBrandsToMatchAllProperties(updatedGmsBrands);
    }

    @Test
    @Transactional
    void putNonExistingGmsBrands() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        gmsBrands.setId(longCount.incrementAndGet());

        // Create the GmsBrands
        GmsBrandsDTO gmsBrandsDTO = gmsBrandsMapper.toDto(gmsBrands);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restGmsBrandsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, gmsBrandsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(gmsBrandsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the GmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchGmsBrands() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        gmsBrands.setId(longCount.incrementAndGet());

        // Create the GmsBrands
        GmsBrandsDTO gmsBrandsDTO = gmsBrandsMapper.toDto(gmsBrands);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restGmsBrandsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(gmsBrandsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the GmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamGmsBrands() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        gmsBrands.setId(longCount.incrementAndGet());

        // Create the GmsBrands
        GmsBrandsDTO gmsBrandsDTO = gmsBrandsMapper.toDto(gmsBrands);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restGmsBrandsMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(gmsBrandsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the GmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateGmsBrandsWithPatch() throws Exception {
        // Initialize the database
        insertedGmsBrands = gmsBrandsRepository.saveAndFlush(gmsBrands);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the gmsBrands using partial update
        GmsBrands partialUpdatedGmsBrands = new GmsBrands();
        partialUpdatedGmsBrands.setId(gmsBrands.getId());

        partialUpdatedGmsBrands.name(UPDATED_NAME).iconPath(UPDATED_ICON_PATH);

        restGmsBrandsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedGmsBrands.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedGmsBrands))
            )
            .andExpect(status().isOk());

        // Validate the GmsBrands in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertGmsBrandsUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedGmsBrands, gmsBrands),
            getPersistedGmsBrands(gmsBrands)
        );
    }

    @Test
    @Transactional
    void fullUpdateGmsBrandsWithPatch() throws Exception {
        // Initialize the database
        insertedGmsBrands = gmsBrandsRepository.saveAndFlush(gmsBrands);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the gmsBrands using partial update
        GmsBrands partialUpdatedGmsBrands = new GmsBrands();
        partialUpdatedGmsBrands.setId(gmsBrands.getId());

        partialUpdatedGmsBrands.name(UPDATED_NAME).classe(UPDATED_CLASSE).iconPath(UPDATED_ICON_PATH);

        restGmsBrandsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedGmsBrands.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedGmsBrands))
            )
            .andExpect(status().isOk());

        // Validate the GmsBrands in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertGmsBrandsUpdatableFieldsEquals(partialUpdatedGmsBrands, getPersistedGmsBrands(partialUpdatedGmsBrands));
    }

    @Test
    @Transactional
    void patchNonExistingGmsBrands() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        gmsBrands.setId(longCount.incrementAndGet());

        // Create the GmsBrands
        GmsBrandsDTO gmsBrandsDTO = gmsBrandsMapper.toDto(gmsBrands);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restGmsBrandsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, gmsBrandsDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(gmsBrandsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the GmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchGmsBrands() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        gmsBrands.setId(longCount.incrementAndGet());

        // Create the GmsBrands
        GmsBrandsDTO gmsBrandsDTO = gmsBrandsMapper.toDto(gmsBrands);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restGmsBrandsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(gmsBrandsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the GmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamGmsBrands() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        gmsBrands.setId(longCount.incrementAndGet());

        // Create the GmsBrands
        GmsBrandsDTO gmsBrandsDTO = gmsBrandsMapper.toDto(gmsBrands);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restGmsBrandsMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(gmsBrandsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the GmsBrands in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteGmsBrands() throws Exception {
        // Initialize the database
        insertedGmsBrands = gmsBrandsRepository.saveAndFlush(gmsBrands);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the gmsBrands
        restGmsBrandsMockMvc
            .perform(delete(ENTITY_API_URL_ID, gmsBrands.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return gmsBrandsRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected GmsBrands getPersistedGmsBrands(GmsBrands gmsBrands) {
        return gmsBrandsRepository.findById(gmsBrands.getId()).orElseThrow();
    }

    protected void assertPersistedGmsBrandsToMatchAllProperties(GmsBrands expectedGmsBrands) {
        assertGmsBrandsAllPropertiesEquals(expectedGmsBrands, getPersistedGmsBrands(expectedGmsBrands));
    }

    protected void assertPersistedGmsBrandsToMatchUpdatableProperties(GmsBrands expectedGmsBrands) {
        assertGmsBrandsAllUpdatablePropertiesEquals(expectedGmsBrands, getPersistedGmsBrands(expectedGmsBrands));
    }
}
