package com.dq.lilas.rest;

import static com.dq.lilas.domain.DailyStockAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.DailyStock;
import com.dq.lilas.repository.DailyStockRepository;
import com.dq.lilas.service.dto.DailyStockDTO;
import com.dq.lilas.service.mapper.DailyStockMapper;
import com.dq.lilas.web.rest.DailyStockResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link DailyStockResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class DailyStockResourceIT {

    private static final String DEFAULT_INTERNAL_CODE = "AAAAAAAAAA";
    private static final String UPDATED_INTERNAL_CODE = "BBBBBBBBBB";

    private static final String DEFAULT_BARCODE = "AAAAAAAAAA";
    private static final String UPDATED_BARCODE = "BBBBBBBBBB";

    private static final String DEFAULT_PRODUCT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_PRODUCT_NAME = "BBBBBBBBBB";

    private static final Long DEFAULT_STOCK_QTY = 1L;
    private static final Long UPDATED_STOCK_QTY = 2L;

    private static final LocalDate DEFAULT_STOCK_DATE = LocalDate.ofEpochDay(0L);
    private static final LocalDate UPDATED_STOCK_DATE = LocalDate.now(ZoneId.systemDefault());

    private static final Long DEFAULT_REMAINING_QUANTITY = 1L;
    private static final Long UPDATED_REMAINING_QUANTITY = 2L;

    private static final String ENTITY_API_URL = "/api/daily-stocks";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private DailyStockRepository dailyStockRepository;

    @Autowired
    private DailyStockMapper dailyStockMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restDailyStockMockMvc;

    private DailyStock dailyStock;

    private DailyStock insertedDailyStock;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static DailyStock createEntity() {
        return new DailyStock()
            .internalCode(DEFAULT_INTERNAL_CODE)
            .barcode(DEFAULT_BARCODE)
            .productName(DEFAULT_PRODUCT_NAME)
            .stockQty(DEFAULT_STOCK_QTY)
            .stockDate(DEFAULT_STOCK_DATE)
            .remainingQuantity(DEFAULT_REMAINING_QUANTITY);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static DailyStock createUpdatedEntity() {
        return new DailyStock()
            .internalCode(UPDATED_INTERNAL_CODE)
            .barcode(UPDATED_BARCODE)
            .productName(UPDATED_PRODUCT_NAME)
            .stockQty(UPDATED_STOCK_QTY)
            .stockDate(UPDATED_STOCK_DATE)
            .remainingQuantity(UPDATED_REMAINING_QUANTITY);
    }

    @BeforeEach
    void initTest() {
        dailyStock = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedDailyStock != null) {
            dailyStockRepository.delete(insertedDailyStock);
            insertedDailyStock = null;
        }
    }

    @Test
    @Transactional
    void createDailyStock() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the DailyStock
        DailyStockDTO dailyStockDTO = dailyStockMapper.toDto(dailyStock);
        var returnedDailyStockDTO = om.readValue(
            restDailyStockMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(dailyStockDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            DailyStockDTO.class
        );

        // Validate the DailyStock in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedDailyStock = dailyStockMapper.toEntity(returnedDailyStockDTO);
        assertDailyStockUpdatableFieldsEquals(returnedDailyStock, getPersistedDailyStock(returnedDailyStock));

        insertedDailyStock = returnedDailyStock;
    }

    @Test
    @Transactional
    void createDailyStockWithExistingId() throws Exception {
        // Create the DailyStock with an existing ID
        dailyStock.setId(1L);
        DailyStockDTO dailyStockDTO = dailyStockMapper.toDto(dailyStock);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restDailyStockMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(dailyStockDTO)))
            .andExpect(status().isBadRequest());

        // Validate the DailyStock in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllDailyStocks() throws Exception {
        // Initialize the database
        insertedDailyStock = dailyStockRepository.saveAndFlush(dailyStock);

        // Get all the dailyStockList
        restDailyStockMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(dailyStock.getId().intValue())))
            .andExpect(jsonPath("$.[*].internalCode").value(hasItem(DEFAULT_INTERNAL_CODE)))
            .andExpect(jsonPath("$.[*].barcode").value(hasItem(DEFAULT_BARCODE)))
            .andExpect(jsonPath("$.[*].productName").value(hasItem(DEFAULT_PRODUCT_NAME)))
            .andExpect(jsonPath("$.[*].stockQty").value(hasItem(DEFAULT_STOCK_QTY.intValue())))
            .andExpect(jsonPath("$.[*].stockDate").value(hasItem(DEFAULT_STOCK_DATE.toString())))
            .andExpect(jsonPath("$.[*].remainingQuantity").value(hasItem(DEFAULT_REMAINING_QUANTITY.intValue())));
    }

    @Test
    @Transactional
    void getDailyStock() throws Exception {
        // Initialize the database
        insertedDailyStock = dailyStockRepository.saveAndFlush(dailyStock);

        // Get the dailyStock
        restDailyStockMockMvc
            .perform(get(ENTITY_API_URL_ID, dailyStock.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(dailyStock.getId().intValue()))
            .andExpect(jsonPath("$.internalCode").value(DEFAULT_INTERNAL_CODE))
            .andExpect(jsonPath("$.barcode").value(DEFAULT_BARCODE))
            .andExpect(jsonPath("$.productName").value(DEFAULT_PRODUCT_NAME))
            .andExpect(jsonPath("$.stockQty").value(DEFAULT_STOCK_QTY.intValue()))
            .andExpect(jsonPath("$.stockDate").value(DEFAULT_STOCK_DATE.toString()))
            .andExpect(jsonPath("$.remainingQuantity").value(DEFAULT_REMAINING_QUANTITY.intValue()));
    }

    @Test
    @Transactional
    void getNonExistingDailyStock() throws Exception {
        // Get the dailyStock
        restDailyStockMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingDailyStock() throws Exception {
        // Initialize the database
        insertedDailyStock = dailyStockRepository.saveAndFlush(dailyStock);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the dailyStock
        DailyStock updatedDailyStock = dailyStockRepository.findById(dailyStock.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedDailyStock are not directly saved in db
        em.detach(updatedDailyStock);
        updatedDailyStock
            .internalCode(UPDATED_INTERNAL_CODE)
            .barcode(UPDATED_BARCODE)
            .productName(UPDATED_PRODUCT_NAME)
            .stockQty(UPDATED_STOCK_QTY)
            .stockDate(UPDATED_STOCK_DATE)
            .remainingQuantity(UPDATED_REMAINING_QUANTITY);
        DailyStockDTO dailyStockDTO = dailyStockMapper.toDto(updatedDailyStock);

        restDailyStockMockMvc
            .perform(
                put(ENTITY_API_URL_ID, dailyStockDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(dailyStockDTO))
            )
            .andExpect(status().isOk());

        // Validate the DailyStock in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedDailyStockToMatchAllProperties(updatedDailyStock);
    }

    @Test
    @Transactional
    void putNonExistingDailyStock() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        dailyStock.setId(longCount.incrementAndGet());

        // Create the DailyStock
        DailyStockDTO dailyStockDTO = dailyStockMapper.toDto(dailyStock);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restDailyStockMockMvc
            .perform(
                put(ENTITY_API_URL_ID, dailyStockDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(dailyStockDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the DailyStock in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchDailyStock() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        dailyStock.setId(longCount.incrementAndGet());

        // Create the DailyStock
        DailyStockDTO dailyStockDTO = dailyStockMapper.toDto(dailyStock);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDailyStockMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(dailyStockDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the DailyStock in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamDailyStock() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        dailyStock.setId(longCount.incrementAndGet());

        // Create the DailyStock
        DailyStockDTO dailyStockDTO = dailyStockMapper.toDto(dailyStock);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDailyStockMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(dailyStockDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the DailyStock in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateDailyStockWithPatch() throws Exception {
        // Initialize the database
        insertedDailyStock = dailyStockRepository.saveAndFlush(dailyStock);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the dailyStock using partial update
        DailyStock partialUpdatedDailyStock = new DailyStock();
        partialUpdatedDailyStock.setId(dailyStock.getId());

        partialUpdatedDailyStock.barcode(UPDATED_BARCODE).stockDate(UPDATED_STOCK_DATE).remainingQuantity(UPDATED_REMAINING_QUANTITY);

        restDailyStockMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedDailyStock.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedDailyStock))
            )
            .andExpect(status().isOk());

        // Validate the DailyStock in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertDailyStockUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedDailyStock, dailyStock),
            getPersistedDailyStock(dailyStock)
        );
    }

    @Test
    @Transactional
    void fullUpdateDailyStockWithPatch() throws Exception {
        // Initialize the database
        insertedDailyStock = dailyStockRepository.saveAndFlush(dailyStock);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the dailyStock using partial update
        DailyStock partialUpdatedDailyStock = new DailyStock();
        partialUpdatedDailyStock.setId(dailyStock.getId());

        partialUpdatedDailyStock
            .internalCode(UPDATED_INTERNAL_CODE)
            .barcode(UPDATED_BARCODE)
            .productName(UPDATED_PRODUCT_NAME)
            .stockQty(UPDATED_STOCK_QTY)
            .stockDate(UPDATED_STOCK_DATE)
            .remainingQuantity(UPDATED_REMAINING_QUANTITY);

        restDailyStockMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedDailyStock.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedDailyStock))
            )
            .andExpect(status().isOk());

        // Validate the DailyStock in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertDailyStockUpdatableFieldsEquals(partialUpdatedDailyStock, getPersistedDailyStock(partialUpdatedDailyStock));
    }

    @Test
    @Transactional
    void patchNonExistingDailyStock() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        dailyStock.setId(longCount.incrementAndGet());

        // Create the DailyStock
        DailyStockDTO dailyStockDTO = dailyStockMapper.toDto(dailyStock);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restDailyStockMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, dailyStockDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(dailyStockDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the DailyStock in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchDailyStock() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        dailyStock.setId(longCount.incrementAndGet());

        // Create the DailyStock
        DailyStockDTO dailyStockDTO = dailyStockMapper.toDto(dailyStock);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDailyStockMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(dailyStockDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the DailyStock in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamDailyStock() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        dailyStock.setId(longCount.incrementAndGet());

        // Create the DailyStock
        DailyStockDTO dailyStockDTO = dailyStockMapper.toDto(dailyStock);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDailyStockMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(dailyStockDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the DailyStock in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteDailyStock() throws Exception {
        // Initialize the database
        insertedDailyStock = dailyStockRepository.saveAndFlush(dailyStock);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the dailyStock
        restDailyStockMockMvc
            .perform(delete(ENTITY_API_URL_ID, dailyStock.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return dailyStockRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected DailyStock getPersistedDailyStock(DailyStock dailyStock) {
        return dailyStockRepository.findById(dailyStock.getId()).orElseThrow();
    }

    protected void assertPersistedDailyStockToMatchAllProperties(DailyStock expectedDailyStock) {
        assertDailyStockAllPropertiesEquals(expectedDailyStock, getPersistedDailyStock(expectedDailyStock));
    }

    protected void assertPersistedDailyStockToMatchUpdatableProperties(DailyStock expectedDailyStock) {
        assertDailyStockAllUpdatablePropertiesEquals(expectedDailyStock, getPersistedDailyStock(expectedDailyStock));
    }
}
