package com.dq.lilas.rest;

import static com.dq.lilas.domain.TemplateConditionsAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.TemplateConditions;
import com.dq.lilas.repository.TemplateConditionsRepository;
import com.dq.lilas.service.dto.TemplateConditionsDTO;
import com.dq.lilas.service.mapper.TemplateConditionsMapper;
import com.dq.lilas.web.rest.TemplateConditionsResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link TemplateConditionsResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class TemplateConditionsResourceIT {

    private static final String DEFAULT_NAME = "AAAAAAAAAA";
    private static final String UPDATED_NAME = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/template-conditions";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private TemplateConditionsRepository templateConditionsRepository;

    @Autowired
    private TemplateConditionsMapper templateConditionsMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restTemplateConditionsMockMvc;

    private TemplateConditions templateConditions;

    private TemplateConditions insertedTemplateConditions;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TemplateConditions createEntity() {
        return new TemplateConditions().name(DEFAULT_NAME);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static TemplateConditions createUpdatedEntity() {
        return new TemplateConditions().name(UPDATED_NAME);
    }

    @BeforeEach
    void initTest() {
        templateConditions = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedTemplateConditions != null) {
            templateConditionsRepository.delete(insertedTemplateConditions);
            insertedTemplateConditions = null;
        }
    }

    @Test
    @Transactional
    void createTemplateConditions() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the TemplateConditions
        TemplateConditionsDTO templateConditionsDTO = templateConditionsMapper.toDto(templateConditions);
        var returnedTemplateConditionsDTO = om.readValue(
            restTemplateConditionsMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(templateConditionsDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            TemplateConditionsDTO.class
        );

        // Validate the TemplateConditions in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedTemplateConditions = templateConditionsMapper.toEntity(returnedTemplateConditionsDTO);
        assertTemplateConditionsUpdatableFieldsEquals(
            returnedTemplateConditions,
            getPersistedTemplateConditions(returnedTemplateConditions)
        );

        insertedTemplateConditions = returnedTemplateConditions;
    }

    @Test
    @Transactional
    void createTemplateConditionsWithExistingId() throws Exception {
        // Create the TemplateConditions with an existing ID
        templateConditions.setId(1L);
        TemplateConditionsDTO templateConditionsDTO = templateConditionsMapper.toDto(templateConditions);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restTemplateConditionsMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(templateConditionsDTO)))
            .andExpect(status().isBadRequest());

        // Validate the TemplateConditions in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllTemplateConditions() throws Exception {
        // Initialize the database
        insertedTemplateConditions = templateConditionsRepository.saveAndFlush(templateConditions);

        // Get all the templateConditionsList
        restTemplateConditionsMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(templateConditions.getId().intValue())))
            .andExpect(jsonPath("$.[*].name").value(hasItem(DEFAULT_NAME)));
    }

    @Test
    @Transactional
    void getTemplateConditions() throws Exception {
        // Initialize the database
        insertedTemplateConditions = templateConditionsRepository.saveAndFlush(templateConditions);

        // Get the templateConditions
        restTemplateConditionsMockMvc
            .perform(get(ENTITY_API_URL_ID, templateConditions.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(templateConditions.getId().intValue()))
            .andExpect(jsonPath("$.name").value(DEFAULT_NAME));
    }

    @Test
    @Transactional
    void getNonExistingTemplateConditions() throws Exception {
        // Get the templateConditions
        restTemplateConditionsMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingTemplateConditions() throws Exception {
        // Initialize the database
        insertedTemplateConditions = templateConditionsRepository.saveAndFlush(templateConditions);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the templateConditions
        TemplateConditions updatedTemplateConditions = templateConditionsRepository.findById(templateConditions.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedTemplateConditions are not directly saved in db
        em.detach(updatedTemplateConditions);
        updatedTemplateConditions.name(UPDATED_NAME);
        TemplateConditionsDTO templateConditionsDTO = templateConditionsMapper.toDto(updatedTemplateConditions);

        restTemplateConditionsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, templateConditionsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(templateConditionsDTO))
            )
            .andExpect(status().isOk());

        // Validate the TemplateConditions in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedTemplateConditionsToMatchAllProperties(updatedTemplateConditions);
    }

    @Test
    @Transactional
    void putNonExistingTemplateConditions() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        templateConditions.setId(longCount.incrementAndGet());

        // Create the TemplateConditions
        TemplateConditionsDTO templateConditionsDTO = templateConditionsMapper.toDto(templateConditions);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTemplateConditionsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, templateConditionsDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(templateConditionsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TemplateConditions in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchTemplateConditions() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        templateConditions.setId(longCount.incrementAndGet());

        // Create the TemplateConditions
        TemplateConditionsDTO templateConditionsDTO = templateConditionsMapper.toDto(templateConditions);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTemplateConditionsMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(templateConditionsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TemplateConditions in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamTemplateConditions() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        templateConditions.setId(longCount.incrementAndGet());

        // Create the TemplateConditions
        TemplateConditionsDTO templateConditionsDTO = templateConditionsMapper.toDto(templateConditions);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTemplateConditionsMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(templateConditionsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TemplateConditions in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateTemplateConditionsWithPatch() throws Exception {
        // Initialize the database
        insertedTemplateConditions = templateConditionsRepository.saveAndFlush(templateConditions);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the templateConditions using partial update
        TemplateConditions partialUpdatedTemplateConditions = new TemplateConditions();
        partialUpdatedTemplateConditions.setId(templateConditions.getId());

        partialUpdatedTemplateConditions.name(UPDATED_NAME);

        restTemplateConditionsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTemplateConditions.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTemplateConditions))
            )
            .andExpect(status().isOk());

        // Validate the TemplateConditions in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTemplateConditionsUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedTemplateConditions, templateConditions),
            getPersistedTemplateConditions(templateConditions)
        );
    }

    @Test
    @Transactional
    void fullUpdateTemplateConditionsWithPatch() throws Exception {
        // Initialize the database
        insertedTemplateConditions = templateConditionsRepository.saveAndFlush(templateConditions);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the templateConditions using partial update
        TemplateConditions partialUpdatedTemplateConditions = new TemplateConditions();
        partialUpdatedTemplateConditions.setId(templateConditions.getId());

        partialUpdatedTemplateConditions.name(UPDATED_NAME);

        restTemplateConditionsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTemplateConditions.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTemplateConditions))
            )
            .andExpect(status().isOk());

        // Validate the TemplateConditions in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTemplateConditionsUpdatableFieldsEquals(
            partialUpdatedTemplateConditions,
            getPersistedTemplateConditions(partialUpdatedTemplateConditions)
        );
    }

    @Test
    @Transactional
    void patchNonExistingTemplateConditions() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        templateConditions.setId(longCount.incrementAndGet());

        // Create the TemplateConditions
        TemplateConditionsDTO templateConditionsDTO = templateConditionsMapper.toDto(templateConditions);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTemplateConditionsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, templateConditionsDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(templateConditionsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TemplateConditions in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchTemplateConditions() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        templateConditions.setId(longCount.incrementAndGet());

        // Create the TemplateConditions
        TemplateConditionsDTO templateConditionsDTO = templateConditionsMapper.toDto(templateConditions);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTemplateConditionsMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(templateConditionsDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the TemplateConditions in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamTemplateConditions() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        templateConditions.setId(longCount.incrementAndGet());

        // Create the TemplateConditions
        TemplateConditionsDTO templateConditionsDTO = templateConditionsMapper.toDto(templateConditions);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTemplateConditionsMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(templateConditionsDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the TemplateConditions in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteTemplateConditions() throws Exception {
        // Initialize the database
        insertedTemplateConditions = templateConditionsRepository.saveAndFlush(templateConditions);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the templateConditions
        restTemplateConditionsMockMvc
            .perform(delete(ENTITY_API_URL_ID, templateConditions.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return templateConditionsRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected TemplateConditions getPersistedTemplateConditions(TemplateConditions templateConditions) {
        return templateConditionsRepository.findById(templateConditions.getId()).orElseThrow();
    }

    protected void assertPersistedTemplateConditionsToMatchAllProperties(TemplateConditions expectedTemplateConditions) {
        assertTemplateConditionsAllPropertiesEquals(expectedTemplateConditions, getPersistedTemplateConditions(expectedTemplateConditions));
    }

    protected void assertPersistedTemplateConditionsToMatchUpdatableProperties(TemplateConditions expectedTemplateConditions) {
        assertTemplateConditionsAllUpdatablePropertiesEquals(
            expectedTemplateConditions,
            getPersistedTemplateConditions(expectedTemplateConditions)
        );
    }
}
