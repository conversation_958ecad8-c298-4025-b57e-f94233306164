package com.dq.lilas.rest;

import static com.dq.lilas.domain.TypecorrespondenceAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Typecorrespondence;
import com.dq.lilas.repository.TypecorrespondenceRepository;
import com.dq.lilas.service.dto.TypecorrespondenceDTO;
import com.dq.lilas.service.mapper.TypecorrespondenceMapper;
import com.dq.lilas.web.rest.TypecorrespondenceResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link TypecorrespondenceResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class TypecorrespondenceResourceIT {

    private static final String DEFAULT_TYPECORRESP = "AAAAAAAAAA";
    private static final String UPDATED_TYPECORRESP = "BBBBBBBBBB";

    private static final String DEFAULT_TYPEDECISION = "AAAAAAAAAA";
    private static final String UPDATED_TYPEDECISION = "BBBBBBBBBB";

    private static final String DEFAULT_HOSP = "AAAAAAAAAA";
    private static final String UPDATED_HOSP = "BBBBBBBBBB";

    private static final String DEFAULT_LBL = "AAAAAAAAAA";
    private static final String UPDATED_LBL = "BBBBBBBBBB";

    private static final String DEFAULT_STATUT = "AAAAAAAAAA";
    private static final String UPDATED_STATUT = "BBBBBBBBBB";

    private static final String DEFAULT_ABBREVIATED = "AAAAAAAAAA";
    private static final String UPDATED_ABBREVIATED = "BBBBBBBBBB";

    private static final String DEFAULT_SPEECH = "AAAAAAAAAA";
    private static final String UPDATED_SPEECH = "BBBBBBBBBB";

    private static final Long DEFAULT_NBRNOTIFBEFOREREC = 1L;
    private static final Long UPDATED_NBRNOTIFBEFOREREC = 2L;

    private static final Long DEFAULT_NBRNOTIFAFTERREC = 1L;
    private static final Long UPDATED_NBRNOTIFAFTERREC = 2L;

    private static final String DEFAULT_ARCHIVE_SUBJECTS_ID = "AAAAAAAAAA";
    private static final String UPDATED_ARCHIVE_SUBJECTS_ID = "BBBBBBBBBB";

    private static final String ENTITY_API_URL = "/api/typecorrespondences";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private TypecorrespondenceRepository typecorrespondenceRepository;

    @Autowired
    private TypecorrespondenceMapper typecorrespondenceMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restTypecorrespondenceMockMvc;

    private Typecorrespondence typecorrespondence;

    private Typecorrespondence insertedTypecorrespondence;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Typecorrespondence createEntity() {
        return new Typecorrespondence()
            .typecorresp(DEFAULT_TYPECORRESP)
            .typedecision(DEFAULT_TYPEDECISION)
            .hosp(DEFAULT_HOSP)
            .lbl(DEFAULT_LBL)
            .statut(DEFAULT_STATUT)
            .abbreviated(DEFAULT_ABBREVIATED)
            .speech(DEFAULT_SPEECH)
            .nbrnotifbeforerec(DEFAULT_NBRNOTIFBEFOREREC)
            .nbrnotifafterrec(DEFAULT_NBRNOTIFAFTERREC)
            .archiveSubjectsId(DEFAULT_ARCHIVE_SUBJECTS_ID);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Typecorrespondence createUpdatedEntity() {
        return new Typecorrespondence()
            .typecorresp(UPDATED_TYPECORRESP)
            .typedecision(UPDATED_TYPEDECISION)
            .hosp(UPDATED_HOSP)
            .lbl(UPDATED_LBL)
            .statut(UPDATED_STATUT)
            .abbreviated(UPDATED_ABBREVIATED)
            .speech(UPDATED_SPEECH)
            .nbrnotifbeforerec(UPDATED_NBRNOTIFBEFOREREC)
            .nbrnotifafterrec(UPDATED_NBRNOTIFAFTERREC)
            .archiveSubjectsId(UPDATED_ARCHIVE_SUBJECTS_ID);
    }

    @BeforeEach
    void initTest() {
        typecorrespondence = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedTypecorrespondence != null) {
            typecorrespondenceRepository.delete(insertedTypecorrespondence);
            insertedTypecorrespondence = null;
        }
    }

    @Test
    @Transactional
    void createTypecorrespondence() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Typecorrespondence
        TypecorrespondenceDTO typecorrespondenceDTO = typecorrespondenceMapper.toDto(typecorrespondence);
        var returnedTypecorrespondenceDTO = om.readValue(
            restTypecorrespondenceMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(typecorrespondenceDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            TypecorrespondenceDTO.class
        );

        // Validate the Typecorrespondence in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedTypecorrespondence = typecorrespondenceMapper.toEntity(returnedTypecorrespondenceDTO);
        assertTypecorrespondenceUpdatableFieldsEquals(
            returnedTypecorrespondence,
            getPersistedTypecorrespondence(returnedTypecorrespondence)
        );

        insertedTypecorrespondence = returnedTypecorrespondence;
    }

    @Test
    @Transactional
    void createTypecorrespondenceWithExistingId() throws Exception {
        // Create the Typecorrespondence with an existing ID
        typecorrespondence.setId(1L);
        TypecorrespondenceDTO typecorrespondenceDTO = typecorrespondenceMapper.toDto(typecorrespondence);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restTypecorrespondenceMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(typecorrespondenceDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Typecorrespondence in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllTypecorrespondences() throws Exception {
        // Initialize the database
        insertedTypecorrespondence = typecorrespondenceRepository.saveAndFlush(typecorrespondence);

        // Get all the typecorrespondenceList
        restTypecorrespondenceMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(typecorrespondence.getId().intValue())))
            .andExpect(jsonPath("$.[*].typecorresp").value(hasItem(DEFAULT_TYPECORRESP)))
            .andExpect(jsonPath("$.[*].typedecision").value(hasItem(DEFAULT_TYPEDECISION)))
            .andExpect(jsonPath("$.[*].hosp").value(hasItem(DEFAULT_HOSP)))
            .andExpect(jsonPath("$.[*].lbl").value(hasItem(DEFAULT_LBL)))
            .andExpect(jsonPath("$.[*].statut").value(hasItem(DEFAULT_STATUT)))
            .andExpect(jsonPath("$.[*].abbreviated").value(hasItem(DEFAULT_ABBREVIATED)))
            .andExpect(jsonPath("$.[*].speech").value(hasItem(DEFAULT_SPEECH)))
            .andExpect(jsonPath("$.[*].nbrnotifbeforerec").value(hasItem(DEFAULT_NBRNOTIFBEFOREREC.intValue())))
            .andExpect(jsonPath("$.[*].nbrnotifafterrec").value(hasItem(DEFAULT_NBRNOTIFAFTERREC.intValue())))
            .andExpect(jsonPath("$.[*].archiveSubjectsId").value(hasItem(DEFAULT_ARCHIVE_SUBJECTS_ID)));
    }

    @Test
    @Transactional
    void getTypecorrespondence() throws Exception {
        // Initialize the database
        insertedTypecorrespondence = typecorrespondenceRepository.saveAndFlush(typecorrespondence);

        // Get the typecorrespondence
        restTypecorrespondenceMockMvc
            .perform(get(ENTITY_API_URL_ID, typecorrespondence.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(typecorrespondence.getId().intValue()))
            .andExpect(jsonPath("$.typecorresp").value(DEFAULT_TYPECORRESP))
            .andExpect(jsonPath("$.typedecision").value(DEFAULT_TYPEDECISION))
            .andExpect(jsonPath("$.hosp").value(DEFAULT_HOSP))
            .andExpect(jsonPath("$.lbl").value(DEFAULT_LBL))
            .andExpect(jsonPath("$.statut").value(DEFAULT_STATUT))
            .andExpect(jsonPath("$.abbreviated").value(DEFAULT_ABBREVIATED))
            .andExpect(jsonPath("$.speech").value(DEFAULT_SPEECH))
            .andExpect(jsonPath("$.nbrnotifbeforerec").value(DEFAULT_NBRNOTIFBEFOREREC.intValue()))
            .andExpect(jsonPath("$.nbrnotifafterrec").value(DEFAULT_NBRNOTIFAFTERREC.intValue()))
            .andExpect(jsonPath("$.archiveSubjectsId").value(DEFAULT_ARCHIVE_SUBJECTS_ID));
    }

    @Test
    @Transactional
    void getNonExistingTypecorrespondence() throws Exception {
        // Get the typecorrespondence
        restTypecorrespondenceMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingTypecorrespondence() throws Exception {
        // Initialize the database
        insertedTypecorrespondence = typecorrespondenceRepository.saveAndFlush(typecorrespondence);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the typecorrespondence
        Typecorrespondence updatedTypecorrespondence = typecorrespondenceRepository.findById(typecorrespondence.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedTypecorrespondence are not directly saved in db
        em.detach(updatedTypecorrespondence);
        updatedTypecorrespondence
            .typecorresp(UPDATED_TYPECORRESP)
            .typedecision(UPDATED_TYPEDECISION)
            .hosp(UPDATED_HOSP)
            .lbl(UPDATED_LBL)
            .statut(UPDATED_STATUT)
            .abbreviated(UPDATED_ABBREVIATED)
            .speech(UPDATED_SPEECH)
            .nbrnotifbeforerec(UPDATED_NBRNOTIFBEFOREREC)
            .nbrnotifafterrec(UPDATED_NBRNOTIFAFTERREC)
            .archiveSubjectsId(UPDATED_ARCHIVE_SUBJECTS_ID);
        TypecorrespondenceDTO typecorrespondenceDTO = typecorrespondenceMapper.toDto(updatedTypecorrespondence);

        restTypecorrespondenceMockMvc
            .perform(
                put(ENTITY_API_URL_ID, typecorrespondenceDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(typecorrespondenceDTO))
            )
            .andExpect(status().isOk());

        // Validate the Typecorrespondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedTypecorrespondenceToMatchAllProperties(updatedTypecorrespondence);
    }

    @Test
    @Transactional
    void putNonExistingTypecorrespondence() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        typecorrespondence.setId(longCount.incrementAndGet());

        // Create the Typecorrespondence
        TypecorrespondenceDTO typecorrespondenceDTO = typecorrespondenceMapper.toDto(typecorrespondence);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTypecorrespondenceMockMvc
            .perform(
                put(ENTITY_API_URL_ID, typecorrespondenceDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(typecorrespondenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Typecorrespondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchTypecorrespondence() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        typecorrespondence.setId(longCount.incrementAndGet());

        // Create the Typecorrespondence
        TypecorrespondenceDTO typecorrespondenceDTO = typecorrespondenceMapper.toDto(typecorrespondence);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTypecorrespondenceMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(typecorrespondenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Typecorrespondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamTypecorrespondence() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        typecorrespondence.setId(longCount.incrementAndGet());

        // Create the Typecorrespondence
        TypecorrespondenceDTO typecorrespondenceDTO = typecorrespondenceMapper.toDto(typecorrespondence);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTypecorrespondenceMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(typecorrespondenceDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Typecorrespondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateTypecorrespondenceWithPatch() throws Exception {
        // Initialize the database
        insertedTypecorrespondence = typecorrespondenceRepository.saveAndFlush(typecorrespondence);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the typecorrespondence using partial update
        Typecorrespondence partialUpdatedTypecorrespondence = new Typecorrespondence();
        partialUpdatedTypecorrespondence.setId(typecorrespondence.getId());

        partialUpdatedTypecorrespondence
            .typecorresp(UPDATED_TYPECORRESP)
            .statut(UPDATED_STATUT)
            .nbrnotifbeforerec(UPDATED_NBRNOTIFBEFOREREC);

        restTypecorrespondenceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTypecorrespondence.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTypecorrespondence))
            )
            .andExpect(status().isOk());

        // Validate the Typecorrespondence in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTypecorrespondenceUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedTypecorrespondence, typecorrespondence),
            getPersistedTypecorrespondence(typecorrespondence)
        );
    }

    @Test
    @Transactional
    void fullUpdateTypecorrespondenceWithPatch() throws Exception {
        // Initialize the database
        insertedTypecorrespondence = typecorrespondenceRepository.saveAndFlush(typecorrespondence);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the typecorrespondence using partial update
        Typecorrespondence partialUpdatedTypecorrespondence = new Typecorrespondence();
        partialUpdatedTypecorrespondence.setId(typecorrespondence.getId());

        partialUpdatedTypecorrespondence
            .typecorresp(UPDATED_TYPECORRESP)
            .typedecision(UPDATED_TYPEDECISION)
            .hosp(UPDATED_HOSP)
            .lbl(UPDATED_LBL)
            .statut(UPDATED_STATUT)
            .abbreviated(UPDATED_ABBREVIATED)
            .speech(UPDATED_SPEECH)
            .nbrnotifbeforerec(UPDATED_NBRNOTIFBEFOREREC)
            .nbrnotifafterrec(UPDATED_NBRNOTIFAFTERREC)
            .archiveSubjectsId(UPDATED_ARCHIVE_SUBJECTS_ID);

        restTypecorrespondenceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedTypecorrespondence.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedTypecorrespondence))
            )
            .andExpect(status().isOk());

        // Validate the Typecorrespondence in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertTypecorrespondenceUpdatableFieldsEquals(
            partialUpdatedTypecorrespondence,
            getPersistedTypecorrespondence(partialUpdatedTypecorrespondence)
        );
    }

    @Test
    @Transactional
    void patchNonExistingTypecorrespondence() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        typecorrespondence.setId(longCount.incrementAndGet());

        // Create the Typecorrespondence
        TypecorrespondenceDTO typecorrespondenceDTO = typecorrespondenceMapper.toDto(typecorrespondence);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restTypecorrespondenceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, typecorrespondenceDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(typecorrespondenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Typecorrespondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchTypecorrespondence() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        typecorrespondence.setId(longCount.incrementAndGet());

        // Create the Typecorrespondence
        TypecorrespondenceDTO typecorrespondenceDTO = typecorrespondenceMapper.toDto(typecorrespondence);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTypecorrespondenceMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(typecorrespondenceDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Typecorrespondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamTypecorrespondence() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        typecorrespondence.setId(longCount.incrementAndGet());

        // Create the Typecorrespondence
        TypecorrespondenceDTO typecorrespondenceDTO = typecorrespondenceMapper.toDto(typecorrespondence);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restTypecorrespondenceMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(typecorrespondenceDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Typecorrespondence in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteTypecorrespondence() throws Exception {
        // Initialize the database
        insertedTypecorrespondence = typecorrespondenceRepository.saveAndFlush(typecorrespondence);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the typecorrespondence
        restTypecorrespondenceMockMvc
            .perform(delete(ENTITY_API_URL_ID, typecorrespondence.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return typecorrespondenceRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Typecorrespondence getPersistedTypecorrespondence(Typecorrespondence typecorrespondence) {
        return typecorrespondenceRepository.findById(typecorrespondence.getId()).orElseThrow();
    }

    protected void assertPersistedTypecorrespondenceToMatchAllProperties(Typecorrespondence expectedTypecorrespondence) {
        assertTypecorrespondenceAllPropertiesEquals(expectedTypecorrespondence, getPersistedTypecorrespondence(expectedTypecorrespondence));
    }

    protected void assertPersistedTypecorrespondenceToMatchUpdatableProperties(Typecorrespondence expectedTypecorrespondence) {
        assertTypecorrespondenceAllUpdatablePropertiesEquals(
            expectedTypecorrespondence,
            getPersistedTypecorrespondence(expectedTypecorrespondence)
        );
    }
}
