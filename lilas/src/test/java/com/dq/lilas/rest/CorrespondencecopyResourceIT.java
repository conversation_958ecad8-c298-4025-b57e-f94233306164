package com.dq.lilas.rest;

import static com.dq.lilas.domain.CorrespondencecopyAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Correspondencecopy;
import com.dq.lilas.repository.CorrespondencecopyRepository;
import com.dq.lilas.service.dto.CorrespondencecopyDTO;
import com.dq.lilas.service.mapper.CorrespondencecopyMapper;
import com.dq.lilas.web.rest.CorrespondencecopyResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link CorrespondencecopyResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class CorrespondencecopyResourceIT {

    private static final String DEFAULT_NUMCOPY = "AAAAAAAAAA";
    private static final String UPDATED_NUMCOPY = "BBBBBBBBBB";

    private static final String DEFAULT_COMMENTS = "AAAAAAAAAA";
    private static final String UPDATED_COMMENTS = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEJCCREATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCCREATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATEHJRCREATE = "AAAAAAAAAA";
    private static final String UPDATED_DATEHJRCREATE = "BBBBBBBBBB";

    private static final String DEFAULT_TYPERECEIVE = "A";
    private static final String UPDATED_TYPERECEIVE = "B";

    private static final String DEFAULT_TYPECOPY = "AAAAAAAAAA";
    private static final String UPDATED_TYPECOPY = "BBBBBBBBBB";

    private static final String DEFAULT_USERRECEIVE = "AAAAAAAAAA";
    private static final String UPDATED_USERRECEIVE = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEJCRECEIVE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCRECEIVE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATEHJRRECEIVE = "AAAAAAAAAA";
    private static final String UPDATED_DATEHJRRECEIVE = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEJCACTION = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCACTION = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATEHJRACTION = "AAAAAAAAAA";
    private static final String UPDATED_DATEHJRACTION = "BBBBBBBBBB";

    private static final String DEFAULT_ACTIONTYPE = "AAAAAAAAAA";
    private static final String UPDATED_ACTIONTYPE = "BBBBBBBBBB";

    private static final String DEFAULT_SAVECORRESPCPY = "AAAAA";
    private static final String UPDATED_SAVECORRESPCPY = "BBBBB";

    private static final String DEFAULT_ORDERNBR = "AAAAAAAAAA";
    private static final String UPDATED_ORDERNBR = "BBBBBBBBBB";

    private static final String DEFAULT_PAGENBR = "AAAAAAAAAA";
    private static final String UPDATED_PAGENBR = "BBBBBBBBBB";

    private static final String DEFAULT_EXPNO = "AAAAAAAAAA";
    private static final String UPDATED_EXPNO = "BBBBBBBBBB";

    private static final String DEFAULT_EXPYEAR = "AAAAAAAAAA";
    private static final String UPDATED_EXPYEAR = "BBBBBBBBBB";

    private static final String DEFAULT_DOCYEAR = "AAAAAAAAAA";
    private static final String UPDATED_DOCYEAR = "BBBBBBBBBB";

    private static final String DEFAULT_CATEGORYCORRESP = "AAAAA";
    private static final String UPDATED_CATEGORYCORRESP = "BBBBB";

    private static final String DEFAULT_HEUREACTION = "AAAAAAAAAA";
    private static final String UPDATED_HEUREACTION = "BBBBBBBBBB";

    private static final String DEFAULT_STATUSDENIEDCOPY = "AAAAAAAAAA";
    private static final String UPDATED_STATUSDENIEDCOPY = "BBBBBBBBBB";

    private static final String DEFAULT_PAGENBRPAPER = "AAAAAAAAAA";
    private static final String UPDATED_PAGENBRPAPER = "BBBBBBBBBB";

    private static final String DEFAULT_TASKSCAN = "AAAAAAAAAA";
    private static final String UPDATED_TASKSCAN = "BBBBBBBBBB";

    private static final String DEFAULT_PATHTRANSTO = "AAAAAAAAAA";
    private static final String UPDATED_PATHTRANSTO = "BBBBBBBBBB";

    private static final String DEFAULT_PATHTRANSCC = "AAAAAAAAAA";
    private static final String UPDATED_PATHTRANSCC = "BBBBBBBBBB";

    private static final String DEFAULT_PATHTRANSTOLIB = "AAAAAAAAAA";
    private static final String UPDATED_PATHTRANSTOLIB = "BBBBBBBBBB";

    private static final String DEFAULT_PATHTRANSCCLIB = "AAAAAAAAAA";
    private static final String UPDATED_PATHTRANSCCLIB = "BBBBBBBBBB";

    private static final String DEFAULT_FLGGROUP = "AAAAAAAAAA";
    private static final String UPDATED_FLGGROUP = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEJCDELETE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCDELETE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final Instant DEFAULT_DATEJCREVOKE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEJCREVOKE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String DEFAULT_DATEHJRREVOKE = "AAAAAAAAAA";
    private static final String UPDATED_DATEHJRREVOKE = "BBBBBBBBBB";

    private static final Instant DEFAULT_DATEREMOVE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_DATEREMOVE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String ENTITY_API_URL = "/api/correspondencecopies";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private CorrespondencecopyRepository correspondencecopyRepository;

    @Autowired
    private CorrespondencecopyMapper correspondencecopyMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restCorrespondencecopyMockMvc;

    private Correspondencecopy correspondencecopy;

    private Correspondencecopy insertedCorrespondencecopy;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Correspondencecopy createEntity() {
        return new Correspondencecopy()
            .numcopy(DEFAULT_NUMCOPY)
            .comments(DEFAULT_COMMENTS)
            .datejccreate(DEFAULT_DATEJCCREATE)
            .datehjrcreate(DEFAULT_DATEHJRCREATE)
            .typereceive(DEFAULT_TYPERECEIVE)
            .typecopy(DEFAULT_TYPECOPY)
            .userreceive(DEFAULT_USERRECEIVE)
            .datejcreceive(DEFAULT_DATEJCRECEIVE)
            .datehjrreceive(DEFAULT_DATEHJRRECEIVE)
            .datejcaction(DEFAULT_DATEJCACTION)
            .datehjraction(DEFAULT_DATEHJRACTION)
            .actiontype(DEFAULT_ACTIONTYPE)
            .savecorrespcpy(DEFAULT_SAVECORRESPCPY)
            .ordernbr(DEFAULT_ORDERNBR)
            .pagenbr(DEFAULT_PAGENBR)
            .expno(DEFAULT_EXPNO)
            .expyear(DEFAULT_EXPYEAR)
            .docyear(DEFAULT_DOCYEAR)
            .categorycorresp(DEFAULT_CATEGORYCORRESP)
            .heureaction(DEFAULT_HEUREACTION)
            .statusdeniedcopy(DEFAULT_STATUSDENIEDCOPY)
            .pagenbrpaper(DEFAULT_PAGENBRPAPER)
            .taskscan(DEFAULT_TASKSCAN)
            .pathtransto(DEFAULT_PATHTRANSTO)
            .pathtranscc(DEFAULT_PATHTRANSCC)
            .pathtranstolib(DEFAULT_PATHTRANSTOLIB)
            .pathtranscclib(DEFAULT_PATHTRANSCCLIB)
            .flggroup(DEFAULT_FLGGROUP)
            .datejcdelete(DEFAULT_DATEJCDELETE)
            .datejcrevoke(DEFAULT_DATEJCREVOKE)
            .datehjrrevoke(DEFAULT_DATEHJRREVOKE)
            .dateremove(DEFAULT_DATEREMOVE);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Correspondencecopy createUpdatedEntity() {
        return new Correspondencecopy()
            .numcopy(UPDATED_NUMCOPY)
            .comments(UPDATED_COMMENTS)
            .datejccreate(UPDATED_DATEJCCREATE)
            .datehjrcreate(UPDATED_DATEHJRCREATE)
            .typereceive(UPDATED_TYPERECEIVE)
            .typecopy(UPDATED_TYPECOPY)
            .userreceive(UPDATED_USERRECEIVE)
            .datejcreceive(UPDATED_DATEJCRECEIVE)
            .datehjrreceive(UPDATED_DATEHJRRECEIVE)
            .datejcaction(UPDATED_DATEJCACTION)
            .datehjraction(UPDATED_DATEHJRACTION)
            .actiontype(UPDATED_ACTIONTYPE)
            .savecorrespcpy(UPDATED_SAVECORRESPCPY)
            .ordernbr(UPDATED_ORDERNBR)
            .pagenbr(UPDATED_PAGENBR)
            .expno(UPDATED_EXPNO)
            .expyear(UPDATED_EXPYEAR)
            .docyear(UPDATED_DOCYEAR)
            .categorycorresp(UPDATED_CATEGORYCORRESP)
            .heureaction(UPDATED_HEUREACTION)
            .statusdeniedcopy(UPDATED_STATUSDENIEDCOPY)
            .pagenbrpaper(UPDATED_PAGENBRPAPER)
            .taskscan(UPDATED_TASKSCAN)
            .pathtransto(UPDATED_PATHTRANSTO)
            .pathtranscc(UPDATED_PATHTRANSCC)
            .pathtranstolib(UPDATED_PATHTRANSTOLIB)
            .pathtranscclib(UPDATED_PATHTRANSCCLIB)
            .flggroup(UPDATED_FLGGROUP)
            .datejcdelete(UPDATED_DATEJCDELETE)
            .datejcrevoke(UPDATED_DATEJCREVOKE)
            .datehjrrevoke(UPDATED_DATEHJRREVOKE)
            .dateremove(UPDATED_DATEREMOVE);
    }

    @BeforeEach
    void initTest() {
        correspondencecopy = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedCorrespondencecopy != null) {
            correspondencecopyRepository.delete(insertedCorrespondencecopy);
            insertedCorrespondencecopy = null;
        }
    }

    @Test
    @Transactional
    void createCorrespondencecopy() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Correspondencecopy
        CorrespondencecopyDTO correspondencecopyDTO = correspondencecopyMapper.toDto(correspondencecopy);
        var returnedCorrespondencecopyDTO = om.readValue(
            restCorrespondencecopyMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(correspondencecopyDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            CorrespondencecopyDTO.class
        );

        // Validate the Correspondencecopy in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedCorrespondencecopy = correspondencecopyMapper.toEntity(returnedCorrespondencecopyDTO);
        assertCorrespondencecopyUpdatableFieldsEquals(
            returnedCorrespondencecopy,
            getPersistedCorrespondencecopy(returnedCorrespondencecopy)
        );

        insertedCorrespondencecopy = returnedCorrespondencecopy;
    }

    @Test
    @Transactional
    void createCorrespondencecopyWithExistingId() throws Exception {
        // Create the Correspondencecopy with an existing ID
        correspondencecopy.setId(1L);
        CorrespondencecopyDTO correspondencecopyDTO = correspondencecopyMapper.toDto(correspondencecopy);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restCorrespondencecopyMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(correspondencecopyDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Correspondencecopy in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllCorrespondencecopies() throws Exception {
        // Initialize the database
        insertedCorrespondencecopy = correspondencecopyRepository.saveAndFlush(correspondencecopy);

        // Get all the correspondencecopyList
        restCorrespondencecopyMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(correspondencecopy.getId().intValue())))
            .andExpect(jsonPath("$.[*].numcopy").value(hasItem(DEFAULT_NUMCOPY)))
            .andExpect(jsonPath("$.[*].comments").value(hasItem(DEFAULT_COMMENTS)))
            .andExpect(jsonPath("$.[*].datejccreate").value(hasItem(DEFAULT_DATEJCCREATE.toString())))
            .andExpect(jsonPath("$.[*].datehjrcreate").value(hasItem(DEFAULT_DATEHJRCREATE)))
            .andExpect(jsonPath("$.[*].typereceive").value(hasItem(DEFAULT_TYPERECEIVE)))
            .andExpect(jsonPath("$.[*].typecopy").value(hasItem(DEFAULT_TYPECOPY)))
            .andExpect(jsonPath("$.[*].userreceive").value(hasItem(DEFAULT_USERRECEIVE)))
            .andExpect(jsonPath("$.[*].datejcreceive").value(hasItem(DEFAULT_DATEJCRECEIVE.toString())))
            .andExpect(jsonPath("$.[*].datehjrreceive").value(hasItem(DEFAULT_DATEHJRRECEIVE)))
            .andExpect(jsonPath("$.[*].datejcaction").value(hasItem(DEFAULT_DATEJCACTION.toString())))
            .andExpect(jsonPath("$.[*].datehjraction").value(hasItem(DEFAULT_DATEHJRACTION)))
            .andExpect(jsonPath("$.[*].actiontype").value(hasItem(DEFAULT_ACTIONTYPE)))
            .andExpect(jsonPath("$.[*].savecorrespcpy").value(hasItem(DEFAULT_SAVECORRESPCPY)))
            .andExpect(jsonPath("$.[*].ordernbr").value(hasItem(DEFAULT_ORDERNBR)))
            .andExpect(jsonPath("$.[*].pagenbr").value(hasItem(DEFAULT_PAGENBR)))
            .andExpect(jsonPath("$.[*].expno").value(hasItem(DEFAULT_EXPNO)))
            .andExpect(jsonPath("$.[*].expyear").value(hasItem(DEFAULT_EXPYEAR)))
            .andExpect(jsonPath("$.[*].docyear").value(hasItem(DEFAULT_DOCYEAR)))
            .andExpect(jsonPath("$.[*].categorycorresp").value(hasItem(DEFAULT_CATEGORYCORRESP)))
            .andExpect(jsonPath("$.[*].heureaction").value(hasItem(DEFAULT_HEUREACTION)))
            .andExpect(jsonPath("$.[*].statusdeniedcopy").value(hasItem(DEFAULT_STATUSDENIEDCOPY)))
            .andExpect(jsonPath("$.[*].pagenbrpaper").value(hasItem(DEFAULT_PAGENBRPAPER)))
            .andExpect(jsonPath("$.[*].taskscan").value(hasItem(DEFAULT_TASKSCAN)))
            .andExpect(jsonPath("$.[*].pathtransto").value(hasItem(DEFAULT_PATHTRANSTO)))
            .andExpect(jsonPath("$.[*].pathtranscc").value(hasItem(DEFAULT_PATHTRANSCC)))
            .andExpect(jsonPath("$.[*].pathtranstolib").value(hasItem(DEFAULT_PATHTRANSTOLIB)))
            .andExpect(jsonPath("$.[*].pathtranscclib").value(hasItem(DEFAULT_PATHTRANSCCLIB)))
            .andExpect(jsonPath("$.[*].flggroup").value(hasItem(DEFAULT_FLGGROUP)))
            .andExpect(jsonPath("$.[*].datejcdelete").value(hasItem(DEFAULT_DATEJCDELETE.toString())))
            .andExpect(jsonPath("$.[*].datejcrevoke").value(hasItem(DEFAULT_DATEJCREVOKE.toString())))
            .andExpect(jsonPath("$.[*].datehjrrevoke").value(hasItem(DEFAULT_DATEHJRREVOKE)))
            .andExpect(jsonPath("$.[*].dateremove").value(hasItem(DEFAULT_DATEREMOVE.toString())));
    }

    @Test
    @Transactional
    void getCorrespondencecopy() throws Exception {
        // Initialize the database
        insertedCorrespondencecopy = correspondencecopyRepository.saveAndFlush(correspondencecopy);

        // Get the correspondencecopy
        restCorrespondencecopyMockMvc
            .perform(get(ENTITY_API_URL_ID, correspondencecopy.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(correspondencecopy.getId().intValue()))
            .andExpect(jsonPath("$.numcopy").value(DEFAULT_NUMCOPY))
            .andExpect(jsonPath("$.comments").value(DEFAULT_COMMENTS))
            .andExpect(jsonPath("$.datejccreate").value(DEFAULT_DATEJCCREATE.toString()))
            .andExpect(jsonPath("$.datehjrcreate").value(DEFAULT_DATEHJRCREATE))
            .andExpect(jsonPath("$.typereceive").value(DEFAULT_TYPERECEIVE))
            .andExpect(jsonPath("$.typecopy").value(DEFAULT_TYPECOPY))
            .andExpect(jsonPath("$.userreceive").value(DEFAULT_USERRECEIVE))
            .andExpect(jsonPath("$.datejcreceive").value(DEFAULT_DATEJCRECEIVE.toString()))
            .andExpect(jsonPath("$.datehjrreceive").value(DEFAULT_DATEHJRRECEIVE))
            .andExpect(jsonPath("$.datejcaction").value(DEFAULT_DATEJCACTION.toString()))
            .andExpect(jsonPath("$.datehjraction").value(DEFAULT_DATEHJRACTION))
            .andExpect(jsonPath("$.actiontype").value(DEFAULT_ACTIONTYPE))
            .andExpect(jsonPath("$.savecorrespcpy").value(DEFAULT_SAVECORRESPCPY))
            .andExpect(jsonPath("$.ordernbr").value(DEFAULT_ORDERNBR))
            .andExpect(jsonPath("$.pagenbr").value(DEFAULT_PAGENBR))
            .andExpect(jsonPath("$.expno").value(DEFAULT_EXPNO))
            .andExpect(jsonPath("$.expyear").value(DEFAULT_EXPYEAR))
            .andExpect(jsonPath("$.docyear").value(DEFAULT_DOCYEAR))
            .andExpect(jsonPath("$.categorycorresp").value(DEFAULT_CATEGORYCORRESP))
            .andExpect(jsonPath("$.heureaction").value(DEFAULT_HEUREACTION))
            .andExpect(jsonPath("$.statusdeniedcopy").value(DEFAULT_STATUSDENIEDCOPY))
            .andExpect(jsonPath("$.pagenbrpaper").value(DEFAULT_PAGENBRPAPER))
            .andExpect(jsonPath("$.taskscan").value(DEFAULT_TASKSCAN))
            .andExpect(jsonPath("$.pathtransto").value(DEFAULT_PATHTRANSTO))
            .andExpect(jsonPath("$.pathtranscc").value(DEFAULT_PATHTRANSCC))
            .andExpect(jsonPath("$.pathtranstolib").value(DEFAULT_PATHTRANSTOLIB))
            .andExpect(jsonPath("$.pathtranscclib").value(DEFAULT_PATHTRANSCCLIB))
            .andExpect(jsonPath("$.flggroup").value(DEFAULT_FLGGROUP))
            .andExpect(jsonPath("$.datejcdelete").value(DEFAULT_DATEJCDELETE.toString()))
            .andExpect(jsonPath("$.datejcrevoke").value(DEFAULT_DATEJCREVOKE.toString()))
            .andExpect(jsonPath("$.datehjrrevoke").value(DEFAULT_DATEHJRREVOKE))
            .andExpect(jsonPath("$.dateremove").value(DEFAULT_DATEREMOVE.toString()));
    }

    @Test
    @Transactional
    void getNonExistingCorrespondencecopy() throws Exception {
        // Get the correspondencecopy
        restCorrespondencecopyMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingCorrespondencecopy() throws Exception {
        // Initialize the database
        insertedCorrespondencecopy = correspondencecopyRepository.saveAndFlush(correspondencecopy);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the correspondencecopy
        Correspondencecopy updatedCorrespondencecopy = correspondencecopyRepository.findById(correspondencecopy.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedCorrespondencecopy are not directly saved in db
        em.detach(updatedCorrespondencecopy);
        updatedCorrespondencecopy
            .numcopy(UPDATED_NUMCOPY)
            .comments(UPDATED_COMMENTS)
            .datejccreate(UPDATED_DATEJCCREATE)
            .datehjrcreate(UPDATED_DATEHJRCREATE)
            .typereceive(UPDATED_TYPERECEIVE)
            .typecopy(UPDATED_TYPECOPY)
            .userreceive(UPDATED_USERRECEIVE)
            .datejcreceive(UPDATED_DATEJCRECEIVE)
            .datehjrreceive(UPDATED_DATEHJRRECEIVE)
            .datejcaction(UPDATED_DATEJCACTION)
            .datehjraction(UPDATED_DATEHJRACTION)
            .actiontype(UPDATED_ACTIONTYPE)
            .savecorrespcpy(UPDATED_SAVECORRESPCPY)
            .ordernbr(UPDATED_ORDERNBR)
            .pagenbr(UPDATED_PAGENBR)
            .expno(UPDATED_EXPNO)
            .expyear(UPDATED_EXPYEAR)
            .docyear(UPDATED_DOCYEAR)
            .categorycorresp(UPDATED_CATEGORYCORRESP)
            .heureaction(UPDATED_HEUREACTION)
            .statusdeniedcopy(UPDATED_STATUSDENIEDCOPY)
            .pagenbrpaper(UPDATED_PAGENBRPAPER)
            .taskscan(UPDATED_TASKSCAN)
            .pathtransto(UPDATED_PATHTRANSTO)
            .pathtranscc(UPDATED_PATHTRANSCC)
            .pathtranstolib(UPDATED_PATHTRANSTOLIB)
            .pathtranscclib(UPDATED_PATHTRANSCCLIB)
            .flggroup(UPDATED_FLGGROUP)
            .datejcdelete(UPDATED_DATEJCDELETE)
            .datejcrevoke(UPDATED_DATEJCREVOKE)
            .datehjrrevoke(UPDATED_DATEHJRREVOKE)
            .dateremove(UPDATED_DATEREMOVE);
        CorrespondencecopyDTO correspondencecopyDTO = correspondencecopyMapper.toDto(updatedCorrespondencecopy);

        restCorrespondencecopyMockMvc
            .perform(
                put(ENTITY_API_URL_ID, correspondencecopyDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(correspondencecopyDTO))
            )
            .andExpect(status().isOk());

        // Validate the Correspondencecopy in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedCorrespondencecopyToMatchAllProperties(updatedCorrespondencecopy);
    }

    @Test
    @Transactional
    void putNonExistingCorrespondencecopy() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        correspondencecopy.setId(longCount.incrementAndGet());

        // Create the Correspondencecopy
        CorrespondencecopyDTO correspondencecopyDTO = correspondencecopyMapper.toDto(correspondencecopy);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restCorrespondencecopyMockMvc
            .perform(
                put(ENTITY_API_URL_ID, correspondencecopyDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(correspondencecopyDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Correspondencecopy in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchCorrespondencecopy() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        correspondencecopy.setId(longCount.incrementAndGet());

        // Create the Correspondencecopy
        CorrespondencecopyDTO correspondencecopyDTO = correspondencecopyMapper.toDto(correspondencecopy);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restCorrespondencecopyMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(correspondencecopyDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Correspondencecopy in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamCorrespondencecopy() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        correspondencecopy.setId(longCount.incrementAndGet());

        // Create the Correspondencecopy
        CorrespondencecopyDTO correspondencecopyDTO = correspondencecopyMapper.toDto(correspondencecopy);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restCorrespondencecopyMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(correspondencecopyDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Correspondencecopy in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateCorrespondencecopyWithPatch() throws Exception {
        // Initialize the database
        insertedCorrespondencecopy = correspondencecopyRepository.saveAndFlush(correspondencecopy);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the correspondencecopy using partial update
        Correspondencecopy partialUpdatedCorrespondencecopy = new Correspondencecopy();
        partialUpdatedCorrespondencecopy.setId(correspondencecopy.getId());

        partialUpdatedCorrespondencecopy
            .datejccreate(UPDATED_DATEJCCREATE)
            .typecopy(UPDATED_TYPECOPY)
            .userreceive(UPDATED_USERRECEIVE)
            .datehjraction(UPDATED_DATEHJRACTION)
            .savecorrespcpy(UPDATED_SAVECORRESPCPY)
            .ordernbr(UPDATED_ORDERNBR)
            .pagenbr(UPDATED_PAGENBR)
            .expno(UPDATED_EXPNO)
            .expyear(UPDATED_EXPYEAR)
            .docyear(UPDATED_DOCYEAR)
            .heureaction(UPDATED_HEUREACTION)
            .pagenbrpaper(UPDATED_PAGENBRPAPER)
            .taskscan(UPDATED_TASKSCAN)
            .pathtransto(UPDATED_PATHTRANSTO)
            .pathtranstolib(UPDATED_PATHTRANSTOLIB)
            .pathtranscclib(UPDATED_PATHTRANSCCLIB)
            .flggroup(UPDATED_FLGGROUP)
            .datejcdelete(UPDATED_DATEJCDELETE)
            .datejcrevoke(UPDATED_DATEJCREVOKE);

        restCorrespondencecopyMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedCorrespondencecopy.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedCorrespondencecopy))
            )
            .andExpect(status().isOk());

        // Validate the Correspondencecopy in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertCorrespondencecopyUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedCorrespondencecopy, correspondencecopy),
            getPersistedCorrespondencecopy(correspondencecopy)
        );
    }

    @Test
    @Transactional
    void fullUpdateCorrespondencecopyWithPatch() throws Exception {
        // Initialize the database
        insertedCorrespondencecopy = correspondencecopyRepository.saveAndFlush(correspondencecopy);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the correspondencecopy using partial update
        Correspondencecopy partialUpdatedCorrespondencecopy = new Correspondencecopy();
        partialUpdatedCorrespondencecopy.setId(correspondencecopy.getId());

        partialUpdatedCorrespondencecopy
            .numcopy(UPDATED_NUMCOPY)
            .comments(UPDATED_COMMENTS)
            .datejccreate(UPDATED_DATEJCCREATE)
            .datehjrcreate(UPDATED_DATEHJRCREATE)
            .typereceive(UPDATED_TYPERECEIVE)
            .typecopy(UPDATED_TYPECOPY)
            .userreceive(UPDATED_USERRECEIVE)
            .datejcreceive(UPDATED_DATEJCRECEIVE)
            .datehjrreceive(UPDATED_DATEHJRRECEIVE)
            .datejcaction(UPDATED_DATEJCACTION)
            .datehjraction(UPDATED_DATEHJRACTION)
            .actiontype(UPDATED_ACTIONTYPE)
            .savecorrespcpy(UPDATED_SAVECORRESPCPY)
            .ordernbr(UPDATED_ORDERNBR)
            .pagenbr(UPDATED_PAGENBR)
            .expno(UPDATED_EXPNO)
            .expyear(UPDATED_EXPYEAR)
            .docyear(UPDATED_DOCYEAR)
            .categorycorresp(UPDATED_CATEGORYCORRESP)
            .heureaction(UPDATED_HEUREACTION)
            .statusdeniedcopy(UPDATED_STATUSDENIEDCOPY)
            .pagenbrpaper(UPDATED_PAGENBRPAPER)
            .taskscan(UPDATED_TASKSCAN)
            .pathtransto(UPDATED_PATHTRANSTO)
            .pathtranscc(UPDATED_PATHTRANSCC)
            .pathtranstolib(UPDATED_PATHTRANSTOLIB)
            .pathtranscclib(UPDATED_PATHTRANSCCLIB)
            .flggroup(UPDATED_FLGGROUP)
            .datejcdelete(UPDATED_DATEJCDELETE)
            .datejcrevoke(UPDATED_DATEJCREVOKE)
            .datehjrrevoke(UPDATED_DATEHJRREVOKE)
            .dateremove(UPDATED_DATEREMOVE);

        restCorrespondencecopyMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedCorrespondencecopy.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedCorrespondencecopy))
            )
            .andExpect(status().isOk());

        // Validate the Correspondencecopy in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertCorrespondencecopyUpdatableFieldsEquals(
            partialUpdatedCorrespondencecopy,
            getPersistedCorrespondencecopy(partialUpdatedCorrespondencecopy)
        );
    }

    @Test
    @Transactional
    void patchNonExistingCorrespondencecopy() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        correspondencecopy.setId(longCount.incrementAndGet());

        // Create the Correspondencecopy
        CorrespondencecopyDTO correspondencecopyDTO = correspondencecopyMapper.toDto(correspondencecopy);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restCorrespondencecopyMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, correspondencecopyDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(correspondencecopyDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Correspondencecopy in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchCorrespondencecopy() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        correspondencecopy.setId(longCount.incrementAndGet());

        // Create the Correspondencecopy
        CorrespondencecopyDTO correspondencecopyDTO = correspondencecopyMapper.toDto(correspondencecopy);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restCorrespondencecopyMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(correspondencecopyDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Correspondencecopy in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamCorrespondencecopy() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        correspondencecopy.setId(longCount.incrementAndGet());

        // Create the Correspondencecopy
        CorrespondencecopyDTO correspondencecopyDTO = correspondencecopyMapper.toDto(correspondencecopy);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restCorrespondencecopyMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(correspondencecopyDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Correspondencecopy in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteCorrespondencecopy() throws Exception {
        // Initialize the database
        insertedCorrespondencecopy = correspondencecopyRepository.saveAndFlush(correspondencecopy);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the correspondencecopy
        restCorrespondencecopyMockMvc
            .perform(delete(ENTITY_API_URL_ID, correspondencecopy.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return correspondencecopyRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Correspondencecopy getPersistedCorrespondencecopy(Correspondencecopy correspondencecopy) {
        return correspondencecopyRepository.findById(correspondencecopy.getId()).orElseThrow();
    }

    protected void assertPersistedCorrespondencecopyToMatchAllProperties(Correspondencecopy expectedCorrespondencecopy) {
        assertCorrespondencecopyAllPropertiesEquals(expectedCorrespondencecopy, getPersistedCorrespondencecopy(expectedCorrespondencecopy));
    }

    protected void assertPersistedCorrespondencecopyToMatchUpdatableProperties(Correspondencecopy expectedCorrespondencecopy) {
        assertCorrespondencecopyAllUpdatablePropertiesEquals(
            expectedCorrespondencecopy,
            getPersistedCorrespondencecopy(expectedCorrespondencecopy)
        );
    }
}
