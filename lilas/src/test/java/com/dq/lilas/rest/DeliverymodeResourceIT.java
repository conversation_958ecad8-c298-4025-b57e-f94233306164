package com.dq.lilas.rest;

import static com.dq.lilas.domain.DeliverymodeAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.Deliverymode;
import com.dq.lilas.repository.DeliverymodeRepository;
import com.dq.lilas.service.dto.DeliverymodeDTO;
import com.dq.lilas.service.mapper.DeliverymodeMapper;
import com.dq.lilas.web.rest.DeliverymodeResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link DeliverymodeResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class DeliverymodeResourceIT {

    private static final String DEFAULT_STATUT = "A";
    private static final String UPDATED_STATUT = "B";

    private static final String DEFAULT_ORDERPOS = "AAAAA";
    private static final String UPDATED_ORDERPOS = "BBBBB";

    private static final String ENTITY_API_URL = "/api/deliverymodes";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private DeliverymodeRepository deliverymodeRepository;

    @Autowired
    private DeliverymodeMapper deliverymodeMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restDeliverymodeMockMvc;

    private Deliverymode deliverymode;

    private Deliverymode insertedDeliverymode;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Deliverymode createEntity() {
        return new Deliverymode().statut(DEFAULT_STATUT).orderpos(DEFAULT_ORDERPOS);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static Deliverymode createUpdatedEntity() {
        return new Deliverymode().statut(UPDATED_STATUT).orderpos(UPDATED_ORDERPOS);
    }

    @BeforeEach
    void initTest() {
        deliverymode = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedDeliverymode != null) {
            deliverymodeRepository.delete(insertedDeliverymode);
            insertedDeliverymode = null;
        }
    }

    @Test
    @Transactional
    void createDeliverymode() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the Deliverymode
        DeliverymodeDTO deliverymodeDTO = deliverymodeMapper.toDto(deliverymode);
        var returnedDeliverymodeDTO = om.readValue(
            restDeliverymodeMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(deliverymodeDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            DeliverymodeDTO.class
        );

        // Validate the Deliverymode in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedDeliverymode = deliverymodeMapper.toEntity(returnedDeliverymodeDTO);
        assertDeliverymodeUpdatableFieldsEquals(returnedDeliverymode, getPersistedDeliverymode(returnedDeliverymode));

        insertedDeliverymode = returnedDeliverymode;
    }

    @Test
    @Transactional
    void createDeliverymodeWithExistingId() throws Exception {
        // Create the Deliverymode with an existing ID
        deliverymode.setId(1L);
        DeliverymodeDTO deliverymodeDTO = deliverymodeMapper.toDto(deliverymode);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restDeliverymodeMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(deliverymodeDTO)))
            .andExpect(status().isBadRequest());

        // Validate the Deliverymode in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllDeliverymodes() throws Exception {
        // Initialize the database
        insertedDeliverymode = deliverymodeRepository.saveAndFlush(deliverymode);

        // Get all the deliverymodeList
        restDeliverymodeMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(deliverymode.getId().intValue())))
            .andExpect(jsonPath("$.[*].statut").value(hasItem(DEFAULT_STATUT)))
            .andExpect(jsonPath("$.[*].orderpos").value(hasItem(DEFAULT_ORDERPOS)));
    }

    @Test
    @Transactional
    void getDeliverymode() throws Exception {
        // Initialize the database
        insertedDeliverymode = deliverymodeRepository.saveAndFlush(deliverymode);

        // Get the deliverymode
        restDeliverymodeMockMvc
            .perform(get(ENTITY_API_URL_ID, deliverymode.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(deliverymode.getId().intValue()))
            .andExpect(jsonPath("$.statut").value(DEFAULT_STATUT))
            .andExpect(jsonPath("$.orderpos").value(DEFAULT_ORDERPOS));
    }

    @Test
    @Transactional
    void getNonExistingDeliverymode() throws Exception {
        // Get the deliverymode
        restDeliverymodeMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingDeliverymode() throws Exception {
        // Initialize the database
        insertedDeliverymode = deliverymodeRepository.saveAndFlush(deliverymode);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the deliverymode
        Deliverymode updatedDeliverymode = deliverymodeRepository.findById(deliverymode.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedDeliverymode are not directly saved in db
        em.detach(updatedDeliverymode);
        updatedDeliverymode.statut(UPDATED_STATUT).orderpos(UPDATED_ORDERPOS);
        DeliverymodeDTO deliverymodeDTO = deliverymodeMapper.toDto(updatedDeliverymode);

        restDeliverymodeMockMvc
            .perform(
                put(ENTITY_API_URL_ID, deliverymodeDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(deliverymodeDTO))
            )
            .andExpect(status().isOk());

        // Validate the Deliverymode in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedDeliverymodeToMatchAllProperties(updatedDeliverymode);
    }

    @Test
    @Transactional
    void putNonExistingDeliverymode() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        deliverymode.setId(longCount.incrementAndGet());

        // Create the Deliverymode
        DeliverymodeDTO deliverymodeDTO = deliverymodeMapper.toDto(deliverymode);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restDeliverymodeMockMvc
            .perform(
                put(ENTITY_API_URL_ID, deliverymodeDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(deliverymodeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Deliverymode in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchDeliverymode() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        deliverymode.setId(longCount.incrementAndGet());

        // Create the Deliverymode
        DeliverymodeDTO deliverymodeDTO = deliverymodeMapper.toDto(deliverymode);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDeliverymodeMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(deliverymodeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Deliverymode in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamDeliverymode() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        deliverymode.setId(longCount.incrementAndGet());

        // Create the Deliverymode
        DeliverymodeDTO deliverymodeDTO = deliverymodeMapper.toDto(deliverymode);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDeliverymodeMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(deliverymodeDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Deliverymode in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateDeliverymodeWithPatch() throws Exception {
        // Initialize the database
        insertedDeliverymode = deliverymodeRepository.saveAndFlush(deliverymode);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the deliverymode using partial update
        Deliverymode partialUpdatedDeliverymode = new Deliverymode();
        partialUpdatedDeliverymode.setId(deliverymode.getId());

        partialUpdatedDeliverymode.orderpos(UPDATED_ORDERPOS);

        restDeliverymodeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedDeliverymode.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedDeliverymode))
            )
            .andExpect(status().isOk());

        // Validate the Deliverymode in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertDeliverymodeUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedDeliverymode, deliverymode),
            getPersistedDeliverymode(deliverymode)
        );
    }

    @Test
    @Transactional
    void fullUpdateDeliverymodeWithPatch() throws Exception {
        // Initialize the database
        insertedDeliverymode = deliverymodeRepository.saveAndFlush(deliverymode);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the deliverymode using partial update
        Deliverymode partialUpdatedDeliverymode = new Deliverymode();
        partialUpdatedDeliverymode.setId(deliverymode.getId());

        partialUpdatedDeliverymode.statut(UPDATED_STATUT).orderpos(UPDATED_ORDERPOS);

        restDeliverymodeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedDeliverymode.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedDeliverymode))
            )
            .andExpect(status().isOk());

        // Validate the Deliverymode in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertDeliverymodeUpdatableFieldsEquals(partialUpdatedDeliverymode, getPersistedDeliverymode(partialUpdatedDeliverymode));
    }

    @Test
    @Transactional
    void patchNonExistingDeliverymode() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        deliverymode.setId(longCount.incrementAndGet());

        // Create the Deliverymode
        DeliverymodeDTO deliverymodeDTO = deliverymodeMapper.toDto(deliverymode);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restDeliverymodeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, deliverymodeDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(deliverymodeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Deliverymode in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchDeliverymode() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        deliverymode.setId(longCount.incrementAndGet());

        // Create the Deliverymode
        DeliverymodeDTO deliverymodeDTO = deliverymodeMapper.toDto(deliverymode);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDeliverymodeMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(deliverymodeDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the Deliverymode in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamDeliverymode() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        deliverymode.setId(longCount.incrementAndGet());

        // Create the Deliverymode
        DeliverymodeDTO deliverymodeDTO = deliverymodeMapper.toDto(deliverymode);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDeliverymodeMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(deliverymodeDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the Deliverymode in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteDeliverymode() throws Exception {
        // Initialize the database
        insertedDeliverymode = deliverymodeRepository.saveAndFlush(deliverymode);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the deliverymode
        restDeliverymodeMockMvc
            .perform(delete(ENTITY_API_URL_ID, deliverymode.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return deliverymodeRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected Deliverymode getPersistedDeliverymode(Deliverymode deliverymode) {
        return deliverymodeRepository.findById(deliverymode.getId()).orElseThrow();
    }

    protected void assertPersistedDeliverymodeToMatchAllProperties(Deliverymode expectedDeliverymode) {
        assertDeliverymodeAllPropertiesEquals(expectedDeliverymode, getPersistedDeliverymode(expectedDeliverymode));
    }

    protected void assertPersistedDeliverymodeToMatchUpdatableProperties(Deliverymode expectedDeliverymode) {
        assertDeliverymodeAllUpdatablePropertiesEquals(expectedDeliverymode, getPersistedDeliverymode(expectedDeliverymode));
    }
}
