package com.dq.lilas.rest;

import static com.dq.lilas.domain.DailyBatchesAsserts.*;
import static com.dq.lilas.rest.TestUtil.createUpdateProxyForBean;
import static org.assertj.core.api.Assertions.assertThat;
import static org.hamcrest.Matchers.hasItem;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.*;

import com.dq.lilas.IntegrationTest;
import com.dq.lilas.domain.DailyBatches;
import com.dq.lilas.repository.DailyBatchesRepository;
import com.dq.lilas.service.dto.DailyBatchesDTO;
import com.dq.lilas.service.mapper.DailyBatchesMapper;
import com.dq.lilas.web.rest.DailyBatchesResource;
import com.fasterxml.jackson.databind.ObjectMapper;
import jakarta.persistence.EntityManager;
import java.time.Instant;
import java.time.temporal.ChronoUnit;
import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.http.MediaType;
import org.springframework.security.test.context.support.WithMockUser;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.transaction.annotation.Transactional;

/**
 * Integration tests for the {@link DailyBatchesResource} REST controller.
 */
@IntegrationTest
@AutoConfigureMockMvc
@WithMockUser
class DailyBatchesResourceIT {

    private static final Instant DEFAULT_BATCH_DATE = Instant.ofEpochMilli(0L);
    private static final Instant UPDATED_BATCH_DATE = Instant.now().truncatedTo(ChronoUnit.MILLIS);

    private static final String ENTITY_API_URL = "/api/daily-batches";
    private static final String ENTITY_API_URL_ID = ENTITY_API_URL + "/{id}";

    private static Random random = new Random();
    private static AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    @Autowired
    private ObjectMapper om;

    @Autowired
    private DailyBatchesRepository dailyBatchesRepository;

    @Autowired
    private DailyBatchesMapper dailyBatchesMapper;

    @Autowired
    private EntityManager em;

    @Autowired
    private MockMvc restDailyBatchesMockMvc;

    private DailyBatches dailyBatches;

    private DailyBatches insertedDailyBatches;

    /**
     * Create an entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static DailyBatches createEntity() {
        return new DailyBatches().batchDate(DEFAULT_BATCH_DATE);
    }

    /**
     * Create an updated entity for this test.
     *
     * This is a static method, as tests for other entities might also need it,
     * if they test an entity which requires the current entity.
     */
    public static DailyBatches createUpdatedEntity() {
        return new DailyBatches().batchDate(UPDATED_BATCH_DATE);
    }

    @BeforeEach
    void initTest() {
        dailyBatches = createEntity();
    }

    @AfterEach
    void cleanup() {
        if (insertedDailyBatches != null) {
            dailyBatchesRepository.delete(insertedDailyBatches);
            insertedDailyBatches = null;
        }
    }

    @Test
    @Transactional
    void createDailyBatches() throws Exception {
        long databaseSizeBeforeCreate = getRepositoryCount();
        // Create the DailyBatches
        DailyBatchesDTO dailyBatchesDTO = dailyBatchesMapper.toDto(dailyBatches);
        var returnedDailyBatchesDTO = om.readValue(
            restDailyBatchesMockMvc
                .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(dailyBatchesDTO)))
                .andExpect(status().isCreated())
                .andReturn()
                .getResponse()
                .getContentAsString(),
            DailyBatchesDTO.class
        );

        // Validate the DailyBatches in the database
        assertIncrementedRepositoryCount(databaseSizeBeforeCreate);
        var returnedDailyBatches = dailyBatchesMapper.toEntity(returnedDailyBatchesDTO);
        assertDailyBatchesUpdatableFieldsEquals(returnedDailyBatches, getPersistedDailyBatches(returnedDailyBatches));

        insertedDailyBatches = returnedDailyBatches;
    }

    @Test
    @Transactional
    void createDailyBatchesWithExistingId() throws Exception {
        // Create the DailyBatches with an existing ID
        dailyBatches.setId(1L);
        DailyBatchesDTO dailyBatchesDTO = dailyBatchesMapper.toDto(dailyBatches);

        long databaseSizeBeforeCreate = getRepositoryCount();

        // An entity with an existing ID cannot be created, so this API call must fail
        restDailyBatchesMockMvc
            .perform(post(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(dailyBatchesDTO)))
            .andExpect(status().isBadRequest());

        // Validate the DailyBatches in the database
        assertSameRepositoryCount(databaseSizeBeforeCreate);
    }

    @Test
    @Transactional
    void getAllDailyBatches() throws Exception {
        // Initialize the database
        insertedDailyBatches = dailyBatchesRepository.saveAndFlush(dailyBatches);

        // Get all the dailyBatchesList
        restDailyBatchesMockMvc
            .perform(get(ENTITY_API_URL + "?sort=id,desc"))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.[*].id").value(hasItem(dailyBatches.getId().intValue())))
            .andExpect(jsonPath("$.[*].batchDate").value(hasItem(DEFAULT_BATCH_DATE.toString())));
    }

    @Test
    @Transactional
    void getDailyBatches() throws Exception {
        // Initialize the database
        insertedDailyBatches = dailyBatchesRepository.saveAndFlush(dailyBatches);

        // Get the dailyBatches
        restDailyBatchesMockMvc
            .perform(get(ENTITY_API_URL_ID, dailyBatches.getId()))
            .andExpect(status().isOk())
            .andExpect(content().contentType(MediaType.APPLICATION_JSON_VALUE))
            .andExpect(jsonPath("$.id").value(dailyBatches.getId().intValue()))
            .andExpect(jsonPath("$.batchDate").value(DEFAULT_BATCH_DATE.toString()));
    }

    @Test
    @Transactional
    void getNonExistingDailyBatches() throws Exception {
        // Get the dailyBatches
        restDailyBatchesMockMvc.perform(get(ENTITY_API_URL_ID, Long.MAX_VALUE)).andExpect(status().isNotFound());
    }

    @Test
    @Transactional
    void putExistingDailyBatches() throws Exception {
        // Initialize the database
        insertedDailyBatches = dailyBatchesRepository.saveAndFlush(dailyBatches);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the dailyBatches
        DailyBatches updatedDailyBatches = dailyBatchesRepository.findById(dailyBatches.getId()).orElseThrow();
        // Disconnect from session so that the updates on updatedDailyBatches are not directly saved in db
        em.detach(updatedDailyBatches);
        updatedDailyBatches.batchDate(UPDATED_BATCH_DATE);
        DailyBatchesDTO dailyBatchesDTO = dailyBatchesMapper.toDto(updatedDailyBatches);

        restDailyBatchesMockMvc
            .perform(
                put(ENTITY_API_URL_ID, dailyBatchesDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(dailyBatchesDTO))
            )
            .andExpect(status().isOk());

        // Validate the DailyBatches in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertPersistedDailyBatchesToMatchAllProperties(updatedDailyBatches);
    }

    @Test
    @Transactional
    void putNonExistingDailyBatches() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        dailyBatches.setId(longCount.incrementAndGet());

        // Create the DailyBatches
        DailyBatchesDTO dailyBatchesDTO = dailyBatchesMapper.toDto(dailyBatches);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restDailyBatchesMockMvc
            .perform(
                put(ENTITY_API_URL_ID, dailyBatchesDTO.getId())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(dailyBatchesDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the DailyBatches in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithIdMismatchDailyBatches() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        dailyBatches.setId(longCount.incrementAndGet());

        // Create the DailyBatches
        DailyBatchesDTO dailyBatchesDTO = dailyBatchesMapper.toDto(dailyBatches);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDailyBatchesMockMvc
            .perform(
                put(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType(MediaType.APPLICATION_JSON)
                    .content(om.writeValueAsBytes(dailyBatchesDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the DailyBatches in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void putWithMissingIdPathParamDailyBatches() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        dailyBatches.setId(longCount.incrementAndGet());

        // Create the DailyBatches
        DailyBatchesDTO dailyBatchesDTO = dailyBatchesMapper.toDto(dailyBatches);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDailyBatchesMockMvc
            .perform(put(ENTITY_API_URL).contentType(MediaType.APPLICATION_JSON).content(om.writeValueAsBytes(dailyBatchesDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the DailyBatches in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void partialUpdateDailyBatchesWithPatch() throws Exception {
        // Initialize the database
        insertedDailyBatches = dailyBatchesRepository.saveAndFlush(dailyBatches);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the dailyBatches using partial update
        DailyBatches partialUpdatedDailyBatches = new DailyBatches();
        partialUpdatedDailyBatches.setId(dailyBatches.getId());

        restDailyBatchesMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedDailyBatches.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedDailyBatches))
            )
            .andExpect(status().isOk());

        // Validate the DailyBatches in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertDailyBatchesUpdatableFieldsEquals(
            createUpdateProxyForBean(partialUpdatedDailyBatches, dailyBatches),
            getPersistedDailyBatches(dailyBatches)
        );
    }

    @Test
    @Transactional
    void fullUpdateDailyBatchesWithPatch() throws Exception {
        // Initialize the database
        insertedDailyBatches = dailyBatchesRepository.saveAndFlush(dailyBatches);

        long databaseSizeBeforeUpdate = getRepositoryCount();

        // Update the dailyBatches using partial update
        DailyBatches partialUpdatedDailyBatches = new DailyBatches();
        partialUpdatedDailyBatches.setId(dailyBatches.getId());

        partialUpdatedDailyBatches.batchDate(UPDATED_BATCH_DATE);

        restDailyBatchesMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, partialUpdatedDailyBatches.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(partialUpdatedDailyBatches))
            )
            .andExpect(status().isOk());

        // Validate the DailyBatches in the database

        assertSameRepositoryCount(databaseSizeBeforeUpdate);
        assertDailyBatchesUpdatableFieldsEquals(partialUpdatedDailyBatches, getPersistedDailyBatches(partialUpdatedDailyBatches));
    }

    @Test
    @Transactional
    void patchNonExistingDailyBatches() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        dailyBatches.setId(longCount.incrementAndGet());

        // Create the DailyBatches
        DailyBatchesDTO dailyBatchesDTO = dailyBatchesMapper.toDto(dailyBatches);

        // If the entity doesn't have an ID, it will throw BadRequestAlertException
        restDailyBatchesMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, dailyBatchesDTO.getId())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(dailyBatchesDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the DailyBatches in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithIdMismatchDailyBatches() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        dailyBatches.setId(longCount.incrementAndGet());

        // Create the DailyBatches
        DailyBatchesDTO dailyBatchesDTO = dailyBatchesMapper.toDto(dailyBatches);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDailyBatchesMockMvc
            .perform(
                patch(ENTITY_API_URL_ID, longCount.incrementAndGet())
                    .contentType("application/merge-patch+json")
                    .content(om.writeValueAsBytes(dailyBatchesDTO))
            )
            .andExpect(status().isBadRequest());

        // Validate the DailyBatches in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void patchWithMissingIdPathParamDailyBatches() throws Exception {
        long databaseSizeBeforeUpdate = getRepositoryCount();
        dailyBatches.setId(longCount.incrementAndGet());

        // Create the DailyBatches
        DailyBatchesDTO dailyBatchesDTO = dailyBatchesMapper.toDto(dailyBatches);

        // If url ID doesn't match entity ID, it will throw BadRequestAlertException
        restDailyBatchesMockMvc
            .perform(patch(ENTITY_API_URL).contentType("application/merge-patch+json").content(om.writeValueAsBytes(dailyBatchesDTO)))
            .andExpect(status().isMethodNotAllowed());

        // Validate the DailyBatches in the database
        assertSameRepositoryCount(databaseSizeBeforeUpdate);
    }

    @Test
    @Transactional
    void deleteDailyBatches() throws Exception {
        // Initialize the database
        insertedDailyBatches = dailyBatchesRepository.saveAndFlush(dailyBatches);

        long databaseSizeBeforeDelete = getRepositoryCount();

        // Delete the dailyBatches
        restDailyBatchesMockMvc
            .perform(delete(ENTITY_API_URL_ID, dailyBatches.getId()).accept(MediaType.APPLICATION_JSON))
            .andExpect(status().isNoContent());

        // Validate the database contains one less item
        assertDecrementedRepositoryCount(databaseSizeBeforeDelete);
    }

    protected long getRepositoryCount() {
        return dailyBatchesRepository.count();
    }

    protected void assertIncrementedRepositoryCount(long countBefore) {
        assertThat(countBefore + 1).isEqualTo(getRepositoryCount());
    }

    protected void assertDecrementedRepositoryCount(long countBefore) {
        assertThat(countBefore - 1).isEqualTo(getRepositoryCount());
    }

    protected void assertSameRepositoryCount(long countBefore) {
        assertThat(countBefore).isEqualTo(getRepositoryCount());
    }

    protected DailyBatches getPersistedDailyBatches(DailyBatches dailyBatches) {
        return dailyBatchesRepository.findById(dailyBatches.getId()).orElseThrow();
    }

    protected void assertPersistedDailyBatchesToMatchAllProperties(DailyBatches expectedDailyBatches) {
        assertDailyBatchesAllPropertiesEquals(expectedDailyBatches, getPersistedDailyBatches(expectedDailyBatches));
    }

    protected void assertPersistedDailyBatchesToMatchUpdatableProperties(DailyBatches expectedDailyBatches) {
        assertDailyBatchesAllUpdatablePropertiesEquals(expectedDailyBatches, getPersistedDailyBatches(expectedDailyBatches));
    }
}
