package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class JoblangTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static Joblang getJoblangSample1() {
        return new Joblang().id(1L).lbl("lbl1").lang("lang1").abr("abr1");
    }

    public static Joblang getJoblangSample2() {
        return new Joblang().id(2L).lbl("lbl2").lang("lang2").abr("abr2");
    }

    public static Joblang getJoblangRandomSampleGenerator() {
        return new Joblang()
            .id(longCount.incrementAndGet())
            .lbl(UUID.randomUUID().toString())
            .lang(UUID.randomUUID().toString())
            .abr(UUID.randomUUID().toString());
    }
}
