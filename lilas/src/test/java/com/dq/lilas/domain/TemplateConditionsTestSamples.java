package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class TemplateConditionsTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static TemplateConditions getTemplateConditionsSample1() {
        return new TemplateConditions().id(1L).name("name1");
    }

    public static TemplateConditions getTemplateConditionsSample2() {
        return new TemplateConditions().id(2L).name("name2");
    }

    public static TemplateConditions getTemplateConditionsRandomSampleGenerator() {
        return new TemplateConditions().id(longCount.incrementAndGet()).name(UUID.randomUUID().toString());
    }
}
