package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class OrderDetailsTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static OrderDetails getOrderDetailsSample1() {
        return new OrderDetails()
            .id(1L)
            .discount(1)
            .updatedDiscount(1)
            .barcode("barcode1")
            .updatedBarcode("updatedBarcode1")
            .internalCode("internalCode1")
            .updatedInternalCode("updatedInternalCode1");
    }

    public static OrderDetails getOrderDetailsSample2() {
        return new OrderDetails()
            .id(2L)
            .discount(2)
            .updatedDiscount(2)
            .barcode("barcode2")
            .updatedBarcode("updatedBarcode2")
            .internalCode("internalCode2")
            .updatedInternalCode("updatedInternalCode2");
    }

    public static OrderDetails getOrderDetailsRandomSampleGenerator() {
        return new OrderDetails()
            .id(longCount.incrementAndGet())
            .discount(intCount.incrementAndGet())
            .updatedDiscount(intCount.incrementAndGet())
            .barcode(UUID.randomUUID().toString())
            .updatedBarcode(UUID.randomUUID().toString())
            .internalCode(UUID.randomUUID().toString())
            .updatedInternalCode(UUID.randomUUID().toString());
    }
}
