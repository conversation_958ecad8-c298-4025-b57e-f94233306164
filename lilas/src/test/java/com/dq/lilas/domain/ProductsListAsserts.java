package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class ProductsListAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertProductsListAllPropertiesEquals(ProductsList expected, ProductsList actual) {
        assertProductsListAutoGeneratedPropertiesEquals(expected, actual);
        assertProductsListAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertProductsListAllUpdatablePropertiesEquals(ProductsList expected, ProductsList actual) {
        assertProductsListUpdatableFieldsEquals(expected, actual);
        assertProductsListUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertProductsListAutoGeneratedPropertiesEquals(ProductsList expected, ProductsList actual) {
        assertThat(actual)
            .as("Verify ProductsList auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertProductsListUpdatableFieldsEquals(ProductsList expected, ProductsList actual) {
        assertThat(actual)
            .as("Verify ProductsList relevant properties")
            .satisfies(a -> assertThat(a.getInternalCode()).as("check internalCode").isEqualTo(expected.getInternalCode()))
            .satisfies(a -> assertThat(a.getBarcode()).as("check barcode").isEqualTo(expected.getBarcode()))
            .satisfies(a -> assertThat(a.getQadName()).as("check qadName").isEqualTo(expected.getQadName()))
            .satisfies(a -> assertThat(a.getCategory()).as("check category").isEqualTo(expected.getCategory()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertProductsListUpdatableRelationshipsEquals(ProductsList expected, ProductsList actual) {
        // empty method
    }
}
