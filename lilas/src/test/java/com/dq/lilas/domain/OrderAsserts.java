package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class OrderAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertOrderAllPropertiesEquals(Order expected, Order actual) {
        assertOrderAutoGeneratedPropertiesEquals(expected, actual);
        assertOrderAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertOrderAllUpdatablePropertiesEquals(Order expected, Order actual) {
        assertOrderUpdatableFieldsEquals(expected, actual);
        assertOrderUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertOrderAutoGeneratedPropertiesEquals(Order expected, Order actual) {
        assertThat(actual)
            .as("Verify Order auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertOrderUpdatableFieldsEquals(Order expected, Order actual) {
        assertThat(actual)
            .as("Verify Order relevant properties")
            .satisfies(a -> assertThat(a.getHearderJson()).as("check hearderJson").isEqualTo(expected.getHearderJson()))
            .satisfies(a -> assertThat(a.getRank()).as("check rank").isEqualTo(expected.getRank()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getFooterJson()).as("check footerJson").isEqualTo(expected.getFooterJson()))
            .satisfies(a -> assertThat(a.getLocked()).as("check locked").isEqualTo(expected.getLocked()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertOrderUpdatableRelationshipsEquals(Order expected, Order actual) {
        assertThat(actual)
            .as("Verify Order relationships")
            .satisfies(a -> assertThat(a.getDailyBatches()).as("check dailyBatches").isEqualTo(expected.getDailyBatches()))
            .satisfies(a -> assertThat(a.getGmsClients()).as("check gmsClients").isEqualTo(expected.getGmsClients()))
            .satisfies(a -> assertThat(a.getTemplateConditions()).as("check templateConditions").isEqualTo(expected.getTemplateConditions())
            )
            .satisfies(a -> assertThat(a.getMails()).as("check mails").isEqualTo(expected.getMails()));
    }
}
