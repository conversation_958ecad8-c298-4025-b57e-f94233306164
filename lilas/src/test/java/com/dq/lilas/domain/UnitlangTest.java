package com.dq.lilas.domain;

import static com.dq.lilas.domain.UnitTestSamples.*;
import static com.dq.lilas.domain.UnitlangTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import org.junit.jupiter.api.Test;

class UnitlangTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Unitlang.class);
        Unitlang unitlang1 = getUnitlangSample1();
        Unitlang unitlang2 = new Unitlang();
        assertThat(unitlang1).isNotEqualTo(unitlang2);

        unitlang2.setId(unitlang1.getId());
        assertThat(unitlang1).isEqualTo(unitlang2);

        unitlang2 = getUnitlangSample2();
        assertThat(unitlang1).isNotEqualTo(unitlang2);
    }

    @Test
    void unitTest() {
        Unitlang unitlang = getUnitlangRandomSampleGenerator();
        Unit unitBack = getUnitRandomSampleGenerator();

        unitlang.setUnit(unitBack);
        assertThat(unitlang.getUnit()).isEqualTo(unitBack);

        unitlang.unit(null);
        assertThat(unitlang.getUnit()).isNull();
    }
}
