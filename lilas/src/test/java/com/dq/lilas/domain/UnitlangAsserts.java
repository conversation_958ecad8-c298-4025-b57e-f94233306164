package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class UnitlangAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUnitlangAllPropertiesEquals(Unitlang expected, Unitlang actual) {
        assertUnitlangAutoGeneratedPropertiesEquals(expected, actual);
        assertUnitlangAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUnitlangAllUpdatablePropertiesEquals(Unitlang expected, Unitlang actual) {
        assertUnitlangUpdatableFieldsEquals(expected, actual);
        assertUnitlangUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUnitlangAutoGeneratedPropertiesEquals(Unitlang expected, Unitlang actual) {
        assertThat(actual)
            .as("Verify Unitlang auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUnitlangUpdatableFieldsEquals(Unitlang expected, Unitlang actual) {
        assertThat(actual)
            .as("Verify Unitlang relevant properties")
            .satisfies(a -> assertThat(a.getAddress()).as("check address").isEqualTo(expected.getAddress()))
            .satisfies(a -> assertThat(a.getNbr()).as("check nbr").isEqualTo(expected.getNbr()))
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getLang()).as("check lang").isEqualTo(expected.getLang()))
            .satisfies(a -> assertThat(a.getAbrv()).as("check abrv").isEqualTo(expected.getAbrv()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUnitlangUpdatableRelationshipsEquals(Unitlang expected, Unitlang actual) {
        assertThat(actual)
            .as("Verify Unitlang relationships")
            .satisfies(a -> assertThat(a.getUnit()).as("check unit").isEqualTo(expected.getUnit()));
    }
}
