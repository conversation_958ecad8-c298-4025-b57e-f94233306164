package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class AttachementAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAttachementAllPropertiesEquals(Attachement expected, Attachement actual) {
        assertAttachementAutoGeneratedPropertiesEquals(expected, actual);
        assertAttachementAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAttachementAllUpdatablePropertiesEquals(Attachement expected, Attachement actual) {
        assertAttachementUpdatableFieldsEquals(expected, actual);
        assertAttachementUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAttachementAutoGeneratedPropertiesEquals(Attachement expected, Attachement actual) {
        assertThat(actual)
            .as("Verify Attachement auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAttachementUpdatableFieldsEquals(Attachement expected, Attachement actual) {
        assertThat(actual)
            .as("Verify Attachement relevant properties")
            .satisfies(a -> assertThat(a.getCopyId()).as("check copyId").isEqualTo(expected.getCopyId()))
            .satisfies(a -> assertThat(a.getLblAttachment()).as("check lblAttachment").isEqualTo(expected.getLblAttachment()))
            .satisfies(a -> assertThat(a.getIdDocAttachment()).as("check idDocAttachment").isEqualTo(expected.getIdDocAttachment()))
            .satisfies(a -> assertThat(a.getSizeAttachement()).as("check sizeAttachement").isEqualTo(expected.getSizeAttachement()))
            .satisfies(a -> assertThat(a.getFilenameattachment()).as("check filenameattachment").isEqualTo(expected.getFilenameattachment())
            )
            .satisfies(a -> assertThat(a.getUserattachment()).as("check userattachment").isEqualTo(expected.getUserattachment()))
            .satisfies(a -> assertThat(a.getIddecision()).as("check iddecision").isEqualTo(expected.getIddecision()))
            .satisfies(a -> assertThat(a.getIdtemplate()).as("check idtemplate").isEqualTo(expected.getIdtemplate()))
            .satisfies(a -> assertThat(a.getDatejcattachment()).as("check datejcattachment").isEqualTo(expected.getDatejcattachment()))
            .satisfies(a -> assertThat(a.getDatehjrattachment()).as("check datehjrattachment").isEqualTo(expected.getDatehjrattachment()))
            .satisfies(a -> assertThat(a.getIdtransfer()).as("check idtransfer").isEqualTo(expected.getIdtransfer()))
            .satisfies(a -> assertThat(a.getIdcorresp()).as("check idcorresp").isEqualTo(expected.getIdcorresp()))
            .satisfies(a -> assertThat(a.getOrdering()).as("check ordering").isEqualTo(expected.getOrdering()))
            .satisfies(a -> assertThat(a.getLevelattachement()).as("check levelattachement").isEqualTo(expected.getLevelattachement()))
            .satisfies(a -> assertThat(a.getIdreq()).as("check idreq").isEqualTo(expected.getIdreq()))
            .satisfies(a -> assertThat(a.getOrderingscan()).as("check orderingscan").isEqualTo(expected.getOrderingscan()))
            .satisfies(a -> assertThat(a.getIddocext()).as("check iddocext").isEqualTo(expected.getIddocext()))
            .satisfies(a -> assertThat(a.getIdleave()).as("check idleave").isEqualTo(expected.getIdleave()))
            .satisfies(a -> assertThat(a.getConfigLevel()).as("check configLevel").isEqualTo(expected.getConfigLevel()))
            .satisfies(a -> assertThat(a.getTypeAtach()).as("check typeAtach").isEqualTo(expected.getTypeAtach()))
            .satisfies(a -> assertThat(a.getIdDirectOrder()).as("check idDirectOrder").isEqualTo(expected.getIdDirectOrder()))
            .satisfies(a -> assertThat(a.getPathFile()).as("check pathFile").isEqualTo(expected.getPathFile()))
            .satisfies(a -> assertThat(a.getVersion()).as("check version").isEqualTo(expected.getVersion()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertAttachementUpdatableRelationshipsEquals(Attachement expected, Attachement actual) {
        assertThat(actual)
            .as("Verify Attachement relationships")
            .satisfies(a -> assertThat(a.getOrder()).as("check order").isEqualTo(expected.getOrder()));
    }
}
