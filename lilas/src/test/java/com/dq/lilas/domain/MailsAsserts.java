package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class MailsAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertMailsAllPropertiesEquals(Mails expected, Mails actual) {
        assertMailsAutoGeneratedPropertiesEquals(expected, actual);
        assertMailsAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertMailsAllUpdatablePropertiesEquals(Mails expected, Mails actual) {
        assertMailsUpdatableFieldsEquals(expected, actual);
        assertMailsUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertMailsAutoGeneratedPropertiesEquals(Mails expected, Mails actual) {
        assertThat(actual)
            .as("Verify Mails auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertMailsUpdatableFieldsEquals(Mails expected, Mails actual) {
        assertThat(actual)
            .as("Verify Mails relevant properties")
            .satisfies(a -> assertThat(a.getMaildate()).as("check maildate").isEqualTo(expected.getMaildate()))
            .satisfies(a -> assertThat(a.getSubject()).as("check subject").isEqualTo(expected.getSubject()))
            .satisfies(a -> assertThat(a.getMailbody()).as("check mailbody").isEqualTo(expected.getMailbody()))
            .satisfies(a -> assertThat(a.getRecipient()).as("check recipient").isEqualTo(expected.getRecipient()))
            .satisfies(a -> assertThat(a.getSender()).as("check sender").isEqualTo(expected.getSender()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertMailsUpdatableRelationshipsEquals(Mails expected, Mails actual) {
        // empty method
    }
}
