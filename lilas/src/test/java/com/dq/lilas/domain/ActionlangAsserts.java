package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class ActionlangAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertActionlangAllPropertiesEquals(Actionlang expected, Actionlang actual) {
        assertActionlangAutoGeneratedPropertiesEquals(expected, actual);
        assertActionlangAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertActionlangAllUpdatablePropertiesEquals(Actionlang expected, Actionlang actual) {
        assertActionlangUpdatableFieldsEquals(expected, actual);
        assertActionlangUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertActionlangAutoGeneratedPropertiesEquals(Actionlang expected, Actionlang actual) {
        assertThat(actual)
            .as("Verify Actionlang auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertActionlangUpdatableFieldsEquals(Actionlang expected, Actionlang actual) {
        assertThat(actual)
            .as("Verify Actionlang relevant properties")
            .satisfies(a -> assertThat(a.getAppType()).as("check appType").isEqualTo(expected.getAppType()))
            .satisfies(a -> assertThat(a.getLang()).as("check lang").isEqualTo(expected.getLang()))
            .satisfies(a -> assertThat(a.getAbrv()).as("check abrv").isEqualTo(expected.getAbrv()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertActionlangUpdatableRelationshipsEquals(Actionlang expected, Actionlang actual) {
        assertThat(actual)
            .as("Verify Actionlang relationships")
            .satisfies(a -> assertThat(a.getAction()).as("check action").isEqualTo(expected.getAction()));
    }
}
