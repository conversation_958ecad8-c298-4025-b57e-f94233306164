package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class CorrespondencecopyTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static Correspondencecopy getCorrespondencecopySample1() {
        return new Correspondencecopy()
            .id(1L)
            .numcopy("numcopy1")
            .comments("comments1")
            .datehjrcreate("datehjrcreate1")
            .typereceive("typereceive1")
            .typecopy("typecopy1")
            .userreceive("userreceive1")
            .datehjrreceive("datehjrreceive1")
            .datehjraction("datehjraction1")
            .actiontype("actiontype1")
            .savecorrespcpy("savecorrespcpy1")
            .ordernbr("ordernbr1")
            .pagenbr("pagenbr1")
            .expno("expno1")
            .expyear("expyear1")
            .docyear("docyear1")
            .categorycorresp("categorycorresp1")
            .heureaction("heureaction1")
            .statusdeniedcopy("statusdeniedcopy1")
            .pagenbrpaper("pagenbrpaper1")
            .taskscan("taskscan1")
            .pathtransto("pathtransto1")
            .pathtranscc("pathtranscc1")
            .pathtranstolib("pathtranstolib1")
            .pathtranscclib("pathtranscclib1")
            .flggroup("flggroup1")
            .datehjrrevoke("datehjrrevoke1");
    }

    public static Correspondencecopy getCorrespondencecopySample2() {
        return new Correspondencecopy()
            .id(2L)
            .numcopy("numcopy2")
            .comments("comments2")
            .datehjrcreate("datehjrcreate2")
            .typereceive("typereceive2")
            .typecopy("typecopy2")
            .userreceive("userreceive2")
            .datehjrreceive("datehjrreceive2")
            .datehjraction("datehjraction2")
            .actiontype("actiontype2")
            .savecorrespcpy("savecorrespcpy2")
            .ordernbr("ordernbr2")
            .pagenbr("pagenbr2")
            .expno("expno2")
            .expyear("expyear2")
            .docyear("docyear2")
            .categorycorresp("categorycorresp2")
            .heureaction("heureaction2")
            .statusdeniedcopy("statusdeniedcopy2")
            .pagenbrpaper("pagenbrpaper2")
            .taskscan("taskscan2")
            .pathtransto("pathtransto2")
            .pathtranscc("pathtranscc2")
            .pathtranstolib("pathtranstolib2")
            .pathtranscclib("pathtranscclib2")
            .flggroup("flggroup2")
            .datehjrrevoke("datehjrrevoke2");
    }

    public static Correspondencecopy getCorrespondencecopyRandomSampleGenerator() {
        return new Correspondencecopy()
            .id(longCount.incrementAndGet())
            .numcopy(UUID.randomUUID().toString())
            .comments(UUID.randomUUID().toString())
            .datehjrcreate(UUID.randomUUID().toString())
            .typereceive(UUID.randomUUID().toString())
            .typecopy(UUID.randomUUID().toString())
            .userreceive(UUID.randomUUID().toString())
            .datehjrreceive(UUID.randomUUID().toString())
            .datehjraction(UUID.randomUUID().toString())
            .actiontype(UUID.randomUUID().toString())
            .savecorrespcpy(UUID.randomUUID().toString())
            .ordernbr(UUID.randomUUID().toString())
            .pagenbr(UUID.randomUUID().toString())
            .expno(UUID.randomUUID().toString())
            .expyear(UUID.randomUUID().toString())
            .docyear(UUID.randomUUID().toString())
            .categorycorresp(UUID.randomUUID().toString())
            .heureaction(UUID.randomUUID().toString())
            .statusdeniedcopy(UUID.randomUUID().toString())
            .pagenbrpaper(UUID.randomUUID().toString())
            .taskscan(UUID.randomUUID().toString())
            .pathtransto(UUID.randomUUID().toString())
            .pathtranscc(UUID.randomUUID().toString())
            .pathtranstolib(UUID.randomUUID().toString())
            .pathtranscclib(UUID.randomUUID().toString())
            .flggroup(UUID.randomUUID().toString())
            .datehjrrevoke(UUID.randomUUID().toString());
    }
}
