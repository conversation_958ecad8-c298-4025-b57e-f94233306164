package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class EmployeelangTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static Employeelang getEmployeelangSample1() {
        return new Employeelang().id(1L).fullname("fullname1").address("address1").lang("lang1").matricule("matricule1");
    }

    public static Employeelang getEmployeelangSample2() {
        return new Employeelang().id(2L).fullname("fullname2").address("address2").lang("lang2").matricule("matricule2");
    }

    public static Employeelang getEmployeelangRandomSampleGenerator() {
        return new Employeelang()
            .id(longCount.incrementAndGet())
            .fullname(UUID.randomUUID().toString())
            .address(UUID.randomUUID().toString())
            .lang(UUID.randomUUID().toString())
            .matricule(UUID.randomUUID().toString());
    }
}
