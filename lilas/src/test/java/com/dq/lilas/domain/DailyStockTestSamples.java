package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class DailyStockTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static DailyStock getDailyStockSample1() {
        return new DailyStock()
            .id(1L)
            .internalCode("internalCode1")
            .barcode("barcode1")
            .productName("productName1")
            .stockQty(1L)
            .remainingQuantity(1L);
    }

    public static DailyStock getDailyStockSample2() {
        return new DailyStock()
            .id(2L)
            .internalCode("internalCode2")
            .barcode("barcode2")
            .productName("productName2")
            .stockQty(2L)
            .remainingQuantity(2L);
    }

    public static DailyStock getDailyStockRandomSampleGenerator() {
        return new DailyStock()
            .id(longCount.incrementAndGet())
            .internalCode(UUID.randomUUID().toString())
            .barcode(UUID.randomUUID().toString())
            .productName(UUID.randomUUID().toString())
            .stockQty(longCount.incrementAndGet())
            .remainingQuantity(longCount.incrementAndGet());
    }
}
