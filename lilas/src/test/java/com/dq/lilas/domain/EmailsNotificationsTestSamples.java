package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class EmailsNotificationsTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static EmailsNotifications getEmailsNotificationsSample1() {
        return new EmailsNotifications().id(1L).subject("subject1").recipient(1L).sender("sender1");
    }

    public static EmailsNotifications getEmailsNotificationsSample2() {
        return new EmailsNotifications().id(2L).subject("subject2").recipient(2L).sender("sender2");
    }

    public static EmailsNotifications getEmailsNotificationsRandomSampleGenerator() {
        return new EmailsNotifications()
            .id(longCount.incrementAndGet())
            .subject(UUID.randomUUID().toString())
            .recipient(longCount.incrementAndGet())
            .sender(UUID.randomUUID().toString());
    }
}
