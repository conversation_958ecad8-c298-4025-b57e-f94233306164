package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class EmployeelangAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeelangAllPropertiesEquals(Employeelang expected, Employeelang actual) {
        assertEmployeelangAutoGeneratedPropertiesEquals(expected, actual);
        assertEmployeelangAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeelangAllUpdatablePropertiesEquals(Employeelang expected, Employeelang actual) {
        assertEmployeelangUpdatableFieldsEquals(expected, actual);
        assertEmployeelangUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeelangAutoGeneratedPropertiesEquals(Employeelang expected, Employeelang actual) {
        assertThat(actual)
            .as("Verify Employeelang auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeelangUpdatableFieldsEquals(Employeelang expected, Employeelang actual) {
        assertThat(actual)
            .as("Verify Employeelang relevant properties")
            .satisfies(a -> assertThat(a.getFullname()).as("check fullname").isEqualTo(expected.getFullname()))
            .satisfies(a -> assertThat(a.getAddress()).as("check address").isEqualTo(expected.getAddress()))
            .satisfies(a -> assertThat(a.getLang()).as("check lang").isEqualTo(expected.getLang()))
            .satisfies(a -> assertThat(a.getMatricule()).as("check matricule").isEqualTo(expected.getMatricule()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeelangUpdatableRelationshipsEquals(Employeelang expected, Employeelang actual) {
        assertThat(actual)
            .as("Verify Employeelang relationships")
            .satisfies(a -> assertThat(a.getEmployee()).as("check employee").isEqualTo(expected.getEmployee()));
    }
}
