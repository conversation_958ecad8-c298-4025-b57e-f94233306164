package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class ActionAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertActionAllPropertiesEquals(Action expected, Action actual) {
        assertActionAutoGeneratedPropertiesEquals(expected, actual);
        assertActionAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertActionAllUpdatablePropertiesEquals(Action expected, Action actual) {
        assertActionUpdatableFieldsEquals(expected, actual);
        assertActionUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertActionAutoGeneratedPropertiesEquals(Action expected, Action actual) {
        assertThat(actual)
            .as("Verify Action auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertActionUpdatableFieldsEquals(Action expected, Action actual) {
        assertThat(actual)
            .as("Verify Action relevant properties")
            .satisfies(a -> assertThat(a.getRegulation()).as("check regulation").isEqualTo(expected.getRegulation()))
            .satisfies(a -> assertThat(a.getCircular()).as("check circular").isEqualTo(expected.getCircular()))
            .satisfies(a -> assertThat(a.getAppOrder()).as("check appOrder").isEqualTo(expected.getAppOrder()))
            .satisfies(a -> assertThat(a.getStatut()).as("check statut").isEqualTo(expected.getStatut()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertActionUpdatableRelationshipsEquals(Action expected, Action actual) {
        // empty method
    }
}
