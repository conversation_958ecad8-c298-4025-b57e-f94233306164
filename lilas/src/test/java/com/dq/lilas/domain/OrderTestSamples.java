package com.dq.lilas.domain;

import com.dq.lilas.domain.enumeration.OrderStatus;
import java.util.Random;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class OrderTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static Order getOrderSample1() {
        return new Order().id(1L).rank(1).status(OrderStatus.WAITING).locked(false).orderNumber("order-1");
    }

    public static Order getOrderSample2() {
        return new Order().id(2L).rank(2).status(OrderStatus.VERIFIED).locked(true).orderNumber("order-2");
    }

    public static Order getOrderRandomSampleGenerator() {
        return new Order()
            .id(longCount.incrementAndGet())
            .rank(intCount.incrementAndGet())
            .status(OrderStatus.values()[random.nextInt(OrderStatus.values().length)])
            .locked(random.nextBoolean())
            .orderNumber("order-" + longCount.incrementAndGet());
    }
}
