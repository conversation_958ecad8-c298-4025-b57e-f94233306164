package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class DailyBatchesAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDailyBatchesAllPropertiesEquals(DailyBatches expected, DailyBatches actual) {
        assertDailyBatchesAutoGeneratedPropertiesEquals(expected, actual);
        assertDailyBatchesAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDailyBatchesAllUpdatablePropertiesEquals(DailyBatches expected, DailyBatches actual) {
        assertDailyBatchesUpdatableFieldsEquals(expected, actual);
        assertDailyBatchesUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDailyBatchesAutoGeneratedPropertiesEquals(DailyBatches expected, DailyBatches actual) {
        assertThat(actual)
            .as("Verify DailyBatches auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDailyBatchesUpdatableFieldsEquals(DailyBatches expected, DailyBatches actual) {
        assertThat(actual)
            .as("Verify DailyBatches relevant properties")
            .satisfies(a -> assertThat(a.getBatchDate()).as("check batchDate").isEqualTo(expected.getBatchDate()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDailyBatchesUpdatableRelationshipsEquals(DailyBatches expected, DailyBatches actual) {
        assertThat(actual)
            .as("Verify DailyBatches relationships")
            .satisfies(a -> assertThat(a.getCompany()).as("check company").isEqualTo(expected.getCompany()));
    }
}
