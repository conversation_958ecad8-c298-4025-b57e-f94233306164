package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class EmployeeAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeAllPropertiesEquals(Employee expected, Employee actual) {
        assertEmployeeAutoGeneratedPropertiesEquals(expected, actual);
        assertEmployeeAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeAllUpdatablePropertiesEquals(Employee expected, Employee actual) {
        assertEmployeeUpdatableFieldsEquals(expected, actual);
        assertEmployeeUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeAutoGeneratedPropertiesEquals(Employee expected, Employee actual) {
        assertThat(actual)
            .as("Verify Employee auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeUpdatableFieldsEquals(Employee expected, Employee actual) {
        assertThat(actual)
            .as("Verify Employee relevant properties")
            .satisfies(a -> assertThat(a.getTel()).as("check tel").isEqualTo(expected.getTel()))
            .satisfies(a -> assertThat(a.getFax()).as("check fax").isEqualTo(expected.getFax()))
            .satisfies(a -> assertThat(a.getMail()).as("check mail").isEqualTo(expected.getMail()))
            .satisfies(a -> assertThat(a.getConfidentiel()).as("check confidentiel").isEqualTo(expected.getConfidentiel()))
            .satisfies(a -> assertThat(a.getNumidentity()).as("check numidentity").isEqualTo(expected.getNumidentity()))
            .satisfies(a -> assertThat(a.getEmpnumber()).as("check empnumber").isEqualTo(expected.getEmpnumber()))
            .satisfies(a -> assertThat(a.getFullname()).as("check fullname").isEqualTo(expected.getFullname()))
            .satisfies(a -> assertThat(a.getAddress()).as("check address").isEqualTo(expected.getAddress()))
            .satisfies(a -> assertThat(a.getMatricule()).as("check matricule").isEqualTo(expected.getMatricule()))
            .satisfies(a -> assertThat(a.getUpscale()).as("check upscale").isEqualTo(expected.getUpscale()))
            .satisfies(a -> assertThat(a.getActive()).as("check active").isEqualTo(expected.getActive()))
            .satisfies(a -> assertThat(a.getStatut()).as("check statut").isEqualTo(expected.getStatut()))
            .satisfies(a -> assertThat(a.getGender()).as("check gender").isEqualTo(expected.getGender()))
            .satisfies(a -> assertThat(a.getAvatar()).as("check avatar").isEqualTo(expected.getAvatar()))
            .satisfies(a -> assertThat(a.getFilenameparaf()).as("check filenameparaf").isEqualTo(expected.getFilenameparaf()))
            .satisfies(a -> assertThat(a.getFilenamesign()).as("check filenamesign").isEqualTo(expected.getFilenamesign()))
            .satisfies(a -> assertThat(a.getCoursier()).as("check coursier").isEqualTo(expected.getCoursier()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeUpdatableRelationshipsEquals(Employee expected, Employee actual) {
        assertThat(actual)
            .as("Verify Employee relationships")
            .satisfies(a -> assertThat(a.getJob()).as("check job").isEqualTo(expected.getJob()))
            .satisfies(a -> assertThat(a.getUnit()).as("check unit").isEqualTo(expected.getUnit()));
    }
}
