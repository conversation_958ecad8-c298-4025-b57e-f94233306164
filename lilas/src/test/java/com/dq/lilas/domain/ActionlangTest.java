package com.dq.lilas.domain;

import static com.dq.lilas.domain.ActionTestSamples.*;
import static com.dq.lilas.domain.ActionlangTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import org.junit.jupiter.api.Test;

class ActionlangTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Actionlang.class);
        Actionlang actionlang1 = getActionlangSample1();
        Actionlang actionlang2 = new Actionlang();
        assertThat(actionlang1).isNotEqualTo(actionlang2);

        actionlang2.setId(actionlang1.getId());
        assertThat(actionlang1).isEqualTo(actionlang2);

        actionlang2 = getActionlangSample2();
        assertThat(actionlang1).isNotEqualTo(actionlang2);
    }

    @Test
    void actionTest() {
        Actionlang actionlang = getActionlangRandomSampleGenerator();
        Action actionBack = getActionRandomSampleGenerator();

        actionlang.setAction(actionBack);
        assertThat(actionlang.getAction()).isEqualTo(actionBack);

        actionlang.action(null);
        assertThat(actionlang.getAction()).isNull();
    }
}
