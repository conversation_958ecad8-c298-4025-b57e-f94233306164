package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class GmsClientsTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static GmsClients getGmsClientsSample1() {
        return new GmsClients().id(1L).clientName("clientName1").code("code1").classe("classe1").ville("ville1").fiscaleId("fiscaleId1");
    }

    public static GmsClients getGmsClientsSample2() {
        return new GmsClients().id(2L).clientName("clientName2").code("code2").classe("classe2").ville("ville2").fiscaleId("fiscaleId2");
    }

    public static GmsClients getGmsClientsRandomSampleGenerator() {
        return new GmsClients()
            .id(longCount.incrementAndGet())
            .clientName(UUID.randomUUID().toString())
            .code(UUID.randomUUID().toString())
            .classe(UUID.randomUUID().toString())
            .ville(UUID.randomUUID().toString())
            .fiscaleId(UUID.randomUUID().toString());
    }
}
