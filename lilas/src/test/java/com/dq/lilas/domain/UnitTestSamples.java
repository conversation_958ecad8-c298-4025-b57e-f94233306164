package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class UnitTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static Unit getUnitSample1() {
        return new Unit()
            .id(1L)
            .parentid("parentid1")
            .tel("tel1")
            .fax("fax1")
            .order(1)
            .deanstatus("deanstatus1")
            .address("address1")
            .nbr("nbr1")
            .name("name1")
            .lang("lang1")
            .abbreviated("abbreviated1")
            .mail("mail1")
            .checkenabled("checkenabled1")
            .activate("activate1")
            .active("active1")
            .level("level1")
            .grp("grp1")
            .compid("compid1")
            .branch("branch1")
            .regoffice("regoffice1")
            .unitgroup("unitgroup1")
            .grdparent("grdparent1")
            .status("status1")
            .category("category1")
            .functionUnit("functionUnit1")
            .position("position1")
            .section("section1")
            .categdir("categdir1")
            .categUnit("categUnit1")
            .function("function1")
            .responsible("responsible1");
    }

    public static Unit getUnitSample2() {
        return new Unit()
            .id(2L)
            .parentid("parentid2")
            .tel("tel2")
            .fax("fax2")
            .order(2)
            .deanstatus("deanstatus2")
            .address("address2")
            .nbr("nbr2")
            .name("name2")
            .lang("lang2")
            .abbreviated("abbreviated2")
            .mail("mail2")
            .checkenabled("checkenabled2")
            .activate("activate2")
            .active("active2")
            .level("level2")
            .grp("grp2")
            .compid("compid2")
            .branch("branch2")
            .regoffice("regoffice2")
            .unitgroup("unitgroup2")
            .grdparent("grdparent2")
            .status("status2")
            .category("category2")
            .functionUnit("functionUnit2")
            .position("position2")
            .section("section2")
            .categdir("categdir2")
            .categUnit("categUnit2")
            .function("function2")
            .responsible("responsible2");
    }

    public static Unit getUnitRandomSampleGenerator() {
        return new Unit()
            .id(longCount.incrementAndGet())
            .parentid(UUID.randomUUID().toString())
            .tel(UUID.randomUUID().toString())
            .fax(UUID.randomUUID().toString())
            .order(intCount.incrementAndGet())
            .deanstatus(UUID.randomUUID().toString())
            .address(UUID.randomUUID().toString())
            .nbr(UUID.randomUUID().toString())
            .name(UUID.randomUUID().toString())
            .lang(UUID.randomUUID().toString())
            .abbreviated(UUID.randomUUID().toString())
            .mail(UUID.randomUUID().toString())
            .checkenabled(UUID.randomUUID().toString())
            .activate(UUID.randomUUID().toString())
            .active(UUID.randomUUID().toString())
            .level(UUID.randomUUID().toString())
            .grp(UUID.randomUUID().toString())
            .compid(UUID.randomUUID().toString())
            .branch(UUID.randomUUID().toString())
            .regoffice(UUID.randomUUID().toString())
            .unitgroup(UUID.randomUUID().toString())
            .grdparent(UUID.randomUUID().toString())
            .status(UUID.randomUUID().toString())
            .category(UUID.randomUUID().toString())
            .functionUnit(UUID.randomUUID().toString())
            .position(UUID.randomUUID().toString())
            .section(UUID.randomUUID().toString())
            .categdir(UUID.randomUUID().toString())
            .categUnit(UUID.randomUUID().toString())
            .function(UUID.randomUUID().toString())
            .responsible(UUID.randomUUID().toString());
    }
}
