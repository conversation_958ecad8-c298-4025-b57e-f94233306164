package com.dq.lilas.domain;

import static com.dq.lilas.domain.AssertUtils.zonedDataTimeSameInstant;
import static org.assertj.core.api.Assertions.assertThat;

public class CorrespondenceAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCorrespondenceAllPropertiesEquals(Correspondence expected, Correspondence actual) {
        assertCorrespondenceAutoGeneratedPropertiesEquals(expected, actual);
        assertCorrespondenceAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCorrespondenceAllUpdatablePropertiesEquals(Correspondence expected, Correspondence actual) {
        assertCorrespondenceUpdatableFieldsEquals(expected, actual);
        assertCorrespondenceUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCorrespondenceAutoGeneratedPropertiesEquals(Correspondence expected, Correspondence actual) {
        assertThat(actual)
            .as("Verify Correspondence auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCorrespondenceUpdatableFieldsEquals(Correspondence expected, Correspondence actual) {
        assertThat(actual)
            .as("Verify Correspondence relevant properties")
            .satisfies(a -> assertThat(a.getOrdernbr()).as("check ordernbr").isEqualTo(expected.getOrdernbr()))
            .satisfies(a -> assertThat(a.getNumcopy()).as("check numcopy").isEqualTo(expected.getNumcopy()))
            .satisfies(a -> assertThat(a.getDoclink()).as("check doclink").isEqualTo(expected.getDoclink()))
            .satisfies(a -> assertThat(a.getPagenbr()).as("check pagenbr").isEqualTo(expected.getPagenbr()))
            .satisfies(a -> assertThat(a.getHighlevel()).as("check highlevel").isEqualTo(expected.getHighlevel()))
            .satisfies(a -> assertThat(a.getConfidentiel()).as("check confidentiel").isEqualTo(expected.getConfidentiel()))
            .satisfies(a -> assertThat(a.getPriority()).as("check priority").isEqualTo(expected.getPriority()))
            .satisfies(a -> assertThat(a.getObs()).as("check obs").isEqualTo(expected.getObs()))
            .satisfies(a -> assertThat(a.getCategory()).as("check category").isEqualTo(expected.getCategory()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a ->
                assertThat(a.getDatejc()).as("check datejc").usingComparator(zonedDataTimeSameInstant).isEqualTo(expected.getDatejc())
            )
            .satisfies(a ->
                assertThat(a.getDatejcsend())
                    .as("check datejcsend")
                    .usingComparator(zonedDataTimeSameInstant)
                    .isEqualTo(expected.getDatejcsend())
            )
            .satisfies(a -> assertThat(a.getTypereceive()).as("check typereceive").isEqualTo(expected.getTypereceive()))
            .satisfies(a -> assertThat(a.getTypecopy()).as("check typecopy").isEqualTo(expected.getTypecopy()))
            .satisfies(a -> assertThat(a.getRefsnd()).as("check refsnd").isEqualTo(expected.getRefsnd()))
            .satisfies(a -> assertThat(a.getText()).as("check text").isEqualTo(expected.getText()))
            .satisfies(a -> assertThat(a.getDocyear()).as("check docyear").isEqualTo(expected.getDocyear()))
            .satisfies(a -> assertThat(a.getDatehjrsave()).as("check datehjrsave").isEqualTo(expected.getDatehjrsave()))
            .satisfies(a ->
                assertThat(a.getDatejcsave())
                    .as("check datejcsave")
                    .usingComparator(zonedDataTimeSameInstant)
                    .isEqualTo(expected.getDatejcsave())
            )
            .satisfies(a -> assertThat(a.getOldOrderNumber()).as("check oldOrderNumber").isEqualTo(expected.getOldOrderNumber()))
            .satisfies(a -> assertThat(a.getSubject()).as("check subject").isEqualTo(expected.getSubject()))
            .satisfies(a -> assertThat(a.getAttach()).as("check attach").isEqualTo(expected.getAttach()))
            .satisfies(a -> assertThat(a.getOrdernbradrsbook()).as("check ordernbradrsbook").isEqualTo(expected.getOrdernbradrsbook()))
            .satisfies(a -> assertThat(a.getSeqkeyadrsbook()).as("check seqkeyadrsbook").isEqualTo(expected.getSeqkeyadrsbook()))
            .satisfies(a -> assertThat(a.getUseradrsbook()).as("check useradrsbook").isEqualTo(expected.getUseradrsbook()))
            .satisfies(a -> assertThat(a.getCityzenncard()).as("check cityzenncard").isEqualTo(expected.getCityzenncard()))
            .satisfies(a -> assertThat(a.getCityzenphone()).as("check cityzenphone").isEqualTo(expected.getCityzenphone()))
            .satisfies(a -> assertThat(a.getSms()).as("check sms").isEqualTo(expected.getSms()))
            .satisfies(a -> assertThat(a.getIncident()).as("check incident").isEqualTo(expected.getIncident()))
            .satisfies(a -> assertThat(a.getCheckFavorite()).as("check checkFavorite").isEqualTo(expected.getCheckFavorite()))
            .satisfies(a -> assertThat(a.getCompanyId()).as("check companyId").isEqualTo(expected.getCompanyId()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCorrespondenceUpdatableRelationshipsEquals(Correspondence expected, Correspondence actual) {
        assertThat(actual)
            .as("Verify Correspondence relationships")
            .satisfies(a -> assertThat(a.getEmployee()).as("check employee").isEqualTo(expected.getEmployee()))
            .satisfies(a -> assertThat(a.getUnit()).as("check unit").isEqualTo(expected.getUnit()))
            .satisfies(a -> assertThat(a.getDeliverymode()).as("check deliverymode").isEqualTo(expected.getDeliverymode()))
            .satisfies(a -> assertThat(a.getTypecorrespondence()).as("check typecorrespondence").isEqualTo(expected.getTypecorrespondence())
            );
    }
}
