package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class DemandePromotionAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDemandePromotionAllPropertiesEquals(DemandePromotion expected, DemandePromotion actual) {
        assertDemandePromotionAutoGeneratedPropertiesEquals(expected, actual);
        assertDemandePromotionAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDemandePromotionAllUpdatablePropertiesEquals(DemandePromotion expected, DemandePromotion actual) {
        assertDemandePromotionUpdatableFieldsEquals(expected, actual);
        assertDemandePromotionUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDemandePromotionAutoGeneratedPropertiesEquals(DemandePromotion expected, DemandePromotion actual) {
        assertThat(actual)
            .as("Verify DemandePromotion auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDemandePromotionUpdatableFieldsEquals(DemandePromotion expected, DemandePromotion actual) {
        assertThat(actual)
            .as("Verify DemandePromotion relevant properties")
            .satisfies(a -> assertThat(a.getCodeClient()).as("check codeClient").isEqualTo(expected.getCodeClient()))
            .satisfies(a -> assertThat(a.getEnseigne()).as("check enseigne").isEqualTo(expected.getEnseigne()))
            .satisfies(a -> assertThat(a.getAction()).as("check action").isEqualTo(expected.getAction()))
            .satisfies(a ->
                assertThat(a.getPeriodPromotionStart()).as("check periodPromotionStart").isEqualTo(expected.getPeriodPromotionStart())
            )
            .satisfies(a -> assertThat(a.getPeriodPromotionEnd()).as("check periodPromotionEnd").isEqualTo(expected.getPeriodPromotionEnd())
            )
            .satisfies(a ->
                assertThat(a.getPeriodFacturationStart()).as("check periodFacturationStart").isEqualTo(expected.getPeriodFacturationStart())
            )
            .satisfies(a ->
                assertThat(a.getPeriodFacturationEnd()).as("check periodFacturationEnd").isEqualTo(expected.getPeriodFacturationEnd())
            );
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDemandePromotionUpdatableRelationshipsEquals(DemandePromotion expected, DemandePromotion actual) {
        assertThat(actual)
            .as("Verify DemandePromotion relationships")
            .satisfies(a -> assertThat(a.getEmployee()).as("check employee").isEqualTo(expected.getEmployee()))
            .satisfies(a -> assertThat(a.getPromotionDetails()).as("check promotionDetails").isEqualTo(expected.getPromotionDetails()));
    }
}
