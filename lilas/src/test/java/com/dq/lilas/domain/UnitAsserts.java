package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class UnitAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUnitAllPropertiesEquals(Unit expected, Unit actual) {
        assertUnitAutoGeneratedPropertiesEquals(expected, actual);
        assertUnitAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUnitAllUpdatablePropertiesEquals(Unit expected, Unit actual) {
        assertUnitUpdatableFieldsEquals(expected, actual);
        assertUnitUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUnitAutoGeneratedPropertiesEquals(Unit expected, Unit actual) {
        assertThat(actual)
            .as("Verify Unit auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUnitUpdatableFieldsEquals(Unit expected, Unit actual) {
        assertThat(actual)
            .as("Verify Unit relevant properties")
            .satisfies(a -> assertThat(a.getParentid()).as("check parentid").isEqualTo(expected.getParentid()))
            .satisfies(a -> assertThat(a.getTel()).as("check tel").isEqualTo(expected.getTel()))
            .satisfies(a -> assertThat(a.getFax()).as("check fax").isEqualTo(expected.getFax()))
            .satisfies(a -> assertThat(a.getOrder()).as("check order").isEqualTo(expected.getOrder()))
            .satisfies(a -> assertThat(a.getDeanstatus()).as("check deanstatus").isEqualTo(expected.getDeanstatus()))
            .satisfies(a -> assertThat(a.getAddress()).as("check address").isEqualTo(expected.getAddress()))
            .satisfies(a -> assertThat(a.getNbr()).as("check nbr").isEqualTo(expected.getNbr()))
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getLang()).as("check lang").isEqualTo(expected.getLang()))
            .satisfies(a -> assertThat(a.getAbbreviated()).as("check abbreviated").isEqualTo(expected.getAbbreviated()))
            .satisfies(a -> assertThat(a.getMail()).as("check mail").isEqualTo(expected.getMail()))
            .satisfies(a -> assertThat(a.getCheckenabled()).as("check checkenabled").isEqualTo(expected.getCheckenabled()))
            .satisfies(a -> assertThat(a.getActivate()).as("check activate").isEqualTo(expected.getActivate()))
            .satisfies(a -> assertThat(a.getActive()).as("check active").isEqualTo(expected.getActive()))
            .satisfies(a -> assertThat(a.getLevel()).as("check level").isEqualTo(expected.getLevel()))
            .satisfies(a -> assertThat(a.getGrp()).as("check grp").isEqualTo(expected.getGrp()))
            .satisfies(a -> assertThat(a.getCompid()).as("check compid").isEqualTo(expected.getCompid()))
            .satisfies(a -> assertThat(a.getBranch()).as("check branch").isEqualTo(expected.getBranch()))
            .satisfies(a -> assertThat(a.getRegoffice()).as("check regoffice").isEqualTo(expected.getRegoffice()))
            .satisfies(a -> assertThat(a.getUnitgroup()).as("check unitgroup").isEqualTo(expected.getUnitgroup()))
            .satisfies(a -> assertThat(a.getGrdparent()).as("check grdparent").isEqualTo(expected.getGrdparent()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()))
            .satisfies(a -> assertThat(a.getCategory()).as("check category").isEqualTo(expected.getCategory()))
            .satisfies(a -> assertThat(a.getFunctionUnit()).as("check functionUnit").isEqualTo(expected.getFunctionUnit()))
            .satisfies(a -> assertThat(a.getPosition()).as("check position").isEqualTo(expected.getPosition()))
            .satisfies(a -> assertThat(a.getSection()).as("check section").isEqualTo(expected.getSection()))
            .satisfies(a -> assertThat(a.getCategdir()).as("check categdir").isEqualTo(expected.getCategdir()))
            .satisfies(a -> assertThat(a.getCategUnit()).as("check categUnit").isEqualTo(expected.getCategUnit()))
            .satisfies(a -> assertThat(a.getFunction()).as("check function").isEqualTo(expected.getFunction()))
            .satisfies(a -> assertThat(a.getResponsible()).as("check responsible").isEqualTo(expected.getResponsible()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertUnitUpdatableRelationshipsEquals(Unit expected, Unit actual) {
        assertThat(actual)
            .as("Verify Unit relationships")
            .satisfies(a -> assertThat(a.getCompany()).as("check company").isEqualTo(expected.getCompany()));
    }
}
