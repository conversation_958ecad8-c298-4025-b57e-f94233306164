package com.dq.lilas.domain;

import static com.dq.lilas.domain.GmsClientsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import org.junit.jupiter.api.Test;

class GmsClientsTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(GmsClients.class);
        GmsClients gmsClients1 = getGmsClientsSample1();
        GmsClients gmsClients2 = new GmsClients();
        assertThat(gmsClients1).isNotEqualTo(gmsClients2);

        gmsClients2.setId(gmsClients1.getId());
        assertThat(gmsClients1).isEqualTo(gmsClients2);

        gmsClients2 = getGmsClientsSample2();
        assertThat(gmsClients1).isNotEqualTo(gmsClients2);
    }

    // Note: cadencierTest() method removed as GmsClients entity no longer has a cadencier relationship
}
