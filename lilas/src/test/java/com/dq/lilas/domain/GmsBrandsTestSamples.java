package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class GmsBrandsTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static GmsBrands getGmsBrandsSample1() {
        return new GmsBrands().id(1L).name("name1").classe("classe1").iconPath("iconPath1");
    }

    public static GmsBrands getGmsBrandsSample2() {
        return new GmsBrands().id(2L).name("name2").classe("classe2").iconPath("iconPath2");
    }

    public static GmsBrands getGmsBrandsRandomSampleGenerator() {
        return new GmsBrands()
            .id(longCount.incrementAndGet())
            .name(UUID.randomUUID().toString())
            .classe(UUID.randomUUID().toString())
            .iconPath(UUID.randomUUID().toString());
    }
}
