package com.dq.lilas.domain;

import static com.dq.lilas.domain.AttachementTestSamples.*;
import static com.dq.lilas.domain.OrderTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import org.junit.jupiter.api.Test;

class AttachementTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Attachement.class);
        Attachement attachement1 = getAttachementSample1();
        Attachement attachement2 = new Attachement();
        assertThat(attachement1).isNotEqualTo(attachement2);

        attachement2.setId(attachement1.getId());
        assertThat(attachement1).isEqualTo(attachement2);

        attachement2 = getAttachementSample2();
        assertThat(attachement1).isNotEqualTo(attachement2);
    }

    @Test
    void orderTest() {
        Attachement attachement = getAttachementRandomSampleGenerator();
        Order orderBack = getOrderRandomSampleGenerator();

        attachement.setOrder(orderBack);
        assertThat(attachement.getOrder()).isEqualTo(orderBack);

        attachement.order(null);
        assertThat(attachement.getOrder()).isNull();
    }
}
