package com.dq.lilas.domain;

import static com.dq.lilas.domain.ActionTestSamples.*;
import static com.dq.lilas.domain.CorrespondenceTestSamples.*;
import static com.dq.lilas.domain.CorrespondencecopyTestSamples.*;
import static com.dq.lilas.domain.DeliverymodeTestSamples.*;
import static com.dq.lilas.domain.EmployeeTestSamples.*;
import static com.dq.lilas.domain.TransferTestSamples.*;
import static com.dq.lilas.domain.TypecorrespondenceTestSamples.*;
import static com.dq.lilas.domain.UnitTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TransferTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Transfer.class);
        Transfer transfer1 = getTransferSample1();
        Transfer transfer2 = new Transfer();
        assertThat(transfer1).isNotEqualTo(transfer2);

        transfer2.setId(transfer1.getId());
        assertThat(transfer1).isEqualTo(transfer2);

        transfer2 = getTransferSample2();
        assertThat(transfer1).isNotEqualTo(transfer2);
    }

    @Test
    void transmothTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Transfer transferBack = getTransferRandomSampleGenerator();

        transfer.setTransmoth(transferBack);
        assertThat(transfer.getTransmoth()).isEqualTo(transferBack);

        transfer.transmoth(null);
        assertThat(transfer.getTransmoth()).isNull();
    }

    @Test
    void correspondencecopyTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Correspondencecopy correspondencecopyBack = getCorrespondencecopyRandomSampleGenerator();

        transfer.setCorrespondencecopy(correspondencecopyBack);
        assertThat(transfer.getCorrespondencecopy()).isEqualTo(correspondencecopyBack);

        transfer.correspondencecopy(null);
        assertThat(transfer.getCorrespondencecopy()).isNull();
    }

    @Test
    void correspondenceTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Correspondence correspondenceBack = getCorrespondenceRandomSampleGenerator();

        transfer.setCorrespondence(correspondenceBack);
        assertThat(transfer.getCorrespondence()).isEqualTo(correspondenceBack);

        transfer.correspondence(null);
        assertThat(transfer.getCorrespondence()).isNull();
    }

    @Test
    void deliverymodeTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Deliverymode deliverymodeBack = getDeliverymodeRandomSampleGenerator();

        transfer.setDeliverymode(deliverymodeBack);
        assertThat(transfer.getDeliverymode()).isEqualTo(deliverymodeBack);

        transfer.deliverymode(null);
        assertThat(transfer.getDeliverymode()).isNull();
    }

    @Test
    void employeeTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        transfer.setEmployee(employeeBack);
        assertThat(transfer.getEmployee()).isEqualTo(employeeBack);

        transfer.employee(null);
        assertThat(transfer.getEmployee()).isNull();
    }

    @Test
    void unitTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Unit unitBack = getUnitRandomSampleGenerator();

        transfer.setUnit(unitBack);
        assertThat(transfer.getUnit()).isEqualTo(unitBack);

        transfer.unit(null);
        assertThat(transfer.getUnit()).isNull();
    }

    @Test
    void actionTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Action actionBack = getActionRandomSampleGenerator();

        transfer.setAction(actionBack);
        assertThat(transfer.getAction()).isEqualTo(actionBack);

        transfer.action(null);
        assertThat(transfer.getAction()).isNull();
    }

    @Test
    void userreceiveTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        transfer.setUserreceive(employeeBack);
        assertThat(transfer.getUserreceive()).isEqualTo(employeeBack);

        transfer.userreceive(null);
        assertThat(transfer.getUserreceive()).isNull();
    }

    @Test
    void usertransTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        transfer.setUsertrans(employeeBack);
        assertThat(transfer.getUsertrans()).isEqualTo(employeeBack);

        transfer.usertrans(null);
        assertThat(transfer.getUsertrans()).isNull();
    }

    @Test
    void usertranstoTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        transfer.setUsertransto(employeeBack);
        assertThat(transfer.getUsertransto()).isEqualTo(employeeBack);

        transfer.usertransto(null);
        assertThat(transfer.getUsertransto()).isNull();
    }

    @Test
    void unittranstoTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Unit unitBack = getUnitRandomSampleGenerator();

        transfer.setUnittransto(unitBack);
        assertThat(transfer.getUnittransto()).isEqualTo(unitBack);

        transfer.unittransto(null);
        assertThat(transfer.getUnittransto()).isNull();
    }

    @Test
    void userrevokeTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        transfer.setUserrevoke(employeeBack);
        assertThat(transfer.getUserrevoke()).isEqualTo(employeeBack);

        transfer.userrevoke(null);
        assertThat(transfer.getUserrevoke()).isNull();
    }

    @Test
    void userreceivetoTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        transfer.setUserreceiveto(employeeBack);
        assertThat(transfer.getUserreceiveto()).isEqualTo(employeeBack);

        transfer.userreceiveto(null);
        assertThat(transfer.getUserreceiveto()).isNull();
    }

    @Test
    void useractionTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        transfer.setUseraction(employeeBack);
        assertThat(transfer.getUseraction()).isEqualTo(employeeBack);

        transfer.useraction(null);
        assertThat(transfer.getUseraction()).isNull();
    }

    @Test
    void fromdeptTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Unit unitBack = getUnitRandomSampleGenerator();

        transfer.setFromdept(unitBack);
        assertThat(transfer.getFromdept()).isEqualTo(unitBack);

        transfer.fromdept(null);
        assertThat(transfer.getFromdept()).isNull();
    }

    @Test
    void transprincipTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Transfer transferBack = getTransferRandomSampleGenerator();

        transfer.setTransprincip(transferBack);
        assertThat(transfer.getTransprincip()).isEqualTo(transferBack);

        transfer.transprincip(null);
        assertThat(transfer.getTransprincip()).isNull();
    }

    @Test
    void typecorrespondenceTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Typecorrespondence typecorrespondenceBack = getTypecorrespondenceRandomSampleGenerator();

        transfer.setTypecorrespondence(typecorrespondenceBack);
        assertThat(transfer.getTypecorrespondence()).isEqualTo(typecorrespondenceBack);

        transfer.typecorrespondence(null);
        assertThat(transfer.getTypecorrespondence()).isNull();
    }

    @Test
    void transferTest() {
        Transfer transfer = getTransferRandomSampleGenerator();
        Transfer transferBack = getTransferRandomSampleGenerator();

        transfer.setTransfer(transferBack);
        assertThat(transfer.getTransfer()).isEqualTo(transferBack);
        assertThat(transferBack.getTransmoth()).isEqualTo(transfer);

        transfer.transfer(null);
        assertThat(transfer.getTransfer()).isNull();
        assertThat(transferBack.getTransmoth()).isNull();
    }
}
