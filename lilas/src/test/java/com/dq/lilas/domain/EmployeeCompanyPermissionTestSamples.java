package com.dq.lilas.domain;

import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;

public class EmployeeCompanyPermissionTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static EmployeeCompanyPermission getEmployeeCompanyPermissionSample1() {
        return new EmployeeCompanyPermission().id(1L);
    }

    public static EmployeeCompanyPermission getEmployeeCompanyPermissionSample2() {
        return new EmployeeCompanyPermission().id(2L);
    }

    public static EmployeeCompanyPermission getEmployeeCompanyPermissionRandomSampleGenerator() {
        return new EmployeeCompanyPermission().id(longCount.incrementAndGet());
    }
}
