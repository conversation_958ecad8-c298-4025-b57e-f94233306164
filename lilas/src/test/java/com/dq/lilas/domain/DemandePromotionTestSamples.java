package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class DemandePromotionTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static DemandePromotion getDemandePromotionSample1() {
        return new DemandePromotion().id(1L).codeClient("codeClient1").enseigne("enseigne1").action("action1");
    }

    public static DemandePromotion getDemandePromotionSample2() {
        return new DemandePromotion().id(2L).codeClient("codeClient2").enseigne("enseigne2").action("action2");
    }

    public static DemandePromotion getDemandePromotionRandomSampleGenerator() {
        return new DemandePromotion()
            .id(longCount.incrementAndGet())
            .codeClient(UUID.randomUUID().toString())
            .enseigne(UUID.randomUUID().toString())
            .action(UUID.randomUUID().toString());
    }
}
