package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class EmployeeCompanyPermissionAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeCompanyPermissionAllPropertiesEquals(EmployeeCompanyPermission expected, EmployeeCompanyPermission actual) {
        assertEmployeeCompanyPermissionAutoGeneratedPropertiesEquals(expected, actual);
        assertEmployeeCompanyPermissionAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeCompanyPermissionAllUpdatablePropertiesEquals(
        EmployeeCompanyPermission expected,
        EmployeeCompanyPermission actual
    ) {
        assertEmployeeCompanyPermissionUpdatableFieldsEquals(expected, actual);
        assertEmployeeCompanyPermissionUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeCompanyPermissionAutoGeneratedPropertiesEquals(
        EmployeeCompanyPermission expected,
        EmployeeCompanyPermission actual
    ) {
        assertThat(actual)
            .as("Verify EmployeeCompanyPermission auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeCompanyPermissionUpdatableFieldsEquals(EmployeeCompanyPermission expected, EmployeeCompanyPermission actual) {
        // empty method

    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeCompanyPermissionUpdatableRelationshipsEquals(
        EmployeeCompanyPermission expected,
        EmployeeCompanyPermission actual
    ) {
        assertThat(actual)
            .as("Verify EmployeeCompanyPermission relationships")
            .satisfies(a -> assertThat(a.getCompany()).as("check company").isEqualTo(expected.getCompany()));
    }
}
