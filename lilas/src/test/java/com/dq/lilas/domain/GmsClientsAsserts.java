package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class GmsClientsAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertGmsClientsAllPropertiesEquals(GmsClients expected, GmsClients actual) {
        assertGmsClientsAutoGeneratedPropertiesEquals(expected, actual);
        assertGmsClientsAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertGmsClientsAllUpdatablePropertiesEquals(GmsClients expected, GmsClients actual) {
        assertGmsClientsUpdatableFieldsEquals(expected, actual);
        assertGmsClientsUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertGmsClientsAutoGeneratedPropertiesEquals(GmsClients expected, GmsClients actual) {
        assertThat(actual)
            .as("Verify GmsClients auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertGmsClientsUpdatableFieldsEquals(GmsClients expected, GmsClients actual) {
        assertThat(actual)
            .as("Verify GmsClients relevant properties")
            .satisfies(a -> assertThat(a.getClientName()).as("check clientName").isEqualTo(expected.getClientName()))
            .satisfies(a -> assertThat(a.getCode()).as("check code").isEqualTo(expected.getCode()))
            .satisfies(a -> assertThat(a.getClasse()).as("check classe").isEqualTo(expected.getClasse()))
            .satisfies(a -> assertThat(a.getVille()).as("check ville").isEqualTo(expected.getVille()))
            .satisfies(a -> assertThat(a.getCreationDate()).as("check creationDate").isEqualTo(expected.getCreationDate()))
            .satisfies(a -> assertThat(a.getUpdateDate()).as("check updateDate").isEqualTo(expected.getUpdateDate()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertGmsClientsUpdatableRelationshipsEquals(GmsClients expected, GmsClients actual) {
        // Note: GmsClients entity currently has no relationships to assert
        // This method is kept for consistency with the testing framework pattern
    }
}
