package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class CorrespondenceTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static Correspondence getCorrespondenceSample1() {
        return new Correspondence()
            .id(1L)
            .ordernbr("ordernbr1")
            .numcopy("numcopy1")
            .doclink("doclink1")
            .pagenbr("pagenbr1")
            .highlevel("highlevel1")
            .confidentiel("confidentiel1")
            .priority("priority1")
            .obs("obs1")
            .category("category1")
            .status("status1")
            .typereceive("typereceive1")
            .typecopy("typecopy1")
            .refsnd("refsnd1")
            .text("text1")
            .docyear("docyear1")
            .datehjrsave("datehjrsave1")
            .oldOrderNumber("oldOrderNumber1")
            .subject("subject1")
            .attach("attach1")
            .ordernbradrsbook("ordernbradrsbook1")
            .seqkeyadrsbook(1)
            .useradrsbook("useradrsbook1")
            .cityzenncard("cityzenncard1")
            .cityzenphone("cityzenphone1")
            .sms("sms1")
            .incident("incident1")
            .checkFavorite("checkFavorite1")
            .companyId("companyId1");
    }

    public static Correspondence getCorrespondenceSample2() {
        return new Correspondence()
            .id(2L)
            .ordernbr("ordernbr2")
            .numcopy("numcopy2")
            .doclink("doclink2")
            .pagenbr("pagenbr2")
            .highlevel("highlevel2")
            .confidentiel("confidentiel2")
            .priority("priority2")
            .obs("obs2")
            .category("category2")
            .status("status2")
            .typereceive("typereceive2")
            .typecopy("typecopy2")
            .refsnd("refsnd2")
            .text("text2")
            .docyear("docyear2")
            .datehjrsave("datehjrsave2")
            .oldOrderNumber("oldOrderNumber2")
            .subject("subject2")
            .attach("attach2")
            .ordernbradrsbook("ordernbradrsbook2")
            .seqkeyadrsbook(2)
            .useradrsbook("useradrsbook2")
            .cityzenncard("cityzenncard2")
            .cityzenphone("cityzenphone2")
            .sms("sms2")
            .incident("incident2")
            .checkFavorite("checkFavorite2")
            .companyId("companyId2");
    }

    public static Correspondence getCorrespondenceRandomSampleGenerator() {
        return new Correspondence()
            .id(longCount.incrementAndGet())
            .ordernbr(UUID.randomUUID().toString())
            .numcopy(UUID.randomUUID().toString())
            .doclink(UUID.randomUUID().toString())
            .pagenbr(UUID.randomUUID().toString())
            .highlevel(UUID.randomUUID().toString())
            .confidentiel(UUID.randomUUID().toString())
            .priority(UUID.randomUUID().toString())
            .obs(UUID.randomUUID().toString())
            .category(UUID.randomUUID().toString())
            .status(UUID.randomUUID().toString())
            .typereceive(UUID.randomUUID().toString())
            .typecopy(UUID.randomUUID().toString())
            .refsnd(UUID.randomUUID().toString())
            .text(UUID.randomUUID().toString())
            .docyear(UUID.randomUUID().toString())
            .datehjrsave(UUID.randomUUID().toString())
            .oldOrderNumber(UUID.randomUUID().toString())
            .subject(UUID.randomUUID().toString())
            .attach(UUID.randomUUID().toString())
            .ordernbradrsbook(UUID.randomUUID().toString())
            .seqkeyadrsbook(intCount.incrementAndGet())
            .useradrsbook(UUID.randomUUID().toString())
            .cityzenncard(UUID.randomUUID().toString())
            .cityzenphone(UUID.randomUUID().toString())
            .sms(UUID.randomUUID().toString())
            .incident(UUID.randomUUID().toString())
            .checkFavorite(UUID.randomUUID().toString())
            .companyId(UUID.randomUUID().toString());
    }
}
