package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class EmployeeTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static Employee getEmployeeSample1() {
        return new Employee()
            .id(1L)
            .tel("tel1")
            .fax("fax1")
            .mail("mail1")
            .confidentiel("confidentiel1")
            .numidentity("numidentity1")
            .empnumber("empnumber1")
            .fullname("fullname1")
            .address("address1")
            .matricule("matricule1")
            .upscale("upscale1")
            .active("active1")
            .statut("statut1")
            .gender("gender1")
            .avatar("avatar1")
            .filenameparaf("filenameparaf1")
            .filenamesign("filenamesign1");
    }

    public static Employee getEmployeeSample2() {
        return new Employee()
            .id(2L)
            .tel("tel2")
            .fax("fax2")
            .mail("mail2")
            .confidentiel("confidentiel2")
            .numidentity("numidentity2")
            .empnumber("empnumber2")
            .fullname("fullname2")
            .address("address2")
            .matricule("matricule2")
            .upscale("upscale2")
            .active("active2")
            .statut("statut2")
            .gender("gender2")
            .avatar("avatar2")
            .filenameparaf("filenameparaf2")
            .filenamesign("filenamesign2");
    }

    public static Employee getEmployeeRandomSampleGenerator() {
        return new Employee()
            .id(longCount.incrementAndGet())
            .tel(UUID.randomUUID().toString())
            .fax(UUID.randomUUID().toString())
            .mail(UUID.randomUUID().toString())
            .confidentiel(UUID.randomUUID().toString())
            .numidentity(UUID.randomUUID().toString())
            .empnumber(UUID.randomUUID().toString())
            .fullname(UUID.randomUUID().toString())
            .address(UUID.randomUUID().toString())
            .matricule(UUID.randomUUID().toString())
            .upscale(UUID.randomUUID().toString())
            .active(UUID.randomUUID().toString())
            .statut(UUID.randomUUID().toString())
            .gender(UUID.randomUUID().toString())
            .avatar(UUID.randomUUID().toString())
            .filenameparaf(UUID.randomUUID().toString())
            .filenamesign(UUID.randomUUID().toString());
    }
}
