package com.dq.lilas.domain;

import com.dq.lilas.domain.enumeration.MailType;
import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class MailsTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static Mails getMailsSample1() {
        return new Mails().id(1L).subject("subject1").recipient("recipient1").sender("sender1").mailType(MailType.BC);
    }

    public static Mails getMailsSample2() {
        return new Mails().id(2L).subject("subject2").recipient("recipient2").sender("sender2").mailType(MailType.REDUNDANT);
    }

    public static Mails getMailsRandomSampleGenerator() {
        return new Mails()
            .id(longCount.incrementAndGet())
            .subject(UUID.randomUUID().toString())
            .recipient(UUID.randomUUID().toString())
            .sender(UUID.randomUUID().toString())
            .mailType(MailType.values()[random.nextInt(MailType.values().length)]);
    }
}
