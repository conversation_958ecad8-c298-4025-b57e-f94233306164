package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class DailyStockAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDailyStockAllPropertiesEquals(DailyStock expected, DailyStock actual) {
        assertDailyStockAutoGeneratedPropertiesEquals(expected, actual);
        assertDailyStockAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDailyStockAllUpdatablePropertiesEquals(DailyStock expected, DailyStock actual) {
        assertDailyStockUpdatableFieldsEquals(expected, actual);
        assertDailyStockUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDailyStockAutoGeneratedPropertiesEquals(DailyStock expected, DailyStock actual) {
        assertThat(actual)
            .as("Verify DailyStock auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDailyStockUpdatableFieldsEquals(DailyStock expected, DailyStock actual) {
        assertThat(actual)
            .as("Verify DailyStock relevant properties")
            .satisfies(a -> assertThat(a.getInternalCode()).as("check internalCode").isEqualTo(expected.getInternalCode()))
            .satisfies(a -> assertThat(a.getBarcode()).as("check barcode").isEqualTo(expected.getBarcode()))
            .satisfies(a -> assertThat(a.getProductName()).as("check productName").isEqualTo(expected.getProductName()))
            .satisfies(a -> assertThat(a.getStockQty()).as("check stockQty").isEqualTo(expected.getStockQty()))
            .satisfies(a -> assertThat(a.getStockDate()).as("check stockDate").isEqualTo(expected.getStockDate()))
            .satisfies(a -> assertThat(a.getRemainingQuantity()).as("check remainingQuantity").isEqualTo(expected.getRemainingQuantity()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDailyStockUpdatableRelationshipsEquals(DailyStock expected, DailyStock actual) {
        assertThat(actual)
            .as("Verify DailyStock relationships")
            .satisfies(a -> assertThat(a.getProductsList()).as("check productsList").isEqualTo(expected.getProductsList()));
    }
}
