package com.dq.lilas.domain;

import static com.dq.lilas.domain.EmployeeTestSamples.*;
import static com.dq.lilas.domain.TemplateConditionsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import org.junit.jupiter.api.Test;

class TemplateConditionsTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(TemplateConditions.class);
        TemplateConditions templateConditions1 = getTemplateConditionsSample1();
        TemplateConditions templateConditions2 = new TemplateConditions();
        assertThat(templateConditions1).isNotEqualTo(templateConditions2);

        templateConditions2.setId(templateConditions1.getId());
        assertThat(templateConditions1).isEqualTo(templateConditions2);

        templateConditions2 = getTemplateConditionsSample2();
        assertThat(templateConditions1).isNotEqualTo(templateConditions2);
    }

    @Test
    void employeeTest() {
        TemplateConditions templateConditions = getTemplateConditionsRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        templateConditions.setEmployee(employeeBack);
        assertThat(templateConditions.getEmployee()).isEqualTo(employeeBack);

        templateConditions.employee(null);
        assertThat(templateConditions.getEmployee()).isNull();
    }
}
