package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class TypecorrespondenceAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTypecorrespondenceAllPropertiesEquals(Typecorrespondence expected, Typecorrespondence actual) {
        assertTypecorrespondenceAutoGeneratedPropertiesEquals(expected, actual);
        assertTypecorrespondenceAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTypecorrespondenceAllUpdatablePropertiesEquals(Typecorrespondence expected, Typecorrespondence actual) {
        assertTypecorrespondenceUpdatableFieldsEquals(expected, actual);
        assertTypecorrespondenceUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTypecorrespondenceAutoGeneratedPropertiesEquals(Typecorrespondence expected, Typecorrespondence actual) {
        assertThat(actual)
            .as("Verify Typecorrespondence auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTypecorrespondenceUpdatableFieldsEquals(Typecorrespondence expected, Typecorrespondence actual) {
        assertThat(actual)
            .as("Verify Typecorrespondence relevant properties")
            .satisfies(a -> assertThat(a.getTypecorresp()).as("check typecorresp").isEqualTo(expected.getTypecorresp()))
            .satisfies(a -> assertThat(a.getTypedecision()).as("check typedecision").isEqualTo(expected.getTypedecision()))
            .satisfies(a -> assertThat(a.getHosp()).as("check hosp").isEqualTo(expected.getHosp()))
            .satisfies(a -> assertThat(a.getLbl()).as("check lbl").isEqualTo(expected.getLbl()))
            .satisfies(a -> assertThat(a.getStatut()).as("check statut").isEqualTo(expected.getStatut()))
            .satisfies(a -> assertThat(a.getAbbreviated()).as("check abbreviated").isEqualTo(expected.getAbbreviated()))
            .satisfies(a -> assertThat(a.getSpeech()).as("check speech").isEqualTo(expected.getSpeech()))
            .satisfies(a -> assertThat(a.getNbrnotifbeforerec()).as("check nbrnotifbeforerec").isEqualTo(expected.getNbrnotifbeforerec()))
            .satisfies(a -> assertThat(a.getNbrnotifafterrec()).as("check nbrnotifafterrec").isEqualTo(expected.getNbrnotifafterrec()))
            .satisfies(a -> assertThat(a.getArchiveSubjectsId()).as("check archiveSubjectsId").isEqualTo(expected.getArchiveSubjectsId()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTypecorrespondenceUpdatableRelationshipsEquals(Typecorrespondence expected, Typecorrespondence actual) {
        // empty method
    }
}
