package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class PromotionDetailsTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static PromotionDetails getPromotionDetailsSample1() {
        return new PromotionDetails()
            .id(1L)
            .codeProduit("codeProduit1")
            .description("description1")
            .approManagerDuMagasin("approManagerDuMagasin1")
            .approManagerGMS("approManagerGMS1");
    }

    public static PromotionDetails getPromotionDetailsSample2() {
        return new PromotionDetails()
            .id(2L)
            .codeProduit("codeProduit2")
            .description("description2")
            .approManagerDuMagasin("approManagerDuMagasin2")
            .approManagerGMS("approManagerGMS2");
    }

    public static PromotionDetails getPromotionDetailsRandomSampleGenerator() {
        return new PromotionDetails()
            .id(longCount.incrementAndGet())
            .codeProduit(UUID.randomUUID().toString())
            .description(UUID.randomUUID().toString())
            .approManagerDuMagasin(UUID.randomUUID().toString())
            .approManagerGMS(UUID.randomUUID().toString());
    }
}
