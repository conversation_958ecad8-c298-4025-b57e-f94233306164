package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class TypecorrespondencelangAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTypecorrespondencelangAllPropertiesEquals(Typecorrespondencelang expected, Typecorrespondencelang actual) {
        assertTypecorrespondencelangAutoGeneratedPropertiesEquals(expected, actual);
        assertTypecorrespondencelangAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTypecorrespondencelangAllUpdatablePropertiesEquals(
        Typecorrespondencelang expected,
        Typecorrespondencelang actual
    ) {
        assertTypecorrespondencelangUpdatableFieldsEquals(expected, actual);
        assertTypecorrespondencelangUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTypecorrespondencelangAutoGeneratedPropertiesEquals(
        Typecorrespondencelang expected,
        Typecorrespondencelang actual
    ) {
        assertThat(actual)
            .as("Verify Typecorrespondencelang auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTypecorrespondencelangUpdatableFieldsEquals(Typecorrespondencelang expected, Typecorrespondencelang actual) {
        assertThat(actual)
            .as("Verify Typecorrespondencelang relevant properties")
            .satisfies(a -> assertThat(a.getLbl()).as("check lbl").isEqualTo(expected.getLbl()))
            .satisfies(a -> assertThat(a.getLang()).as("check lang").isEqualTo(expected.getLang()))
            .satisfies(a -> assertThat(a.getAbbreviated()).as("check abbreviated").isEqualTo(expected.getAbbreviated()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTypecorrespondencelangUpdatableRelationshipsEquals(
        Typecorrespondencelang expected,
        Typecorrespondencelang actual
    ) {
        assertThat(actual)
            .as("Verify Typecorrespondencelang relationships")
            .satisfies(a -> assertThat(a.getTypecorrespondence()).as("check typecorrespondence").isEqualTo(expected.getTypecorrespondence())
            );
    }
}
