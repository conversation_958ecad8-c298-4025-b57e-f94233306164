package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class PromotionDetailsAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromotionDetailsAllPropertiesEquals(PromotionDetails expected, PromotionDetails actual) {
        assertPromotionDetailsAutoGeneratedPropertiesEquals(expected, actual);
        assertPromotionDetailsAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromotionDetailsAllUpdatablePropertiesEquals(PromotionDetails expected, PromotionDetails actual) {
        assertPromotionDetailsUpdatableFieldsEquals(expected, actual);
        assertPromotionDetailsUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromotionDetailsAutoGeneratedPropertiesEquals(PromotionDetails expected, PromotionDetails actual) {
        assertThat(actual)
            .as("Verify PromotionDetails auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromotionDetailsUpdatableFieldsEquals(PromotionDetails expected, PromotionDetails actual) {
        assertThat(actual)
            .as("Verify PromotionDetails relevant properties")
            .satisfies(a -> assertThat(a.getCodeProduit()).as("check codeProduit").isEqualTo(expected.getCodeProduit()))
            .satisfies(a -> assertThat(a.getDescription()).as("check description").isEqualTo(expected.getDescription()))
            .satisfies(a -> assertThat(a.getPriceHT()).as("check priceHT").isEqualTo(expected.getPriceHT()))
            .satisfies(a -> assertThat(a.getRemiseFixe()).as("check remiseFixe").isEqualTo(expected.getRemiseFixe()))
            .satisfies(a -> assertThat(a.getRemiseDePro()).as("check remiseDePro").isEqualTo(expected.getRemiseDePro()))
            .satisfies(a -> assertThat(a.getGratuitEnNat()).as("check gratuitEnNat").isEqualTo(expected.getGratuitEnNat()))
            .satisfies(a ->
                assertThat(a.getApproManagerDuMagasin()).as("check approManagerDuMagasin").isEqualTo(expected.getApproManagerDuMagasin())
            )
            .satisfies(a -> assertThat(a.getApproManagerGMS()).as("check approManagerGMS").isEqualTo(expected.getApproManagerGMS()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertPromotionDetailsUpdatableRelationshipsEquals(PromotionDetails expected, PromotionDetails actual) {
        // empty method
    }
}
