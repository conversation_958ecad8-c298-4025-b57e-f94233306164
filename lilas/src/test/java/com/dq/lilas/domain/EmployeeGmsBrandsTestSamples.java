package com.dq.lilas.domain;

import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;

public class EmployeeGmsBrandsTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static EmployeeGmsBrands getEmployeeGmsBrandsSample1() {
        return new EmployeeGmsBrands().id(1L);
    }

    public static EmployeeGmsBrands getEmployeeGmsBrandsSample2() {
        return new EmployeeGmsBrands().id(2L);
    }

    public static EmployeeGmsBrands getEmployeeGmsBrandsRandomSampleGenerator() {
        return new EmployeeGmsBrands().id(longCount.incrementAndGet());
    }
}
