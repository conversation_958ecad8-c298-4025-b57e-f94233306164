package com.dq.lilas.domain;

import static com.dq.lilas.domain.TypecorrespondenceTestSamples.*;
import static com.dq.lilas.domain.TypecorrespondencelangTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class TypecorrespondenceTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Typecorrespondence.class);
        Typecorrespondence typecorrespondence1 = getTypecorrespondenceSample1();
        Typecorrespondence typecorrespondence2 = new Typecorrespondence();
        assertThat(typecorrespondence1).isNotEqualTo(typecorrespondence2);

        typecorrespondence2.setId(typecorrespondence1.getId());
        assertThat(typecorrespondence1).isEqualTo(typecorrespondence2);

        typecorrespondence2 = getTypecorrespondenceSample2();
        assertThat(typecorrespondence1).isNotEqualTo(typecorrespondence2);
    }

    @Test
    void typecorrespondenceTest() {
        Typecorrespondence typecorrespondence = getTypecorrespondenceRandomSampleGenerator();
        Typecorrespondencelang typecorrespondencelangBack = getTypecorrespondencelangRandomSampleGenerator();

        typecorrespondence.addTypecorrespondence(typecorrespondencelangBack);
        assertThat(typecorrespondence.getTypecorrespondences()).containsOnly(typecorrespondencelangBack);
        assertThat(typecorrespondencelangBack.getTypecorrespondence()).isEqualTo(typecorrespondence);

        typecorrespondence.removeTypecorrespondence(typecorrespondencelangBack);
        assertThat(typecorrespondence.getTypecorrespondences()).doesNotContain(typecorrespondencelangBack);
        assertThat(typecorrespondencelangBack.getTypecorrespondence()).isNull();

        typecorrespondence.typecorrespondences(new HashSet<>(Set.of(typecorrespondencelangBack)));
        assertThat(typecorrespondence.getTypecorrespondences()).containsOnly(typecorrespondencelangBack);
        assertThat(typecorrespondencelangBack.getTypecorrespondence()).isEqualTo(typecorrespondence);

        typecorrespondence.setTypecorrespondences(new HashSet<>());
        assertThat(typecorrespondence.getTypecorrespondences()).doesNotContain(typecorrespondencelangBack);
        assertThat(typecorrespondencelangBack.getTypecorrespondence()).isNull();
    }
}
