package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class CadencierTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static Cadencier getCadencierSample1() {
        return new Cadencier().id(1L).orderDay(1).deliveryDay(1).region("region1");
    }

    public static Cadencier getCadencierSample2() {
        return new Cadencier().id(2L).orderDay(2).deliveryDay(2).region("region2");
    }

    public static Cadencier getCadencierRandomSampleGenerator() {
        return new Cadencier()
            .id(longCount.incrementAndGet())
            .orderDay(intCount.incrementAndGet())
            .deliveryDay(intCount.incrementAndGet())
            .region(UUID.randomUUID().toString());
    }
}
