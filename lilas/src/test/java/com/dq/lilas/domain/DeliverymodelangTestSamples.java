package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class DeliverymodelangTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static Deliverymodelang getDeliverymodelangSample1() {
        return new Deliverymodelang().id(1L).lbl("lbl1").lang("lang1").abrv("abrv1");
    }

    public static Deliverymodelang getDeliverymodelangSample2() {
        return new Deliverymodelang().id(2L).lbl("lbl2").lang("lang2").abrv("abrv2");
    }

    public static Deliverymodelang getDeliverymodelangRandomSampleGenerator() {
        return new Deliverymodelang()
            .id(longCount.incrementAndGet())
            .lbl(UUID.randomUUID().toString())
            .lang(UUID.randomUUID().toString())
            .abrv(UUID.randomUUID().toString());
    }
}
