package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class CompanyAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCompanyAllPropertiesEquals(Company expected, Company actual) {
        assertCompanyAutoGeneratedPropertiesEquals(expected, actual);
        assertCompanyAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCompanyAllUpdatablePropertiesEquals(Company expected, Company actual) {
        assertCompanyUpdatableFieldsEquals(expected, actual);
        assertCompanyUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCompanyAutoGeneratedPropertiesEquals(Company expected, Company actual) {
        assertThat(actual)
            .as("Verify Company auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCompanyUpdatableFieldsEquals(Company expected, Company actual) {
        assertThat(actual)
            .as("Verify Company relevant properties")
            .satisfies(a -> assertThat(a.getComapanyName()).as("check comapanyName").isEqualTo(expected.getComapanyName()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCompanyUpdatableRelationshipsEquals(Company expected, Company actual) {
        // empty method
    }
}
