package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class DeliverymodelangAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDeliverymodelangAllPropertiesEquals(Deliverymodelang expected, Deliverymodelang actual) {
        assertDeliverymodelangAutoGeneratedPropertiesEquals(expected, actual);
        assertDeliverymodelangAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDeliverymodelangAllUpdatablePropertiesEquals(Deliverymodelang expected, Deliverymodelang actual) {
        assertDeliverymodelangUpdatableFieldsEquals(expected, actual);
        assertDeliverymodelangUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDeliverymodelangAutoGeneratedPropertiesEquals(Deliverymodelang expected, Deliverymodelang actual) {
        assertThat(actual)
            .as("Verify Deliverymodelang auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDeliverymodelangUpdatableFieldsEquals(Deliverymodelang expected, Deliverymodelang actual) {
        assertThat(actual)
            .as("Verify Deliverymodelang relevant properties")
            .satisfies(a -> assertThat(a.getLbl()).as("check lbl").isEqualTo(expected.getLbl()))
            .satisfies(a -> assertThat(a.getLang()).as("check lang").isEqualTo(expected.getLang()))
            .satisfies(a -> assertThat(a.getAbrv()).as("check abrv").isEqualTo(expected.getAbrv()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDeliverymodelangUpdatableRelationshipsEquals(Deliverymodelang expected, Deliverymodelang actual) {
        assertThat(actual)
            .as("Verify Deliverymodelang relationships")
            .satisfies(a -> assertThat(a.getDeliverymode()).as("check deliverymode").isEqualTo(expected.getDeliverymode()));
    }
}
