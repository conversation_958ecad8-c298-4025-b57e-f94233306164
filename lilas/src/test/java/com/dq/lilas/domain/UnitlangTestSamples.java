package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class UnitlangTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static Unitlang getUnitlangSample1() {
        return new Unitlang().id(1L).address("address1").nbr("nbr1").name("name1").lang("lang1").abrv("abrv1");
    }

    public static Unitlang getUnitlangSample2() {
        return new Unitlang().id(2L).address("address2").nbr("nbr2").name("name2").lang("lang2").abrv("abrv2");
    }

    public static Unitlang getUnitlangRandomSampleGenerator() {
        return new Unitlang()
            .id(longCount.incrementAndGet())
            .address(UUID.randomUUID().toString())
            .nbr(UUID.randomUUID().toString())
            .name(UUID.randomUUID().toString())
            .lang(UUID.randomUUID().toString())
            .abrv(UUID.randomUUID().toString());
    }
}
