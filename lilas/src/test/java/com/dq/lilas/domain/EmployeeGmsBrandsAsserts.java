package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class EmployeeGmsBrandsAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeGmsBrandsAllPropertiesEquals(EmployeeGmsBrands expected, EmployeeGmsBrands actual) {
        assertEmployeeGmsBrandsAutoGeneratedPropertiesEquals(expected, actual);
        assertEmployeeGmsBrandsAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeGmsBrandsAllUpdatablePropertiesEquals(EmployeeGmsBrands expected, EmployeeGmsBrands actual) {
        assertEmployeeGmsBrandsUpdatableFieldsEquals(expected, actual);
        assertEmployeeGmsBrandsUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeGmsBrandsAutoGeneratedPropertiesEquals(EmployeeGmsBrands expected, EmployeeGmsBrands actual) {
        assertThat(actual)
            .as("Verify EmployeeGmsBrands auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeGmsBrandsUpdatableFieldsEquals(EmployeeGmsBrands expected, EmployeeGmsBrands actual) {
        assertThat(actual)
            .as("Verify EmployeeGmsBrands relevant properties")
            .satisfies(a -> assertThat(a.getAssignmentDate()).as("check assignmentDate").isEqualTo(expected.getAssignmentDate()))
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmployeeGmsBrandsUpdatableRelationshipsEquals(EmployeeGmsBrands expected, EmployeeGmsBrands actual) {
        assertThat(actual)
            .as("Verify EmployeeGmsBrands relationships")
            .satisfies(a -> assertThat(a.getEmployee()).as("check employee").isEqualTo(expected.getEmployee()))
            .satisfies(a -> assertThat(a.getGmsBrands()).as("check gmsBrands").isEqualTo(expected.getGmsBrands()));
    }
}
