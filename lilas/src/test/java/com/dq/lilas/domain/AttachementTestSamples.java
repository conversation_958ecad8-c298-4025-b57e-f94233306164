package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class AttachementTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static Attachement getAttachementSample1() {
        return new Attachement()
            .id(1L)
            .copyId("copyId1")
            .lblAttachment("lblAttachment1")
            .idDocAttachment("idDocAttachment1")
            .sizeAttachement("sizeAttachement1")
            .filenameattachment("filenameattachment1")
            .userattachment("userattachment1")
            .iddecision("iddecision1")
            .idtemplate("idtemplate1")
            .idtransfer("idtransfer1")
            .idcorresp("idcorresp1")
            .levelattachement("levelattachement1")
            .idreq("idreq1")
            .iddocext("iddocext1")
            .idleave("idleave1")
            .configLevel("configLevel1")
            .typeAtach("typeAtach1")
            .idDirectOrder("idDirectOrder1")
            .pathFile("pathFile1")
            .version(1);
    }

    public static Attachement getAttachementSample2() {
        return new Attachement()
            .id(2L)
            .copyId("copyId2")
            .lblAttachment("lblAttachment2")
            .idDocAttachment("idDocAttachment2")
            .sizeAttachement("sizeAttachement2")
            .filenameattachment("filenameattachment2")
            .userattachment("userattachment2")
            .iddecision("iddecision2")
            .idtemplate("idtemplate2")
            .idtransfer("idtransfer2")
            .idcorresp("idcorresp2")
            .levelattachement("levelattachement2")
            .idreq("idreq2")
            .iddocext("iddocext2")
            .idleave("idleave2")
            .configLevel("configLevel2")
            .typeAtach("typeAtach2")
            .idDirectOrder("idDirectOrder2")
            .pathFile("pathFile2")
            .version(2);
    }

    public static Attachement getAttachementRandomSampleGenerator() {
        return new Attachement()
            .id(longCount.incrementAndGet())
            .copyId(UUID.randomUUID().toString())
            .lblAttachment(UUID.randomUUID().toString())
            .idDocAttachment(UUID.randomUUID().toString())
            .sizeAttachement(UUID.randomUUID().toString())
            .filenameattachment(UUID.randomUUID().toString())
            .userattachment(UUID.randomUUID().toString())
            .iddecision(UUID.randomUUID().toString())
            .idtemplate(UUID.randomUUID().toString())
            .idtransfer(UUID.randomUUID().toString())
            .idcorresp(UUID.randomUUID().toString())
            .levelattachement(UUID.randomUUID().toString())
            .idreq(UUID.randomUUID().toString())
            .iddocext(UUID.randomUUID().toString())
            .idleave(UUID.randomUUID().toString())
            .configLevel(UUID.randomUUID().toString())
            .typeAtach(UUID.randomUUID().toString())
            .idDirectOrder(UUID.randomUUID().toString())
            .pathFile(UUID.randomUUID().toString())
            .version(intCount.incrementAndGet());
    }
}
