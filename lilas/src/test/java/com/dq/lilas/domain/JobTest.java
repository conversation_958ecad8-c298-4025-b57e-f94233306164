package com.dq.lilas.domain;

import static com.dq.lilas.domain.JobTestSamples.*;
import static com.dq.lilas.domain.JoblangTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class JobTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Job.class);
        Job job1 = getJobSample1();
        Job job2 = new Job();
        assertThat(job1).isNotEqualTo(job2);

        job2.setId(job1.getId());
        assertThat(job1).isEqualTo(job2);

        job2 = getJobSample2();
        assertThat(job1).isNotEqualTo(job2);
    }

    @Test
    void joblangsTest() {
        Job job = getJobRandomSampleGenerator();
        Joblang joblangBack = getJoblangRandomSampleGenerator();

        job.addJoblangs(joblangBack);
        assertThat(job.getJoblangs()).containsOnly(joblangBack);
        assertThat(joblangBack.getJob()).isEqualTo(job);

        job.removeJoblangs(joblangBack);
        assertThat(job.getJoblangs()).doesNotContain(joblangBack);
        assertThat(joblangBack.getJob()).isNull();

        job.joblangs(new HashSet<>(Set.of(joblangBack)));
        assertThat(job.getJoblangs()).containsOnly(joblangBack);
        assertThat(joblangBack.getJob()).isEqualTo(job);

        job.setJoblangs(new HashSet<>());
        assertThat(job.getJoblangs()).doesNotContain(joblangBack);
        assertThat(joblangBack.getJob()).isNull();
    }
}
