package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class JoblangAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertJoblangAllPropertiesEquals(Joblang expected, Joblang actual) {
        assertJoblangAutoGeneratedPropertiesEquals(expected, actual);
        assertJoblangAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertJoblangAllUpdatablePropertiesEquals(Joblang expected, Joblang actual) {
        assertJoblangUpdatableFieldsEquals(expected, actual);
        assertJoblangUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertJoblangAutoGeneratedPropertiesEquals(Joblang expected, Joblang actual) {
        assertThat(actual)
            .as("Verify Joblang auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertJoblangUpdatableFieldsEquals(Joblang expected, Joblang actual) {
        assertThat(actual)
            .as("Verify Joblang relevant properties")
            .satisfies(a -> assertThat(a.getLbl()).as("check lbl").isEqualTo(expected.getLbl()))
            .satisfies(a -> assertThat(a.getLang()).as("check lang").isEqualTo(expected.getLang()))
            .satisfies(a -> assertThat(a.getAbr()).as("check abr").isEqualTo(expected.getAbr()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertJoblangUpdatableRelationshipsEquals(Joblang expected, Joblang actual) {
        assertThat(actual)
            .as("Verify Joblang relationships")
            .satisfies(a -> assertThat(a.getJob()).as("check job").isEqualTo(expected.getJob()));
    }
}
