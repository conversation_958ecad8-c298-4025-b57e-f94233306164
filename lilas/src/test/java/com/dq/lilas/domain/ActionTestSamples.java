package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.concurrent.atomic.AtomicLong;

public class ActionTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));
    private static final AtomicInteger intCount = new AtomicInteger(random.nextInt() + (2 * Short.MAX_VALUE));

    public static Action getActionSample1() {
        return new Action().id(1L).regulation("regulation1").circular("circular1").appOrder(1).statut("statut1");
    }

    public static Action getActionSample2() {
        return new Action().id(2L).regulation("regulation2").circular("circular2").appOrder(2).statut("statut2");
    }

    public static Action getActionRandomSampleGenerator() {
        return new Action()
            .id(longCount.incrementAndGet())
            .regulation(UUID.randomUUID().toString())
            .circular(UUID.randomUUID().toString())
            .appOrder(intCount.incrementAndGet())
            .statut(UUID.randomUUID().toString());
    }
}
