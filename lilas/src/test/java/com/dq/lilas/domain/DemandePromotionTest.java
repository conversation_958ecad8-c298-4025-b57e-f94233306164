package com.dq.lilas.domain;

import static com.dq.lilas.domain.DemandePromotionTestSamples.*;
import static com.dq.lilas.domain.EmployeeTestSamples.*;
import static com.dq.lilas.domain.PromotionDetailsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import org.junit.jupiter.api.Test;

class DemandePromotionTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(DemandePromotion.class);
        DemandePromotion demandePromotion1 = getDemandePromotionSample1();
        DemandePromotion demandePromotion2 = new DemandePromotion();
        assertThat(demandePromotion1).isNotEqualTo(demandePromotion2);

        demandePromotion2.setId(demandePromotion1.getId());
        assertThat(demandePromotion1).isEqualTo(demandePromotion2);

        demandePromotion2 = getDemandePromotionSample2();
        assertThat(demandePromotion1).isNotEqualTo(demandePromotion2);
    }

    @Test
    void employeeTest() {
        DemandePromotion demandePromotion = getDemandePromotionRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        demandePromotion.setEmployee(employeeBack);
        assertThat(demandePromotion.getEmployee()).isEqualTo(employeeBack);

        demandePromotion.employee(null);
        assertThat(demandePromotion.getEmployee()).isNull();
    }

    @Test
    void promotionDetailsTest() {
        DemandePromotion demandePromotion = getDemandePromotionRandomSampleGenerator();
        PromotionDetails promotionDetailsBack = getPromotionDetailsRandomSampleGenerator();

        demandePromotion.addPromotionDetails(promotionDetailsBack);
        assertThat(demandePromotion.getPromotionDetails().size()).isEqualTo(1);

        demandePromotion.removePromotionDetails(promotionDetailsBack);
        assertThat(demandePromotion.getPromotionDetails().size()).isEqualTo(0);
    }
}
