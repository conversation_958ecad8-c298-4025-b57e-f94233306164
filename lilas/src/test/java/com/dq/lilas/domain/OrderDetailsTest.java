package com.dq.lilas.domain;

import static com.dq.lilas.domain.EmailsNotificationsTestSamples.*;
import static com.dq.lilas.domain.OrderDetailsTestSamples.*;
import static com.dq.lilas.domain.OrderTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class OrderDetailsTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(OrderDetails.class);
        OrderDetails orderDetails1 = getOrderDetailsSample1();
        OrderDetails orderDetails2 = new OrderDetails();
        assertThat(orderDetails1).isNotEqualTo(orderDetails2);

        orderDetails2.setId(orderDetails1.getId());
        assertThat(orderDetails1).isEqualTo(orderDetails2);

        orderDetails2 = getOrderDetailsSample2();
        assertThat(orderDetails1).isNotEqualTo(orderDetails2);
    }

    @Test
    void emailsNotificationsTest() {
        OrderDetails orderDetails = getOrderDetailsRandomSampleGenerator();
        EmailsNotifications emailsNotificationsBack = getEmailsNotificationsRandomSampleGenerator();

        orderDetails.addEmailsNotifications(emailsNotificationsBack);
        assertThat(orderDetails.getEmailsNotifications()).containsOnly(emailsNotificationsBack);
        assertThat(emailsNotificationsBack.getOrderDetails()).isEqualTo(orderDetails);

        orderDetails.removeEmailsNotifications(emailsNotificationsBack);
        assertThat(orderDetails.getEmailsNotifications()).doesNotContain(emailsNotificationsBack);
        assertThat(emailsNotificationsBack.getOrderDetails()).isNull();

        orderDetails.emailsNotifications(new HashSet<>(Set.of(emailsNotificationsBack)));
        assertThat(orderDetails.getEmailsNotifications()).containsOnly(emailsNotificationsBack);
        assertThat(emailsNotificationsBack.getOrderDetails()).isEqualTo(orderDetails);

        orderDetails.setEmailsNotifications(new HashSet<>());
        assertThat(orderDetails.getEmailsNotifications()).doesNotContain(emailsNotificationsBack);
        assertThat(emailsNotificationsBack.getOrderDetails()).isNull();
    }

    @Test
    void orderTest() {
        OrderDetails orderDetails = getOrderDetailsRandomSampleGenerator();
        Order orderBack = getOrderRandomSampleGenerator();

        orderDetails.setOrder(orderBack);
        assertThat(orderDetails.getOrder()).isEqualTo(orderBack);

        orderDetails.order(null);
        assertThat(orderDetails.getOrder()).isNull();
    }
}
