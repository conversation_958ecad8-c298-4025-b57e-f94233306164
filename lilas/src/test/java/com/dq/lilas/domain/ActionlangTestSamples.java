package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class ActionlangTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static Actionlang getActionlangSample1() {
        return new Actionlang().id(1L).appType("appType1").lang("lang1").abrv("abrv1");
    }

    public static Actionlang getActionlangSample2() {
        return new Actionlang().id(2L).appType("appType2").lang("lang2").abrv("abrv2");
    }

    public static Actionlang getActionlangRandomSampleGenerator() {
        return new Actionlang()
            .id(longCount.incrementAndGet())
            .appType(UUID.randomUUID().toString())
            .lang(UUID.randomUUID().toString())
            .abrv(UUID.randomUUID().toString());
    }
}
