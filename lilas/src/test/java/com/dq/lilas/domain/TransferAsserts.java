package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class TransferAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTransferAllPropertiesEquals(Transfer expected, Transfer actual) {
        assertTransferAutoGeneratedPropertiesEquals(expected, actual);
        assertTransferAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTransferAllUpdatablePropertiesEquals(Transfer expected, Transfer actual) {
        assertTransferUpdatableFieldsEquals(expected, actual);
        assertTransferUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTransferAutoGeneratedPropertiesEquals(Transfer expected, Transfer actual) {
        assertThat(actual)
            .as("Verify Transfer auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTransferUpdatableFieldsEquals(Transfer expected, Transfer actual) {
        assertThat(actual)
            .as("Verify Transfer relevant properties")
            .satisfies(a -> assertThat(a.getDocyear()).as("check docyear").isEqualTo(expected.getDocyear()))
            .satisfies(a -> assertThat(a.getTexttransfer()).as("check texttransfer").isEqualTo(expected.getTexttransfer()))
            .satisfies(a -> assertThat(a.getDatejctransfer()).as("check datejctransfer").isEqualTo(expected.getDatejctransfer()))
            .satisfies(a -> assertThat(a.getDatehjrtransfer()).as("check datehjrtransfer").isEqualTo(expected.getDatehjrtransfer()))
            .satisfies(a -> assertThat(a.getStatustransfer()).as("check statustransfer").isEqualTo(expected.getStatustransfer()))
            .satisfies(a -> assertThat(a.getDatesendjctransfer()).as("check datesendjctransfer").isEqualTo(expected.getDatesendjctransfer())
            )
            .satisfies(a ->
                assertThat(a.getDatesendhjrtransfer()).as("check datesendhjrtransfer").isEqualTo(expected.getDatesendhjrtransfer())
            )
            .satisfies(a -> assertThat(a.getSavetransfer()).as("check savetransfer").isEqualTo(expected.getSavetransfer()))
            .satisfies(a -> assertThat(a.getNumcopy()).as("check numcopy").isEqualTo(expected.getNumcopy()))
            .satisfies(a -> assertThat(a.getHighlevel()).as("check highlevel").isEqualTo(expected.getHighlevel()))
            .satisfies(a -> assertThat(a.getConfidentiel()).as("check confidentiel").isEqualTo(expected.getConfidentiel()))
            .satisfies(a -> assertThat(a.getPriority()).as("check priority").isEqualTo(expected.getPriority()))
            .satisfies(a -> assertThat(a.getTimeaction()).as("check timeaction").isEqualTo(expected.getTimeaction()))
            .satisfies(a -> assertThat(a.getDeadline()).as("check deadline").isEqualTo(expected.getDeadline()))
            .satisfies(a -> assertThat(a.getRappelnum()).as("check rappelnum").isEqualTo(expected.getRappelnum()))
            .satisfies(a -> assertThat(a.getRappeltype()).as("check rappeltype").isEqualTo(expected.getRappeltype()))
            .satisfies(a -> assertThat(a.getReadrequest()).as("check readrequest").isEqualTo(expected.getReadrequest()))
            .satisfies(a -> assertThat(a.getTypereceive()).as("check typereceive").isEqualTo(expected.getTypereceive()))
            .satisfies(a -> assertThat(a.getDatejcreceive()).as("check datejcreceive").isEqualTo(expected.getDatejcreceive()))
            .satisfies(a -> assertThat(a.getDatehjrreceive()).as("check datehjrreceive").isEqualTo(expected.getDatehjrreceive()))
            .satisfies(a -> assertThat(a.getHeurearch()).as("check heurearch").isEqualTo(expected.getHeurearch()))
            .satisfies(a -> assertThat(a.getActiontype()).as("check actiontype").isEqualTo(expected.getActiontype()))
            .satisfies(a -> assertThat(a.getComments()).as("check comments").isEqualTo(expected.getComments()))
            .satisfies(a -> assertThat(a.getDatearch()).as("check datearch").isEqualTo(expected.getDatearch()))
            .satisfies(a -> assertThat(a.getDatearchhj()).as("check datearchhj").isEqualTo(expected.getDatearchhj()))
            .satisfies(a -> assertThat(a.getLasttransserial()).as("check lasttransserial").isEqualTo(expected.getLasttransserial()))
            .satisfies(a -> assertThat(a.getAdrsbooktransto()).as("check adrsbooktransto").isEqualTo(expected.getAdrsbooktransto()))
            .satisfies(a -> assertThat(a.getStatusreceiveto()).as("check statusreceiveto").isEqualTo(expected.getStatusreceiveto()))
            .satisfies(a -> assertThat(a.getCommentsreceiveto()).as("check commentsreceiveto").isEqualTo(expected.getCommentsreceiveto()))
            .satisfies(a ->
                assertThat(a.getReceivedatejcuserto()).as("check receivedatejcuserto").isEqualTo(expected.getReceivedatejcuserto())
            )
            .satisfies(a ->
                assertThat(a.getReceivedatehjruserto()).as("check receivedatehjruserto").isEqualTo(expected.getReceivedatehjruserto())
            )
            .satisfies(a -> assertThat(a.getTypetransfer()).as("check typetransfer").isEqualTo(expected.getTypetransfer()))
            .satisfies(a -> assertThat(a.getTransrecserial()).as("check transrecserial").isEqualTo(expected.getTransrecserial()))
            .satisfies(a -> assertThat(a.getAttach()).as("check attach").isEqualTo(expected.getAttach()))
            .satisfies(a -> assertThat(a.getTranstype()).as("check transtype").isEqualTo(expected.getTranstype()))
            .satisfies(a -> assertThat(a.getOrdernbr()).as("check ordernbr").isEqualTo(expected.getOrdernbr()))
            .satisfies(a -> assertThat(a.getHeureaction()).as("check heureaction").isEqualTo(expected.getHeureaction()))
            .satisfies(a -> assertThat(a.getDatejcaction()).as("check datejcaction").isEqualTo(expected.getDatejcaction()))
            .satisfies(a -> assertThat(a.getDatehjraction()).as("check datehjraction").isEqualTo(expected.getDatehjraction()))
            .satisfies(a -> assertThat(a.getStatusdenied()).as("check statusdenied").isEqualTo(expected.getStatusdenied()))
            .satisfies(a -> assertThat(a.getSubjectcorresp()).as("check subjectcorresp").isEqualTo(expected.getSubjectcorresp()))
            .satisfies(a -> assertThat(a.getDatejccorresp()).as("check datejccorresp").isEqualTo(expected.getDatejccorresp()))
            .satisfies(a -> assertThat(a.getDatehjrcorresp()).as("check datehjrcorresp").isEqualTo(expected.getDatehjrcorresp()))
            .satisfies(a -> assertThat(a.getOldstatus()).as("check oldstatus").isEqualTo(expected.getOldstatus()))
            .satisfies(a -> assertThat(a.getStep()).as("check step").isEqualTo(expected.getStep()))
            .satisfies(a -> assertThat(a.getTypeprocess()).as("check typeprocess").isEqualTo(expected.getTypeprocess()))
            .satisfies(a -> assertThat(a.getCodetask()).as("check codetask").isEqualTo(expected.getCodetask()))
            .satisfies(a -> assertThat(a.getRefusetext()).as("check refusetext").isEqualTo(expected.getRefusetext()))
            .satisfies(a -> assertThat(a.getStatusrefused()).as("check statusrefused").isEqualTo(expected.getStatusrefused()))
            .satisfies(a -> assertThat(a.getBidadrsbook()).as("check bidadrsbook").isEqualTo(expected.getBidadrsbook()))
            .satisfies(a -> assertThat(a.getPagenbrpaper()).as("check pagenbrpaper").isEqualTo(expected.getPagenbrpaper()))
            .satisfies(a -> assertThat(a.getFlagprint()).as("check flagprint").isEqualTo(expected.getFlagprint()))
            .satisfies(a -> assertThat(a.getDateprint()).as("check dateprint").isEqualTo(expected.getDateprint()))
            .satisfies(a -> assertThat(a.getDatehjrprint()).as("check datehjrprint").isEqualTo(expected.getDatehjrprint()))
            .satisfies(a -> assertThat(a.getDatejcdelete()).as("check datejcdelete").isEqualTo(expected.getDatejcdelete()))
            .satisfies(a -> assertThat(a.getDatejcrevoke()).as("check datejcrevoke").isEqualTo(expected.getDatejcrevoke()))
            .satisfies(a -> assertThat(a.getDatehjrrevoke()).as("check datehjrrevoke").isEqualTo(expected.getDatehjrrevoke()))
            .satisfies(a -> assertThat(a.getGabaritcontext()).as("check gabaritcontext").isEqualTo(expected.getGabaritcontext()))
            .satisfies(a -> assertThat(a.getApprovedspeech()).as("check approvedspeech").isEqualTo(expected.getApprovedspeech()))
            .satisfies(a ->
                assertThat(a.getDatejcapprovedspeech()).as("check datejcapprovedspeech").isEqualTo(expected.getDatejcapprovedspeech())
            )
            .satisfies(a ->
                assertThat(a.getDatehjrapprovedspeech()).as("check datehjrapprovedspeech").isEqualTo(expected.getDatehjrapprovedspeech())
            )
            .satisfies(a -> assertThat(a.getConformitytask()).as("check conformitytask").isEqualTo(expected.getConformitytask()))
            .satisfies(a -> assertThat(a.getUseradrsbook()).as("check useradrsbook").isEqualTo(expected.getUseradrsbook()))
            .satisfies(a -> assertThat(a.getStepmaxwf()).as("check stepmaxwf").isEqualTo(expected.getStepmaxwf()))
            .satisfies(a -> assertThat(a.getIncidenttransfer()).as("check incidenttransfer").isEqualTo(expected.getIncidenttransfer()))
            .satisfies(a ->
                assertThat(a.getQualificationincident()).as("check qualificationincident").isEqualTo(expected.getQualificationincident())
            )
            .satisfies(a -> assertThat(a.getCategorieincident()).as("check categorieincident").isEqualTo(expected.getCategorieincident()))
            .satisfies(a -> assertThat(a.getStatutincident()).as("check statutincident").isEqualTo(expected.getStatutincident()))
            .satisfies(a -> assertThat(a.getCriticiteincident()).as("check criticiteincident").isEqualTo(expected.getCriticiteincident()))
            .satisfies(a -> assertThat(a.getVoiceId()).as("check voiceId").isEqualTo(expected.getVoiceId()))
            .satisfies(a -> assertThat(a.getFavoris()).as("check favoris").isEqualTo(expected.getFavoris()))
            .satisfies(a -> assertThat(a.getCheckinboxfavorite()).as("check checkinboxfavorite").isEqualTo(expected.getCheckinboxfavorite())
            )
            .satisfies(a -> assertThat(a.getCheckclosefavorite()).as("check checkclosefavorite").isEqualTo(expected.getCheckclosefavorite())
            )
            .satisfies(a -> assertThat(a.getCheckfavorite()).as("check checkfavorite").isEqualTo(expected.getCheckfavorite()))
            .satisfies(a -> assertThat(a.getTaskcategId()).as("check taskcategId").isEqualTo(expected.getTaskcategId()))
            .satisfies(a -> assertThat(a.getPinrequired()).as("check pinrequired").isEqualTo(expected.getPinrequired()))
            .satisfies(a -> assertThat(a.getCodePin()).as("check codePin").isEqualTo(expected.getCodePin()))
            .satisfies(a -> assertThat(a.getSendwithmail()).as("check sendwithmail").isEqualTo(expected.getSendwithmail()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTransferUpdatableRelationshipsEquals(Transfer expected, Transfer actual) {
        assertThat(actual)
            .as("Verify Transfer relationships")
            .satisfies(a -> assertThat(a.getTransmoth()).as("check transmoth").isEqualTo(expected.getTransmoth()))
            .satisfies(a -> assertThat(a.getCorrespondencecopy()).as("check correspondencecopy").isEqualTo(expected.getCorrespondencecopy())
            )
            .satisfies(a -> assertThat(a.getCorrespondence()).as("check correspondence").isEqualTo(expected.getCorrespondence()))
            .satisfies(a -> assertThat(a.getDeliverymode()).as("check deliverymode").isEqualTo(expected.getDeliverymode()))
            .satisfies(a -> assertThat(a.getEmployee()).as("check employee").isEqualTo(expected.getEmployee()))
            .satisfies(a -> assertThat(a.getUnit()).as("check unit").isEqualTo(expected.getUnit()))
            .satisfies(a -> assertThat(a.getAction()).as("check action").isEqualTo(expected.getAction()))
            .satisfies(a -> assertThat(a.getUserreceive()).as("check userreceive").isEqualTo(expected.getUserreceive()))
            .satisfies(a -> assertThat(a.getUsertrans()).as("check usertrans").isEqualTo(expected.getUsertrans()))
            .satisfies(a -> assertThat(a.getUsertransto()).as("check usertransto").isEqualTo(expected.getUsertransto()))
            .satisfies(a -> assertThat(a.getUnittransto()).as("check unittransto").isEqualTo(expected.getUnittransto()))
            .satisfies(a -> assertThat(a.getUserrevoke()).as("check userrevoke").isEqualTo(expected.getUserrevoke()))
            .satisfies(a -> assertThat(a.getUserreceiveto()).as("check userreceiveto").isEqualTo(expected.getUserreceiveto()))
            .satisfies(a -> assertThat(a.getUseraction()).as("check useraction").isEqualTo(expected.getUseraction()))
            .satisfies(a -> assertThat(a.getFromdept()).as("check fromdept").isEqualTo(expected.getFromdept()))
            .satisfies(a -> assertThat(a.getTransprincip()).as("check transprincip").isEqualTo(expected.getTransprincip()))
            .satisfies(a -> assertThat(a.getTypecorrespondence()).as("check typecorrespondence").isEqualTo(expected.getTypecorrespondence())
            );
    }
}
