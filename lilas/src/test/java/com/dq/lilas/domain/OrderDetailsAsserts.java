package com.dq.lilas.domain;

import static com.dq.lilas.domain.AssertUtils.bigDecimalCompareTo;
import static org.assertj.core.api.Assertions.assertThat;

public class OrderDetailsAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertOrderDetailsAllPropertiesEquals(OrderDetails expected, OrderDetails actual) {
        assertOrderDetailsAutoGeneratedPropertiesEquals(expected, actual);
        assertOrderDetailsAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertOrderDetailsAllUpdatablePropertiesEquals(OrderDetails expected, OrderDetails actual) {
        assertOrderDetailsUpdatableFieldsEquals(expected, actual);
        assertOrderDetailsUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertOrderDetailsAutoGeneratedPropertiesEquals(OrderDetails expected, OrderDetails actual) {
        assertThat(actual)
            .as("Verify OrderDetails auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertOrderDetailsUpdatableFieldsEquals(OrderDetails expected, OrderDetails actual) {
        assertThat(actual)
            .as("Verify OrderDetails relevant properties")
            .satisfies(a ->
                assertThat(a.getQuantity()).as("check quantity").isEqualTo(expected.getQuantity())
            )
            .satisfies(a ->
                assertThat(a.getUpdatedQty())
                    .as("check updatedQty")
                    .isEqualTo(expected.getUpdatedQty())
            )
            .satisfies(a -> assertThat(a.getDiscount()).as("check discount").isEqualTo(expected.getDiscount()))
            .satisfies(a -> assertThat(a.getUpdatedDiscount()).as("check updatedDiscount").isEqualTo(expected.getUpdatedDiscount()))
            .satisfies(a -> assertThat(a.getUnitPrice()).as("check unitPrice").isEqualTo(expected.getUnitPrice()))
            .satisfies(a -> assertThat(a.getUpdatedUnitPrice()).as("check updatedUnitPrice").isEqualTo(expected.getUpdatedUnitPrice()))
            .satisfies(a -> assertThat(a.getOrderLineJson()).as("check orderLineJson").isEqualTo(expected.getOrderLineJson()))
            .satisfies(a -> assertThat(a.getAvailability()).as("check availability").isEqualTo(expected.getAvailability()))
            // Note: validConditions and injected fields have been removed from the entity
            .satisfies(a -> assertThat(a.getBarcode()).as("check barcode").isEqualTo(expected.getBarcode()))
            .satisfies(a -> assertThat(a.getUpdatedBarcode()).as("check updatedBarcode").isEqualTo(expected.getUpdatedBarcode()))
            .satisfies(a -> assertThat(a.getInternalCode()).as("check internalCode").isEqualTo(expected.getInternalCode()))
            .satisfies(a ->
                assertThat(a.getUpdatedInternalCode()).as("check updatedInternalCode").isEqualTo(expected.getUpdatedInternalCode())
            )
            .satisfies(a -> assertThat(a.getDiscountStatus()).as("check discountStatus").isEqualTo(expected.getDiscountStatus()))
            .satisfies(a -> assertThat(a.getPriceStatus()).as("check priceStatus").isEqualTo(expected.getPriceStatus()))
            .satisfies(a -> assertThat(a.getQuantityStatus()).as("check quantityStatus").isEqualTo(expected.getQuantityStatus()))
            .satisfies(a -> assertThat(a.getProductStatus()).as("check productStatus").isEqualTo(expected.getProductStatus()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertOrderDetailsUpdatableRelationshipsEquals(OrderDetails expected, OrderDetails actual) {
        assertThat(actual)
            .as("Verify OrderDetails relationships")
            .satisfies(a -> assertThat(a.getOrder()).as("check order").isEqualTo(expected.getOrder()));
    }
}
