package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class DeliverymodeTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static Deliverymode getDeliverymodeSample1() {
        return new Deliverymode().id(1L).statut("statut1").orderpos("orderpos1");
    }

    public static Deliverymode getDeliverymodeSample2() {
        return new Deliverymode().id(2L).statut("statut2").orderpos("orderpos2");
    }

    public static Deliverymode getDeliverymodeRandomSampleGenerator() {
        return new Deliverymode()
            .id(longCount.incrementAndGet())
            .statut(UUID.randomUUID().toString())
            .orderpos(UUID.randomUUID().toString());
    }
}
