package com.dq.lilas.domain;

import static com.dq.lilas.domain.EmailsNotificationsTestSamples.*;
import static com.dq.lilas.domain.OrderDetailsTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import org.junit.jupiter.api.Test;

class EmailsNotificationsTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(EmailsNotifications.class);
        EmailsNotifications emailsNotifications1 = getEmailsNotificationsSample1();
        EmailsNotifications emailsNotifications2 = new EmailsNotifications();
        assertThat(emailsNotifications1).isNotEqualTo(emailsNotifications2);

        emailsNotifications2.setId(emailsNotifications1.getId());
        assertThat(emailsNotifications1).isEqualTo(emailsNotifications2);

        emailsNotifications2 = getEmailsNotificationsSample2();
        assertThat(emailsNotifications1).isNotEqualTo(emailsNotifications2);
    }

    @Test
    void orderDetailsTest() {
        EmailsNotifications emailsNotifications = getEmailsNotificationsRandomSampleGenerator();
        OrderDetails orderDetailsBack = getOrderDetailsRandomSampleGenerator();

        emailsNotifications.setOrderDetails(orderDetailsBack);
        assertThat(emailsNotifications.getOrderDetails()).isEqualTo(orderDetailsBack);

        emailsNotifications.orderDetails(null);
        assertThat(emailsNotifications.getOrderDetails()).isNull();
    }
}
