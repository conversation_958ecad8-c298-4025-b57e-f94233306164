package com.dq.lilas.domain;

import static com.dq.lilas.domain.DailyStockTestSamples.*;
import static com.dq.lilas.domain.ProductsListTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import org.junit.jupiter.api.Test;

class DailyStockTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(DailyStock.class);
        DailyStock dailyStock1 = getDailyStockSample1();
        DailyStock dailyStock2 = new DailyStock();
        assertThat(dailyStock1).isNotEqualTo(dailyStock2);

        dailyStock2.setId(dailyStock1.getId());
        assertThat(dailyStock1).isEqualTo(dailyStock2);

        dailyStock2 = getDailyStockSample2();
        assertThat(dailyStock1).isNotEqualTo(dailyStock2);
    }

    @Test
    void productsListTest() {
        DailyStock dailyStock = getDailyStockRandomSampleGenerator();
        ProductsList productsListBack = getProductsListRandomSampleGenerator();

        dailyStock.setProductsList(productsListBack);
        assertThat(dailyStock.getProductsList()).isEqualTo(productsListBack);

        dailyStock.productsList(null);
        assertThat(dailyStock.getProductsList()).isNull();
    }
}
