package com.dq.lilas.domain;

import static com.dq.lilas.domain.CompanyTestSamples.*;
import static com.dq.lilas.domain.DailyBatchesTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import org.junit.jupiter.api.Test;

class DailyBatchesTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(DailyBatches.class);
        DailyBatches dailyBatches1 = getDailyBatchesSample1();
        DailyBatches dailyBatches2 = new DailyBatches();
        assertThat(dailyBatches1).isNotEqualTo(dailyBatches2);

        dailyBatches2.setId(dailyBatches1.getId());
        assertThat(dailyBatches1).isEqualTo(dailyBatches2);

        dailyBatches2 = getDailyBatchesSample2();
        assertThat(dailyBatches1).isNotEqualTo(dailyBatches2);
    }

    @Test
    void companyTest() {
        DailyBatches dailyBatches = getDailyBatchesRandomSampleGenerator();
        Company companyBack = getCompanyRandomSampleGenerator();

        dailyBatches.setCompany(companyBack);
        assertThat(dailyBatches.getCompany()).isEqualTo(companyBack);

        dailyBatches.company(null);
        assertThat(dailyBatches.getCompany()).isNull();
    }
}
