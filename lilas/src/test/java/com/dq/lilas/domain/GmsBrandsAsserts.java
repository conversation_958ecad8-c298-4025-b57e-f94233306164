package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class GmsBrandsAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertGmsBrandsAllPropertiesEquals(GmsBrands expected, GmsBrands actual) {
        assertGmsBrandsAutoGeneratedPropertiesEquals(expected, actual);
        assertGmsBrandsAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertGmsBrandsAllUpdatablePropertiesEquals(GmsBrands expected, GmsBrands actual) {
        assertGmsBrandsUpdatableFieldsEquals(expected, actual);
        assertGmsBrandsUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertGmsBrandsAutoGeneratedPropertiesEquals(GmsBrands expected, GmsBrands actual) {
        assertThat(actual)
            .as("Verify GmsBrands auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertGmsBrandsUpdatableFieldsEquals(GmsBrands expected, GmsBrands actual) {
        assertThat(actual)
            .as("Verify GmsBrands relevant properties")
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()))
            .satisfies(a -> assertThat(a.getClasse()).as("check classe").isEqualTo(expected.getClasse()))
            .satisfies(a -> assertThat(a.getIconPath()).as("check iconPath").isEqualTo(expected.getIconPath()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertGmsBrandsUpdatableRelationshipsEquals(GmsBrands expected, GmsBrands actual) {
        // empty method
    }
}
