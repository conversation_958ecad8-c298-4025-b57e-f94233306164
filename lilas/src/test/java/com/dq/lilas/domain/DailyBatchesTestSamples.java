package com.dq.lilas.domain;

import java.util.Random;
import java.util.concurrent.atomic.AtomicLong;

public class DailyBatchesTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static DailyBatches getDailyBatchesSample1() {
        return new DailyBatches().id(1L);
    }

    public static DailyBatches getDailyBatchesSample2() {
        return new DailyBatches().id(2L);
    }

    public static DailyBatches getDailyBatchesRandomSampleGenerator() {
        return new DailyBatches().id(longCount.incrementAndGet());
    }
}
