package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class DeliverymodeAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDeliverymodeAllPropertiesEquals(Deliverymode expected, Deliverymode actual) {
        assertDeliverymodeAutoGeneratedPropertiesEquals(expected, actual);
        assertDeliverymodeAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDeliverymodeAllUpdatablePropertiesEquals(Deliverymode expected, Deliverymode actual) {
        assertDeliverymodeUpdatableFieldsEquals(expected, actual);
        assertDeliverymodeUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDeliverymodeAutoGeneratedPropertiesEquals(Deliverymode expected, Deliverymode actual) {
        assertThat(actual)
            .as("Verify Deliverymode auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDeliverymodeUpdatableFieldsEquals(Deliverymode expected, Deliverymode actual) {
        assertThat(actual)
            .as("Verify Deliverymode relevant properties")
            .satisfies(a -> assertThat(a.getStatut()).as("check statut").isEqualTo(expected.getStatut()))
            .satisfies(a -> assertThat(a.getOrderpos()).as("check orderpos").isEqualTo(expected.getOrderpos()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertDeliverymodeUpdatableRelationshipsEquals(Deliverymode expected, Deliverymode actual) {
        // empty method
    }
}
