package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class CadencierAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCadencierAllPropertiesEquals(Cadencier expected, Cadencier actual) {
        assertCadencierAutoGeneratedPropertiesEquals(expected, actual);
        assertCadencierAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCadencierAllUpdatablePropertiesEquals(Cadencier expected, Cadencier actual) {
        assertCadencierUpdatableFieldsEquals(expected, actual);
        assertCadencierUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCadencierAutoGeneratedPropertiesEquals(Cadencier expected, Cadencier actual) {
        assertThat(actual)
            .as("Verify Cadencier auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCadencierUpdatableFieldsEquals(Cadencier expected, Cadencier actual) {
        assertThat(actual)
            .as("Verify Cadencier relevant properties")
            .satisfies(a -> assertThat(a.getOrderDay()).as("check orderDay").isEqualTo(expected.getOrderDay()))
            .satisfies(a -> assertThat(a.getDeliveryDay()).as("check deliveryDay").isEqualTo(expected.getDeliveryDay()))
            .satisfies(a -> assertThat(a.getRegion()).as("check region").isEqualTo(expected.getRegion()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCadencierUpdatableRelationshipsEquals(Cadencier expected, Cadencier actual) {
        // empty method
    }
}
