package com.dq.lilas.domain;

import static com.dq.lilas.domain.ActionTestSamples.*;
import static com.dq.lilas.domain.ActionlangTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.Test;

class ActionTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Action.class);
        Action action1 = getActionSample1();
        Action action2 = new Action();
        assertThat(action1).isNotEqualTo(action2);

        action2.setId(action1.getId());
        assertThat(action1).isEqualTo(action2);

        action2 = getActionSample2();
        assertThat(action1).isNotEqualTo(action2);
    }

    @Test
    void actionlangsTest() {
        Action action = getActionRandomSampleGenerator();
        Actionlang actionlangBack = getActionlangRandomSampleGenerator();

        action.addActionlangs(actionlangBack);
        assertThat(action.getActionlangs()).containsOnly(actionlangBack);
        assertThat(actionlangBack.getAction()).isEqualTo(action);

        action.removeActionlangs(actionlangBack);
        assertThat(action.getActionlangs()).doesNotContain(actionlangBack);
        assertThat(actionlangBack.getAction()).isNull();

        action.actionlangs(new HashSet<>(Set.of(actionlangBack)));
        assertThat(action.getActionlangs()).containsOnly(actionlangBack);
        assertThat(actionlangBack.getAction()).isEqualTo(action);

        action.setActionlangs(new HashSet<>());
        assertThat(action.getActionlangs()).doesNotContain(actionlangBack);
        assertThat(actionlangBack.getAction()).isNull();
    }
}
