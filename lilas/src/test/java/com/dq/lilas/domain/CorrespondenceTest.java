package com.dq.lilas.domain;

import static com.dq.lilas.domain.CorrespondenceTestSamples.*;
import static com.dq.lilas.domain.DeliverymodeTestSamples.*;
import static com.dq.lilas.domain.EmployeeTestSamples.*;
import static com.dq.lilas.domain.TypecorrespondenceTestSamples.*;
import static com.dq.lilas.domain.UnitTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import org.junit.jupiter.api.Test;

class CorrespondenceTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Correspondence.class);
        Correspondence correspondence1 = getCorrespondenceSample1();
        Correspondence correspondence2 = new Correspondence();
        assertThat(correspondence1).isNotEqualTo(correspondence2);

        correspondence2.setId(correspondence1.getId());
        assertThat(correspondence1).isEqualTo(correspondence2);

        correspondence2 = getCorrespondenceSample2();
        assertThat(correspondence1).isNotEqualTo(correspondence2);
    }

    @Test
    void employeeTest() {
        Correspondence correspondence = getCorrespondenceRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        correspondence.setEmployee(employeeBack);
        assertThat(correspondence.getEmployee()).isEqualTo(employeeBack);

        correspondence.employee(null);
        assertThat(correspondence.getEmployee()).isNull();
    }

    @Test
    void unitTest() {
        Correspondence correspondence = getCorrespondenceRandomSampleGenerator();
        Unit unitBack = getUnitRandomSampleGenerator();

        correspondence.setUnit(unitBack);
        assertThat(correspondence.getUnit()).isEqualTo(unitBack);

        correspondence.unit(null);
        assertThat(correspondence.getUnit()).isNull();
    }

    @Test
    void deliverymodeTest() {
        Correspondence correspondence = getCorrespondenceRandomSampleGenerator();
        Deliverymode deliverymodeBack = getDeliverymodeRandomSampleGenerator();

        correspondence.setDeliverymode(deliverymodeBack);
        assertThat(correspondence.getDeliverymode()).isEqualTo(deliverymodeBack);

        correspondence.deliverymode(null);
        assertThat(correspondence.getDeliverymode()).isNull();
    }

    @Test
    void typecorrespondenceTest() {
        Correspondence correspondence = getCorrespondenceRandomSampleGenerator();
        Typecorrespondence typecorrespondenceBack = getTypecorrespondenceRandomSampleGenerator();

        correspondence.setTypecorrespondence(typecorrespondenceBack);
        assertThat(correspondence.getTypecorrespondence()).isEqualTo(typecorrespondenceBack);

        correspondence.typecorrespondence(null);
        assertThat(correspondence.getTypecorrespondence()).isNull();
    }
}
