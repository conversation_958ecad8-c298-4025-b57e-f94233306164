package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class CorrespondencecopyAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCorrespondencecopyAllPropertiesEquals(Correspondencecopy expected, Correspondencecopy actual) {
        assertCorrespondencecopyAutoGeneratedPropertiesEquals(expected, actual);
        assertCorrespondencecopyAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCorrespondencecopyAllUpdatablePropertiesEquals(Correspondencecopy expected, Correspondencecopy actual) {
        assertCorrespondencecopyUpdatableFieldsEquals(expected, actual);
        assertCorrespondencecopyUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCorrespondencecopyAutoGeneratedPropertiesEquals(Correspondencecopy expected, Correspondencecopy actual) {
        assertThat(actual)
            .as("Verify Correspondencecopy auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCorrespondencecopyUpdatableFieldsEquals(Correspondencecopy expected, Correspondencecopy actual) {
        assertThat(actual)
            .as("Verify Correspondencecopy relevant properties")
            .satisfies(a -> assertThat(a.getNumcopy()).as("check numcopy").isEqualTo(expected.getNumcopy()))
            .satisfies(a -> assertThat(a.getComments()).as("check comments").isEqualTo(expected.getComments()))
            .satisfies(a -> assertThat(a.getDatejccreate()).as("check datejccreate").isEqualTo(expected.getDatejccreate()))
            .satisfies(a -> assertThat(a.getDatehjrcreate()).as("check datehjrcreate").isEqualTo(expected.getDatehjrcreate()))
            .satisfies(a -> assertThat(a.getTypereceive()).as("check typereceive").isEqualTo(expected.getTypereceive()))
            .satisfies(a -> assertThat(a.getTypecopy()).as("check typecopy").isEqualTo(expected.getTypecopy()))
            .satisfies(a -> assertThat(a.getUserreceive()).as("check userreceive").isEqualTo(expected.getUserreceive()))
            .satisfies(a -> assertThat(a.getDatejcreceive()).as("check datejcreceive").isEqualTo(expected.getDatejcreceive()))
            .satisfies(a -> assertThat(a.getDatehjrreceive()).as("check datehjrreceive").isEqualTo(expected.getDatehjrreceive()))
            .satisfies(a -> assertThat(a.getDatejcaction()).as("check datejcaction").isEqualTo(expected.getDatejcaction()))
            .satisfies(a -> assertThat(a.getDatehjraction()).as("check datehjraction").isEqualTo(expected.getDatehjraction()))
            .satisfies(a -> assertThat(a.getActiontype()).as("check actiontype").isEqualTo(expected.getActiontype()))
            .satisfies(a -> assertThat(a.getSavecorrespcpy()).as("check savecorrespcpy").isEqualTo(expected.getSavecorrespcpy()))
            .satisfies(a -> assertThat(a.getOrdernbr()).as("check ordernbr").isEqualTo(expected.getOrdernbr()))
            .satisfies(a -> assertThat(a.getPagenbr()).as("check pagenbr").isEqualTo(expected.getPagenbr()))
            .satisfies(a -> assertThat(a.getExpno()).as("check expno").isEqualTo(expected.getExpno()))
            .satisfies(a -> assertThat(a.getExpyear()).as("check expyear").isEqualTo(expected.getExpyear()))
            .satisfies(a -> assertThat(a.getDocyear()).as("check docyear").isEqualTo(expected.getDocyear()))
            .satisfies(a -> assertThat(a.getCategorycorresp()).as("check categorycorresp").isEqualTo(expected.getCategorycorresp()))
            .satisfies(a -> assertThat(a.getHeureaction()).as("check heureaction").isEqualTo(expected.getHeureaction()))
            .satisfies(a -> assertThat(a.getStatusdeniedcopy()).as("check statusdeniedcopy").isEqualTo(expected.getStatusdeniedcopy()))
            .satisfies(a -> assertThat(a.getPagenbrpaper()).as("check pagenbrpaper").isEqualTo(expected.getPagenbrpaper()))
            .satisfies(a -> assertThat(a.getTaskscan()).as("check taskscan").isEqualTo(expected.getTaskscan()))
            .satisfies(a -> assertThat(a.getPathtransto()).as("check pathtransto").isEqualTo(expected.getPathtransto()))
            .satisfies(a -> assertThat(a.getPathtranscc()).as("check pathtranscc").isEqualTo(expected.getPathtranscc()))
            .satisfies(a -> assertThat(a.getPathtranstolib()).as("check pathtranstolib").isEqualTo(expected.getPathtranstolib()))
            .satisfies(a -> assertThat(a.getPathtranscclib()).as("check pathtranscclib").isEqualTo(expected.getPathtranscclib()))
            .satisfies(a -> assertThat(a.getFlggroup()).as("check flggroup").isEqualTo(expected.getFlggroup()))
            .satisfies(a -> assertThat(a.getDatejcdelete()).as("check datejcdelete").isEqualTo(expected.getDatejcdelete()))
            .satisfies(a -> assertThat(a.getDatejcrevoke()).as("check datejcrevoke").isEqualTo(expected.getDatejcrevoke()))
            .satisfies(a -> assertThat(a.getDatehjrrevoke()).as("check datehjrrevoke").isEqualTo(expected.getDatehjrrevoke()))
            .satisfies(a -> assertThat(a.getDateremove()).as("check dateremove").isEqualTo(expected.getDateremove()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertCorrespondencecopyUpdatableRelationshipsEquals(Correspondencecopy expected, Correspondencecopy actual) {
        assertThat(actual)
            .as("Verify Correspondencecopy relationships")
            .satisfies(a -> assertThat(a.getCorrespondence()).as("check correspondence").isEqualTo(expected.getCorrespondence()))
            .satisfies(a -> assertThat(a.getEmployee()).as("check employee").isEqualTo(expected.getEmployee()))
            .satisfies(a -> assertThat(a.getUnit()).as("check unit").isEqualTo(expected.getUnit()))
            .satisfies(a -> assertThat(a.getAction()).as("check action").isEqualTo(expected.getAction()))
            .satisfies(a -> assertThat(a.getUseraction()).as("check useraction").isEqualTo(expected.getUseraction()))
            .satisfies(a -> assertThat(a.getUserrevoke()).as("check userrevoke").isEqualTo(expected.getUserrevoke()))
            .satisfies(a -> assertThat(a.getUserremove()).as("check userremove").isEqualTo(expected.getUserremove()));
    }
}
