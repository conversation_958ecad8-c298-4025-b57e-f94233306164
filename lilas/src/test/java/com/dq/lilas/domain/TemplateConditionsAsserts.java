package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class TemplateConditionsAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTemplateConditionsAllPropertiesEquals(TemplateConditions expected, TemplateConditions actual) {
        assertTemplateConditionsAutoGeneratedPropertiesEquals(expected, actual);
        assertTemplateConditionsAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTemplateConditionsAllUpdatablePropertiesEquals(TemplateConditions expected, TemplateConditions actual) {
        assertTemplateConditionsUpdatableFieldsEquals(expected, actual);
        assertTemplateConditionsUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTemplateConditionsAutoGeneratedPropertiesEquals(TemplateConditions expected, TemplateConditions actual) {
        assertThat(actual)
            .as("Verify TemplateConditions auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTemplateConditionsUpdatableFieldsEquals(TemplateConditions expected, TemplateConditions actual) {
        assertThat(actual)
            .as("Verify TemplateConditions relevant properties")
            .satisfies(a -> assertThat(a.getName()).as("check name").isEqualTo(expected.getName()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertTemplateConditionsUpdatableRelationshipsEquals(TemplateConditions expected, TemplateConditions actual) {
        assertThat(actual)
            .as("Verify TemplateConditions relationships")
            .satisfies(a -> assertThat(a.getEmployee()).as("check employee").isEqualTo(expected.getEmployee()));
    }
}
