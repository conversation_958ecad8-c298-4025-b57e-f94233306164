package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class TypecorrespondenceTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static Typecorrespondence getTypecorrespondenceSample1() {
        return new Typecorrespondence()
            .id(1L)
            .typecorresp("typecorresp1")
            .typedecision("typedecision1")
            .hosp("hosp1")
            .lbl("lbl1")
            .statut("statut1")
            .abbreviated("abbreviated1")
            .speech("speech1")
            .nbrnotifbeforerec(1L)
            .nbrnotifafterrec(1L)
            .archiveSubjectsId("archiveSubjectsId1");
    }

    public static Typecorrespondence getTypecorrespondenceSample2() {
        return new Typecorrespondence()
            .id(2L)
            .typecorresp("typecorresp2")
            .typedecision("typedecision2")
            .hosp("hosp2")
            .lbl("lbl2")
            .statut("statut2")
            .abbreviated("abbreviated2")
            .speech("speech2")
            .nbrnotifbeforerec(2L)
            .nbrnotifafterrec(2L)
            .archiveSubjectsId("archiveSubjectsId2");
    }

    public static Typecorrespondence getTypecorrespondenceRandomSampleGenerator() {
        return new Typecorrespondence()
            .id(longCount.incrementAndGet())
            .typecorresp(UUID.randomUUID().toString())
            .typedecision(UUID.randomUUID().toString())
            .hosp(UUID.randomUUID().toString())
            .lbl(UUID.randomUUID().toString())
            .statut(UUID.randomUUID().toString())
            .abbreviated(UUID.randomUUID().toString())
            .speech(UUID.randomUUID().toString())
            .nbrnotifbeforerec(longCount.incrementAndGet())
            .nbrnotifafterrec(longCount.incrementAndGet())
            .archiveSubjectsId(UUID.randomUUID().toString());
    }
}
