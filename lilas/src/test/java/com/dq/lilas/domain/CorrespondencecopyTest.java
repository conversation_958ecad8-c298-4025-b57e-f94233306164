package com.dq.lilas.domain;

import static com.dq.lilas.domain.ActionTestSamples.*;
import static com.dq.lilas.domain.CorrespondenceTestSamples.*;
import static com.dq.lilas.domain.CorrespondencecopyTestSamples.*;
import static com.dq.lilas.domain.EmployeeTestSamples.*;
import static com.dq.lilas.domain.UnitTestSamples.*;
import static org.assertj.core.api.Assertions.assertThat;

import com.dq.lilas.rest.TestUtil;
import org.junit.jupiter.api.Test;

class CorrespondencecopyTest {

    @Test
    void equalsVerifier() throws Exception {
        TestUtil.equalsVerifier(Correspondencecopy.class);
        Correspondencecopy correspondencecopy1 = getCorrespondencecopySample1();
        Correspondencecopy correspondencecopy2 = new Correspondencecopy();
        assertThat(correspondencecopy1).isNotEqualTo(correspondencecopy2);

        correspondencecopy2.setId(correspondencecopy1.getId());
        assertThat(correspondencecopy1).isEqualTo(correspondencecopy2);

        correspondencecopy2 = getCorrespondencecopySample2();
        assertThat(correspondencecopy1).isNotEqualTo(correspondencecopy2);
    }

    @Test
    void correspondenceTest() {
        Correspondencecopy correspondencecopy = getCorrespondencecopyRandomSampleGenerator();
        Correspondence correspondenceBack = getCorrespondenceRandomSampleGenerator();

        correspondencecopy.setCorrespondence(correspondenceBack);
        assertThat(correspondencecopy.getCorrespondence()).isEqualTo(correspondenceBack);

        correspondencecopy.correspondence(null);
        assertThat(correspondencecopy.getCorrespondence()).isNull();
    }

    @Test
    void employeeTest() {
        Correspondencecopy correspondencecopy = getCorrespondencecopyRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        correspondencecopy.setEmployee(employeeBack);
        assertThat(correspondencecopy.getEmployee()).isEqualTo(employeeBack);

        correspondencecopy.employee(null);
        assertThat(correspondencecopy.getEmployee()).isNull();
    }

    @Test
    void unitTest() {
        Correspondencecopy correspondencecopy = getCorrespondencecopyRandomSampleGenerator();
        Unit unitBack = getUnitRandomSampleGenerator();

        correspondencecopy.setUnit(unitBack);
        assertThat(correspondencecopy.getUnit()).isEqualTo(unitBack);

        correspondencecopy.unit(null);
        assertThat(correspondencecopy.getUnit()).isNull();
    }

    @Test
    void actionTest() {
        Correspondencecopy correspondencecopy = getCorrespondencecopyRandomSampleGenerator();
        Action actionBack = getActionRandomSampleGenerator();

        correspondencecopy.setAction(actionBack);
        assertThat(correspondencecopy.getAction()).isEqualTo(actionBack);

        correspondencecopy.action(null);
        assertThat(correspondencecopy.getAction()).isNull();
    }

    @Test
    void useractionTest() {
        Correspondencecopy correspondencecopy = getCorrespondencecopyRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        correspondencecopy.setUseraction(employeeBack);
        assertThat(correspondencecopy.getUseraction()).isEqualTo(employeeBack);

        correspondencecopy.useraction(null);
        assertThat(correspondencecopy.getUseraction()).isNull();
    }

    @Test
    void userrevokeTest() {
        Correspondencecopy correspondencecopy = getCorrespondencecopyRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        correspondencecopy.setUserrevoke(employeeBack);
        assertThat(correspondencecopy.getUserrevoke()).isEqualTo(employeeBack);

        correspondencecopy.userrevoke(null);
        assertThat(correspondencecopy.getUserrevoke()).isNull();
    }

    @Test
    void userremoveTest() {
        Correspondencecopy correspondencecopy = getCorrespondencecopyRandomSampleGenerator();
        Employee employeeBack = getEmployeeRandomSampleGenerator();

        correspondencecopy.setUserremove(employeeBack);
        assertThat(correspondencecopy.getUserremove()).isEqualTo(employeeBack);

        correspondencecopy.userremove(null);
        assertThat(correspondencecopy.getUserremove()).isNull();
    }
}
