package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class EmailsNotificationsAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmailsNotificationsAllPropertiesEquals(EmailsNotifications expected, EmailsNotifications actual) {
        assertEmailsNotificationsAutoGeneratedPropertiesEquals(expected, actual);
        assertEmailsNotificationsAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmailsNotificationsAllUpdatablePropertiesEquals(EmailsNotifications expected, EmailsNotifications actual) {
        assertEmailsNotificationsUpdatableFieldsEquals(expected, actual);
        assertEmailsNotificationsUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmailsNotificationsAutoGeneratedPropertiesEquals(EmailsNotifications expected, EmailsNotifications actual) {
        assertThat(actual)
            .as("Verify EmailsNotifications auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmailsNotificationsUpdatableFieldsEquals(EmailsNotifications expected, EmailsNotifications actual) {
        assertThat(actual)
            .as("Verify EmailsNotifications relevant properties")
            .satisfies(a -> assertThat(a.getSubject()).as("check subject").isEqualTo(expected.getSubject()))
            .satisfies(a -> assertThat(a.getNotificationDate()).as("check notificationDate").isEqualTo(expected.getNotificationDate()))
            .satisfies(a -> assertThat(a.getBody()).as("check body").isEqualTo(expected.getBody()))
            .satisfies(a -> assertThat(a.getBodyContentType()).as("check body contenty type").isEqualTo(expected.getBodyContentType()))
            .satisfies(a -> assertThat(a.getRecipient()).as("check recipient").isEqualTo(expected.getRecipient()))
            .satisfies(a -> assertThat(a.getSender()).as("check sender").isEqualTo(expected.getSender()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertEmailsNotificationsUpdatableRelationshipsEquals(EmailsNotifications expected, EmailsNotifications actual) {
        assertThat(actual)
            .as("Verify EmailsNotifications relationships")
            .satisfies(a -> assertThat(a.getOrderDetails()).as("check orderDetails").isEqualTo(expected.getOrderDetails()));
    }
}
