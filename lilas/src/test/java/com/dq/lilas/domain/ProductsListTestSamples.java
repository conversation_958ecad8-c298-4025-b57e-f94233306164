package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class ProductsListTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static ProductsList getProductsListSample1() {
        return new ProductsList().id(1L).internalCode("internalCode1").barcode("barcode1").qadName("qadName1").category("category1");
    }

    public static ProductsList getProductsListSample2() {
        return new ProductsList().id(2L).internalCode("internalCode2").barcode("barcode2").qadName("qadName2").category("category2");
    }

    public static ProductsList getProductsListRandomSampleGenerator() {
        return new ProductsList()
            .id(longCount.incrementAndGet())
            .internalCode(UUID.randomUUID().toString())
            .barcode(UUID.randomUUID().toString())
            .qadName(UUID.randomUUID().toString())
            .category(UUID.randomUUID().toString());
    }
}
