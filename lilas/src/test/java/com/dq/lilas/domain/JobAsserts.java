package com.dq.lilas.domain;

import static org.assertj.core.api.Assertions.assertThat;

public class JobAsserts {

    /**
     * Asserts that the entity has all properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertJobAllPropertiesEquals(Job expected, Job actual) {
        assertJobAutoGeneratedPropertiesEquals(expected, actual);
        assertJobAllUpdatablePropertiesEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all updatable properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertJobAllUpdatablePropertiesEquals(Job expected, Job actual) {
        assertJobUpdatableFieldsEquals(expected, actual);
        assertJobUpdatableRelationshipsEquals(expected, actual);
    }

    /**
     * Asserts that the entity has all the auto generated properties (fields/relationships) set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertJobAutoGeneratedPropertiesEquals(Job expected, Job actual) {
        assertThat(actual)
            .as("Verify Job auto generated properties")
            .satisfies(a -> assertThat(a.getId()).as("check id").isEqualTo(expected.getId()));
    }

    /**
     * Asserts that the entity has all the updatable fields set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertJobUpdatableFieldsEquals(Job expected, Job actual) {
        assertThat(actual)
            .as("Verify Job relevant properties")
            .satisfies(a -> assertThat(a.getStatus()).as("check status").isEqualTo(expected.getStatus()));
    }

    /**
     * Asserts that the entity has all the updatable relationships set.
     *
     * @param expected the expected entity
     * @param actual the actual entity
     */
    public static void assertJobUpdatableRelationshipsEquals(Job expected, Job actual) {
        // empty method
    }
}
