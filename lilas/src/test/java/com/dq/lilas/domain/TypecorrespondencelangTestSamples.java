package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class TypecorrespondencelangTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static Typecorrespondencelang getTypecorrespondencelangSample1() {
        return new Typecorrespondencelang().id(1L).lbl("lbl1").lang("lang1").abbreviated("abbreviated1");
    }

    public static Typecorrespondencelang getTypecorrespondencelangSample2() {
        return new Typecorrespondencelang().id(2L).lbl("lbl2").lang("lang2").abbreviated("abbreviated2");
    }

    public static Typecorrespondencelang getTypecorrespondencelangRandomSampleGenerator() {
        return new Typecorrespondencelang()
            .id(longCount.incrementAndGet())
            .lbl(UUID.randomUUID().toString())
            .lang(UUID.randomUUID().toString())
            .abbreviated(UUID.randomUUID().toString());
    }
}
