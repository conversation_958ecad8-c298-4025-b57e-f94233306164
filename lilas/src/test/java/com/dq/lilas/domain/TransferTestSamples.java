package com.dq.lilas.domain;

import java.util.Random;
import java.util.UUID;
import java.util.concurrent.atomic.AtomicLong;

public class TransferTestSamples {

    private static final Random random = new Random();
    private static final AtomicLong longCount = new AtomicLong(random.nextInt() + (2 * Integer.MAX_VALUE));

    public static Transfer getTransferSample1() {
        return new Transfer()
            .id(1L)
            .docyear("docyear1")
            .texttransfer("texttransfer1")
            .datehjrtransfer("datehjrtransfer1")
            .statustransfer("statustransfer1")
            .datesendhjrtransfer("datesendhjrtransfer1")
            .savetransfer("savetransfer1")
            .numcopy("numcopy1")
            .highlevel("highlevel1")
            .confidentiel("confidentiel1")
            .priority("priority1")
            .timeaction("timeaction1")
            .rappelnum("rappelnum1")
            .rappeltype("rappeltype1")
            .readrequest("readrequest1")
            .typereceive("typereceive1")
            .datehjrreceive("datehjrreceive1")
            .heurearch("heurearch1")
            .actiontype("actiontype1")
            .comments("comments1")
            .datearchhj("datearchhj1")
            .lasttransserial("lasttransserial1")
            .adrsbooktransto("adrsbooktransto1")
            .statusreceiveto("statusreceiveto1")
            .commentsreceiveto("commentsreceiveto1")
            .receivedatehjruserto("receivedatehjruserto1")
            .typetransfer("typetransfer1")
            .transrecserial("transrecserial1")
            .attach("attach1")
            .transtype("transtype1")
            .ordernbr("ordernbr1")
            .heureaction("heureaction1")
            .datehjraction("datehjraction1")
            .statusdenied("statusdenied1")
            .subjectcorresp("subjectcorresp1")
            .datehjrcorresp("datehjrcorresp1")
            .oldstatus("oldstatus1")
            .step("step1")
            .typeprocess("typeprocess1")
            .codetask("codetask1")
            .refusetext("refusetext1")
            .statusrefused("statusrefused1")
            .bidadrsbook("bidadrsbook1")
            .pagenbrpaper("pagenbrpaper1")
            .flagprint("flagprint1")
            .datehjrprint("datehjrprint1")
            .datehjrrevoke("datehjrrevoke1")
            .gabaritcontext("gabaritcontext1")
            .approvedspeech("approvedspeech1")
            .datehjrapprovedspeech("datehjrapprovedspeech1")
            .conformitytask("conformitytask1")
            .useradrsbook("useradrsbook1")
            .stepmaxwf("stepmaxwf1")
            .incidenttransfer("incidenttransfer1")
            .qualificationincident("qualificationincident1")
            .categorieincident("categorieincident1")
            .statutincident("statutincident1")
            .criticiteincident("criticiteincident1")
            .voiceId("voiceId1")
            .favoris("favoris1")
            .checkinboxfavorite("checkinboxfavorite1")
            .checkclosefavorite("checkclosefavorite1")
            .checkfavorite("checkfavorite1")
            .taskcategId("taskcategId1")
            .codePin("codePin1");
    }

    public static Transfer getTransferSample2() {
        return new Transfer()
            .id(2L)
            .docyear("docyear2")
            .texttransfer("texttransfer2")
            .datehjrtransfer("datehjrtransfer2")
            .statustransfer("statustransfer2")
            .datesendhjrtransfer("datesendhjrtransfer2")
            .savetransfer("savetransfer2")
            .numcopy("numcopy2")
            .highlevel("highlevel2")
            .confidentiel("confidentiel2")
            .priority("priority2")
            .timeaction("timeaction2")
            .rappelnum("rappelnum2")
            .rappeltype("rappeltype2")
            .readrequest("readrequest2")
            .typereceive("typereceive2")
            .datehjrreceive("datehjrreceive2")
            .heurearch("heurearch2")
            .actiontype("actiontype2")
            .comments("comments2")
            .datearchhj("datearchhj2")
            .lasttransserial("lasttransserial2")
            .adrsbooktransto("adrsbooktransto2")
            .statusreceiveto("statusreceiveto2")
            .commentsreceiveto("commentsreceiveto2")
            .receivedatehjruserto("receivedatehjruserto2")
            .typetransfer("typetransfer2")
            .transrecserial("transrecserial2")
            .attach("attach2")
            .transtype("transtype2")
            .ordernbr("ordernbr2")
            .heureaction("heureaction2")
            .datehjraction("datehjraction2")
            .statusdenied("statusdenied2")
            .subjectcorresp("subjectcorresp2")
            .datehjrcorresp("datehjrcorresp2")
            .oldstatus("oldstatus2")
            .step("step2")
            .typeprocess("typeprocess2")
            .codetask("codetask2")
            .refusetext("refusetext2")
            .statusrefused("statusrefused2")
            .bidadrsbook("bidadrsbook2")
            .pagenbrpaper("pagenbrpaper2")
            .flagprint("flagprint2")
            .datehjrprint("datehjrprint2")
            .datehjrrevoke("datehjrrevoke2")
            .gabaritcontext("gabaritcontext2")
            .approvedspeech("approvedspeech2")
            .datehjrapprovedspeech("datehjrapprovedspeech2")
            .conformitytask("conformitytask2")
            .useradrsbook("useradrsbook2")
            .stepmaxwf("stepmaxwf2")
            .incidenttransfer("incidenttransfer2")
            .qualificationincident("qualificationincident2")
            .categorieincident("categorieincident2")
            .statutincident("statutincident2")
            .criticiteincident("criticiteincident2")
            .voiceId("voiceId2")
            .favoris("favoris2")
            .checkinboxfavorite("checkinboxfavorite2")
            .checkclosefavorite("checkclosefavorite2")
            .checkfavorite("checkfavorite2")
            .taskcategId("taskcategId2")
            .codePin("codePin2");
    }

    public static Transfer getTransferRandomSampleGenerator() {
        return new Transfer()
            .id(longCount.incrementAndGet())
            .docyear(UUID.randomUUID().toString())
            .texttransfer(UUID.randomUUID().toString())
            .datehjrtransfer(UUID.randomUUID().toString())
            .statustransfer(UUID.randomUUID().toString())
            .datesendhjrtransfer(UUID.randomUUID().toString())
            .savetransfer(UUID.randomUUID().toString())
            .numcopy(UUID.randomUUID().toString())
            .highlevel(UUID.randomUUID().toString())
            .confidentiel(UUID.randomUUID().toString())
            .priority(UUID.randomUUID().toString())
            .timeaction(UUID.randomUUID().toString())
            .rappelnum(UUID.randomUUID().toString())
            .rappeltype(UUID.randomUUID().toString())
            .readrequest(UUID.randomUUID().toString())
            .typereceive(UUID.randomUUID().toString())
            .datehjrreceive(UUID.randomUUID().toString())
            .heurearch(UUID.randomUUID().toString())
            .actiontype(UUID.randomUUID().toString())
            .comments(UUID.randomUUID().toString())
            .datearchhj(UUID.randomUUID().toString())
            .lasttransserial(UUID.randomUUID().toString())
            .adrsbooktransto(UUID.randomUUID().toString())
            .statusreceiveto(UUID.randomUUID().toString())
            .commentsreceiveto(UUID.randomUUID().toString())
            .receivedatehjruserto(UUID.randomUUID().toString())
            .typetransfer(UUID.randomUUID().toString())
            .transrecserial(UUID.randomUUID().toString())
            .attach(UUID.randomUUID().toString())
            .transtype(UUID.randomUUID().toString())
            .ordernbr(UUID.randomUUID().toString())
            .heureaction(UUID.randomUUID().toString())
            .datehjraction(UUID.randomUUID().toString())
            .statusdenied(UUID.randomUUID().toString())
            .subjectcorresp(UUID.randomUUID().toString())
            .datehjrcorresp(UUID.randomUUID().toString())
            .oldstatus(UUID.randomUUID().toString())
            .step(UUID.randomUUID().toString())
            .typeprocess(UUID.randomUUID().toString())
            .codetask(UUID.randomUUID().toString())
            .refusetext(UUID.randomUUID().toString())
            .statusrefused(UUID.randomUUID().toString())
            .bidadrsbook(UUID.randomUUID().toString())
            .pagenbrpaper(UUID.randomUUID().toString())
            .flagprint(UUID.randomUUID().toString())
            .datehjrprint(UUID.randomUUID().toString())
            .datehjrrevoke(UUID.randomUUID().toString())
            .gabaritcontext(UUID.randomUUID().toString())
            .approvedspeech(UUID.randomUUID().toString())
            .datehjrapprovedspeech(UUID.randomUUID().toString())
            .conformitytask(UUID.randomUUID().toString())
            .useradrsbook(UUID.randomUUID().toString())
            .stepmaxwf(UUID.randomUUID().toString())
            .incidenttransfer(UUID.randomUUID().toString())
            .qualificationincident(UUID.randomUUID().toString())
            .categorieincident(UUID.randomUUID().toString())
            .statutincident(UUID.randomUUID().toString())
            .criticiteincident(UUID.randomUUID().toString())
            .voiceId(UUID.randomUUID().toString())
            .favoris(UUID.randomUUID().toString())
            .checkinboxfavorite(UUID.randomUUID().toString())
            .checkclosefavorite(UUID.randomUUID().toString())
            .checkfavorite(UUID.randomUUID().toString())
            .taskcategId(UUID.randomUUID().toString())
            .codePin(UUID.randomUUID().toString());
    }
}
