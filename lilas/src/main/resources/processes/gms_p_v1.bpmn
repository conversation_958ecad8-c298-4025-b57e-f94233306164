<?xml version="1.0" encoding="UTF-8"?>
<bpmn:definitions xmlns:bpmn="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:bpmndi="http://www.omg.org/spec/BPMN/20100524/DI" xmlns:dc="http://www.omg.org/spec/DD/20100524/DC" xmlns:di="http://www.omg.org/spec/DD/20100524/DI" xmlns:camunda="http://camunda.org/schema/1.0/bpmn" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" id="Definitions_1" targetNamespace="http://bpmn.io/schema/bpmn" exporter="Camunda Modeler" exporterVersion="5.32.0">
  <bpmn:process id="Process_1" name="Demande de Promotion " isExecutable="true" camunda:historyTimeToLive="1000">
    <bpmn:sequenceFlow id="SequenceFlow_1" sourceRef="StartEvent_1" targetRef="UserTask_1" />
    <bpmn:userTask id="UserTask_1" name="Revue de la demande" camunda:assignee="controle gestion">
      <bpmn:incoming>SequenceFlow_1</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_2</bpmn:outgoing>
    </bpmn:userTask>
    <bpmn:sequenceFlow id="SequenceFlow_2" sourceRef="UserTask_1" targetRef="ExclusiveGateway_1" />
    <bpmn:exclusiveGateway id="ExclusiveGateway_1" name="Approuvée?">
      <bpmn:incoming>SequenceFlow_2</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_3</bpmn:outgoing>
      <bpmn:outgoing>SequenceFlow_4</bpmn:outgoing>
    </bpmn:exclusiveGateway>
    <bpmn:sequenceFlow id="SequenceFlow_3" name="Oui" sourceRef="ExclusiveGateway_1" targetRef="ServiceTask_1">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == true}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:sequenceFlow id="SequenceFlow_4" name="Non" sourceRef="ExclusiveGateway_1" targetRef="ServiceTask_Reject">
      <bpmn:conditionExpression xsi:type="bpmn:tFormalExpression">${approved == false}</bpmn:conditionExpression>
    </bpmn:sequenceFlow>
    <bpmn:serviceTask id="ServiceTask_1" name="Appliquer la promotion" camunda:delegateExpression="${promotionService}">
      <bpmn:incoming>SequenceFlow_3</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_5</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_5" sourceRef="ServiceTask_1" targetRef="EndEvent_1" />
    <bpmn:serviceTask id="ServiceTask_Reject" name="Traiter rejet" camunda:delegateExpression="${promotionService}">
      <bpmn:incoming>SequenceFlow_4</bpmn:incoming>
      <bpmn:outgoing>SequenceFlow_Reject_To_End</bpmn:outgoing>
    </bpmn:serviceTask>
    <bpmn:sequenceFlow id="SequenceFlow_Reject_To_End" sourceRef="ServiceTask_Reject" targetRef="EndEvent_2" />
    <bpmn:endEvent id="EndEvent_1" name="Promotion appliquée">
      <bpmn:incoming>SequenceFlow_5</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:endEvent id="EndEvent_2" name="Demande rejetée">
      <bpmn:incoming>SequenceFlow_Reject_To_End</bpmn:incoming>
    </bpmn:endEvent>
    <bpmn:startEvent id="StartEvent_1" name="Demande soumise">
      <bpmn:outgoing>SequenceFlow_1</bpmn:outgoing>
      <bpmn:messageEventDefinition id="MessageEventDefinition_0jbxh6h" messageRef="Message_0gu6vpn" />
    </bpmn:startEvent>
  </bpmn:process>
  <bpmn:message id="Message_0gu6vpn" name="Message_0gu6vpn" />
  <bpmndi:BPMNDiagram id="BPMNDiagram_1">
    <bpmndi:BPMNPlane id="BPMNPlane_1" bpmnElement="Process_1">
      <bpmndi:BPMNShape id="UserTask_1_di" bpmnElement="UserTask_1">
        <dc:Bounds x="250" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ExclusiveGateway_1_di" bpmnElement="ExclusiveGateway_1" isMarkerVisible="true">
        <dc:Bounds x="400" y="100" width="40" height="40" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="390" y="76" width="59" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_1_di" bpmnElement="ServiceTask_1">
        <dc:Bounds x="500" y="80" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_1_di" bpmnElement="EndEvent_1">
        <dc:Bounds x="650" y="100" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="643" y="136" width="51" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="Event_12wqghm_di" bpmnElement="StartEvent_1">
        <dc:Bounds x="150" y="100" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="144" y="136" width="48" height="27" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="ServiceTask_Reject_di" bpmnElement="ServiceTask_Reject">
        <dc:Bounds x="420" y="200" width="100" height="80" />
      </bpmndi:BPMNShape>
      <bpmndi:BPMNShape id="EndEvent_2_di" bpmnElement="EndEvent_2">
        <dc:Bounds x="452" y="352" width="36" height="36" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="428" y="388" width="85" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNShape>
      <bpmndi:BPMNEdge id="SequenceFlow_1_di" bpmnElement="SequenceFlow_1">
        <di:waypoint x="186" y="118" />
        <di:waypoint x="250" y="118" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_2_di" bpmnElement="SequenceFlow_2">
        <di:waypoint x="350" y="118" />
        <di:waypoint x="400" y="118" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_3_di" bpmnElement="SequenceFlow_3">
        <di:waypoint x="440" y="118" />
        <di:waypoint x="500" y="118" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="461" y="93" width="18" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_4_di" bpmnElement="SequenceFlow_4">
        <di:waypoint x="420" y="140" />
        <di:waypoint x="420" y="210" />
        <bpmndi:BPMNLabel>
          <dc:Bounds x="402" y="147" width="21" height="14" />
        </bpmndi:BPMNLabel>
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_5_di" bpmnElement="SequenceFlow_5">
        <di:waypoint x="600" y="118" />
        <di:waypoint x="650" y="118" />
      </bpmndi:BPMNEdge>
      <bpmndi:BPMNEdge id="SequenceFlow_Reject_To_End_di" bpmnElement="SequenceFlow_Reject_To_End">
        <di:waypoint x="470" y="280" />
        <di:waypoint x="470" y="352" />
      </bpmndi:BPMNEdge>
    </bpmndi:BPMNPlane>
  </bpmndi:BPMNDiagram>
</bpmn:definitions>
