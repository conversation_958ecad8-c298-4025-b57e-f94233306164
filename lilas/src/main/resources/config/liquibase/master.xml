<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">
    <property name="now" value="current_timestamp" dbms="postgresql"/>
    <property name="floatType" value="float4" dbms="postgresql"/>
    <property name="clobType" value="clob" dbms="postgresql"/>
    <property name="blobType" value="blob" dbms="postgresql"/>
    <property name="uuidType" value="uuid" dbms="postgresql"/>
    <property name="datetimeType" value="datetime" dbms="postgresql"/>
    <property name="timeType" value="time(6)" dbms="postgresql"/>

    <include file="config/liquibase/changelog/00000000000000_initial_schema.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211021_added_entity_Employee.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211022_added_entity_Job.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211023_added_entity_Joblang.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211024_added_entity_Employeelang.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211025_added_entity_Unit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211026_added_entity_Unitlang.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211027_added_entity_Company.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211028_added_entity_Action.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211029_added_entity_Actionlang.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211030_added_entity_Deliverymode.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211031_added_entity_Deliverymodelang.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211032_added_entity_Typecorrespondence.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211033_added_entity_Typecorrespondencelang.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211034_added_entity_Correspondence.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211035_added_entity_Transfer.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211036_added_entity_Correspondencecopy.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211037_added_entity_Attachement.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211038_added_entity_OrderDetails.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211039_added_entity_EmailsNotifications.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211040_added_entity_Mails.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211041_added_entity_DailyBatches.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211042_added_entity_GmsClients.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211043_added_entity_GmsBrands.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211044_added_entity_EmployeeGmsBrands.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211045_added_entity_Order.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211046_added_entity_TemplateConditions.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211047_added_entity_DemandePromotion.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211048_added_entity_PromotionDetails.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211049_added_entity_Cadencier.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211050_added_entity_ProductsList.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211051_added_entity_DailyStock.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250717134643_added_entity_EmployeeCompanyPermission.xml" relativeToChangelogFile="false"/>
    <!-- jhipster-needle-liquibase-add-changelog - JHipster will add liquibase changelogs here -->
    <include file="config/liquibase/changelog/20250619211021_added_entity_constraints_Employee.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211023_added_entity_constraints_Joblang.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211024_added_entity_constraints_Employeelang.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211025_added_entity_constraints_Unit.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211026_added_entity_constraints_Unitlang.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211029_added_entity_constraints_Actionlang.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211031_added_entity_constraints_Deliverymodelang.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211033_added_entity_constraints_Typecorrespondencelang.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211034_added_entity_constraints_Correspondence.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211035_added_entity_constraints_Transfer.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211036_added_entity_constraints_Correspondencecopy.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211037_added_entity_constraints_Attachement.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211038_added_entity_constraints_OrderDetails.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211039_added_entity_constraints_EmailsNotifications.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211041_added_entity_constraints_DailyBatches.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211042_added_entity_constraints_GmsClients.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211044_added_entity_constraints_EmployeeGmsBrands.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211045_added_entity_constraints_Order.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211046_added_entity_constraints_TemplateConditions.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211047_added_entity_constraints_DemandePromotion.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250619211051_added_entity_constraints_DailyStock.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250702001_add_demande_promotion_id_column.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250710202000_remove_status_column.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250710203000_add_status_columns_properly.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250710203100_set_default_status_for_existing_records.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250710220000_update_existing_records_status.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250715001_add_date_of_request_column.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250717134643_added_entity_constraints_EmployeeCompanyPermission.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250718001_add_mail_type_column.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250724001_fix_mailbody_column_type.xml" relativeToChangelogFile="false"/>
    <include file="config/liquibase/changelog/20250718002_modify_order_entity.xml" relativeToChangelogFile="false"/>
    <!-- jhipster-needle-liquibase-add-constraints-changelog - JHipster will add liquibase constraints changelogs here -->
    <!-- jhipster-needle-liquibase-add-incremental-changelog - JHipster will add incremental liquibase changelogs here -->
</databaseChangeLog>
