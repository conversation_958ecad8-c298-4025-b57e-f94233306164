<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">
    <!--
        Added the constraints for entity Order.
    -->
    <changeSet id="20250619211045-2" author="jhipster">

        <addForeignKeyConstraint baseColumnNames="daily_batches_id"
                                 baseTableName="jhi_order"
                                 constraintName="fk_jhi_order__daily_batches_id"
                                 referencedColumnNames="id"
                                 referencedTableName="daily_batches"
                                 />

        <addForeignKeyConstraint baseColumnNames="gms_clients_id"
                                 baseTableName="jhi_order"
                                 constraintName="fk_jhi_order__gms_clients_id"
                                 referencedColumnNames="id"
                                 referencedTableName="gms_clients"
                                 />

        <addForeignKeyConstraint baseColumnNames="template_conditions_id"
                                 baseTableName="jhi_order"
                                 constraintName="fk_jhi_order__template_conditions_id"
                                 referencedColumnNames="id"
                                 referencedTableName="template_conditions"
                                 />

        <addForeignKeyConstraint baseColumnNames="mails_id"
                                 baseTableName="jhi_order"
                                 constraintName="fk_jhi_order__mails_id"
                                 referencedColumnNames="id"
                                 referencedTableName="mails"
                                 />
    </changeSet>
</databaseChangeLog>
