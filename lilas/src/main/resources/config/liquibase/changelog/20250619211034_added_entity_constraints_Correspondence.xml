<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">
    <!--
        Added the constraints for entity Correspondence.
    -->
    <changeSet id="20250619211034-2" author="jhipster">

        <addForeignKeyConstraint baseColumnNames="employee_id"
                                 baseTableName="correspondence"
                                 constraintName="fk_correspondence__employee_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"
                                 />

        <addForeignKeyConstraint baseColumnNames="unit_id"
                                 baseTableName="correspondence"
                                 constraintName="fk_correspondence__unit_id"
                                 referencedColumnNames="id"
                                 referencedTableName="unit"
                                 />

        <addForeignKeyConstraint baseColumnNames="deliverymode_id"
                                 baseTableName="correspondence"
                                 constraintName="fk_correspondence__deliverymode_id"
                                 referencedColumnNames="id"
                                 referencedTableName="deliverymode"
                                 />

        <addForeignKeyConstraint baseColumnNames="typecorrespondence_id"
                                 baseTableName="correspondence"
                                 constraintName="fk_correspondence__typecorrespondence_id"
                                 referencedColumnNames="id"
                                 referencedTableName="typecorrespondence"
                                 />
    </changeSet>
</databaseChangeLog>
