<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Correspondencecopy.
    -->
    <changeSet id="20250619211036-1" author="jhipster">
        <createTable tableName="correspondencecopy">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="numcopy" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="comments" type="varchar(4000)">
                <constraints nullable="true" />
            </column>
            <column name="datejccreate" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datehjrcreate" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="typereceive" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="typecopy" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="userreceive" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="datejcreceive" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datehjrreceive" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="datejcaction" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datehjraction" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="actiontype" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="savecorrespcpy" type="varchar(5)">
                <constraints nullable="true" />
            </column>
            <column name="ordernbr" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="pagenbr" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="expno" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="expyear" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="docyear" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="categorycorresp" type="varchar(5)">
                <constraints nullable="true" />
            </column>
            <column name="heureaction" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="statusdeniedcopy" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="pagenbrpaper" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="taskscan" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="pathtransto" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="pathtranscc" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="pathtranstolib" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="pathtranscclib" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="flggroup" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="datejcdelete" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datejcrevoke" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datehjrrevoke" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="dateremove" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="correspondence_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="employee_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="unit_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="action_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="useraction_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="userrevoke_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="userremove_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="correspondencecopy" columnName="datejccreate" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="correspondencecopy" columnName="datejcreceive" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="correspondencecopy" columnName="datejcaction" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="correspondencecopy" columnName="datejcdelete" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="correspondencecopy" columnName="datejcrevoke" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="correspondencecopy" columnName="dateremove" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619211036-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/correspondencecopy.csv"
                  separator=";"
                  tableName="correspondencecopy"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="numcopy" type="string"/>
            <column name="comments" type="string"/>
            <column name="datejccreate" type="date"/>
            <column name="datehjrcreate" type="string"/>
            <column name="typereceive" type="string"/>
            <column name="typecopy" type="string"/>
            <column name="userreceive" type="string"/>
            <column name="datejcreceive" type="date"/>
            <column name="datehjrreceive" type="string"/>
            <column name="datejcaction" type="date"/>
            <column name="datehjraction" type="string"/>
            <column name="actiontype" type="string"/>
            <column name="savecorrespcpy" type="string"/>
            <column name="ordernbr" type="string"/>
            <column name="pagenbr" type="string"/>
            <column name="expno" type="string"/>
            <column name="expyear" type="string"/>
            <column name="docyear" type="string"/>
            <column name="categorycorresp" type="string"/>
            <column name="heureaction" type="string"/>
            <column name="statusdeniedcopy" type="string"/>
            <column name="pagenbrpaper" type="string"/>
            <column name="taskscan" type="string"/>
            <column name="pathtransto" type="string"/>
            <column name="pathtranscc" type="string"/>
            <column name="pathtranstolib" type="string"/>
            <column name="pathtranscclib" type="string"/>
            <column name="flggroup" type="string"/>
            <column name="datejcdelete" type="date"/>
            <column name="datejcrevoke" type="date"/>
            <column name="datehjrrevoke" type="string"/>
            <column name="dateremove" type="date"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
