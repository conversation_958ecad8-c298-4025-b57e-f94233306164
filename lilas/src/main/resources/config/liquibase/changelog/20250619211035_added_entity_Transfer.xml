<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Transfer.
    -->
    <changeSet id="20250619211035-1" author="jhipster">
        <createTable tableName="transfer">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="docyear" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="texttransfer" type="varchar(4000)">
                <constraints nullable="true" />
            </column>
            <column name="datejctransfer" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datehjrtransfer" type="varchar(30)">
                <constraints nullable="true" />
            </column>
            <column name="statustransfer" type="varchar(5)">
                <constraints nullable="true" />
            </column>
            <column name="datesendjctransfer" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datesendhjrtransfer" type="varchar(30)">
                <constraints nullable="true" />
            </column>
            <column name="savetransfer" type="varchar(5)">
                <constraints nullable="true" />
            </column>
            <column name="numcopy" type="varchar(25)">
                <constraints nullable="true" />
            </column>
            <column name="highlevel" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="confidentiel" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="priority" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="timeaction" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="deadline" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="rappelnum" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="rappeltype" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="readrequest" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="typereceive" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="datejcreceive" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datehjrreceive" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="heurearch" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="actiontype" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="comments" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="datearch" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datearchhj" type="varchar(30)">
                <constraints nullable="true" />
            </column>
            <column name="lasttransserial" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="adrsbooktransto" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="statusreceiveto" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="commentsreceiveto" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="receivedatejcuserto" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="receivedatehjruserto" type="varchar(30)">
                <constraints nullable="true" />
            </column>
            <column name="typetransfer" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="transrecserial" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="attach" type="varchar(300)">
                <constraints nullable="true" />
            </column>
            <column name="transtype" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="ordernbr" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="heureaction" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="datejcaction" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datehjraction" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="statusdenied" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="subjectcorresp" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="datejccorresp" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datehjrcorresp" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="oldstatus" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="step" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="typeprocess" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="codetask" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="refusetext" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="statusrefused" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="bidadrsbook" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="pagenbrpaper" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="flagprint" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="dateprint" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datehjrprint" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="datejcdelete" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datejcrevoke" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datehjrrevoke" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="gabaritcontext" type="varchar(4000)">
                <constraints nullable="true" />
            </column>
            <column name="approvedspeech" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="datejcapprovedspeech" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datehjrapprovedspeech" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="conformitytask" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="useradrsbook" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="stepmaxwf" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="incidenttransfer" type="varchar(5)">
                <constraints nullable="true" />
            </column>
            <column name="qualificationincident" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="categorieincident" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="statutincident" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="criticiteincident" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="voice_id" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="favoris" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="checkinboxfavorite" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="checkclosefavorite" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="checkfavorite" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="taskcateg_id" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="pinrequired" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="code_pin" type="varchar(6)">
                <constraints nullable="true" />
            </column>
            <column name="sendwithmail" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="transmoth_id" type="bigint">
                <constraints nullable="true" unique="true" uniqueConstraintName="ux_transfer__transmoth_id" />
            </column>
            <column name="correspondencecopy_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="correspondence_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="deliverymode_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="employee_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="unit_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="action_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="userreceive_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="usertrans_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="usertransto_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="unittransto_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="userrevoke_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="userreceiveto_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="useraction_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="fromdept_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="transprincip_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="typecorrespondence_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="transfer" columnName="datejctransfer" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="transfer" columnName="datesendjctransfer" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="transfer" columnName="deadline" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="transfer" columnName="datejcreceive" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="transfer" columnName="datearch" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="transfer" columnName="receivedatejcuserto" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="transfer" columnName="datejcaction" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="transfer" columnName="datejccorresp" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="transfer" columnName="dateprint" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="transfer" columnName="datejcdelete" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="transfer" columnName="datejcrevoke" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="transfer" columnName="datejcapprovedspeech" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619211035-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/transfer.csv"
                  separator=";"
                  tableName="transfer"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="docyear" type="string"/>
            <column name="texttransfer" type="string"/>
            <column name="datejctransfer" type="date"/>
            <column name="datehjrtransfer" type="string"/>
            <column name="statustransfer" type="string"/>
            <column name="datesendjctransfer" type="date"/>
            <column name="datesendhjrtransfer" type="string"/>
            <column name="savetransfer" type="string"/>
            <column name="numcopy" type="string"/>
            <column name="highlevel" type="string"/>
            <column name="confidentiel" type="string"/>
            <column name="priority" type="string"/>
            <column name="timeaction" type="string"/>
            <column name="deadline" type="date"/>
            <column name="rappelnum" type="string"/>
            <column name="rappeltype" type="string"/>
            <column name="readrequest" type="string"/>
            <column name="typereceive" type="string"/>
            <column name="datejcreceive" type="date"/>
            <column name="datehjrreceive" type="string"/>
            <column name="heurearch" type="string"/>
            <column name="actiontype" type="string"/>
            <column name="comments" type="string"/>
            <column name="datearch" type="date"/>
            <column name="datearchhj" type="string"/>
            <column name="lasttransserial" type="string"/>
            <column name="adrsbooktransto" type="string"/>
            <column name="statusreceiveto" type="string"/>
            <column name="commentsreceiveto" type="string"/>
            <column name="receivedatejcuserto" type="date"/>
            <column name="receivedatehjruserto" type="string"/>
            <column name="typetransfer" type="string"/>
            <column name="transrecserial" type="string"/>
            <column name="attach" type="string"/>
            <column name="transtype" type="string"/>
            <column name="ordernbr" type="string"/>
            <column name="heureaction" type="string"/>
            <column name="datejcaction" type="date"/>
            <column name="datehjraction" type="string"/>
            <column name="statusdenied" type="string"/>
            <column name="subjectcorresp" type="string"/>
            <column name="datejccorresp" type="date"/>
            <column name="datehjrcorresp" type="string"/>
            <column name="oldstatus" type="string"/>
            <column name="step" type="string"/>
            <column name="typeprocess" type="string"/>
            <column name="codetask" type="string"/>
            <column name="refusetext" type="string"/>
            <column name="statusrefused" type="string"/>
            <column name="bidadrsbook" type="string"/>
            <column name="pagenbrpaper" type="string"/>
            <column name="flagprint" type="string"/>
            <column name="dateprint" type="date"/>
            <column name="datehjrprint" type="string"/>
            <column name="datejcdelete" type="date"/>
            <column name="datejcrevoke" type="date"/>
            <column name="datehjrrevoke" type="string"/>
            <column name="gabaritcontext" type="string"/>
            <column name="approvedspeech" type="string"/>
            <column name="datejcapprovedspeech" type="date"/>
            <column name="datehjrapprovedspeech" type="string"/>
            <column name="conformitytask" type="string"/>
            <column name="useradrsbook" type="string"/>
            <column name="stepmaxwf" type="string"/>
            <column name="incidenttransfer" type="string"/>
            <column name="qualificationincident" type="string"/>
            <column name="categorieincident" type="string"/>
            <column name="statutincident" type="string"/>
            <column name="criticiteincident" type="string"/>
            <column name="voice_id" type="string"/>
            <column name="favoris" type="string"/>
            <column name="checkinboxfavorite" type="string"/>
            <column name="checkclosefavorite" type="string"/>
            <column name="checkfavorite" type="string"/>
            <column name="taskcateg_id" type="string"/>
            <column name="pinrequired" type="boolean"/>
            <column name="code_pin" type="string"/>
            <column name="sendwithmail" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
