<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Add status column to DemandePromotion and PromotionDetails entities with proper enum constraints.
    -->
    <changeSet id="20250710203000-1" author="user">
        <!-- Add status column to demande_promotion table -->
        <addColumn tableName="demande_promotion">
            <column name="status" type="varchar(50)" defaultValue="Waiting">
                <constraints nullable="false" />
            </column>
        </addColumn>

        <!-- Add status column to promotion_details table -->
        <addColumn tableName="promotion_details">
            <column name="status" type="varchar(50)" defaultValue="Waiting">
                <constraints nullable="false" />
            </column>
        </addColumn>

        <!-- Add check constraints to ensure only valid status values -->
        <sql>
            ALTER TABLE demande_promotion ADD CONSTRAINT chk_demande_promotion_status 
            CHECK (status IN ('Waiting', 'Approved', 'Rejected'));
        </sql>
        
        <sql>
            ALTER TABLE promotion_details ADD CONSTRAINT chk_promotion_details_status 
            CHECK (status IN ('Waiting', 'Approved', 'Rejected'));
        </sql>
    </changeSet>
</databaseChangeLog>
