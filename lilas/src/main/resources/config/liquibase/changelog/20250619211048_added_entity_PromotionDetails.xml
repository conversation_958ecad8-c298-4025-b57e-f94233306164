<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity PromotionDetails.
    -->
    <changeSet id="20250619211048-1" author="jhipster">
        <createTable tableName="promotion_details">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="code_produit" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="description" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="price_ht" type="double">
                <constraints nullable="true" />
            </column>
            <column name="remise_fixe" type="double">
                <constraints nullable="true" />
            </column>
            <column name="remise_de_pro" type="double">
                <constraints nullable="true" />
            </column>
            <column name="gratuit_en_nat" type="double">
                <constraints nullable="true" />
            </column>
            <column name="appro_manager_du_magasin" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="appro_manager_gms" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619211048-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/promotion_details.csv"
                  separator=";"
                  tableName="promotion_details"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="code_produit" type="string"/>
            <column name="description" type="string"/>
            <column name="price_ht" type="numeric"/>
            <column name="remise_fixe" type="numeric"/>
            <column name="remise_de_pro" type="numeric"/>
            <column name="gratuit_en_nat" type="numeric"/>
            <column name="appro_manager_du_magasin" type="string"/>
            <column name="appro_manager_gms" type="string"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
