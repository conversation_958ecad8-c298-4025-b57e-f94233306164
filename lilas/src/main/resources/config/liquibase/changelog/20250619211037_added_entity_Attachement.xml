<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Attachement.
    -->
    <changeSet id="20250619211037-1" author="jhipster">
        <createTable tableName="attachement">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="copy_id" type="varchar(30)">
                <constraints nullable="true" />
            </column>
            <column name="lbl_attachment" type="varchar(150)">
                <constraints nullable="true" />
            </column>
            <column name="id_doc_attachment" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="size_attachement" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="filenameattachment" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="userattachment" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="iddecision" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="idtemplate" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="datejcattachment" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datehjrattachment" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="idtransfer" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="idcorresp" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="ordering" type="double">
                <constraints nullable="true" />
            </column>
            <column name="levelattachement" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="idreq" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="orderingscan" type="double">
                <constraints nullable="true" />
            </column>
            <column name="iddocext" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="idleave" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="config_level" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="type_atach" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="id_direct_order" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="path_file" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="version" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="order_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="attachement" columnName="datejcattachment" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="attachement" columnName="datehjrattachment" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619211037-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/attachement.csv"
                  separator=";"
                  tableName="attachement"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="copy_id" type="string"/>
            <column name="lbl_attachment" type="string"/>
            <column name="id_doc_attachment" type="string"/>
            <column name="size_attachement" type="string"/>
            <column name="filenameattachment" type="string"/>
            <column name="userattachment" type="string"/>
            <column name="iddecision" type="string"/>
            <column name="idtemplate" type="string"/>
            <column name="datejcattachment" type="date"/>
            <column name="datehjrattachment" type="date"/>
            <column name="idtransfer" type="string"/>
            <column name="idcorresp" type="string"/>
            <column name="ordering" type="numeric"/>
            <column name="levelattachement" type="string"/>
            <column name="idreq" type="string"/>
            <column name="orderingscan" type="numeric"/>
            <column name="iddocext" type="string"/>
            <column name="idleave" type="string"/>
            <column name="config_level" type="string"/>
            <column name="type_atach" type="string"/>
            <column name="id_direct_order" type="string"/>
            <column name="path_file" type="string"/>
            <column name="version" type="numeric"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
