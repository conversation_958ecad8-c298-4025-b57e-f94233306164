<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Employee.
    -->
    <changeSet id="20250619211021-1" author="jhipster">
        <createTable tableName="employee">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="tel" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="fax" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="mail" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="confidentiel" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="numidentity" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="empnumber" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="fullname" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="address" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="matricule" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="upscale" type="varchar(5)">
                <constraints nullable="true" />
            </column>
            <column name="active" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="statut" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="gender" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="avatar" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="filenameparaf" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="filenamesign" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="coursier" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="user_id" type="bigint">
                <constraints nullable="true" unique="true" uniqueConstraintName="ux_employee__user_id" />
            </column>
            <column name="job_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="unit_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619211021-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/employee.csv"
                  separator=";"
                  tableName="employee"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="tel" type="string"/>
            <column name="fax" type="string"/>
            <column name="mail" type="string"/>
            <column name="confidentiel" type="string"/>
            <column name="numidentity" type="string"/>
            <column name="empnumber" type="string"/>
            <column name="fullname" type="string"/>
            <column name="address" type="string"/>
            <column name="matricule" type="string"/>
            <column name="upscale" type="string"/>
            <column name="active" type="string"/>
            <column name="statut" type="string"/>
            <column name="gender" type="string"/>
            <column name="avatar" type="string"/>
            <column name="filenameparaf" type="string"/>
            <column name="filenamesign" type="string"/>
            <column name="coursier" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
