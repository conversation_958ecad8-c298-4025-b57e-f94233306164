<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">
    <!--
        Added the constraints for entity Correspondencecopy.
    -->
    <changeSet id="20250619211036-2" author="jhipster">

        <addForeignKeyConstraint baseColumnNames="correspondence_id"
                                 baseTableName="correspondencecopy"
                                 constraintName="fk_correspondencecopy__correspondence_id"
                                 referencedColumnNames="id"
                                 referencedTableName="correspondence"
                                 />

        <addForeignKeyConstraint baseColumnNames="employee_id"
                                 baseTableName="correspondencecopy"
                                 constraintName="fk_correspondencecopy__employee_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"
                                 />

        <addForeignKeyConstraint baseColumnNames="unit_id"
                                 baseTableName="correspondencecopy"
                                 constraintName="fk_correspondencecopy__unit_id"
                                 referencedColumnNames="id"
                                 referencedTableName="unit"
                                 />

        <addForeignKeyConstraint baseColumnNames="action_id"
                                 baseTableName="correspondencecopy"
                                 constraintName="fk_correspondencecopy__action_id"
                                 referencedColumnNames="id"
                                 referencedTableName="action"
                                 />

        <addForeignKeyConstraint baseColumnNames="useraction_id"
                                 baseTableName="correspondencecopy"
                                 constraintName="fk_correspondencecopy__useraction_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"
                                 />

        <addForeignKeyConstraint baseColumnNames="userrevoke_id"
                                 baseTableName="correspondencecopy"
                                 constraintName="fk_correspondencecopy__userrevoke_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"
                                 />

        <addForeignKeyConstraint baseColumnNames="userremove_id"
                                 baseTableName="correspondencecopy"
                                 constraintName="fk_correspondencecopy__userremove_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"
                                 />
    </changeSet>
</databaseChangeLog>
