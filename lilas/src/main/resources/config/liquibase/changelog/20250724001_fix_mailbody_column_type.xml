<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Fix mailbody column type in mails table to avoid PostgreSQL oid casting issues.
    -->
    <changeSet id="20250724001_fix_mailbody_column_type" author="system">
        <comment>Fix mailbody column type from clob to text for PostgreSQL compatibility</comment>
        
        <!-- For PostgreSQL, we need to explicitly cast the column -->
        <sql dbms="postgresql">
            ALTER TABLE mails ALTER COLUMN mailbody TYPE text USING mailbody::text;
        </sql>
        
        <!-- For other databases, use the standard approach -->
        <sql dbms="h2,mysql,oracle,mssql">
            ALTER TABLE mails ALTER COLUMN mailbody TYPE text;
        </sql>
        
        <rollback>
            <modifyDataType tableName="mails" columnName="mailbody" newDataType="${clobType}"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
