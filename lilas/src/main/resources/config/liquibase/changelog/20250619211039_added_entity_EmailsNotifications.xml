<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity EmailsNotifications.
    -->
    <changeSet id="20250619211039-1" author="jhipster">
        <createTable tableName="emails_notifications">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="subject" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="notification_date" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="body" type="${blobType}">
                <constraints nullable="true" />
            </column>
            <column name="body_content_type" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="recipient" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="sender" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="order_details_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="emails_notifications" columnName="notification_date" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619211039-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/emails_notifications.csv"
                  separator=";"
                  tableName="emails_notifications"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="subject" type="string"/>
            <column name="notification_date" type="date"/>
            <column name="body" type="blob"/>
            <column name="body_content_type" type="string"/>
            <column name="recipient" type="numeric"/>
            <column name="sender" type="string"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
