<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Action.
    -->
    <changeSet id="20250619211028-1" author="jhipster">
        <createTable tableName="action">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="regulation" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="circular" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="app_order" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="statut" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619211028-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/action.csv"
                  separator=";"
                  tableName="action"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="regulation" type="string"/>
            <column name="circular" type="string"/>
            <column name="app_order" type="numeric"/>
            <column name="statut" type="string"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
