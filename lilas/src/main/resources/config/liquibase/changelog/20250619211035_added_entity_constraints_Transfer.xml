<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">
    <!--
        Added the constraints for entity Transfer.
    -->
    <changeSet id="20250619211035-2" author="jhipster">

        <addForeignKeyConstraint baseColumnNames="transmoth_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__transmoth_id"
                                 referencedColumnNames="id"
                                 referencedTableName="transfer"
                                 />

        <addForeignKeyConstraint baseColumnNames="correspondencecopy_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__correspondencecopy_id"
                                 referencedColumnNames="id"
                                 referencedTableName="correspondencecopy"
                                 />

        <addForeignKeyConstraint baseColumnNames="correspondence_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__correspondence_id"
                                 referencedColumnNames="id"
                                 referencedTableName="correspondence"
                                 />

        <addForeignKeyConstraint baseColumnNames="deliverymode_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__deliverymode_id"
                                 referencedColumnNames="id"
                                 referencedTableName="deliverymode"
                                 />

        <addForeignKeyConstraint baseColumnNames="employee_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__employee_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"
                                 />

        <addForeignKeyConstraint baseColumnNames="unit_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__unit_id"
                                 referencedColumnNames="id"
                                 referencedTableName="unit"
                                 />

        <addForeignKeyConstraint baseColumnNames="action_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__action_id"
                                 referencedColumnNames="id"
                                 referencedTableName="action"
                                 />

        <addForeignKeyConstraint baseColumnNames="userreceive_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__userreceive_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"
                                 />

        <addForeignKeyConstraint baseColumnNames="usertrans_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__usertrans_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"
                                 />

        <addForeignKeyConstraint baseColumnNames="usertransto_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__usertransto_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"
                                 />

        <addForeignKeyConstraint baseColumnNames="unittransto_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__unittransto_id"
                                 referencedColumnNames="id"
                                 referencedTableName="unit"
                                 />

        <addForeignKeyConstraint baseColumnNames="userrevoke_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__userrevoke_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"
                                 />

        <addForeignKeyConstraint baseColumnNames="userreceiveto_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__userreceiveto_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"
                                 />

        <addForeignKeyConstraint baseColumnNames="useraction_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__useraction_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"
                                 />

        <addForeignKeyConstraint baseColumnNames="fromdept_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__fromdept_id"
                                 referencedColumnNames="id"
                                 referencedTableName="unit"
                                 />

        <addForeignKeyConstraint baseColumnNames="transprincip_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__transprincip_id"
                                 referencedColumnNames="id"
                                 referencedTableName="transfer"
                                 />

        <addForeignKeyConstraint baseColumnNames="typecorrespondence_id"
                                 baseTableName="transfer"
                                 constraintName="fk_transfer__typecorrespondence_id"
                                 referencedColumnNames="id"
                                 referencedTableName="typecorrespondence"
                                 />
    </changeSet>
</databaseChangeLog>
