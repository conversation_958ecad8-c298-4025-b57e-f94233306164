<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Correspondence.
    -->
    <changeSet id="20250619211034-1" author="jhipster">
        <createTable tableName="correspondence">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="ordernbr" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="numcopy" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="doclink" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="pagenbr" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="highlevel" type="varchar(5)">
                <constraints nullable="true" />
            </column>
            <column name="confidentiel" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="priority" type="varchar(5)">
                <constraints nullable="true" />
            </column>
            <column name="obs" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="category" type="varchar(5)">
                <constraints nullable="false" />
            </column>
            <column name="status" type="varchar(5)">
                <constraints nullable="true" />
            </column>
            <column name="datejc" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="datejcsend" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="typereceive" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="typecopy" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="refsnd" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="text" type="varchar(4000)">
                <constraints nullable="true" />
            </column>
            <column name="docyear" type="varchar(4)">
                <constraints nullable="true" />
            </column>
            <column name="datehjrsave" type="varchar(10)">
                <constraints nullable="true" />
            </column>
            <column name="datejcsave" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="old_order_number" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="subject" type="varchar(1000)">
                <constraints nullable="false" />
            </column>
            <column name="attach" type="varchar(200)">
                <constraints nullable="true" />
            </column>
            <column name="ordernbradrsbook" type="varchar(50)">
                <constraints nullable="true" />
            </column>
            <column name="seqkeyadrsbook" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="useradrsbook" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="cityzenncard" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="cityzenphone" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="sms" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="incident" type="varchar(5)">
                <constraints nullable="true" />
            </column>
            <column name="check_favorite" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="company_id" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="employee_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="unit_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="deliverymode_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="typecorrespondence_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="correspondence" columnName="datejc" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="correspondence" columnName="datejcsend" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="correspondence" columnName="datejcsave" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619211034-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/correspondence.csv"
                  separator=";"
                  tableName="correspondence"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="ordernbr" type="string"/>
            <column name="numcopy" type="string"/>
            <column name="doclink" type="string"/>
            <column name="pagenbr" type="string"/>
            <column name="highlevel" type="string"/>
            <column name="confidentiel" type="string"/>
            <column name="priority" type="string"/>
            <column name="obs" type="string"/>
            <column name="category" type="string"/>
            <column name="status" type="string"/>
            <column name="datejc" type="date"/>
            <column name="datejcsend" type="date"/>
            <column name="typereceive" type="string"/>
            <column name="typecopy" type="string"/>
            <column name="refsnd" type="string"/>
            <column name="text" type="string"/>
            <column name="docyear" type="string"/>
            <column name="datehjrsave" type="string"/>
            <column name="datejcsave" type="date"/>
            <column name="old_order_number" type="string"/>
            <column name="subject" type="string"/>
            <column name="attach" type="string"/>
            <column name="ordernbradrsbook" type="string"/>
            <column name="seqkeyadrsbook" type="numeric"/>
            <column name="useradrsbook" type="string"/>
            <column name="cityzenncard" type="string"/>
            <column name="cityzenphone" type="string"/>
            <column name="sms" type="string"/>
            <column name="incident" type="string"/>
            <column name="check_favorite" type="string"/>
            <column name="company_id" type="string"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
