<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                        http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-4.6.xsd">

    <changeSet id="20250710203100-1" author="system">
        <comment>Set default status for existing records in demande_promotion table</comment>
        <sql>
            UPDATE demande_promotion 
            SET status = 'Waiting' 
            WHERE status IS NULL;
        </sql>
    </changeSet>

    <changeSet id="20250710203100-2" author="system">
        <comment>Set default status for existing records in promotion_details table</comment>
        <sql>
            UPDATE promotion_details 
            SET status = 'Waiting' 
            WHERE status IS NULL;
        </sql>
    </changeSet>

</databaseChangeLog>
