<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Modified the Order entity to use enum status and add new fields.
    -->
    <changeSet id="20250718002_modify_order_entity" author="jhipster">
        <comment>Modify Order entity - change status to varchar and add new columns</comment>
        
        <!-- First, change the status column from integer to varchar -->
        <modifyDataType tableName="jhi_order" columnName="status" newDataType="varchar(50)"/>
        
        <!-- Add new columns -->
        <addColumn tableName="jhi_order">
            <column name="locked" type="boolean">
                <constraints nullable="true" />
            </column>
        </addColumn>
        
        <addColumn tableName="jhi_order">
            <column name="order_number" type="varchar(255)">
                <constraints nullable="true" />
            </column>
        </addColumn>
        
        <addColumn tableName="jhi_order">
            <column name="employee_id" type="bigint">
                <constraints nullable="true" />
            </column>
        </addColumn>
        
        <rollback>
            <dropColumn tableName="jhi_order" columnName="employee_id"/>
            <dropColumn tableName="jhi_order" columnName="order_number"/>
            <dropColumn tableName="jhi_order" columnName="locked"/>
            <modifyDataType tableName="jhi_order" columnName="status" newDataType="integer"/>
        </rollback>
    </changeSet>
    
    <!-- Add foreign key constraint for employee_id -->
    <changeSet id="20250718003_add_order_employee_fk" author="jhipster">
        <comment>Add foreign key constraint for employee_id in Order entity</comment>
        <addForeignKeyConstraint baseColumnNames="employee_id"
                                 baseTableName="jhi_order"
                                 constraintName="fk_jhi_order_employee_id"
                                 referencedColumnNames="id"
                                 referencedTableName="employee"/>
        <rollback>
            <dropForeignKeyConstraint baseTableName="jhi_order" constraintName="fk_jhi_order_employee_id"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
