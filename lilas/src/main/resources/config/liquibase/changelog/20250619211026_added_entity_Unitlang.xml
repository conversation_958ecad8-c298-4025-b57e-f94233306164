<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Unitlang.
    -->
    <changeSet id="20250619211026-1" author="jhipster">
        <createTable tableName="unitlang">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="address" type="varchar(500)">
                <constraints nullable="true" />
            </column>
            <column name="nbr" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="name" type="varchar(500)">
                <constraints nullable="false" />
            </column>
            <column name="lang" type="varchar(5)">
                <constraints nullable="false" />
            </column>
            <column name="abrv" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="unit_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619211026-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/unitlang.csv"
                  separator=";"
                  tableName="unitlang"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="address" type="string"/>
            <column name="nbr" type="string"/>
            <column name="name" type="string"/>
            <column name="lang" type="string"/>
            <column name="abrv" type="string"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
