<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity Unit.
    -->
    <changeSet id="20250619211025-1" author="jhipster">
        <createTable tableName="unit">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="parentid" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="tel" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="fax" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="jhi_order" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="deanstatus" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="address" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="nbr" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="name" type="varchar(1000)">
                <constraints nullable="true" />
            </column>
            <column name="lang" type="varchar(5)">
                <constraints nullable="true" />
            </column>
            <column name="abbreviated" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="mail" type="varchar(100)">
                <constraints nullable="true" />
            </column>
            <column name="checkenabled" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="activate" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="active" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="level" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="grp" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="compid" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="branch" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="regoffice" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="unitgroup" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="grdparent" type="varchar(20)">
                <constraints nullable="true" />
            </column>
            <column name="status" type="varchar(1)">
                <constraints nullable="true" />
            </column>
            <column name="category" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="function_unit" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="position" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="section" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="categdir" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="categ_unit" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="function" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="responsible" type="varchar(15)">
                <constraints nullable="true" />
            </column>
            <column name="company_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619211025-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/unit.csv"
                  separator=";"
                  tableName="unit"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="parentid" type="string"/>
            <column name="tel" type="string"/>
            <column name="fax" type="string"/>
            <column name="jhi_order" type="numeric"/>
            <column name="deanstatus" type="string"/>
            <column name="address" type="string"/>
            <column name="nbr" type="string"/>
            <column name="name" type="string"/>
            <column name="lang" type="string"/>
            <column name="abbreviated" type="string"/>
            <column name="mail" type="string"/>
            <column name="checkenabled" type="string"/>
            <column name="activate" type="string"/>
            <column name="active" type="string"/>
            <column name="level" type="string"/>
            <column name="grp" type="string"/>
            <column name="compid" type="string"/>
            <column name="branch" type="string"/>
            <column name="regoffice" type="string"/>
            <column name="unitgroup" type="string"/>
            <column name="grdparent" type="string"/>
            <column name="status" type="string"/>
            <column name="category" type="string"/>
            <column name="function_unit" type="string"/>
            <column name="position" type="string"/>
            <column name="section" type="string"/>
            <column name="categdir" type="string"/>
            <column name="categ_unit" type="string"/>
            <column name="function" type="string"/>
            <column name="responsible" type="string"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
