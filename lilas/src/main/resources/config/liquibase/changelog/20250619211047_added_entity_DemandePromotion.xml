<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity DemandePromotion.
    -->
    <changeSet id="20250619211047-1" author="jhipster">
        <createTable tableName="demande_promotion">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="code_client" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="enseigne" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="action" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="period_promotion_start" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="period_promotion_end" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="period_facturation_start" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="period_facturation_end" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="employee_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <column name="articles_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="demande_promotion" columnName="period_promotion_start" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="demande_promotion" columnName="period_promotion_end" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="demande_promotion" columnName="period_facturation_start" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="demande_promotion" columnName="period_facturation_end" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <!-- Temporarily commented out to avoid checksum issues
    <changeSet id="20250619211047-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/demande_promotion.csv"
                  separator=";"
                  tableName="demande_promotion"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="code_client" type="string"/>
            <column name="enseigne" type="string"/>
            <column name="action" type="string"/>
            <column name="period_promotion_start" type="date"/>
            <column name="period_promotion_end" type="date"/>
            <column name="period_facturation_start" type="date"/>
            <column name="period_facturation_end" type="date"/>
        </loadData>
    </changeSet>
    -->
</databaseChangeLog>
