<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity GmsClients.
    -->
    <changeSet id="20250619211042-1" author="jhipster">
        <createTable tableName="gms_clients">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="client_name" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="code" type="varchar(255)">
                <constraints nullable="true" unique="true" uniqueConstraintName="ux_gms_clients__code" />
            </column>
            <column name="classe" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="ville" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="creation_date" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="update_date" type="${datetimeType}">
                <constraints nullable="true" />
            </column>
            <column name="cadencier_id" type="bigint">
                <constraints nullable="true" unique="true" uniqueConstraintName="ux_gms_clients__cadencier_id" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
        <dropDefaultValue tableName="gms_clients" columnName="creation_date" columnDataType="${datetimeType}"/>
        <dropDefaultValue tableName="gms_clients" columnName="update_date" columnDataType="${datetimeType}"/>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619211042-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/gms_clients.csv"
                  separator=";"
                  tableName="gms_clients"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="client_name" type="string"/>
            <column name="code" type="string"/>
            <column name="classe" type="string"/>
            <column name="ville" type="string"/>
            <column name="creation_date" type="date"/>
            <column name="update_date" type="date"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
