<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the entity OrderDetails.
    -->
    <changeSet id="20250619211038-1" author="jhipster">
        <createTable tableName="order_details">
            <column name="id" type="bigint">
                <constraints primaryKey="true" nullable="false"/>
            </column>
            <column name="quantity" type="decimal(21,2)">
                <constraints nullable="true" />
            </column>
            <column name="updated_qty" type="decimal(21,2)">
                <constraints nullable="true" />
            </column>
            <column name="discount" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="updated_discount" type="integer">
                <constraints nullable="true" />
            </column>
            <column name="unit_price" type="double">
                <constraints nullable="true" />
            </column>
            <column name="updated_unit_price" type="double">
                <constraints nullable="true" />
            </column>
            <column name="order_line_json" type="${clobType}">
                <constraints nullable="true" />
            </column>
            <column name="availability" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="valid_conditions" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="injected" type="boolean">
                <constraints nullable="true" />
            </column>
            <column name="barcode" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="updated_barcode" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="internal_code" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="updated_internal_code" type="varchar(255)">
                <constraints nullable="true" />
            </column>
            <column name="discount_status" type="boolean">
              <constraints nullable="true" />
            </column>
            <column name="price_status" type="boolean">
              <constraints nullable="true" />
            </column>
            <column name="quantity_status" type="boolean">
              <constraints nullable="true" />
            </column>
            <column name="order_id" type="bigint">
                <constraints nullable="true" />
            </column>
            <!-- jhipster-needle-liquibase-add-column - JHipster will add columns here -->
        </createTable>
    </changeSet>

    <!-- jhipster-needle-liquibase-add-changeset - JHipster will add changesets here -->

    <!--
        Load sample data generated with Faker.js
        - This data can be easily edited using a CSV editor (or even MS Excel) and
          is located in the 'src/main/resources/config/liquibase/fake-data' directory
        - By default this data is applied when running with the JHipster 'dev' profile.
          This can be customized by adding or removing 'faker' in the 'spring.liquibase.contexts'
          Spring Boot configuration key.
    -->
    <changeSet id="20250619211038-1-data" author="jhipster" context="faker">
        <loadData
                  file="config/liquibase/fake-data/order_details.csv"
                  separator=";"
                  tableName="order_details"
                  usePreparedStatements="true">
            <column name="id" type="numeric"/>
            <column name="quantity" type="numeric"/>
            <column name="updated_qty" type="numeric"/>
            <column name="discount" type="numeric"/>
            <column name="updated_discount" type="numeric"/>
            <column name="unit_price" type="numeric"/>
            <column name="updated_unit_price" type="numeric"/>
            <column name="order_line_json" type="clob"/>
            <column name="availability" type="boolean"/>
            <column name="valid_conditions" type="boolean"/>
            <column name="injected" type="boolean"/>
            <column name="barcode" type="string"/>
            <column name="updated_barcode" type="string"/>
            <column name="internal_code" type="string"/>
            <column name="updated_internal_code" type="string"/>
            <column name="discount_status" type="boolean"/>
            <column name="price_status" type="boolean"/>
            <column name="quantity_status" type="boolean"/>
            <!-- jhipster-needle-liquibase-add-loadcolumn - JHipster (and/or extensions) can add load columns here -->
        </loadData>
    </changeSet>
</databaseChangeLog>
