<?xml version="1.0" encoding="utf-8"?>
<databaseChangeLog
    xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
    xmlns:ext="http://www.liquibase.org/xml/ns/dbchangelog-ext"
    xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
    xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd
                        http://www.liquibase.org/xml/ns/dbchangelog-ext http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-ext.xsd">

    <!--
        Added the mail_type column to the Mails entity.
    -->
    <changeSet id="20250718001_add_mail_type_column" author="jhipster">
        <comment>Add mail_type column to mails table</comment>
        <addColumn tableName="mails">
            <column name="mail_type" type="varchar(50)">
                <constraints nullable="true" />
            </column>
        </addColumn>
        <rollback>
            <dropColumn tableName="mails" columnName="mail_type"/>
        </rollback>
    </changeSet>

</databaseChangeLog>
