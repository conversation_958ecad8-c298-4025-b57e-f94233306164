package com.dq.lilas.repository;

import com.dq.lilas.domain.DailyStock;
import com.dq.lilas.domain.OrderDetails;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;

/**
 * Spring Data JPA repository for the OrderDetails entity.
 */
@SuppressWarnings("unused")
@Repository
public interface OrderDetailsRepository extends JpaRepository<OrderDetails, Long> {
    @Query("SELECT d FROM OrderDetails d WHERE d.internalCode = :internalCode ")
    OrderDetails findOrderDetailsByInternalCode(String internalCode);
}
