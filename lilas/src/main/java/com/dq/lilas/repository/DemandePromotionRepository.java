package com.dq.lilas.repository;

import com.dq.lilas.domain.DemandePromotion;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the DemandePromotion entity.
 */
@SuppressWarnings("unused")
@Repository
public interface DemandePromotionRepository extends JpaRepository<DemandePromotion, Long> {
    
    /**
     * Find all DemandePromotion entities for a specific user ID.
     *
     * @param userId the user ID
     * @param pageable the pagination information
     * @return the page of entities
     */
    @Query("SELECT dp FROM DemandePromotion dp LEFT JOIN dp.employee e LEFT JOIN e.user u WHERE u.id = :userId")
    Page<DemandePromotion> findByEmployeeUserId(@Param("userId") Long userId, Pageable pageable);
}
