package com.dq.lilas.repository;

import com.dq.lilas.domain.EmployeeGmsBrands;
import com.dq.lilas.domain.GmsBrands;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data JPA repository for the EmployeeGmsBrands entity.
 */
@SuppressWarnings("unused")
@Repository
public interface EmployeeGmsBrandsRepository extends JpaRepository<EmployeeGmsBrands, Long> {
    @Query("SELECT e FROM EmployeeGmsBrands e WHERE e.employee.id = :employeeId")
    List<EmployeeGmsBrands> findByEmployee(Long employeeId);

    @Query("SELECT e.gmsBrands.id FROM EmployeeGmsBrands e WHERE e.employee.id = :employeeId")
    List<Long> findBrandByEmployee(Long employeeId);
}
