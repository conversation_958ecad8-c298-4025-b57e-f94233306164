package com.dq.lilas.repository;

import com.dq.lilas.domain.Order;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;
import java.time.Instant;
import java.util.List;
import java.util.Optional;

/**
 * Spring Data JPA repository for the Order entity.
 */
@SuppressWarnings("unused")
@Repository
public interface OrderRepository extends JpaRepository<Order, Long> {

    /**
     * Find orders by delivery date comparison and status
     * @param comparisonDate the date to compare against
     * @param status the order status
     * @return list of orders matching the criteria
     */
    @Query("SELECT o FROM Order o WHERE o.dailyBatches.batchDate > :comparisonDate AND o.status = :status")
    List<Order> findOrdersByDeliveryDateAfterAndStatus(@Param("comparisonDate") Instant comparisonDate, @Param("status") Integer status);

    @Query("SELECT o FROM Order o WHERE o.dailyBatches.batchDate <= :comparisonDate AND o.status = :status")
    List<Order> findOrdersByDeliveryDateBeforeOrEqualAndStatus(@Param("comparisonDate") Instant comparisonDate, @Param("status") Integer status);

    /**
     * Find orders by status
     * @param status the order status ('D' for livré/delivered, 'P' for payé/paid)
     * @return list of orders matching the status
     */
    @Query("SELECT o FROM Order o WHERE o.status = :status")
    List<Order> findOrdersByStatus(@Param("status") Integer status);

    @Query("SELECT o FROM Order o WHERE o.dailyBatches.id = :batchId AND o.brandId = :brandId")
    Optional<Order> findByBrandAndBatche(Long batchId, Long brandId);

    /**
     * Find orders by brand ID
     * @param brandId the brand ID
     * @return list of orders matching the brand ID
     */
    @Query("SELECT o FROM Order o WHERE o.brandId = :brandId")
    List<Order> findOrdersByBrandId(@Param("brandId") Long brandId);

    /**
     * Find orders by company name
     * @param company the company name
     * @return list of orders matching the company name
     */
    @Query("SELECT o FROM Order o WHERE o.company = :company")
    List<Order> findOrdersByCompany(@Param("company") String company);
}
