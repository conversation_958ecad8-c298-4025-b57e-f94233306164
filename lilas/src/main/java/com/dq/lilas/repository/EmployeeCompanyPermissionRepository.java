package com.dq.lilas.repository;

import com.dq.lilas.domain.EmployeeCompanyPermission;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.*;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

/**
 * Spring Data JPA repository for the EmployeeCompanyPermission entity.
 */
@Repository
public interface EmployeeCompanyPermissionRepository extends JpaRepository<EmployeeCompanyPermission, Long> {
    @Query(
        "select employeeCompanyPermission from EmployeeCompanyPermission employeeCompanyPermission where employeeCompanyPermission.employee.id = ?#{authentication.name}"
    )
    List<EmployeeCompanyPermission> findByEmployeeIsCurrentEmployee();

    default Optional<EmployeeCompanyPermission> findOneWithEagerRelationships(Long id) {
        return this.findOneWithToOneRelationships(id);
    }

    default List<EmployeeCompanyPermission> findAllWithEagerRelationships() {
        return this.findAllWithToOneRelationships();
    }

    default Page<EmployeeCompanyPermission> findAllWithEagerRelationships(Pageable pageable) {
        return this.findAllWithToOneRelationships(pageable);
    }

    @Query(
        value = "select employeeCompanyPermission from EmployeeCompanyPermission employeeCompanyPermission left join fetch employeeCompanyPermission.employee",
        countQuery = "select count(employeeCompanyPermission) from EmployeeCompanyPermission employeeCompanyPermission"
    )
    Page<EmployeeCompanyPermission> findAllWithToOneRelationships(Pageable pageable);

    @Query("select employeeCompanyPermission from EmployeeCompanyPermission employeeCompanyPermission left join fetch employeeCompanyPermission.employee")
    List<EmployeeCompanyPermission> findAllWithToOneRelationships();

    @Query(
        "select employeeCompanyPermission from EmployeeCompanyPermission employeeCompanyPermission left join fetch employeeCompanyPermission.employee where employeeCompanyPermission.id =:id"
    )
    Optional<EmployeeCompanyPermission> findOneWithToOneRelationships(@Param("id") Long id);

    @Query("select ecp.company.id from EmployeeCompanyPermission ecp where ecp.employee.id = :employeeId")
    List<Long> findCompanyIdsByEmployeeId(@Param("employeeId") Long employeeId);

}
