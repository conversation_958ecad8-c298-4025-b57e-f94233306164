package com.dq.lilas.repository;

import com.dq.lilas.domain.DailyStock;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;

/**
 * Spring Data JPA repository for the DailyStock entity.
 */
@SuppressWarnings("unused")
@Repository
public interface DailyStockRepository extends JpaRepository<DailyStock, Long> {
    @Query("SELECT d FROM DailyStock d WHERE d.internalCode = :internalCode AND d.stockDate = :stockDate")
    DailyStock findByInternalCodeAndStockDate(String internalCode, LocalDate stockDate);


}
