package com.dq.lilas.repository;

import com.dq.lilas.domain.Conversation;
import com.dq.lilas.domain.enumeration.ConversationState;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Modifying;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * Spring Data SQL repository for the Conversation entity.
 */
@SuppressWarnings("unused")
@Repository
public interface ConversationRepository extends JpaRepository<Conversation, Long> {

    @Query("SELECT c from Conversation c WHERE c.room.id =:id ORDER BY c.createdDate DESC ")
    Page<Conversation> findAllByRoomId(Pageable pageable, @Param("id") Long roomId);

    @Query(value = "SELECT DISTINCT c.* FROM conversation c " +
        "LEFT JOIN conversation_receiver cr ON c.id = cr.conversation_id " +
        "WHERE c.room_id = :roomId AND " +
        "(c.sender_id = :userId OR cr.receiver_id = :userId) " +
        "ORDER BY c.created_date DESC", nativeQuery = true)
    Page<Conversation> findAllByLoggedUserOfRoomAfterJoinDate(Pageable pageable, @Param("roomId") Long roomId, @Param("userId") Long userId);

    void deleteAllByRoomId(Long id);

    @Query("SELECT c.sender.id AS sender, COUNT(c) AS count FROM Conversation c LEFT JOIN ConversationReceiver cr on c.id = cr.conversation.id WHERE cr.conversationState =:state AND cr.receiver.id =:receiverId AND c.room.group is null GROUP BY c.sender.id")
    List<?> getConversationsCountByReceiverAndStateGroupedBySender(@Param("state") ConversationState state, @Param("receiverId") Long receiverId);

    @Query("SELECT c.room.id AS room, COUNT(c) AS count FROM Conversation c LEFT JOIN ConversationReceiver cr on c.id = cr.conversation.id WHERE cr.conversationState =:state AND cr.receiver.id =:receiverId GROUP BY c.room.id")
    List<?> getConversationsCountByReceiverAndStateGroupedByRoom(@Param("state") ConversationState state, @Param("receiverId") Long receiverId);

    @Query("SELECT COUNT(c) AS count FROM Conversation c LEFT JOIN ConversationReceiver cr on c.id = cr.conversation.id WHERE cr.conversationState =:state AND cr.receiver.id =:receiverId AND c.room.id =:roomId")
    int getConversationCountByRoomAndReceiverAndState(@Param("state") ConversationState state, @Param("receiverId") Long receiverId, @Param("roomId") Long roomId );

    @Modifying
    @Query("UPDATE ConversationReceiver cr SET cr.conversationState =:state WHERE cr.receiver.id =:receiverId AND cr.conversation.id IN (SELECT c.id FROM Conversation c LEFT JOIN ConversationReceiver cr1 ON c.id = cr1.conversation.id AND c.room.id =:roomId)")
    void updateConversationStateByRoom(@Param(value = "state") ConversationState state, @Param(value = "roomId") Long roomId, @Param(value = "receiverId") Long receiverId);
}
