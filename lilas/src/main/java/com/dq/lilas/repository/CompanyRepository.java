package com.dq.lilas.repository;

import com.dq.lilas.domain.Company;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;
import java.util.Optional;

/**
 * Spring Data JPA repository for the Company entity.
 */
@SuppressWarnings("unused")
@Repository
public interface CompanyRepository extends JpaRepository<Company, Long> {
    
    /**
     * Find company by name (case-insensitive).
     *
     * @param companyName the company name to search for.
     * @return the company if found.
     */
    Optional<Company> findByComapanyNameIgnoreCase(String companyName);
}
