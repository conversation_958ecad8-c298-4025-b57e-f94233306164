package com.dq.lilas.repository;

import com.dq.lilas.domain.Room;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

/**
 * Spring Data SQL repository for the Room entity.
 */
@Repository
public interface RoomRepository extends JpaRepository<Room, Long> {

    @Query(value = "select distinct room from Room room")
    Page<Room> findAllWithEagerRelationships(Pageable pageable);

    @Query("select room from Room room where room.id =:id")
    Optional<Room> findOneWithEagerRelationships(@Param("id") Long id);

    List<Room> findAllByGroup_Id(Long groupId);

    Optional<Room> findFirstByNameAndGroupId(String name, Long groupId);

    @Query(value = "select distinct ru.room from RoomUser ru where ru.user.id =:userId")
    List<Room> findAllByUsersIsContaining(@Param("userId") Long userId);

    @Query(value = "select distinct ru.room from RoomUser ru where ru.room.group.id =:groupId and ru.user.id =:userId")
    List<Room> findAllByUsersIsContainingAndGroup(@Param("userId") Long userId, @Param("groupId") Long groupId);

    @Query(value = "select distinct r from Room r where r.group is null and r.id in " +
        "(select r1.id from Room r1 inner join RoomUser ru1 on r1.id = ru1.room.id where ru1.user.id =:u1)" +
        "and r.id in (select r2.id from Room r2 inner join RoomUser ru2 on r2.id = ru2.room.id where ru2.user.id =:u2)")
    Optional<Room> findPrivateRoomBySenderAndReceiver(@Param("u1") Long senderId, @Param("u2") Long receiverId);
}
