package com.dq.lilas.repository;

import com.dq.lilas.domain.DailyBatches;
import org.springframework.data.jpa.repository.*;
import org.springframework.stereotype.Repository;

import java.time.Instant;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

/**
 * Spring Data JPA repository for the DailyBatches entity.
 */
@SuppressWarnings("unused")
@Repository
public interface DailyBatchesRepository extends JpaRepository<DailyBatches, Long> {
    List<DailyBatches> findByBatchDateBetween(Instant startOfDay, Instant endOfDay);

    @Query("SELECT d FROM DailyBatches d WHERE d.company.id IN :companyIds")
    List<DailyBatches> findBatchesByCompanies(List<Long> companyIds);
    
    /**
     * Find the daily batch for a company on a specific date.
     *
     * @param companyId the company ID.
     * @param batchDate the batch date.
     * @return the daily batch if found.
     */
    @Query("SELECT d FROM DailyBatches d WHERE d.company.id = :companyId AND DATE(d.batchDate) = :batchDate")
    Optional<DailyBatches> findByCompanyIdAndBatchDate(Long companyId, LocalDate batchDate);
    
    /**
     * Update the order number for a specific batch.
     *
     * @param batchId the batch ID.
     * @param newOrderNb the new order number.
     */
    @Modifying
    @Query("UPDATE DailyBatches d SET d.orderNb = :newOrderNb WHERE d.id = :batchId")
    void updateOrderNumber(Long batchId, int newOrderNb);
}
