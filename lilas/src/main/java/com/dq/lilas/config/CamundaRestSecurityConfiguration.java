package com.dq.lilas.config;

import static org.springframework.security.config.Customizer.withDefaults;

import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.core.annotation.Order;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.web.SecurityFilterChain;

@Configuration
public class CamundaRestSecurityConfiguration {

    private final AuthenticationProvider camundaAuthenticationProvider;

    public CamundaRestSecurityConfiguration(@Qualifier("camundaAuthenticationProvider") AuthenticationProvider camundaAuthenticationProvider) {
        this.camundaAuthenticationProvider = camundaAuthenticationProvider;
    }

    @Bean
    @Order(1) // Higher priority than main security configuration
    public SecurityFilter<PERSON>hain camundaRestSecurityFilterChain(HttpSecurity http) throws Exception {
        http
                .securityMatchers(matchers ->
                        matchers.requestMatchers("/engine-rest/**")
                )
                .authorizeHttpRequests(auth -> auth
                        .anyRequest().authenticated()
                )
                .httpBasic(withDefaults()) // Enable HTTP Basic Authentication for REST API
                .csrf(csrf -> csrf.disable())
                .sessionManagement(session ->
                        session.sessionCreationPolicy(SessionCreationPolicy.STATELESS)
                )
                .authenticationProvider(camundaAuthenticationProvider);

        return http.build();
    }
}
