package com.dq.lilas.config;

import com.azure.identity.ClientSecretCredential;
import com.azure.identity.ClientSecretCredentialBuilder;
import com.microsoft.graph.serviceclient.GraphServiceClient;
import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class GraphClientConfiguration {

    @Bean
    @ConfigurationProperties(prefix = "azure.graph")
    public GraphProperties graphProperties() {
        return new GraphProperties();
    }

    @Bean
    public ClientSecretCredential clientSecretCredential(GraphProperties properties) {
        return new ClientSecretCredentialBuilder()
            .clientId(properties.getClientId())
            .tenantId(properties.getTenantId())
            .clientSecret(properties.getClientSecret())
            .build();
    }

    @Bean
    public GraphServiceClient graphServiceClient(ClientSecretCredential credential, GraphProperties properties) {
        return new GraphServiceClient(credential, properties.getScope());
    }

    @Data
    public static class GraphProperties {
        private String clientId;
        private String tenantId;
        private String clientSecret;
        private String scope;
        private Webhook webhook;

        @Data
        public static class Webhook {
            private String url;
            private String notificationSecret;
            private boolean autoRenew;
        }
    }
}
