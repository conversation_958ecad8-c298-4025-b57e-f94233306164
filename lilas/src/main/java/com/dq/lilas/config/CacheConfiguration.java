package com.dq.lilas.config;

import java.time.Duration;
import org.ehcache.config.builders.*;
import org.ehcache.jsr107.Eh107Configuration;
import org.hibernate.cache.jcache.ConfigSettings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.autoconfigure.cache.JCacheManagerCustomizer;
import org.springframework.boot.autoconfigure.orm.jpa.HibernatePropertiesCustomizer;
import org.springframework.boot.info.BuildProperties;
import org.springframework.boot.info.GitProperties;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.cache.interceptor.KeyGenerator;
import org.springframework.context.annotation.*;
import tech.jhipster.config.JHipsterProperties;
import tech.jhipster.config.cache.PrefixedKeyGenerator;

@Configuration
@EnableCaching
public class CacheConfiguration {

    private GitProperties gitProperties;
    private BuildProperties buildProperties;
    private final javax.cache.configuration.Configuration<Object, Object> jcacheConfiguration;

    public CacheConfiguration(JHipsterProperties jHipsterProperties) {
        JHipsterProperties.Cache.Ehcache ehcache = jHipsterProperties.getCache().getEhcache();

        jcacheConfiguration = Eh107Configuration.fromEhcacheCacheConfiguration(
            CacheConfigurationBuilder.newCacheConfigurationBuilder(
                Object.class,
                Object.class,
                ResourcePoolsBuilder.heap(ehcache.getMaxEntries())
            )
                .withExpiry(ExpiryPolicyBuilder.timeToLiveExpiration(Duration.ofSeconds(ehcache.getTimeToLiveSeconds())))
                .build()
        );
    }

    @Bean
    public HibernatePropertiesCustomizer hibernatePropertiesCustomizer(javax.cache.CacheManager cacheManager) {
        return hibernateProperties -> hibernateProperties.put(ConfigSettings.CACHE_MANAGER, cacheManager);
    }

    @Bean
    public JCacheManagerCustomizer cacheManagerCustomizer() {
        return cm -> {
            createCache(cm, com.dq.lilas.repository.UserRepository.USERS_BY_LOGIN_CACHE);
            createCache(cm, com.dq.lilas.repository.UserRepository.USERS_BY_EMAIL_CACHE);
            createCache(cm, com.dq.lilas.domain.User.class.getName());
            createCache(cm, com.dq.lilas.domain.Authority.class.getName());
            createCache(cm, com.dq.lilas.domain.User.class.getName() + ".authorities");
            createCache(cm, com.dq.lilas.domain.Employee.class.getName());
            createCache(cm, com.dq.lilas.domain.Employee.class.getName() + ".employeelangs");
            createCache(cm, com.dq.lilas.domain.Job.class.getName());
            createCache(cm, com.dq.lilas.domain.Job.class.getName() + ".joblangs");
            createCache(cm, com.dq.lilas.domain.Joblang.class.getName());
            createCache(cm, com.dq.lilas.domain.Employeelang.class.getName());
            createCache(cm, com.dq.lilas.domain.Unit.class.getName());
            createCache(cm, com.dq.lilas.domain.Unit.class.getName() + ".unitlangs");
            createCache(cm, com.dq.lilas.domain.Unit.class.getName() + ".employees");
            createCache(cm, com.dq.lilas.domain.Unitlang.class.getName());
            createCache(cm, com.dq.lilas.domain.Company.class.getName());
            createCache(cm, com.dq.lilas.domain.Company.class.getName() + ".units");
            createCache(cm, com.dq.lilas.domain.Action.class.getName());
            createCache(cm, com.dq.lilas.domain.Action.class.getName() + ".actionlangs");
            createCache(cm, com.dq.lilas.domain.Actionlang.class.getName());
            createCache(cm, com.dq.lilas.domain.Deliverymode.class.getName());
            createCache(cm, com.dq.lilas.domain.Deliverymode.class.getName() + ".deliverymodelangs");
            createCache(cm, com.dq.lilas.domain.Deliverymodelang.class.getName());
            createCache(cm, com.dq.lilas.domain.Typecorrespondence.class.getName());
            createCache(cm, com.dq.lilas.domain.Typecorrespondence.class.getName() + ".typecorrespondencelangs");
            createCache(cm, com.dq.lilas.domain.Typecorrespondencelang.class.getName());
            createCache(cm, com.dq.lilas.domain.Correspondence.class.getName());
            createCache(cm, com.dq.lilas.domain.Transfer.class.getName());
            createCache(cm, com.dq.lilas.domain.Correspondencecopy.class.getName());
            createCache(cm, com.dq.lilas.domain.Attachement.class.getName());
            createCache(cm, com.dq.lilas.domain.OrderDetails.class.getName());
            createCache(cm, com.dq.lilas.domain.OrderDetails.class.getName() + ".emailsNotifications");
            createCache(cm, com.dq.lilas.domain.EmailsNotifications.class.getName());
            createCache(cm, com.dq.lilas.domain.Mails.class.getName());
            createCache(cm, com.dq.lilas.domain.DailyBatches.class.getName());
            createCache(cm, com.dq.lilas.domain.GmsClients.class.getName());
            createCache(cm, com.dq.lilas.domain.GmsBrands.class.getName());
            createCache(cm, com.dq.lilas.domain.EmployeeGmsBrands.class.getName());
            createCache(cm, com.dq.lilas.domain.Order.class.getName());
            createCache(cm, com.dq.lilas.domain.Order.class.getName() + ".orderDetails");
            createCache(cm, com.dq.lilas.domain.TemplateConditions.class.getName());
            createCache(cm, com.dq.lilas.domain.DemandePromotion.class.getName());
            createCache(cm, com.dq.lilas.domain.PromotionDetails.class.getName());
            createCache(cm, com.dq.lilas.domain.Cadencier.class.getName());
            createCache(cm, com.dq.lilas.domain.ProductsList.class.getName());
            createCache(cm, com.dq.lilas.domain.DailyStock.class.getName());
            createCache(cm, com.dq.lilas.domain.Deliverymode.class.getName() + ".deleverymodelangs");
            createCache(cm, com.dq.lilas.domain.Typecorrespondence.class.getName() + ".typecorrespondences");
            createCache(cm, com.dq.lilas.domain.EmployeeCompanyPermission.class.getName() + ".usercompanypermission");
            // jhipster-needle-ehcache-add-entry
        };
    }

    private void createCache(javax.cache.CacheManager cm, String cacheName) {
        javax.cache.Cache<Object, Object> cache = cm.getCache(cacheName);
        if (cache != null) {
            cache.clear();
        } else {
            cm.createCache(cacheName, jcacheConfiguration);
        }
    }

    @Autowired(required = false)
    public void setGitProperties(GitProperties gitProperties) {
        this.gitProperties = gitProperties;
    }

    @Autowired(required = false)
    public void setBuildProperties(BuildProperties buildProperties) {
        this.buildProperties = buildProperties;
    }

    @Bean
    public KeyGenerator keyGenerator() {
        return new PrefixedKeyGenerator(this.gitProperties, this.buildProperties);
    }
}
