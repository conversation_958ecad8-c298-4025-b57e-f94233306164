package com.dq.lilas.config;

import com.dq.lilas.web.websocket.domain.NotificationSocket;
import com.dq.lilas.web.websocket.dto.UserSocketDTO;
import com.dq.lilas.web.websocket.enumeration.NotificationSocketType;
import com.dq.lilas.web.websocket.enumeration.UserConnexionState;
import com.dq.lilas.web.websocket.service.ActiveCallSocketService;
import com.dq.lilas.web.websocket.service.UserSocketService;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.event.EventListener;
import org.springframework.messaging.simp.SimpMessagingTemplate;
import org.springframework.messaging.support.GenericMessage;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.messaging.SessionConnectedEvent;
import org.springframework.web.socket.messaging.SessionDisconnectEvent;
import org.springframework.web.socket.messaging.SessionSubscribeEvent;

import java.security.Principal;
import java.time.Instant;
import java.util.List;

@Component
public class WebSocketEventListener {

    private static final Logger logger = LoggerFactory.getLogger(WebSocketEventListener.class);
    private final SimpMessagingTemplate template;
    private final UserSocketService userSocketService;
    private final ActiveCallSocketService activeCallSocketService;

    public WebSocketEventListener(SimpMessagingTemplate template, UserSocketService userSocketService, ActiveCallSocketService activeCallSocketService) {
        this.template = template;
        this.userSocketService = userSocketService;
        this.activeCallSocketService = activeCallSocketService;
    }

    private void handleSession(String username, String logText, UserConnexionState userConnexionState) {
        logger.info(logText);
        NotificationSocket notificationSocket = new NotificationSocket();
        notificationSocket.setTime(Instant.now());
        notificationSocket.setContent(logText);
        notificationSocket.setType(NotificationSocketType.USER_STATE);
        notificationSocket.addToMetadata("USER", username);
        notificationSocket.addToMetadata("STATE", userConnexionState.name());
        // Notify everyone about user.
        template.convertAndSend("/topic/public", notificationSocket);
    }

    private void onUserSubscribe(String username) {
        NotificationSocket notificationSocket = new NotificationSocket();
        notificationSocket.setTime(Instant.now());
        notificationSocket.setType(NotificationSocketType.ONLINE_USERS);
        List<UserSocketDTO> userSocketDTOS = userSocketService.getSubscribedSocketUsersByDestination("/topic/public");
        userSocketDTOS.removeIf(userSocketDTO -> userSocketDTO.getUsername().equals(username));
        notificationSocket.addToMetadata("USERS", String.join(", ", new Gson().toJson(userSocketDTOS)));
        template.convertAndSendToUser(username
            , "/queue/messages", notificationSocket);
    }

    @EventListener
    public void handleWebSocketConnectListener(SessionConnectedEvent event) {
        Principal principal = event.getUser();
        if (principal != null) {
            String username = principal.getName();
            userSocketService.updateUserSocketConnexionState(username, UserConnexionState.ONLINE);
            handleSession(username, "User «" + username + "» Connected", UserConnexionState.ONLINE);
        }
    }

    @EventListener
    public void handleWebSocketDisconnectListener(SessionDisconnectEvent event) {
        Principal principal = event.getUser();
        if (principal != null) {
            String username = principal.getName();
            userSocketService.removeByUsername(username);
            activeCallSocketService.removeUser(username);
            handleSession(username, "User «" + username + "» Disconnected", UserConnexionState.OFFLINE);
        }
    }

    @EventListener
    public void handleSessionSubscribeEvent(SessionSubscribeEvent event) {
        Principal principal = event.getUser();
        if (principal != null) {
            String user = principal.getName();
            GenericMessage message = (GenericMessage) event.getMessage();
            String simpDestination = (String) message.getHeaders().get("simpDestination");
            String destination = "/user/" + user + "/queue/messages";
            if (simpDestination.startsWith(destination)) {
                onUserSubscribe(principal.getName());
            }
        }
    }
}
