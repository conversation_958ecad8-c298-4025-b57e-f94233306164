package com.dq.lilas.config;

import org.camunda.bpm.engine.IdentityService;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.crypto.password.PasswordEncoder;

import java.util.Collections;

@Configuration
public class CamundaAuthenticationConfiguration {

    private final IdentityService identityService;
    private final PasswordEncoder passwordEncoder;
    private final UserDetailsService userDetailsService;

    public CamundaAuthenticationConfiguration(
        IdentityService identityService, 
        PasswordEncoder passwordEncoder, 
        UserDetailsService userDetailsService) {
        this.identityService = identityService;
        this.passwordEncoder = passwordEncoder;
        this.userDetailsService = userDetailsService;
    }

    @Bean
    public AuthenticationProvider daoAuthenticationProvider() {
        DaoAuthenticationProvider provider = new DaoAuthenticationProvider();
        provider.setUserDetailsService(userDetailsService);
        provider.setPasswordEncoder(passwordEncoder);
        return provider;
    }
    
    @Bean
    public AuthenticationProvider camundaAuthenticationProvider() {
        return new AuthenticationProvider() {
            @Override
            public Authentication authenticate(Authentication authentication) throws AuthenticationException {
                String username = authentication.getName();
                String password = authentication.getCredentials().toString();

                // Only handle Camunda users here - the standard users will be handled by daoAuthenticationProvider
                // Check if user exists in Camunda
                org.camunda.bpm.engine.identity.User camundaUser = identityService.createUserQuery()
                        .userId(username)
                        .singleResult();

                if (camundaUser != null) {
                    // For demo purposes, we'll check the plain text password
                    // In production, you'd want proper password hashing
                    if ("demo".equals(password) && "demo".equals(username) ||
                            "admin".equals(password) && "admin".equals(username)) {
                        return new UsernamePasswordAuthenticationToken(
                                username,
                                password,
                                Collections.singletonList(new SimpleGrantedAuthority("ROLE_USER"))
                        );
                    }
                }

                // Return null to allow the next authentication provider to try
                return null;
            }

            @Override
            public boolean supports(Class<?> authentication) {
                return UsernamePasswordAuthenticationToken.class.isAssignableFrom(authentication);
            }
        };
    }
}
