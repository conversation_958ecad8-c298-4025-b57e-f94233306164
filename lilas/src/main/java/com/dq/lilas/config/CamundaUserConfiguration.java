package com.dq.lilas.config;

import org.camunda.bpm.engine.IdentityService;
import org.camunda.bpm.engine.identity.Group;
import org.camunda.bpm.engine.identity.User;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.CommandLineRunner;
import org.springframework.stereotype.Component;

@Component
public class CamundaUserConfiguration implements CommandLineRunner {

    private static final Logger LOG = LoggerFactory.getLogger(CamundaUserConfiguration.class);
    private final IdentityService identityService;

    public CamundaUserConfiguration(IdentityService identityService) {
        this.identityService = identityService;
    }

    @Override
    public void run(String... args) throws Exception {
        try {
            LOG.info("Starting Camunda user setup...");

            // Create demo user
            User demoUser = identityService.createUserQuery().userId("demo").singleResult();
            if (demoUser == null) {
                LOG.info("Creating demo user...");
                demoUser = identityService.newUser("demo");
                demoUser.setFirstName("Demo");
                demoUser.setLastName("User");
                demoUser.setEmail("demo@localhost");
                demoUser.setPassword("demo");
                identityService.saveUser(demoUser);
                LOG.info("Demo user created successfully");
            } else {
                LOG.info("Demo user already exists");
            }

            // Also create admin user for backup
            User adminUser = identityService.createUserQuery().userId("admin").singleResult();
            if (adminUser == null) {
                LOG.info("Creating admin user...");
                adminUser = identityService.newUser("admin");
                adminUser.setFirstName("Admin");
                adminUser.setLastName("User");
                adminUser.setEmail("admin@localhost");
                adminUser.setPassword("admin");
                identityService.saveUser(adminUser);
                LOG.info("Admin user created successfully");
            } else {
                LOG.info("Admin user already exists");
            }

            // Create camunda-admin group
            Group adminGroup = identityService.createGroupQuery().groupId("camunda-admin").singleResult();
            if (adminGroup == null) {
                LOG.info("Creating camunda-admin group...");
                adminGroup = identityService.newGroup("camunda-admin");
                adminGroup.setName("Camunda Administrators");
                adminGroup.setType("SYSTEM");
                identityService.saveGroup(adminGroup);
                LOG.info("camunda-admin group created successfully");
            } else {
                LOG.info("camunda-admin group already exists");
            }

            // Add users to admin group
            if (identityService.createGroupQuery().groupMember("demo").groupId("camunda-admin").count() == 0) {
                LOG.info("Adding demo user to camunda-admin group...");
                identityService.createMembership("demo", "camunda-admin");
                LOG.info("Demo user added to camunda-admin group successfully");
            }

            if (identityService.createGroupQuery().groupMember("admin").groupId("camunda-admin").count() == 0) {
                LOG.info("Adding admin user to camunda-admin group...");
                identityService.createMembership("admin", "camunda-admin");
                LOG.info("Admin user added to camunda-admin group successfully");
            }

            // List all users for debugging
            LOG.info("All users in system:");
            identityService.createUserQuery().list().forEach(user ->
                    LOG.info("User: {} ({})", user.getId(), user.getFirstName()));

            LOG.info("Camunda user setup completed. Try demo/demo or admin/admin");
        } catch (Exception e) {
            LOG.error("Error setting up Camunda users: {}", e.getMessage(), e);
        }
    }
}
