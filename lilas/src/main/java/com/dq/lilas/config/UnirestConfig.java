package com.dq.lilas.config;

import jakarta.annotation.PreDestroy;
import kong.unirest.Config;
import kong.unirest.Unirest;
import kong.unirest.UnirestInstance;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

@Configuration
public class UnirestConfig {

    @Bean
    public UnirestInstance unirestInstance() {
        // Reset Unirest configuration to avoid "Http Clients are already built" error
        kong.unirest.Unirest.config().reset();

        RequestConfig requestConfig = RequestConfig.custom()
            .setConnectTimeout(10000) // 10 seconds
            .setSocketTimeout(30000) // 30 seconds
            .build();

        CloseableHttpClient httpClient = HttpClients.custom()
            .setDefaultRequestConfig(requestConfig)
            .build();

        Config config = new Config()
            .connectTimeout(10000)
            .socketTimeout(30000);

        // Use deprecated httpClient(HttpClient) for HttpClient 4.x compatibility
        config.httpClient(httpClient);

        return new UnirestInstance(config);
    }

    @PreDestroy
    public void shutdown() {
        try {
            Unirest.shutDown();
        } catch (Exception e) {
            // Log shutdown errors if needed
        }
    }
}
