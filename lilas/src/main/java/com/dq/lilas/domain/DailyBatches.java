package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A DailyBatches.
 */
@Entity
@Table(name = "daily_batches")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class DailyBatches implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "batch_date")
    private Instant batchDate;

    @Column(name = "order_nb")
    private int orderNb;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "units" }, allowSetters = true)
    private Company company;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public DailyBatches id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Instant getBatchDate() {
        return this.batchDate;
    }

    public DailyBatches batchDate(Instant batchDate) {
        this.setBatchDate(batchDate);
        return this;
    }

    public void setBatchDate(Instant batchDate) {
        this.batchDate = batchDate;
    }

    public int getOrderNb() {
        return this.orderNb;
    }

    public DailyBatches orderNb(int orderNb) {
        this.setOrderNb(orderNb);
        return this;
    }

    public void setOrderNb(int orderNb) {
        this.orderNb = orderNb;
    }

    public Company getCompany() {
        return this.company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    public DailyBatches company(Company company) {
        this.setCompany(company);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DailyBatches)) {
            return false;
        }
        return getId() != null && getId().equals(((DailyBatches) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "DailyBatches{" +
            "id=" + getId() +
            ", batchDate='" + getBatchDate() + "'" +
            ", orderNb='" + getOrderNb() + "'" +
            "}";
    }
}
