package com.dq.lilas.domain;

import jakarta.persistence.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A GmsBrands.
 */
@Entity
@Table(name = "gms_brands")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class GmsBrands implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "brand_class")
    private String classe;

    @Column(name = "icon_path")
    private String iconPath;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public GmsBrands id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return this.name;
    }

    public GmsBrands name(String name) {
        this.setName(name);
        return this;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getClasse() {
        return this.classe;
    }

    public GmsBrands classe(String classe) {
        this.setClasse(classe);
        return this;
    }

    public void setClasse(String classe) {
        this.classe = classe;
    }

    public String getIconPath() {
        return this.iconPath;
    }

    public GmsBrands iconPath(String iconPath) {
        this.setIconPath(iconPath);
        return this;
    }

    public void setIconPath(String iconPath) {
        this.iconPath = iconPath;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof GmsBrands)) {
            return false;
        }
        return getId() != null && getId().equals(((GmsBrands) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "GmsBrands{" +
            "id=" + getId() +
            ", name='" + getName() + "'" +
            ", classe='" + getClasse() + "'" +
            ", iconPath='" + getIconPath() + "'" +
            "}";
    }
}
