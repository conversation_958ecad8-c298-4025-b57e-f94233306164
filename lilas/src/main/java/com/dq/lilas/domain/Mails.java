package com.dq.lilas.domain;

import com.dq.lilas.domain.enumeration.MailType;
import jakarta.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Mails.
 */
@Entity
@Table(name = "mails")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Mails implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "maildate")
    private Instant maildate;

    @Column(name = "subject")
    private String subject;

    @Column(name = "mailbody", columnDefinition = "text")
    private String mailbody;

    @Column(name = "recipient")
    private String recipient;

    @Column(name = "sender")
    private String sender;

    @Enumerated(EnumType.STRING)
    @Column(name = "mail_type")
    private MailType mailType;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Mails id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Instant getMaildate() {
        return this.maildate;
    }

    public Mails maildate(Instant maildate) {
        this.setMaildate(maildate);
        return this;
    }

    public void setMaildate(Instant maildate) {
        this.maildate = maildate;
    }

    public String getSubject() {
        return this.subject;
    }

    public Mails subject(String subject) {
        this.setSubject(subject);
        return this;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getMailbody() {
        return this.mailbody;
    }

    public Mails mailbody(String mailbody) {
        this.setMailbody(mailbody);
        return this;
    }

    public void setMailbody(String mailbody) {
        this.mailbody = mailbody;
    }

    public String getRecipient() {
        return this.recipient;
    }

    public Mails recipient(String recipient) {
        this.setRecipient(recipient);
        return this;
    }

    public void setRecipient(String recipient) {
        this.recipient = recipient;
    }

    public String getSender() {
        return this.sender;
    }

    public Mails sender(String sender) {
        this.setSender(sender);
        return this;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public MailType getMailType() {
        return this.mailType;
    }

    public Mails mailType(MailType mailType) {
        this.setMailType(mailType);
        return this;
    }

    public void setMailType(MailType mailType) {
        this.mailType = mailType;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Mails)) {
            return false;
        }
        return getId() != null && getId().equals(((Mails) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Mails{" +
            "id=" + getId() +
            ", maildate='" + getMaildate() + "'" +
            ", subject='" + getSubject() + "'" +
            ", mailbody='" + getMailbody() + "'" +
            ", recipient='" + getRecipient() + "'" +
            ", sender='" + getSender() + "'" +
            ", mailType='" + getMailType() + "'" +
            "}";
    }
}
