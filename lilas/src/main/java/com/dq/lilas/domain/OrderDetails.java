package com.dq.lilas.domain;

import com.dq.lilas.domain.enumeration.OrderDetailsStatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A OrderDetails.
 */
@Entity
@Table(name = "order_details")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class OrderDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "quantity", precision = 21, scale = 2)
    private Long quantity;

    @Column(name = "updated_qty", precision = 21, scale = 2)
    private Long updatedQty;

    @Column(name = "discount")
    private Integer discount;

    @Column(name = "updated_discount")
    private Integer updatedDiscount;

    @Column(name = "unit_price")
    private Double unitPrice;

    @Column(name = "updated_unit_price")
    private Double updatedUnitPrice;

    @Column(name = "order_line_json", columnDefinition = "TEXT")
    private String orderLineJson;

    @Column(name = "availability")
    private Boolean availability;

    @Column(name = "barcode")
    private String barcode;

    @Column(name = "updated_barcode")
    private String updatedBarcode;

    @Column(name = "internal_code")
    private String internalCode;

    @Column(name = "updated_internal_code")
    private String updatedInternalCode;

    // These fields don't exist in the database, so mark them as @Transient
    // to exclude them from SQL generation
    @Transient
    private Boolean discountStatus;

    @Transient
    private Boolean priceStatus;

    @Transient
    private Boolean quantityStatus;

    @Transient
    private Boolean productStatus;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private OrderDetailsStatus status;

    @Column(name = "ref")
    private String ref;

    @Column(name = "product_name")
    private String productName;

    @Column(name = "pack_nb")
    private Integer packNb;

    @Column(name = "pcb")
    private Double pcb;

    @Column(name = "product_unit")
    private String productUnit;

    @Column(name = "tva")
    private Double tva;

    @Column(name = "type_uc")
    private String typeUc;

    @Column(name = "uvc_uc")
    private String uvcUc;

    @Column(name = "unit")
    private String unit;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "orderDetails")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "orderDetails" }, allowSetters = true)
    private Set<EmailsNotifications> emailsNotifications = new HashSet<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "orderDetails", "dailyBatches", "gmsClients", "templateConditions", "mails" }, allowSetters = true)
    private Order order;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public OrderDetails id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getQuantity() {
        return this.quantity;
    }

    public OrderDetails quantity(Long quantity) {
        this.setQuantity(quantity);
        return this;
    }

    public void setQuantity(Long quantity) {
        this.quantity = quantity;
    }

    public Long getUpdatedQty() {
        return this.updatedQty;
    }

    public OrderDetails updatedQty(Long updatedQty) {
        this.setUpdatedQty(updatedQty);
        return this;
    }

    public void setUpdatedQty(Long updatedQty) {
        this.updatedQty = updatedQty;
    }

    public Integer getDiscount() {
        return this.discount;
    }

    public OrderDetails discount(Integer discount) {
        this.setDiscount(discount);
        return this;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }

    public Integer getUpdatedDiscount() {
        return this.updatedDiscount;
    }

    public OrderDetails updatedDiscount(Integer updatedDiscount) {
        this.setUpdatedDiscount(updatedDiscount);
        return this;
    }

    public void setUpdatedDiscount(Integer updatedDiscount) {
        this.updatedDiscount = updatedDiscount;
    }

    public Double getUnitPrice() {
        return this.unitPrice;
    }

    public OrderDetails unitPrice(Double unitPrice) {
        this.setUnitPrice(unitPrice);
        return this;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Double getUpdatedUnitPrice() {
        return this.updatedUnitPrice;
    }

    public OrderDetails updatedUnitPrice(Double updatedUnitPrice) {
        this.setUpdatedUnitPrice(updatedUnitPrice);
        return this;
    }

    public void setUpdatedUnitPrice(Double updatedUnitPrice) {
        this.updatedUnitPrice = updatedUnitPrice;
    }

    public String getOrderLineJson() {
        return this.orderLineJson;
    }

    public OrderDetails orderLineJson(String orderLineJson) {
        this.setOrderLineJson(orderLineJson);
        return this;
    }

    public void setOrderLineJson(String orderLineJson) {
        this.orderLineJson = orderLineJson;
    }

    public Boolean getAvailability() {
        return this.availability;
    }

    public OrderDetails availability(Boolean availability) {
        this.setAvailability(availability);
        return this;
    }

    public void setAvailability(Boolean availability) {
        this.availability = availability;
    }

    public OrderDetailsStatus getStatus() {
        return this.status;
    }

    public OrderDetails status(OrderDetailsStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(OrderDetailsStatus status) {
        this.status = status;
    }

    public String getBarcode() {
        return this.barcode;
    }

    public OrderDetails barcode(String barcode) {
        this.setBarcode(barcode);
        return this;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getUpdatedBarcode() {
        return this.updatedBarcode;
    }

    public OrderDetails updatedBarcode(String updatedBarcode) {
        this.setUpdatedBarcode(updatedBarcode);
        return this;
    }

    public void setUpdatedBarcode(String updatedBarcode) {
        this.updatedBarcode = updatedBarcode;
    }

    public String getInternalCode() {
        return this.internalCode;
    }

    public OrderDetails internalCode(String internalCode) {
        this.setInternalCode(internalCode);
        return this;
    }

    public void setInternalCode(String internalCode) {
        this.internalCode = internalCode;
    }

    public String getUpdatedInternalCode() {
        return this.updatedInternalCode;
    }

    public OrderDetails updatedInternalCode(String updatedInternalCode) {
        this.setUpdatedInternalCode(updatedInternalCode);
        return this;
    }

    public void setUpdatedInternalCode(String updatedInternalCode) {
        this.updatedInternalCode = updatedInternalCode;
    }

    public Boolean getDiscountStatus() {
        return this.discountStatus;
    }

    public OrderDetails discountStatus(Boolean discountStatus) {
        this.setDiscountStatus(discountStatus);
        return this;
    }

    public void setDiscountStatus(Boolean discountStatus) {
        this.discountStatus = discountStatus;
    }

    public Boolean getPriceStatus() {
        return this.priceStatus;
    }

    public OrderDetails priceStatus(Boolean priceStatus) {
        this.setPriceStatus(priceStatus);
        return this;
    }

    public void setPriceStatus(Boolean priceStatus) {
        this.priceStatus = priceStatus;
    }

    public Boolean getQuantityStatus() {
        return this.quantityStatus;
    }

    public OrderDetails quantityStatus(Boolean quantityStatus) {
        this.setQuantityStatus(quantityStatus);
        return this;
    }

    public void setQuantityStatus(Boolean quantityStatus) {
        this.quantityStatus = quantityStatus;
    }

    public Boolean getProductStatus() {
        return this.productStatus;
    }

    public OrderDetails productStatus(Boolean productStatus) {
        this.setProductStatus(productStatus);
        return this;
    }

    public void setProductStatus(Boolean productStatus) {
        this.productStatus = productStatus;
    }

    public String getRef() {
        return ref;
    }

    public void setRef(String ref) {
        this.ref = ref;
    }

    public OrderDetails ref(String ref) {
        this.setRef(ref);
        return this;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public OrderDetails productName(String productName) {
        this.setProductName(productName);
        return this;
    }

    public Integer getPackNb() {
        return packNb;
    }

    public void setPackNb(Integer packNb) {
        this.packNb = packNb;
    }

    public OrderDetails packNb(Integer packNb) {
        this.setPackNb(packNb);
        return this;
    }

    public Double getPcb() {
        return pcb;
    }

    public void setPcb(Double pcb) {
        this.pcb = pcb;
    }

    public OrderDetails pcb(Double pcb) {
        this.setPcb(pcb);
        return this;
    }

    public String getProductUnit() {
        return productUnit;
    }

    public void setProductUnit(String productUnit) {
        this.productUnit = productUnit;
    }

    public OrderDetails productUnit(String productUnit) {
        this.setProductUnit(productUnit);
        return this;
    }

    public Double getTva() {
        return tva;
    }

    public void setTva(Double tva) {
        this.tva = tva;
    }

    public OrderDetails tva(Double tva) {
        this.setTva(tva);
        return this;
    }

    public String getTypeUc() {
        return typeUc;
    }

    public void setTypeUc(String typeUc) {
        this.typeUc = typeUc;
    }

    public OrderDetails typeUc(String typeUc) {
        this.setTypeUc(typeUc);
        return this;
    }

    public String getUvcUc() {
        return uvcUc;
    }

    public void setUvcUc(String uvcUc) {
        this.uvcUc = uvcUc;
    }

    public OrderDetails uvcUc(String uvcUc) {
        this.setUvcUc(uvcUc);
        return this;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public OrderDetails unit(String unit) {
        this.setUnit(unit);
        return this;
    }

    public Set<EmailsNotifications> getEmailsNotifications() {
        return this.emailsNotifications;
    }

    public void setEmailsNotifications(Set<EmailsNotifications> emailsNotifications) {
        if (this.emailsNotifications != null) {
            this.emailsNotifications.forEach(i -> i.setOrderDetails(null));
        }
        if (emailsNotifications != null) {
            emailsNotifications.forEach(i -> i.setOrderDetails(this));
        }
        this.emailsNotifications = emailsNotifications;
    }

    public OrderDetails emailsNotifications(Set<EmailsNotifications> emailsNotifications) {
        this.setEmailsNotifications(emailsNotifications);
        return this;
    }

    public OrderDetails addEmailsNotifications(EmailsNotifications emailsNotifications) {
        this.emailsNotifications.add(emailsNotifications);
        emailsNotifications.setOrderDetails(this);
        return this;
    }

    public OrderDetails removeEmailsNotifications(EmailsNotifications emailsNotifications) {
        this.emailsNotifications.remove(emailsNotifications);
        emailsNotifications.setOrderDetails(null);
        return this;
    }

    public Order getOrder() {
        return this.order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public OrderDetails order(Order order) {
        this.setOrder(order);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof OrderDetails)) {
            return false;
        }
        return getId() != null && getId().equals(((OrderDetails) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "OrderDetails{" +
            "id=" + getId() +
            ", quantity=" + getQuantity() +
            ", updatedQty=" + getUpdatedQty() +
            ", discount=" + getDiscount() +
            ", updatedDiscount=" + getUpdatedDiscount() +
            ", unitPrice=" + getUnitPrice() +
            ", updatedUnitPrice=" + getUpdatedUnitPrice() +
            ", orderLineJson='" + getOrderLineJson() + "'" +
            ", availability='" + getAvailability() + "'" +
            ", status='" + getStatus() + "'" +
            ", barcode='" + getBarcode() + "'" +
            ", updatedBarcode='" + getUpdatedBarcode() + "'" +
            ", internalCode='" + getInternalCode() + "'" +
            ", updatedInternalCode='" + getUpdatedInternalCode() + "'" +
            ", discountStatus='" + getDiscountStatus() + "'" +
            ", priceStatus='" + getPriceStatus() + "'" +
            ", quantityStatus='" + getQuantityStatus() + "'" +
            ", productStatus='" + getProductStatus() + "'" +
            ", ref='" + getRef() + "'" +
            ", productName='" + getProductName() + "'" +
            ", packNb=" + getPackNb() +
            ", pcb=" + getPcb() +
            ", productUnit='" + getProductUnit() + "'" +
            ", tva=" + getTva() +
            ", typeUc='" + getTypeUc() + "'" +
            ", uvcUc='" + getUvcUc() + "'" +
            ", unit='" + getUnit() + "'" +
            "}";
    }
}
