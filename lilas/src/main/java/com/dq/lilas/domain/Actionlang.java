package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Actionlang.
 */
@Entity
@Table(name = "actionlang")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Actionlang implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Size(max = 100)
    @Column(name = "app_type", length = 100)
    private String appType;

    @Size(max = 5)
    @Column(name = "lang", length = 5)
    private String lang;

    @Size(max = 10)
    @Column(name = "abrv", length = 10)
    private String abrv;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "actionlangs" }, allowSetters = true)
    private Action action;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Actionlang id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppType() {
        return this.appType;
    }

    public Actionlang appType(String appType) {
        this.setAppType(appType);
        return this;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getLang() {
        return this.lang;
    }

    public Actionlang lang(String lang) {
        this.setLang(lang);
        return this;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getAbrv() {
        return this.abrv;
    }

    public Actionlang abrv(String abrv) {
        this.setAbrv(abrv);
        return this;
    }

    public void setAbrv(String abrv) {
        this.abrv = abrv;
    }

    public Action getAction() {
        return this.action;
    }

    public void setAction(Action action) {
        this.action = action;
    }

    public Actionlang action(Action action) {
        this.setAction(action);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Actionlang)) {
            return false;
        }
        return getId() != null && getId().equals(((Actionlang) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Actionlang{" +
            "id=" + getId() +
            ", appType='" + getAppType() + "'" +
            ", lang='" + getLang() + "'" +
            ", abrv='" + getAbrv() + "'" +
            "}";
    }
}
