package com.dq.lilas.domain;

import com.dq.lilas.domain.enumeration.PromotionStatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A PromotionDetails.
 */
@Entity
@Table(name = "promotion_details")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PromotionDetails implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "code_produit")
    private String codeProduit;

    @Column(name = "description")
    private String description;

    @Column(name = "price_ht")
    private Double priceHT;

    @Column(name = "remise_fixe")
    private Double remiseFixe;

    @Column(name = "remise_de_pro")
    private Double remiseDePro;

    @Column(name = "gratuit_en_nat")
    private Double gratuitEnNat;

    @Column(name = "appro_manager_du_magasin")
    private String approManagerDuMagasin;

    @Column(name = "appro_manager_gms")
    private String approManagerGMS;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private PromotionStatus status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "demande_promotion_id")
    @JsonIgnoreProperties(value = { "employee", "promotionDetails" }, allowSetters = true)
    private DemandePromotion demandePromotion;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public PromotionDetails id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCodeProduit() {
        return this.codeProduit;
    }

    public PromotionDetails codeProduit(String codeProduit) {
        this.setCodeProduit(codeProduit);
        return this;
    }

    public void setCodeProduit(String codeProduit) {
        this.codeProduit = codeProduit;
    }

    public String getDescription() {
        return this.description;
    }

    public PromotionDetails description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double getPriceHT() {
        return this.priceHT;
    }

    public PromotionDetails priceHT(Double priceHT) {
        this.setPriceHT(priceHT);
        return this;
    }

    public void setPriceHT(Double priceHT) {
        this.priceHT = priceHT;
    }

    public Double getRemiseFixe() {
        return this.remiseFixe;
    }

    public PromotionDetails remiseFixe(Double remiseFixe) {
        this.setRemiseFixe(remiseFixe);
        return this;
    }

    public void setRemiseFixe(Double remiseFixe) {
        this.remiseFixe = remiseFixe;
    }

    public Double getRemiseDePro() {
        return this.remiseDePro;
    }

    public PromotionDetails remiseDePro(Double remiseDePro) {
        this.setRemiseDePro(remiseDePro);
        return this;
    }

    public void setRemiseDePro(Double remiseDePro) {
        this.remiseDePro = remiseDePro;
    }

    public Double getGratuitEnNat() {
        return this.gratuitEnNat;
    }

    public PromotionDetails gratuitEnNat(Double gratuitEnNat) {
        this.setGratuitEnNat(gratuitEnNat);
        return this;
    }

    public void setGratuitEnNat(Double gratuitEnNat) {
        this.gratuitEnNat = gratuitEnNat;
    }

    public String getApproManagerDuMagasin() {
        return this.approManagerDuMagasin;
    }

    public PromotionDetails approManagerDuMagasin(String approManagerDuMagasin) {
        this.setApproManagerDuMagasin(approManagerDuMagasin);
        return this;
    }

    public void setApproManagerDuMagasin(String approManagerDuMagasin) {
        this.approManagerDuMagasin = approManagerDuMagasin;
    }

    public String getApproManagerGMS() {
        return this.approManagerGMS;
    }

    public PromotionDetails approManagerGMS(String approManagerGMS) {
        this.setApproManagerGMS(approManagerGMS);
        return this;
    }

    public void setApproManagerGMS(String approManagerGMS) {
        this.approManagerGMS = approManagerGMS;
    }

    public PromotionStatus getStatus() {
        return this.status;
    }

    public PromotionDetails status(PromotionStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(PromotionStatus status) {
        this.status = status;
    }

    public DemandePromotion getDemandePromotion() {
        return this.demandePromotion;
    }

    public void setDemandePromotion(DemandePromotion demandePromotion) {
        this.demandePromotion = demandePromotion;
    }

    public PromotionDetails demandePromotion(DemandePromotion demandePromotion) {
        this.setDemandePromotion(demandePromotion);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PromotionDetails)) {
            return false;
        }
        return getId() != null && getId().equals(((PromotionDetails) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PromotionDetails{" +
            "id=" + getId() +
            ", codeProduit='" + getCodeProduit() + "'" +
            ", description='" + getDescription() + "'" +
            ", priceHT=" + getPriceHT() +
            ", remiseFixe=" + getRemiseFixe() +
            ", remiseDePro=" + getRemiseDePro() +
            ", gratuitEnNat=" + getGratuitEnNat() +
            ", approManagerDuMagasin='" + getApproManagerDuMagasin() + "'" +
            ", approManagerGMS='" + getApproManagerGMS() + "'" +
            ", status='" + getStatus() + "'" +
            "}";
    }
}
