package com.dq.lilas.domain;

import com.dq.lilas.service.dto.RoomDTO;
import com.dq.lilas.service.dto.UserDTO;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Objects;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * A Room.
 */
@Entity
@Table(name = "room")
public class Room extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    private Long id;

    @Column(name = "name")
    private String name;

    @Column(name = "is_activated")
    private Boolean isActivated;

    @Column(name = "allow_call")
    private Boolean allowCall;

    @Column(name = "allow_image_message")
    private Boolean allowImageMessage;

    @Column(name = "allow_voice_message")
    private Boolean allowVoiceMessage;

    @Column(name = "allow_sticker_message")
    private Boolean allowStickerMessage;

    @ManyToOne
    /*@OnDelete(action = OnDeleteAction.CASCADE)*/
    @JsonIgnoreProperties("")
    private Group group;

    @OneToMany(mappedBy = "room", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<RoomUser> roomUsers = new HashSet<RoomUser>();

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Room name(String name) {
        this.name = name;
        return this;
    }

    public Boolean isIsActivated() {
        return isActivated;
    }

    public Room isActivated(Boolean isActivated) {
        this.isActivated = isActivated;
        return this;
    }

    public void setIsActivated(Boolean isActivated) {
        this.isActivated = isActivated;
    }

    public Boolean getAllowCall() {
        return allowCall;
    }

    public void setAllowCall(Boolean allowCall) {
        this.allowCall = allowCall;
    }

    public Boolean isAllowImageMessage() {
        return allowImageMessage;
    }

    public Room allowImageMessage(Boolean allowImageMessage) {
        this.allowImageMessage = allowImageMessage;
        return this;
    }

    public void setAllowImageMessage(Boolean allowImageMessage) {
        this.allowImageMessage = allowImageMessage;
    }

    public Boolean isAllowVoiceMessage() {
        return allowVoiceMessage;
    }

    public Room allowVoiceMessage(Boolean allowVoiceMessage) {
        this.allowVoiceMessage = allowVoiceMessage;
        return this;
    }

    public void setAllowVoiceMessage(Boolean allowVoiceMessage) {
        this.allowVoiceMessage = allowVoiceMessage;
    }

    public Boolean isAllowStickerMessage() {
        return allowStickerMessage;
    }

    public Room allowStickerMessage(Boolean allowStickerMessage) {
        this.allowStickerMessage = allowStickerMessage;
        return this;
    }

    public void setAllowStickerMessage(Boolean allowStickerMessage) {
        this.allowStickerMessage = allowStickerMessage;
    }

    public Group getGroup() {
        return group;
    }

    public void setGroup(Group group) {
        this.group = group;
    }

    public Room group(Group group) {
        this.group = group;
        return this;
    }

    public Set<RoomUser> getRoomUsers() {
        return roomUsers;
    }

    public void setRoomUsers(Set<RoomUser> roomUsers) {
        this.roomUsers.retainAll(roomUsers);
        this.roomUsers.addAll(roomUsers);
    }

    public void addRoomUser(RoomUser roomUser) {
        roomUsers.add(roomUser);
    }

    public void removeRoomUser(RoomUser roomUser) {
        for (Iterator<RoomUser> iterator = roomUsers.iterator();
             iterator.hasNext(); ) {
            RoomUser roomUserIt = iterator.next();
            if (roomUserIt.getRoom().equals(roomUser.getRoom()) &&
                roomUserIt.getUser().equals(roomUser.getUser())) {
                iterator.remove();
                roomUserIt.setUser(null);
                roomUserIt.setRoom(null);
            }
        }
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Room room = (Room) o;
        if (room.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), room.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "Room{" +
            "id=" + getId() +
            ", name='" + getName() + "'" +
            ", isActivated='" + isIsActivated() + "'" +
            ", allowCall='" + getAllowCall() + "'" +
            ", allowImageMessage='" + isAllowImageMessage() + "'" +
            ", allowVoiceMessage='" + isAllowVoiceMessage() + "'" +
            ", allowStickerMessage='" + isAllowStickerMessage() + "'" +
            "}";
    }

    public RoomDTO toDTO() {
        RoomDTO roomDTO = new RoomDTO();
        roomDTO.setGroupId(this.getId());
        roomDTO.setName(this.getName());
        roomDTO.setId(this.getId());
        roomDTO.setIsActivated(this.isIsActivated());
        roomDTO.setAllowCall(this.getAllowCall());
        Set<RoomUser> roomUsers = this.getRoomUsers();
        Set<UserDTO> users = roomUsers.stream()
            .map(RoomUser::getUser)
            .map(u -> {
                UserDTO userDTO = new UserDTO();
                userDTO.setId(u.getId());
                userDTO.setFirstName(u.getFirstName());
                userDTO.setLastName(u.getLastName());
                userDTO.setLogin(u.getLogin());
                return userDTO;
            })
            .collect(Collectors.toSet());
        roomDTO.setUsers(users);
        return roomDTO;
    }
}
