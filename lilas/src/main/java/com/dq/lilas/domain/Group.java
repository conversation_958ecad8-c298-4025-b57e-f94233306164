package com.dq.lilas.domain;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * A Group.
 */
@Entity
@Table(name = "jhi_group")
public class Group extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    private Long id;

    @Column(unique = true, nullable = false, name = "name")
    private String name;

    @Column(name = "is_activated")
    private Boolean isActivated;

    /*@OneToMany(orphanRemoval = true, cascade = CascadeType.PERSIST,mappedBy="group")
    private Set<Room> rooms;*/

    @ManyToMany(/*cascade = {CascadeType.DETACH, CascadeType.REFRESH}*/)
    @JoinTable(name = "group_users",
            joinColumns = @JoinColumn(name = "groups_id", referencedColumnName = "id"),
            inverseJoinColumns = @JoinColumn(name = "users_id", referencedColumnName = "id"))
    private Set<User> users = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public Group name(String name) {
        this.name = name;
        return this;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean isIsActivated() {
        return isActivated;
    }

    public Group isActivated(Boolean isActivated) {
        this.isActivated = isActivated;
        return this;
    }

    public void setIsActivated(Boolean isActivated) {
        this.isActivated = isActivated;
    }

    public Set<User> getUsers() {
        return users;
    }

    public Group users(Set<User> users) {
        this.users.clear();
        if(users != null) {
            this.users.addAll(users);
        }
        return this;
    }

    public void setUsers(Set<User> users) {
        this.users.clear();
        if(users != null) {
            this.users.addAll(users);
        }
    }

    public void addUser(User user) {
        this.users.add(user);
    }

    public void removeUser(Long userId) {
        this.users.removeIf(user -> user.getId().equals(userId));
    }

    /*public Set<Room> getRooms() {
        return rooms;
    }

    public void setRooms(Set<Room> rooms) {
        this.rooms.clear();
        if(rooms != null) {
            this.rooms.addAll(rooms);
        }
        this.rooms = rooms;
    }*/

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Group group = (Group) o;
        if (group.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), group.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "Group{" +
                "id=" + getId() +
                ", name='" + getName() + "'" +
                ", isActivated='" + isIsActivated() + "'" +
                "}";
    }
}
