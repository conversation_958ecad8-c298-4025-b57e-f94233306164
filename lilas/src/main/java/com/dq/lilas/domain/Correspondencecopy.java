package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Correspondencecopy.
 */
@Entity
@Table(name = "correspondencecopy")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Correspondencecopy implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Size(max = 15)
    @Column(name = "numcopy", length = 15)
    private String numcopy;

    @Size(max = 4000)
    @Column(name = "comments", length = 4000)
    private String comments;

    @Column(name = "datejccreate")
    private Instant datejccreate;

    @Size(max = 10)
    @Column(name = "datehjrcreate", length = 10)
    private String datehjrcreate;

    @Size(max = 1)
    @Column(name = "typereceive", length = 1)
    private String typereceive;

    @Size(max = 15)
    @Column(name = "typecopy", length = 15)
    private String typecopy;

    @Size(max = 15)
    @Column(name = "userreceive", length = 15)
    private String userreceive;

    @Column(name = "datejcreceive")
    private Instant datejcreceive;

    @Size(max = 10)
    @Column(name = "datehjrreceive", length = 10)
    private String datehjrreceive;

    @Column(name = "datejcaction")
    private Instant datejcaction;

    @Size(max = 10)
    @Column(name = "datehjraction", length = 10)
    private String datehjraction;

    @Size(max = 15)
    @Column(name = "actiontype", length = 15)
    private String actiontype;

    @Size(max = 5)
    @Column(name = "savecorrespcpy", length = 5)
    private String savecorrespcpy;

    @Size(max = 50)
    @Column(name = "ordernbr", length = 50)
    private String ordernbr;

    @Size(max = 200)
    @Column(name = "pagenbr", length = 200)
    private String pagenbr;

    @Size(max = 200)
    @Column(name = "expno", length = 200)
    private String expno;

    @Size(max = 15)
    @Column(name = "expyear", length = 15)
    private String expyear;

    @Size(max = 15)
    @Column(name = "docyear", length = 15)
    private String docyear;

    @Size(max = 5)
    @Column(name = "categorycorresp", length = 5)
    private String categorycorresp;

    @Size(max = 20)
    @Column(name = "heureaction", length = 20)
    private String heureaction;

    @Size(max = 10)
    @Column(name = "statusdeniedcopy", length = 10)
    private String statusdeniedcopy;

    @Size(max = 1000)
    @Column(name = "pagenbrpaper", length = 1000)
    private String pagenbrpaper;

    @Size(max = 20)
    @Column(name = "taskscan", length = 20)
    private String taskscan;

    @Size(max = 1000)
    @Column(name = "pathtransto", length = 1000)
    private String pathtransto;

    @Size(max = 1000)
    @Column(name = "pathtranscc", length = 1000)
    private String pathtranscc;

    @Size(max = 1000)
    @Column(name = "pathtranstolib", length = 1000)
    private String pathtranstolib;

    @Size(max = 1000)
    @Column(name = "pathtranscclib", length = 1000)
    private String pathtranscclib;

    @Size(max = 20)
    @Column(name = "flggroup", length = 20)
    private String flggroup;

    @Column(name = "datejcdelete")
    private Instant datejcdelete;

    @Column(name = "datejcrevoke")
    private Instant datejcrevoke;

    @Size(max = 10)
    @Column(name = "datehjrrevoke", length = 10)
    private String datehjrrevoke;

    @Column(name = "dateremove")
    private Instant dateremove;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "employee", "unit", "deliverymode", "typecorrespondence" }, allowSetters = true)
    private Correspondence correspondence;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee employee;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "unitlangs", "employees", "company" }, allowSetters = true)
    private Unit unit;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "actionlangs" }, allowSetters = true)
    private Action action;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee useraction;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee userrevoke;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee userremove;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Correspondencecopy id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNumcopy() {
        return this.numcopy;
    }

    public Correspondencecopy numcopy(String numcopy) {
        this.setNumcopy(numcopy);
        return this;
    }

    public void setNumcopy(String numcopy) {
        this.numcopy = numcopy;
    }

    public String getComments() {
        return this.comments;
    }

    public Correspondencecopy comments(String comments) {
        this.setComments(comments);
        return this;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Instant getDatejccreate() {
        return this.datejccreate;
    }

    public Correspondencecopy datejccreate(Instant datejccreate) {
        this.setDatejccreate(datejccreate);
        return this;
    }

    public void setDatejccreate(Instant datejccreate) {
        this.datejccreate = datejccreate;
    }

    public String getDatehjrcreate() {
        return this.datehjrcreate;
    }

    public Correspondencecopy datehjrcreate(String datehjrcreate) {
        this.setDatehjrcreate(datehjrcreate);
        return this;
    }

    public void setDatehjrcreate(String datehjrcreate) {
        this.datehjrcreate = datehjrcreate;
    }

    public String getTypereceive() {
        return this.typereceive;
    }

    public Correspondencecopy typereceive(String typereceive) {
        this.setTypereceive(typereceive);
        return this;
    }

    public void setTypereceive(String typereceive) {
        this.typereceive = typereceive;
    }

    public String getTypecopy() {
        return this.typecopy;
    }

    public Correspondencecopy typecopy(String typecopy) {
        this.setTypecopy(typecopy);
        return this;
    }

    public void setTypecopy(String typecopy) {
        this.typecopy = typecopy;
    }

    public String getUserreceive() {
        return this.userreceive;
    }

    public Correspondencecopy userreceive(String userreceive) {
        this.setUserreceive(userreceive);
        return this;
    }

    public void setUserreceive(String userreceive) {
        this.userreceive = userreceive;
    }

    public Instant getDatejcreceive() {
        return this.datejcreceive;
    }

    public Correspondencecopy datejcreceive(Instant datejcreceive) {
        this.setDatejcreceive(datejcreceive);
        return this;
    }

    public void setDatejcreceive(Instant datejcreceive) {
        this.datejcreceive = datejcreceive;
    }

    public String getDatehjrreceive() {
        return this.datehjrreceive;
    }

    public Correspondencecopy datehjrreceive(String datehjrreceive) {
        this.setDatehjrreceive(datehjrreceive);
        return this;
    }

    public void setDatehjrreceive(String datehjrreceive) {
        this.datehjrreceive = datehjrreceive;
    }

    public Instant getDatejcaction() {
        return this.datejcaction;
    }

    public Correspondencecopy datejcaction(Instant datejcaction) {
        this.setDatejcaction(datejcaction);
        return this;
    }

    public void setDatejcaction(Instant datejcaction) {
        this.datejcaction = datejcaction;
    }

    public String getDatehjraction() {
        return this.datehjraction;
    }

    public Correspondencecopy datehjraction(String datehjraction) {
        this.setDatehjraction(datehjraction);
        return this;
    }

    public void setDatehjraction(String datehjraction) {
        this.datehjraction = datehjraction;
    }

    public String getActiontype() {
        return this.actiontype;
    }

    public Correspondencecopy actiontype(String actiontype) {
        this.setActiontype(actiontype);
        return this;
    }

    public void setActiontype(String actiontype) {
        this.actiontype = actiontype;
    }

    public String getSavecorrespcpy() {
        return this.savecorrespcpy;
    }

    public Correspondencecopy savecorrespcpy(String savecorrespcpy) {
        this.setSavecorrespcpy(savecorrespcpy);
        return this;
    }

    public void setSavecorrespcpy(String savecorrespcpy) {
        this.savecorrespcpy = savecorrespcpy;
    }

    public String getOrdernbr() {
        return this.ordernbr;
    }

    public Correspondencecopy ordernbr(String ordernbr) {
        this.setOrdernbr(ordernbr);
        return this;
    }

    public void setOrdernbr(String ordernbr) {
        this.ordernbr = ordernbr;
    }

    public String getPagenbr() {
        return this.pagenbr;
    }

    public Correspondencecopy pagenbr(String pagenbr) {
        this.setPagenbr(pagenbr);
        return this;
    }

    public void setPagenbr(String pagenbr) {
        this.pagenbr = pagenbr;
    }

    public String getExpno() {
        return this.expno;
    }

    public Correspondencecopy expno(String expno) {
        this.setExpno(expno);
        return this;
    }

    public void setExpno(String expno) {
        this.expno = expno;
    }

    public String getExpyear() {
        return this.expyear;
    }

    public Correspondencecopy expyear(String expyear) {
        this.setExpyear(expyear);
        return this;
    }

    public void setExpyear(String expyear) {
        this.expyear = expyear;
    }

    public String getDocyear() {
        return this.docyear;
    }

    public Correspondencecopy docyear(String docyear) {
        this.setDocyear(docyear);
        return this;
    }

    public void setDocyear(String docyear) {
        this.docyear = docyear;
    }

    public String getCategorycorresp() {
        return this.categorycorresp;
    }

    public Correspondencecopy categorycorresp(String categorycorresp) {
        this.setCategorycorresp(categorycorresp);
        return this;
    }

    public void setCategorycorresp(String categorycorresp) {
        this.categorycorresp = categorycorresp;
    }

    public String getHeureaction() {
        return this.heureaction;
    }

    public Correspondencecopy heureaction(String heureaction) {
        this.setHeureaction(heureaction);
        return this;
    }

    public void setHeureaction(String heureaction) {
        this.heureaction = heureaction;
    }

    public String getStatusdeniedcopy() {
        return this.statusdeniedcopy;
    }

    public Correspondencecopy statusdeniedcopy(String statusdeniedcopy) {
        this.setStatusdeniedcopy(statusdeniedcopy);
        return this;
    }

    public void setStatusdeniedcopy(String statusdeniedcopy) {
        this.statusdeniedcopy = statusdeniedcopy;
    }

    public String getPagenbrpaper() {
        return this.pagenbrpaper;
    }

    public Correspondencecopy pagenbrpaper(String pagenbrpaper) {
        this.setPagenbrpaper(pagenbrpaper);
        return this;
    }

    public void setPagenbrpaper(String pagenbrpaper) {
        this.pagenbrpaper = pagenbrpaper;
    }

    public String getTaskscan() {
        return this.taskscan;
    }

    public Correspondencecopy taskscan(String taskscan) {
        this.setTaskscan(taskscan);
        return this;
    }

    public void setTaskscan(String taskscan) {
        this.taskscan = taskscan;
    }

    public String getPathtransto() {
        return this.pathtransto;
    }

    public Correspondencecopy pathtransto(String pathtransto) {
        this.setPathtransto(pathtransto);
        return this;
    }

    public void setPathtransto(String pathtransto) {
        this.pathtransto = pathtransto;
    }

    public String getPathtranscc() {
        return this.pathtranscc;
    }

    public Correspondencecopy pathtranscc(String pathtranscc) {
        this.setPathtranscc(pathtranscc);
        return this;
    }

    public void setPathtranscc(String pathtranscc) {
        this.pathtranscc = pathtranscc;
    }

    public String getPathtranstolib() {
        return this.pathtranstolib;
    }

    public Correspondencecopy pathtranstolib(String pathtranstolib) {
        this.setPathtranstolib(pathtranstolib);
        return this;
    }

    public void setPathtranstolib(String pathtranstolib) {
        this.pathtranstolib = pathtranstolib;
    }

    public String getPathtranscclib() {
        return this.pathtranscclib;
    }

    public Correspondencecopy pathtranscclib(String pathtranscclib) {
        this.setPathtranscclib(pathtranscclib);
        return this;
    }

    public void setPathtranscclib(String pathtranscclib) {
        this.pathtranscclib = pathtranscclib;
    }

    public String getFlggroup() {
        return this.flggroup;
    }

    public Correspondencecopy flggroup(String flggroup) {
        this.setFlggroup(flggroup);
        return this;
    }

    public void setFlggroup(String flggroup) {
        this.flggroup = flggroup;
    }

    public Instant getDatejcdelete() {
        return this.datejcdelete;
    }

    public Correspondencecopy datejcdelete(Instant datejcdelete) {
        this.setDatejcdelete(datejcdelete);
        return this;
    }

    public void setDatejcdelete(Instant datejcdelete) {
        this.datejcdelete = datejcdelete;
    }

    public Instant getDatejcrevoke() {
        return this.datejcrevoke;
    }

    public Correspondencecopy datejcrevoke(Instant datejcrevoke) {
        this.setDatejcrevoke(datejcrevoke);
        return this;
    }

    public void setDatejcrevoke(Instant datejcrevoke) {
        this.datejcrevoke = datejcrevoke;
    }

    public String getDatehjrrevoke() {
        return this.datehjrrevoke;
    }

    public Correspondencecopy datehjrrevoke(String datehjrrevoke) {
        this.setDatehjrrevoke(datehjrrevoke);
        return this;
    }

    public void setDatehjrrevoke(String datehjrrevoke) {
        this.datehjrrevoke = datehjrrevoke;
    }

    public Instant getDateremove() {
        return this.dateremove;
    }

    public Correspondencecopy dateremove(Instant dateremove) {
        this.setDateremove(dateremove);
        return this;
    }

    public void setDateremove(Instant dateremove) {
        this.dateremove = dateremove;
    }

    public Correspondence getCorrespondence() {
        return this.correspondence;
    }

    public void setCorrespondence(Correspondence correspondence) {
        this.correspondence = correspondence;
    }

    public Correspondencecopy correspondence(Correspondence correspondence) {
        this.setCorrespondence(correspondence);
        return this;
    }

    public Employee getEmployee() {
        return this.employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }

    public Correspondencecopy employee(Employee employee) {
        this.setEmployee(employee);
        return this;
    }

    public Unit getUnit() {
        return this.unit;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }

    public Correspondencecopy unit(Unit unit) {
        this.setUnit(unit);
        return this;
    }

    public Action getAction() {
        return this.action;
    }

    public void setAction(Action action) {
        this.action = action;
    }

    public Correspondencecopy action(Action action) {
        this.setAction(action);
        return this;
    }

    public Employee getUseraction() {
        return this.useraction;
    }

    public void setUseraction(Employee employee) {
        this.useraction = employee;
    }

    public Correspondencecopy useraction(Employee employee) {
        this.setUseraction(employee);
        return this;
    }

    public Employee getUserrevoke() {
        return this.userrevoke;
    }

    public void setUserrevoke(Employee employee) {
        this.userrevoke = employee;
    }

    public Correspondencecopy userrevoke(Employee employee) {
        this.setUserrevoke(employee);
        return this;
    }

    public Employee getUserremove() {
        return this.userremove;
    }

    public void setUserremove(Employee employee) {
        this.userremove = employee;
    }

    public Correspondencecopy userremove(Employee employee) {
        this.setUserremove(employee);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Correspondencecopy)) {
            return false;
        }
        return getId() != null && getId().equals(((Correspondencecopy) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Correspondencecopy{" +
            "id=" + getId() +
            ", numcopy='" + getNumcopy() + "'" +
            ", comments='" + getComments() + "'" +
            ", datejccreate='" + getDatejccreate() + "'" +
            ", datehjrcreate='" + getDatehjrcreate() + "'" +
            ", typereceive='" + getTypereceive() + "'" +
            ", typecopy='" + getTypecopy() + "'" +
            ", userreceive='" + getUserreceive() + "'" +
            ", datejcreceive='" + getDatejcreceive() + "'" +
            ", datehjrreceive='" + getDatehjrreceive() + "'" +
            ", datejcaction='" + getDatejcaction() + "'" +
            ", datehjraction='" + getDatehjraction() + "'" +
            ", actiontype='" + getActiontype() + "'" +
            ", savecorrespcpy='" + getSavecorrespcpy() + "'" +
            ", ordernbr='" + getOrdernbr() + "'" +
            ", pagenbr='" + getPagenbr() + "'" +
            ", expno='" + getExpno() + "'" +
            ", expyear='" + getExpyear() + "'" +
            ", docyear='" + getDocyear() + "'" +
            ", categorycorresp='" + getCategorycorresp() + "'" +
            ", heureaction='" + getHeureaction() + "'" +
            ", statusdeniedcopy='" + getStatusdeniedcopy() + "'" +
            ", pagenbrpaper='" + getPagenbrpaper() + "'" +
            ", taskscan='" + getTaskscan() + "'" +
            ", pathtransto='" + getPathtransto() + "'" +
            ", pathtranscc='" + getPathtranscc() + "'" +
            ", pathtranstolib='" + getPathtranstolib() + "'" +
            ", pathtranscclib='" + getPathtranscclib() + "'" +
            ", flggroup='" + getFlggroup() + "'" +
            ", datejcdelete='" + getDatejcdelete() + "'" +
            ", datejcrevoke='" + getDatejcrevoke() + "'" +
            ", datehjrrevoke='" + getDatehjrrevoke() + "'" +
            ", dateremove='" + getDateremove() + "'" +
            "}";
    }
}
