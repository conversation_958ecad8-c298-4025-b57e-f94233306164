package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Deliverymode.
 */
@Entity
@Table(name = "deliverymode")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Deliverymode implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Size(max = 1)
    @Column(name = "statut", length = 1)
    private String statut;

    @Size(max = 5)
    @Column(name = "orderpos", length = 5)
    private String orderpos;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "deliverymode")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "deliverymode" }, allowSetters = true)
    private Set<Deliverymodelang> deleverymodelangs = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Deliverymode id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatut() {
        return this.statut;
    }

    public Deliverymode statut(String statut) {
        this.setStatut(statut);
        return this;
    }

    public void setStatut(String statut) {
        this.statut = statut;
    }

    public String getOrderpos() {
        return this.orderpos;
    }

    public Deliverymode orderpos(String orderpos) {
        this.setOrderpos(orderpos);
        return this;
    }

    public void setOrderpos(String orderpos) {
        this.orderpos = orderpos;
    }

    public Set<Deliverymodelang> getDeleverymodelangs() {
        return this.deleverymodelangs;
    }

    public void setDeleverymodelangs(Set<Deliverymodelang> deliverymodelangs) {
        if (this.deleverymodelangs != null) {
            this.deleverymodelangs.forEach(i -> i.setDeliverymode(null));
        }
        if (deliverymodelangs != null) {
            deliverymodelangs.forEach(i -> i.setDeliverymode(this));
        }
        this.deleverymodelangs = deliverymodelangs;
    }

    public Deliverymode deleverymodelangs(Set<Deliverymodelang> deliverymodelangs) {
        this.setDeleverymodelangs(deliverymodelangs);
        return this;
    }

    public Deliverymode addDeleverymodelang(Deliverymodelang deliverymodelang) {
        this.deleverymodelangs.add(deliverymodelang);
        deliverymodelang.setDeliverymode(this);
        return this;
    }

    public Deliverymode removeDeleverymodelang(Deliverymodelang deliverymodelang) {
        this.deleverymodelangs.remove(deliverymodelang);
        deliverymodelang.setDeliverymode(null);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Deliverymode)) {
            return false;
        }
        return getId() != null && getId().equals(((Deliverymode) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Deliverymode{" +
            "id=" + getId() +
            ", statut='" + getStatut() + "'" +
            ", orderpos='" + getOrderpos() + "'" +
            "}";
    }
}
