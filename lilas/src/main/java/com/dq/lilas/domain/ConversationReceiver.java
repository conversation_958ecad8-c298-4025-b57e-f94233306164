package com.dq.lilas.domain;

import com.dq.lilas.domain.enumeration.ConversationState;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "conversation_receivers")
public class ConversationReceiver implements Serializable {

    @EmbeddedId
    private ConversationReceiverPK id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @MapsId("conversationId") //This is the name of attr in ConversationReceiverPK class
    @JoinColumn(name = "conversation_id")
    private Conversation conversation;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @MapsId("receiverId")
    @JoinColumn(name = "receiver_id")
    private User receiver;

    @Enumerated(EnumType.STRING)
    @Column(name = "conversation_state")
    private ConversationState conversationState;

    public ConversationReceiver() {
    }

    public ConversationReceiver(Conversation conversation, User receiver, ConversationState conversationState) {
        this.conversation = conversation;
        this.receiver = receiver;
        this.conversationState = conversationState;
        this.id = new ConversationReceiverPK(conversation.getId(), receiver.getId());
    }

    public ConversationReceiverPK getId() {
        return id;
    }

    public void setId(ConversationReceiverPK id) {
        this.id = id;
    }

    public Conversation getConversation() {
        return conversation;
    }

    public void setConversation(Conversation conversation) {
        this.conversation = conversation;
    }

    public User getReceiver() {
        return receiver;
    }

    public void setReceiver(User receiver) {
        this.receiver = receiver;
    }

    public ConversationState getConversationState() {
        return this.conversationState;
    }

    public void setConversationState(ConversationState conversationState) {
        this.conversationState = conversationState;
    }
}
