package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Attachement.
 */
@Entity
@Table(name = "attachement")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Attachement implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Size(max = 30)
    @Column(name = "copy_id", length = 30)
    private String copyId;

    @Size(max = 150)
    @Column(name = "lbl_attachment", length = 150)
    private String lblAttachment;

    @Size(max = 50)
    @Column(name = "id_doc_attachment", length = 50)
    private String idDocAttachment;

    @Size(max = 15)
    @Column(name = "size_attachement", length = 15)
    private String sizeAttachement;

    @Size(max = 500)
    @Column(name = "filenameattachment", length = 500)
    private String filenameattachment;

    @Size(max = 20)
    @Column(name = "userattachment", length = 20)
    private String userattachment;

    @Size(max = 20)
    @Column(name = "iddecision", length = 20)
    private String iddecision;

    @Size(max = 20)
    @Column(name = "idtemplate", length = 20)
    private String idtemplate;

    @Column(name = "datejcattachment")
    private Instant datejcattachment;

    @Column(name = "datehjrattachment")
    private Instant datehjrattachment;

    @Size(max = 20)
    @Column(name = "idtransfer", length = 20)
    private String idtransfer;

    @Size(max = 20)
    @Column(name = "idcorresp", length = 20)
    private String idcorresp;

    @Column(name = "ordering")
    private Double ordering;

    @Size(max = 10)
    @Column(name = "levelattachement", length = 10)
    private String levelattachement;

    @Size(max = 20)
    @Column(name = "idreq", length = 20)
    private String idreq;

    @Column(name = "orderingscan")
    private Double orderingscan;

    @Size(max = 20)
    @Column(name = "iddocext", length = 20)
    private String iddocext;

    @Size(max = 20)
    @Column(name = "idleave", length = 20)
    private String idleave;

    @Size(max = 15)
    @Column(name = "config_level", length = 15)
    private String configLevel;

    @Size(max = 15)
    @Column(name = "type_atach", length = 15)
    private String typeAtach;

    @Size(max = 15)
    @Column(name = "id_direct_order", length = 15)
    private String idDirectOrder;

    @Size(max = 100)
    @Column(name = "path_file", length = 100)
    private String pathFile;

    @Column(name = "version")
    private Integer version;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "orderDetails", "dailyBatches", "gmsClients", "templateConditions", "mails" }, allowSetters = true)
    private Order order;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Attachement id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCopyId() {
        return this.copyId;
    }

    public Attachement copyId(String copyId) {
        this.setCopyId(copyId);
        return this;
    }

    public void setCopyId(String copyId) {
        this.copyId = copyId;
    }

    public String getLblAttachment() {
        return this.lblAttachment;
    }

    public Attachement lblAttachment(String lblAttachment) {
        this.setLblAttachment(lblAttachment);
        return this;
    }

    public void setLblAttachment(String lblAttachment) {
        this.lblAttachment = lblAttachment;
    }

    public String getIdDocAttachment() {
        return this.idDocAttachment;
    }

    public Attachement idDocAttachment(String idDocAttachment) {
        this.setIdDocAttachment(idDocAttachment);
        return this;
    }

    public void setIdDocAttachment(String idDocAttachment) {
        this.idDocAttachment = idDocAttachment;
    }

    public String getSizeAttachement() {
        return this.sizeAttachement;
    }

    public Attachement sizeAttachement(String sizeAttachement) {
        this.setSizeAttachement(sizeAttachement);
        return this;
    }

    public void setSizeAttachement(String sizeAttachement) {
        this.sizeAttachement = sizeAttachement;
    }

    public String getFilenameattachment() {
        return this.filenameattachment;
    }

    public Attachement filenameattachment(String filenameattachment) {
        this.setFilenameattachment(filenameattachment);
        return this;
    }

    public void setFilenameattachment(String filenameattachment) {
        this.filenameattachment = filenameattachment;
    }

    public String getUserattachment() {
        return this.userattachment;
    }

    public Attachement userattachment(String userattachment) {
        this.setUserattachment(userattachment);
        return this;
    }

    public void setUserattachment(String userattachment) {
        this.userattachment = userattachment;
    }

    public String getIddecision() {
        return this.iddecision;
    }

    public Attachement iddecision(String iddecision) {
        this.setIddecision(iddecision);
        return this;
    }

    public void setIddecision(String iddecision) {
        this.iddecision = iddecision;
    }

    public String getIdtemplate() {
        return this.idtemplate;
    }

    public Attachement idtemplate(String idtemplate) {
        this.setIdtemplate(idtemplate);
        return this;
    }

    public void setIdtemplate(String idtemplate) {
        this.idtemplate = idtemplate;
    }

    public Instant getDatejcattachment() {
        return this.datejcattachment;
    }

    public Attachement datejcattachment(Instant datejcattachment) {
        this.setDatejcattachment(datejcattachment);
        return this;
    }

    public void setDatejcattachment(Instant datejcattachment) {
        this.datejcattachment = datejcattachment;
    }

    public Instant getDatehjrattachment() {
        return this.datehjrattachment;
    }

    public Attachement datehjrattachment(Instant datehjrattachment) {
        this.setDatehjrattachment(datehjrattachment);
        return this;
    }

    public void setDatehjrattachment(Instant datehjrattachment) {
        this.datehjrattachment = datehjrattachment;
    }

    public String getIdtransfer() {
        return this.idtransfer;
    }

    public Attachement idtransfer(String idtransfer) {
        this.setIdtransfer(idtransfer);
        return this;
    }

    public void setIdtransfer(String idtransfer) {
        this.idtransfer = idtransfer;
    }

    public String getIdcorresp() {
        return this.idcorresp;
    }

    public Attachement idcorresp(String idcorresp) {
        this.setIdcorresp(idcorresp);
        return this;
    }

    public void setIdcorresp(String idcorresp) {
        this.idcorresp = idcorresp;
    }

    public Double getOrdering() {
        return this.ordering;
    }

    public Attachement ordering(Double ordering) {
        this.setOrdering(ordering);
        return this;
    }

    public void setOrdering(Double ordering) {
        this.ordering = ordering;
    }

    public String getLevelattachement() {
        return this.levelattachement;
    }

    public Attachement levelattachement(String levelattachement) {
        this.setLevelattachement(levelattachement);
        return this;
    }

    public void setLevelattachement(String levelattachement) {
        this.levelattachement = levelattachement;
    }

    public String getIdreq() {
        return this.idreq;
    }

    public Attachement idreq(String idreq) {
        this.setIdreq(idreq);
        return this;
    }

    public void setIdreq(String idreq) {
        this.idreq = idreq;
    }

    public Double getOrderingscan() {
        return this.orderingscan;
    }

    public Attachement orderingscan(Double orderingscan) {
        this.setOrderingscan(orderingscan);
        return this;
    }

    public void setOrderingscan(Double orderingscan) {
        this.orderingscan = orderingscan;
    }

    public String getIddocext() {
        return this.iddocext;
    }

    public Attachement iddocext(String iddocext) {
        this.setIddocext(iddocext);
        return this;
    }

    public void setIddocext(String iddocext) {
        this.iddocext = iddocext;
    }

    public String getIdleave() {
        return this.idleave;
    }

    public Attachement idleave(String idleave) {
        this.setIdleave(idleave);
        return this;
    }

    public void setIdleave(String idleave) {
        this.idleave = idleave;
    }

    public String getConfigLevel() {
        return this.configLevel;
    }

    public Attachement configLevel(String configLevel) {
        this.setConfigLevel(configLevel);
        return this;
    }

    public void setConfigLevel(String configLevel) {
        this.configLevel = configLevel;
    }

    public String getTypeAtach() {
        return this.typeAtach;
    }

    public Attachement typeAtach(String typeAtach) {
        this.setTypeAtach(typeAtach);
        return this;
    }

    public void setTypeAtach(String typeAtach) {
        this.typeAtach = typeAtach;
    }

    public String getIdDirectOrder() {
        return this.idDirectOrder;
    }

    public Attachement idDirectOrder(String idDirectOrder) {
        this.setIdDirectOrder(idDirectOrder);
        return this;
    }

    public void setIdDirectOrder(String idDirectOrder) {
        this.idDirectOrder = idDirectOrder;
    }

    public String getPathFile() {
        return this.pathFile;
    }

    public Attachement pathFile(String pathFile) {
        this.setPathFile(pathFile);
        return this;
    }

    public void setPathFile(String pathFile) {
        this.pathFile = pathFile;
    }

    public Integer getVersion() {
        return this.version;
    }

    public Attachement version(Integer version) {
        this.setVersion(version);
        return this;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public Order getOrder() {
        return this.order;
    }

    public void setOrder(Order order) {
        this.order = order;
    }

    public Attachement order(Order order) {
        this.setOrder(order);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Attachement)) {
            return false;
        }
        return getId() != null && getId().equals(((Attachement) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Attachement{" +
            "id=" + getId() +
            ", copyId='" + getCopyId() + "'" +
            ", lblAttachment='" + getLblAttachment() + "'" +
            ", idDocAttachment='" + getIdDocAttachment() + "'" +
            ", sizeAttachement='" + getSizeAttachement() + "'" +
            ", filenameattachment='" + getFilenameattachment() + "'" +
            ", userattachment='" + getUserattachment() + "'" +
            ", iddecision='" + getIddecision() + "'" +
            ", idtemplate='" + getIdtemplate() + "'" +
            ", datejcattachment='" + getDatejcattachment() + "'" +
            ", datehjrattachment='" + getDatehjrattachment() + "'" +
            ", idtransfer='" + getIdtransfer() + "'" +
            ", idcorresp='" + getIdcorresp() + "'" +
            ", ordering=" + getOrdering() +
            ", levelattachement='" + getLevelattachement() + "'" +
            ", idreq='" + getIdreq() + "'" +
            ", orderingscan=" + getOrderingscan() +
            ", iddocext='" + getIddocext() + "'" +
            ", idleave='" + getIdleave() + "'" +
            ", configLevel='" + getConfigLevel() + "'" +
            ", typeAtach='" + getTypeAtach() + "'" +
            ", idDirectOrder='" + getIdDirectOrder() + "'" +
            ", pathFile='" + getPathFile() + "'" +
            ", version=" + getVersion() +
            "}";
    }
}
