package com.dq.lilas.domain;

import jakarta.persistence.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

/**
 * A ProductsList.
 */
@Entity
@Table(name = "products_list")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ProductsList implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "internal_code")
    private String internalCode;

    @Column(name = "barcode")
    private String barcode;

    @Column(name = "qad_name")
    private String qadName;

    @Column(name = "category")
    private String category;

    @ManyToOne
    @JsonIgnoreProperties(value = { "productsList" }, allowSetters = true)
    @JoinColumn(name = "company_id")
    private Company company;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public ProductsList id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getInternalCode() {
        return this.internalCode;
    }

    public ProductsList internalCode(String internalCode) {
        this.setInternalCode(internalCode);
        return this;
    }

    public void setInternalCode(String internalCode) {
        this.internalCode = internalCode;
    }

    public String getBarcode() {
        return this.barcode;
    }

    public ProductsList barcode(String barcode) {
        this.setBarcode(barcode);
        return this;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getQadName() {
        return this.qadName;
    }

    public ProductsList qadName(String qadName) {
        this.setQadName(qadName);
        return this;
    }

    public void setQadName(String qadName) {
        this.qadName = qadName;
    }

    public String getCategory() {
        return this.category;
    }

    public ProductsList category(String category) {
        this.setCategory(category);
        return this;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public Company getCompany() {
        return this.company;
    }

    public ProductsList company(Company company) {
        this.setCompany(company);
        return this;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ProductsList)) {
            return false;
        }
        return getId() != null && getId().equals(((ProductsList) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ProductsList{" +
            "id=" + getId() +
            ", internalCode='" + getInternalCode() + "'" +
            ", barcode='" + getBarcode() + "'" +
            ", qadName='" + getQadName() + "'" +
            ", category='" + getCategory() + "'" +
            "}";
    }
}
