package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Typecorrespondencelang.
 */
@Entity
@Table(name = "typecorrespondencelang")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Typecorrespondencelang implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "lbl")
    private String lbl;

    @Column(name = "lang")
    private String lang;

    @Column(name = "abbreviated")
    private String abbreviated;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "typecorrespondences" }, allowSetters = true)
    private Typecorrespondence typecorrespondence;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Typecorrespondencelang id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLbl() {
        return this.lbl;
    }

    public Typecorrespondencelang lbl(String lbl) {
        this.setLbl(lbl);
        return this;
    }

    public void setLbl(String lbl) {
        this.lbl = lbl;
    }

    public String getLang() {
        return this.lang;
    }

    public Typecorrespondencelang lang(String lang) {
        this.setLang(lang);
        return this;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getAbbreviated() {
        return this.abbreviated;
    }

    public Typecorrespondencelang abbreviated(String abbreviated) {
        this.setAbbreviated(abbreviated);
        return this;
    }

    public void setAbbreviated(String abbreviated) {
        this.abbreviated = abbreviated;
    }

    public Typecorrespondence getTypecorrespondence() {
        return this.typecorrespondence;
    }

    public void setTypecorrespondence(Typecorrespondence typecorrespondence) {
        this.typecorrespondence = typecorrespondence;
    }

    public Typecorrespondencelang typecorrespondence(Typecorrespondence typecorrespondence) {
        this.setTypecorrespondence(typecorrespondence);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Typecorrespondencelang)) {
            return false;
        }
        return getId() != null && getId().equals(((Typecorrespondencelang) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Typecorrespondencelang{" +
            "id=" + getId() +
            ", lbl='" + getLbl() + "'" +
            ", lang='" + getLang() + "'" +
            ", abbreviated='" + getAbbreviated() + "'" +
            "}";
    }
}
