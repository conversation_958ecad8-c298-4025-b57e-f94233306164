package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A EmailsNotifications.
 */
@Entity
@Table(name = "emails_notifications")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class EmailsNotifications implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "subject")
    private String subject;

    @Column(name = "notification_date")
    private Instant notificationDate;

    @Lob
    @Column(name = "body")
    private byte[] body;

    @Column(name = "body_content_type")
    private String bodyContentType;

    @Column(name = "recipient")
    private Long recipient;

    @Column(name = "sender")
    private String sender;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "emailsNotifications", "order" }, allowSetters = true)
    private OrderDetails orderDetails;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public EmailsNotifications id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSubject() {
        return this.subject;
    }

    public EmailsNotifications subject(String subject) {
        this.setSubject(subject);
        return this;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public Instant getNotificationDate() {
        return this.notificationDate;
    }

    public EmailsNotifications notificationDate(Instant notificationDate) {
        this.setNotificationDate(notificationDate);
        return this;
    }

    public void setNotificationDate(Instant notificationDate) {
        this.notificationDate = notificationDate;
    }

    public byte[] getBody() {
        return this.body;
    }

    public EmailsNotifications body(byte[] body) {
        this.setBody(body);
        return this;
    }

    public void setBody(byte[] body) {
        this.body = body;
    }

    public String getBodyContentType() {
        return this.bodyContentType;
    }

    public EmailsNotifications bodyContentType(String bodyContentType) {
        this.bodyContentType = bodyContentType;
        return this;
    }

    public void setBodyContentType(String bodyContentType) {
        this.bodyContentType = bodyContentType;
    }

    public Long getRecipient() {
        return this.recipient;
    }

    public EmailsNotifications recipient(Long recipient) {
        this.setRecipient(recipient);
        return this;
    }

    public void setRecipient(Long recipient) {
        this.recipient = recipient;
    }

    public String getSender() {
        return this.sender;
    }

    public EmailsNotifications sender(String sender) {
        this.setSender(sender);
        return this;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public OrderDetails getOrderDetails() {
        return this.orderDetails;
    }

    public void setOrderDetails(OrderDetails orderDetails) {
        this.orderDetails = orderDetails;
    }

    public EmailsNotifications orderDetails(OrderDetails orderDetails) {
        this.setOrderDetails(orderDetails);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof EmailsNotifications)) {
            return false;
        }
        return getId() != null && getId().equals(((EmailsNotifications) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "EmailsNotifications{" +
            "id=" + getId() +
            ", subject='" + getSubject() + "'" +
            ", notificationDate='" + getNotificationDate() + "'" +
            ", body='" + getBody() + "'" +
            ", bodyContentType='" + getBodyContentType() + "'" +
            ", recipient=" + getRecipient() +
            ", sender='" + getSender() + "'" +
            "}";
    }
}
