package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Company.
 */
@Entity
@Table(name = "company")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Company implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @NotNull
    @Size(max = 255)
    @Column(name = "comapany_name", length = 255, nullable = false)
    private String comapanyName;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "company")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "unitlangs", "employees", "company" }, allowSetters = true)
    private Set<Unit> units = new HashSet<>();

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "company")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "company" }, allowSetters = true)
    private Set<ProductsList> productsList = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Company id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getComapanyName() {
        return this.comapanyName;
    }

    public Company comapanyName(String comapanyName) {
        this.setComapanyName(comapanyName);
        return this;
    }

    public void setComapanyName(String comapanyName) {
        this.comapanyName = comapanyName;
    }

    public Set<Unit> getUnits() {
        return this.units;
    }

    public void setUnits(Set<Unit> units) {
        if (this.units != null) {
            this.units.forEach(i -> i.setCompany(null));
        }
        if (units != null) {
            units.forEach(i -> i.setCompany(this));
        }
        this.units = units;
    }

    public Company units(Set<Unit> units) {
        this.setUnits(units);
        return this;
    }

    public Company addUnit(Unit unit) {
        this.units.add(unit);
        unit.setCompany(this);
        return this;
    }

    public Company removeUnit(Unit unit) {
        this.units.remove(unit);
        unit.setCompany(null);
        return this;
    }

    public Set<ProductsList> getProductsList() {
        return this.productsList;
    }

    public void setProductsList(Set<ProductsList> productsList) {
        if (this.productsList != null) {
            this.productsList.forEach(i -> i.setCompany(null));
        }
        if (productsList != null) {
            productsList.forEach(i -> i.setCompany(this));
        }
        this.productsList = productsList;
    }

    public Company productsList(Set<ProductsList> productsList) {
        this.setProductsList(productsList);
        return this;
    }

    public Company addProductsList(ProductsList productsList) {
        this.productsList.add(productsList);
        productsList.setCompany(this);
        return this;
    }

    public Company removeProductsList(ProductsList productsList) {
        this.productsList.remove(productsList);
        productsList.setCompany(null);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Company)) {
            return false;
        }
        return getId() != null && getId().equals(((Company) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Company{" +
            "id=" + getId() +
            ", comapanyName='" + getComapanyName() + "'" +
            "}";
    }
}
