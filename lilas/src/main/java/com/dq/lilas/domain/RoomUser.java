package com.dq.lilas.domain;

import jakarta.persistence.*;
import java.io.Serializable;

@Entity
@Table(name = "room_users")
public class RoomUser extends AbstractAuditingEntity implements Serializable {

    @EmbeddedId
    private RoomUserPK id;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @MapsId("roomId")
    @JoinColumn(name = "room_id")
    private Room room;

    @ManyToOne(fetch = FetchType.LAZY, optional = false)
    @MapsId("userId")
    @JoinColumn(name = "user_id")
    private User user;

    public RoomUser() {
    }

    public RoomUser(RoomUserPK id, Room room, User user) {
        this.id = id;
        this.room = room;
        this.user = user;
    }

    public RoomUserPK getId() {
        return id;
    }

    public void setId(RoomUserPK id) {
        this.id = id;
    }

    public Room getRoom() {
        return room;
    }

    public void setRoom(Room room) {
        this.room = room;
    }

    public User getUser() {
        return user;
    }

    public void setUser(User user) {
        this.user = user;
    }
}
