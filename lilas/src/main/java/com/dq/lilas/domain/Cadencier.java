package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Cadencier.
 */
@Entity
@Table(name = "cadencier")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Cadencier implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "order_day")
    private Integer orderDay;

    @Column(name = "delivery_day")
    private Integer deliveryDay;

    @Column(name = "region")
    private String region;


    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "cadencier" }, allowSetters = true)
    private GmsClients gmsClients;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Cadencier id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrderDay() {
        return this.orderDay;
    }

    public Cadencier orderDay(Integer orderDay) {
        this.setOrderDay(orderDay);
        return this;
    }

    public void setOrderDay(Integer orderDay) {
        this.orderDay = orderDay;
    }

    public Integer getDeliveryDay() {
        return this.deliveryDay;
    }

    public Cadencier deliveryDay(Integer deliveryDay) {
        this.setDeliveryDay(deliveryDay);
        return this;
    }

    public void setDeliveryDay(Integer deliveryDay) {
        this.deliveryDay = deliveryDay;
    }

    public String getRegion() {
        return this.region;
    }

    public Cadencier region(String region) {
        this.setRegion(region);
        return this;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public GmsClients getGmsClients() {
        return this.gmsClients;
    }

    public void setGmsClients(GmsClients gmsClients) {
        this.gmsClients = gmsClients;
    }

    public Cadencier gmsClients(GmsClients gmsClients) {
        this.setGmsClients(gmsClients);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Cadencier)) {
            return false;
        }
        return getId() != null && getId().equals(((Cadencier) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Cadencier{" +
            "id=" + getId() +
            ", orderDay=" + getOrderDay() +
            ", deliveryDay=" + getDeliveryDay() +
            ", region='" + getRegion() + "'" +
            "}";
    }
}
