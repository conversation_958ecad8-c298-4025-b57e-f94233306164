package com.dq.lilas.domain;

import jakarta.persistence.Column;
import jakarta.persistence.Embeddable;
import java.io.Serializable;

@Embeddable
public class ConversationReceiverPK implements Serializable {

    @Column(name = "conversation_id")
    private Long conversationId;

    @Column(name = "receiver_id")
    private Long receiverId;

    public ConversationReceiverPK() {
    }

    public ConversationReceiverPK(Long conversationId, Long receiverId) {
        this.conversationId = conversationId;
        this.receiverId = receiverId;
    }

    public Long getConversationId() {
        return conversationId;
    }

    public void setConversationId(Long conversationId) {
        this.conversationId = conversationId;
    }

    public Long getReceiverId() {
        return receiverId;
    }

    public void setReceiverId(Long receiverId) {
        this.receiverId = receiverId;
    }
}
