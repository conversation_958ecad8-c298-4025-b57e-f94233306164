package com.dq.lilas.domain;

import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A DailyStock.
 */
@Entity
@Table(name = "daily_stock")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class DailyStock implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "internal_code")
    private String internalCode;

    @Column(name = "barcode")
    private String barcode;

    @Column(name = "product_name")
    private String productName;

    @Column(name = "stock_qty")
    private Long stockQty;

    @Column(name = "stock_date")
    private LocalDate stockDate;

    @Column(name = "remaining_quantity")
    private Long remainingQuantity;

    @ManyToOne(fetch = FetchType.LAZY)
    private ProductsList productsList;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public DailyStock id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getInternalCode() {
        return this.internalCode;
    }

    public DailyStock internalCode(String internalCode) {
        this.setInternalCode(internalCode);
        return this;
    }

    public void setInternalCode(String internalCode) {
        this.internalCode = internalCode;
    }

    public String getBarcode() {
        return this.barcode;
    }

    public DailyStock barcode(String barcode) {
        this.setBarcode(barcode);
        return this;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getProductName() {
        return this.productName;
    }

    public DailyStock productName(String productName) {
        this.setProductName(productName);
        return this;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getStockQty() {
        return this.stockQty;
    }

    public DailyStock stockQty(Long stockQty) {
        this.setStockQty(stockQty);
        return this;
    }

    public void setStockQty(Long stockQty) {
        this.stockQty = stockQty;
    }

    public LocalDate getStockDate() {
        return this.stockDate;
    }

    public DailyStock stockDate(LocalDate stockDate) {
        this.setStockDate(stockDate);
        return this;
    }

    public void setStockDate(LocalDate stockDate) {
        this.stockDate = stockDate;
    }

    public Long getRemainingQuantity() {
        return this.remainingQuantity;
    }

    public DailyStock remainingQuantity(Long remainingQuantity) {
        this.setRemainingQuantity(remainingQuantity);
        return this;
    }

    public void setRemainingQuantity(Long remainingQuantity) {
        this.remainingQuantity = remainingQuantity;
    }

    public ProductsList getProductsList() {
        return this.productsList;
    }

    public void setProductsList(ProductsList productsList) {
        this.productsList = productsList;
    }

    public DailyStock productsList(ProductsList productsList) {
        this.setProductsList(productsList);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DailyStock)) {
            return false;
        }
        return getId() != null && getId().equals(((DailyStock) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "DailyStock{" +
            "id=" + getId() +
            ", internalCode='" + getInternalCode() + "'" +
            ", barcode='" + getBarcode() + "'" +
            ", productName='" + getProductName() + "'" +
            ", stockQty=" + getStockQty() +
            ", stockDate='" + getStockDate() + "'" +
            ", remainingQuantity=" + getRemainingQuantity() +
            "}";
    }
}
