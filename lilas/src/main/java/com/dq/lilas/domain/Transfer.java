package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Transfer.
 */
@Entity
@Table(name = "transfer")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Transfer implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Size(max = 15)
    @Column(name = "docyear", length = 15)
    private String docyear;

    @Size(max = 4000)
    @Column(name = "texttransfer", length = 4000)
    private String texttransfer;

    @Column(name = "datejctransfer")
    private Instant datejctransfer;

    @Size(max = 30)
    @Column(name = "datehjrtransfer", length = 30)
    private String datehjrtransfer;

    @Size(max = 5)
    @Column(name = "statustransfer", length = 5)
    private String statustransfer;

    @Column(name = "datesendjctransfer")
    private Instant datesendjctransfer;

    @Size(max = 30)
    @Column(name = "datesendhjrtransfer", length = 30)
    private String datesendhjrtransfer;

    @Size(max = 5)
    @Column(name = "savetransfer", length = 5)
    private String savetransfer;

    @Size(max = 25)
    @Column(name = "numcopy", length = 25)
    private String numcopy;

    @Size(max = 1)
    @Column(name = "highlevel", length = 1)
    private String highlevel;

    @Size(max = 10)
    @Column(name = "confidentiel", length = 10)
    private String confidentiel;

    @Size(max = 1)
    @Column(name = "priority", length = 1)
    private String priority;

    @Size(max = 10)
    @Column(name = "timeaction", length = 10)
    private String timeaction;

    @Column(name = "deadline")
    private Instant deadline;

    @Size(max = 15)
    @Column(name = "rappelnum", length = 15)
    private String rappelnum;

    @Size(max = 15)
    @Column(name = "rappeltype", length = 15)
    private String rappeltype;

    @Size(max = 1)
    @Column(name = "readrequest", length = 1)
    private String readrequest;

    @Size(max = 10)
    @Column(name = "typereceive", length = 10)
    private String typereceive;

    @Column(name = "datejcreceive")
    private Instant datejcreceive;

    @Size(max = 10)
    @Column(name = "datehjrreceive", length = 10)
    private String datehjrreceive;

    @Size(max = 20)
    @Column(name = "heurearch", length = 20)
    private String heurearch;

    @Size(max = 20)
    @Column(name = "actiontype", length = 20)
    private String actiontype;

    @Size(max = 1000)
    @Column(name = "comments", length = 1000)
    private String comments;

    @Column(name = "datearch")
    private Instant datearch;

    @Size(max = 30)
    @Column(name = "datearchhj", length = 30)
    private String datearchhj;

    @Size(max = 20)
    @Column(name = "lasttransserial", length = 20)
    private String lasttransserial;

    @Size(max = 100)
    @Column(name = "adrsbooktransto", length = 100)
    private String adrsbooktransto;

    @Size(max = 20)
    @Column(name = "statusreceiveto", length = 20)
    private String statusreceiveto;

    @Size(max = 1000)
    @Column(name = "commentsreceiveto", length = 1000)
    private String commentsreceiveto;

    @Column(name = "receivedatejcuserto")
    private Instant receivedatejcuserto;

    @Size(max = 30)
    @Column(name = "receivedatehjruserto", length = 30)
    private String receivedatehjruserto;

    @Size(max = 15)
    @Column(name = "typetransfer", length = 15)
    private String typetransfer;

    @Size(max = 20)
    @Column(name = "transrecserial", length = 20)
    private String transrecserial;

    @Size(max = 300)
    @Column(name = "attach", length = 300)
    private String attach;

    @Size(max = 10)
    @Column(name = "transtype", length = 10)
    private String transtype;

    @Size(max = 50)
    @Column(name = "ordernbr", length = 50)
    private String ordernbr;

    @Size(max = 20)
    @Column(name = "heureaction", length = 20)
    private String heureaction;

    @Column(name = "datejcaction")
    private Instant datejcaction;

    @Size(max = 20)
    @Column(name = "datehjraction", length = 20)
    private String datehjraction;

    @Size(max = 10)
    @Column(name = "statusdenied", length = 10)
    private String statusdenied;

    @Size(max = 1000)
    @Column(name = "subjectcorresp", length = 1000)
    private String subjectcorresp;

    @Column(name = "datejccorresp")
    private Instant datejccorresp;

    @Size(max = 20)
    @Column(name = "datehjrcorresp", length = 20)
    private String datehjrcorresp;

    @Size(max = 10)
    @Column(name = "oldstatus", length = 10)
    private String oldstatus;

    @Size(max = 20)
    @Column(name = "step", length = 20)
    private String step;

    @Size(max = 20)
    @Column(name = "typeprocess", length = 20)
    private String typeprocess;

    @Size(max = 50)
    @Column(name = "codetask", length = 50)
    private String codetask;

    @Size(max = 100)
    @Column(name = "refusetext", length = 100)
    private String refusetext;

    @Size(max = 10)
    @Column(name = "statusrefused", length = 10)
    private String statusrefused;

    @Size(max = 20)
    @Column(name = "bidadrsbook", length = 20)
    private String bidadrsbook;

    @Size(max = 1000)
    @Column(name = "pagenbrpaper", length = 1000)
    private String pagenbrpaper;

    @Size(max = 20)
    @Column(name = "flagprint", length = 20)
    private String flagprint;

    @Column(name = "dateprint")
    private Instant dateprint;

    @Size(max = 20)
    @Column(name = "datehjrprint", length = 20)
    private String datehjrprint;

    @Column(name = "datejcdelete")
    private Instant datejcdelete;

    @Column(name = "datejcrevoke")
    private Instant datejcrevoke;

    @Size(max = 20)
    @Column(name = "datehjrrevoke", length = 20)
    private String datehjrrevoke;

    @Size(max = 4000)
    @Column(name = "gabaritcontext", length = 4000)
    private String gabaritcontext;

    @Size(max = 20)
    @Column(name = "approvedspeech", length = 20)
    private String approvedspeech;

    @Column(name = "datejcapprovedspeech")
    private Instant datejcapprovedspeech;

    @Size(max = 20)
    @Column(name = "datehjrapprovedspeech", length = 20)
    private String datehjrapprovedspeech;

    @Size(max = 50)
    @Column(name = "conformitytask", length = 50)
    private String conformitytask;

    @Size(max = 500)
    @Column(name = "useradrsbook", length = 500)
    private String useradrsbook;

    @Size(max = 20)
    @Column(name = "stepmaxwf", length = 20)
    private String stepmaxwf;

    @Size(max = 5)
    @Column(name = "incidenttransfer", length = 5)
    private String incidenttransfer;

    @Size(max = 20)
    @Column(name = "qualificationincident", length = 20)
    private String qualificationincident;

    @Size(max = 20)
    @Column(name = "categorieincident", length = 20)
    private String categorieincident;

    @Size(max = 20)
    @Column(name = "statutincident", length = 20)
    private String statutincident;

    @Size(max = 20)
    @Column(name = "criticiteincident", length = 20)
    private String criticiteincident;

    @Size(max = 255)
    @Column(name = "voice_id", length = 255)
    private String voiceId;

    @Size(max = 1)
    @Column(name = "favoris", length = 1)
    private String favoris;

    @Size(max = 1)
    @Column(name = "checkinboxfavorite", length = 1)
    private String checkinboxfavorite;

    @Size(max = 1)
    @Column(name = "checkclosefavorite", length = 1)
    private String checkclosefavorite;

    @Size(max = 1)
    @Column(name = "checkfavorite", length = 1)
    private String checkfavorite;

    @Size(max = 20)
    @Column(name = "taskcateg_id", length = 20)
    private String taskcategId;

    @Column(name = "pinrequired")
    private Boolean pinrequired;

    @Size(max = 6)
    @Column(name = "code_pin", length = 6)
    private String codePin;

    @Column(name = "sendwithmail")
    private Boolean sendwithmail;

    @JsonIgnoreProperties(
        value = {
            "transmoth",
            "correspondencecopy",
            "correspondence",
            "deliverymode",
            "employee",
            "unit",
            "action",
            "userreceive",
            "usertrans",
            "usertransto",
            "unittransto",
            "userrevoke",
            "userreceiveto",
            "useraction",
            "fromdept",
            "transprincip",
            "typecorrespondence",
            "transfer",
        },
        allowSetters = true
    )
    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(unique = true)
    private Transfer transmoth;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(
        value = { "correspondence", "employee", "unit", "action", "useraction", "userrevoke", "userremove" },
        allowSetters = true
    )
    private Correspondencecopy correspondencecopy;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "employee", "unit", "deliverymode", "typecorrespondence" }, allowSetters = true)
    private Correspondence correspondence;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "deleverymodelangs" }, allowSetters = true)
    private Deliverymode deliverymode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee employee;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "unitlangs", "employees", "company" }, allowSetters = true)
    private Unit unit;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "actionlangs" }, allowSetters = true)
    private Action action;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee userreceive;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee usertrans;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee usertransto;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "unitlangs", "employees", "company" }, allowSetters = true)
    private Unit unittransto;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee userrevoke;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee userreceiveto;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee useraction;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "unitlangs", "employees", "company" }, allowSetters = true)
    private Unit fromdept;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(
        value = {
            "transmoth",
            "correspondencecopy",
            "correspondence",
            "deliverymode",
            "employee",
            "unit",
            "action",
            "userreceive",
            "usertrans",
            "usertransto",
            "unittransto",
            "userrevoke",
            "userreceiveto",
            "useraction",
            "fromdept",
            "transprincip",
            "typecorrespondence",
            "transfer",
        },
        allowSetters = true
    )
    private Transfer transprincip;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "typecorrespondences" }, allowSetters = true)
    private Typecorrespondence typecorrespondence;

    @JsonIgnoreProperties(
        value = {
            "transmoth",
            "correspondencecopy",
            "correspondence",
            "deliverymode",
            "employee",
            "unit",
            "action",
            "userreceive",
            "usertrans",
            "usertransto",
            "unittransto",
            "userrevoke",
            "userreceiveto",
            "useraction",
            "fromdept",
            "transprincip",
            "typecorrespondence",
            "transfer",
        },
        allowSetters = true
    )
    @OneToOne(fetch = FetchType.LAZY, mappedBy = "transmoth")
    private Transfer transfer;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Transfer id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDocyear() {
        return this.docyear;
    }

    public Transfer docyear(String docyear) {
        this.setDocyear(docyear);
        return this;
    }

    public void setDocyear(String docyear) {
        this.docyear = docyear;
    }

    public String getTexttransfer() {
        return this.texttransfer;
    }

    public Transfer texttransfer(String texttransfer) {
        this.setTexttransfer(texttransfer);
        return this;
    }

    public void setTexttransfer(String texttransfer) {
        this.texttransfer = texttransfer;
    }

    public Instant getDatejctransfer() {
        return this.datejctransfer;
    }

    public Transfer datejctransfer(Instant datejctransfer) {
        this.setDatejctransfer(datejctransfer);
        return this;
    }

    public void setDatejctransfer(Instant datejctransfer) {
        this.datejctransfer = datejctransfer;
    }

    public String getDatehjrtransfer() {
        return this.datehjrtransfer;
    }

    public Transfer datehjrtransfer(String datehjrtransfer) {
        this.setDatehjrtransfer(datehjrtransfer);
        return this;
    }

    public void setDatehjrtransfer(String datehjrtransfer) {
        this.datehjrtransfer = datehjrtransfer;
    }

    public String getStatustransfer() {
        return this.statustransfer;
    }

    public Transfer statustransfer(String statustransfer) {
        this.setStatustransfer(statustransfer);
        return this;
    }

    public void setStatustransfer(String statustransfer) {
        this.statustransfer = statustransfer;
    }

    public Instant getDatesendjctransfer() {
        return this.datesendjctransfer;
    }

    public Transfer datesendjctransfer(Instant datesendjctransfer) {
        this.setDatesendjctransfer(datesendjctransfer);
        return this;
    }

    public void setDatesendjctransfer(Instant datesendjctransfer) {
        this.datesendjctransfer = datesendjctransfer;
    }

    public String getDatesendhjrtransfer() {
        return this.datesendhjrtransfer;
    }

    public Transfer datesendhjrtransfer(String datesendhjrtransfer) {
        this.setDatesendhjrtransfer(datesendhjrtransfer);
        return this;
    }

    public void setDatesendhjrtransfer(String datesendhjrtransfer) {
        this.datesendhjrtransfer = datesendhjrtransfer;
    }

    public String getSavetransfer() {
        return this.savetransfer;
    }

    public Transfer savetransfer(String savetransfer) {
        this.setSavetransfer(savetransfer);
        return this;
    }

    public void setSavetransfer(String savetransfer) {
        this.savetransfer = savetransfer;
    }

    public String getNumcopy() {
        return this.numcopy;
    }

    public Transfer numcopy(String numcopy) {
        this.setNumcopy(numcopy);
        return this;
    }

    public void setNumcopy(String numcopy) {
        this.numcopy = numcopy;
    }

    public String getHighlevel() {
        return this.highlevel;
    }

    public Transfer highlevel(String highlevel) {
        this.setHighlevel(highlevel);
        return this;
    }

    public void setHighlevel(String highlevel) {
        this.highlevel = highlevel;
    }

    public String getConfidentiel() {
        return this.confidentiel;
    }

    public Transfer confidentiel(String confidentiel) {
        this.setConfidentiel(confidentiel);
        return this;
    }

    public void setConfidentiel(String confidentiel) {
        this.confidentiel = confidentiel;
    }

    public String getPriority() {
        return this.priority;
    }

    public Transfer priority(String priority) {
        this.setPriority(priority);
        return this;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getTimeaction() {
        return this.timeaction;
    }

    public Transfer timeaction(String timeaction) {
        this.setTimeaction(timeaction);
        return this;
    }

    public void setTimeaction(String timeaction) {
        this.timeaction = timeaction;
    }

    public Instant getDeadline() {
        return this.deadline;
    }

    public Transfer deadline(Instant deadline) {
        this.setDeadline(deadline);
        return this;
    }

    public void setDeadline(Instant deadline) {
        this.deadline = deadline;
    }

    public String getRappelnum() {
        return this.rappelnum;
    }

    public Transfer rappelnum(String rappelnum) {
        this.setRappelnum(rappelnum);
        return this;
    }

    public void setRappelnum(String rappelnum) {
        this.rappelnum = rappelnum;
    }

    public String getRappeltype() {
        return this.rappeltype;
    }

    public Transfer rappeltype(String rappeltype) {
        this.setRappeltype(rappeltype);
        return this;
    }

    public void setRappeltype(String rappeltype) {
        this.rappeltype = rappeltype;
    }

    public String getReadrequest() {
        return this.readrequest;
    }

    public Transfer readrequest(String readrequest) {
        this.setReadrequest(readrequest);
        return this;
    }

    public void setReadrequest(String readrequest) {
        this.readrequest = readrequest;
    }

    public String getTypereceive() {
        return this.typereceive;
    }

    public Transfer typereceive(String typereceive) {
        this.setTypereceive(typereceive);
        return this;
    }

    public void setTypereceive(String typereceive) {
        this.typereceive = typereceive;
    }

    public Instant getDatejcreceive() {
        return this.datejcreceive;
    }

    public Transfer datejcreceive(Instant datejcreceive) {
        this.setDatejcreceive(datejcreceive);
        return this;
    }

    public void setDatejcreceive(Instant datejcreceive) {
        this.datejcreceive = datejcreceive;
    }

    public String getDatehjrreceive() {
        return this.datehjrreceive;
    }

    public Transfer datehjrreceive(String datehjrreceive) {
        this.setDatehjrreceive(datehjrreceive);
        return this;
    }

    public void setDatehjrreceive(String datehjrreceive) {
        this.datehjrreceive = datehjrreceive;
    }

    public String getHeurearch() {
        return this.heurearch;
    }

    public Transfer heurearch(String heurearch) {
        this.setHeurearch(heurearch);
        return this;
    }

    public void setHeurearch(String heurearch) {
        this.heurearch = heurearch;
    }

    public String getActiontype() {
        return this.actiontype;
    }

    public Transfer actiontype(String actiontype) {
        this.setActiontype(actiontype);
        return this;
    }

    public void setActiontype(String actiontype) {
        this.actiontype = actiontype;
    }

    public String getComments() {
        return this.comments;
    }

    public Transfer comments(String comments) {
        this.setComments(comments);
        return this;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Instant getDatearch() {
        return this.datearch;
    }

    public Transfer datearch(Instant datearch) {
        this.setDatearch(datearch);
        return this;
    }

    public void setDatearch(Instant datearch) {
        this.datearch = datearch;
    }

    public String getDatearchhj() {
        return this.datearchhj;
    }

    public Transfer datearchhj(String datearchhj) {
        this.setDatearchhj(datearchhj);
        return this;
    }

    public void setDatearchhj(String datearchhj) {
        this.datearchhj = datearchhj;
    }

    public String getLasttransserial() {
        return this.lasttransserial;
    }

    public Transfer lasttransserial(String lasttransserial) {
        this.setLasttransserial(lasttransserial);
        return this;
    }

    public void setLasttransserial(String lasttransserial) {
        this.lasttransserial = lasttransserial;
    }

    public String getAdrsbooktransto() {
        return this.adrsbooktransto;
    }

    public Transfer adrsbooktransto(String adrsbooktransto) {
        this.setAdrsbooktransto(adrsbooktransto);
        return this;
    }

    public void setAdrsbooktransto(String adrsbooktransto) {
        this.adrsbooktransto = adrsbooktransto;
    }

    public String getStatusreceiveto() {
        return this.statusreceiveto;
    }

    public Transfer statusreceiveto(String statusreceiveto) {
        this.setStatusreceiveto(statusreceiveto);
        return this;
    }

    public void setStatusreceiveto(String statusreceiveto) {
        this.statusreceiveto = statusreceiveto;
    }

    public String getCommentsreceiveto() {
        return this.commentsreceiveto;
    }

    public Transfer commentsreceiveto(String commentsreceiveto) {
        this.setCommentsreceiveto(commentsreceiveto);
        return this;
    }

    public void setCommentsreceiveto(String commentsreceiveto) {
        this.commentsreceiveto = commentsreceiveto;
    }

    public Instant getReceivedatejcuserto() {
        return this.receivedatejcuserto;
    }

    public Transfer receivedatejcuserto(Instant receivedatejcuserto) {
        this.setReceivedatejcuserto(receivedatejcuserto);
        return this;
    }

    public void setReceivedatejcuserto(Instant receivedatejcuserto) {
        this.receivedatejcuserto = receivedatejcuserto;
    }

    public String getReceivedatehjruserto() {
        return this.receivedatehjruserto;
    }

    public Transfer receivedatehjruserto(String receivedatehjruserto) {
        this.setReceivedatehjruserto(receivedatehjruserto);
        return this;
    }

    public void setReceivedatehjruserto(String receivedatehjruserto) {
        this.receivedatehjruserto = receivedatehjruserto;
    }

    public String getTypetransfer() {
        return this.typetransfer;
    }

    public Transfer typetransfer(String typetransfer) {
        this.setTypetransfer(typetransfer);
        return this;
    }

    public void setTypetransfer(String typetransfer) {
        this.typetransfer = typetransfer;
    }

    public String getTransrecserial() {
        return this.transrecserial;
    }

    public Transfer transrecserial(String transrecserial) {
        this.setTransrecserial(transrecserial);
        return this;
    }

    public void setTransrecserial(String transrecserial) {
        this.transrecserial = transrecserial;
    }

    public String getAttach() {
        return this.attach;
    }

    public Transfer attach(String attach) {
        this.setAttach(attach);
        return this;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }

    public String getTranstype() {
        return this.transtype;
    }

    public Transfer transtype(String transtype) {
        this.setTranstype(transtype);
        return this;
    }

    public void setTranstype(String transtype) {
        this.transtype = transtype;
    }

    public String getOrdernbr() {
        return this.ordernbr;
    }

    public Transfer ordernbr(String ordernbr) {
        this.setOrdernbr(ordernbr);
        return this;
    }

    public void setOrdernbr(String ordernbr) {
        this.ordernbr = ordernbr;
    }

    public String getHeureaction() {
        return this.heureaction;
    }

    public Transfer heureaction(String heureaction) {
        this.setHeureaction(heureaction);
        return this;
    }

    public void setHeureaction(String heureaction) {
        this.heureaction = heureaction;
    }

    public Instant getDatejcaction() {
        return this.datejcaction;
    }

    public Transfer datejcaction(Instant datejcaction) {
        this.setDatejcaction(datejcaction);
        return this;
    }

    public void setDatejcaction(Instant datejcaction) {
        this.datejcaction = datejcaction;
    }

    public String getDatehjraction() {
        return this.datehjraction;
    }

    public Transfer datehjraction(String datehjraction) {
        this.setDatehjraction(datehjraction);
        return this;
    }

    public void setDatehjraction(String datehjraction) {
        this.datehjraction = datehjraction;
    }

    public String getStatusdenied() {
        return this.statusdenied;
    }

    public Transfer statusdenied(String statusdenied) {
        this.setStatusdenied(statusdenied);
        return this;
    }

    public void setStatusdenied(String statusdenied) {
        this.statusdenied = statusdenied;
    }

    public String getSubjectcorresp() {
        return this.subjectcorresp;
    }

    public Transfer subjectcorresp(String subjectcorresp) {
        this.setSubjectcorresp(subjectcorresp);
        return this;
    }

    public void setSubjectcorresp(String subjectcorresp) {
        this.subjectcorresp = subjectcorresp;
    }

    public Instant getDatejccorresp() {
        return this.datejccorresp;
    }

    public Transfer datejccorresp(Instant datejccorresp) {
        this.setDatejccorresp(datejccorresp);
        return this;
    }

    public void setDatejccorresp(Instant datejccorresp) {
        this.datejccorresp = datejccorresp;
    }

    public String getDatehjrcorresp() {
        return this.datehjrcorresp;
    }

    public Transfer datehjrcorresp(String datehjrcorresp) {
        this.setDatehjrcorresp(datehjrcorresp);
        return this;
    }

    public void setDatehjrcorresp(String datehjrcorresp) {
        this.datehjrcorresp = datehjrcorresp;
    }

    public String getOldstatus() {
        return this.oldstatus;
    }

    public Transfer oldstatus(String oldstatus) {
        this.setOldstatus(oldstatus);
        return this;
    }

    public void setOldstatus(String oldstatus) {
        this.oldstatus = oldstatus;
    }

    public String getStep() {
        return this.step;
    }

    public Transfer step(String step) {
        this.setStep(step);
        return this;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public String getTypeprocess() {
        return this.typeprocess;
    }

    public Transfer typeprocess(String typeprocess) {
        this.setTypeprocess(typeprocess);
        return this;
    }

    public void setTypeprocess(String typeprocess) {
        this.typeprocess = typeprocess;
    }

    public String getCodetask() {
        return this.codetask;
    }

    public Transfer codetask(String codetask) {
        this.setCodetask(codetask);
        return this;
    }

    public void setCodetask(String codetask) {
        this.codetask = codetask;
    }

    public String getRefusetext() {
        return this.refusetext;
    }

    public Transfer refusetext(String refusetext) {
        this.setRefusetext(refusetext);
        return this;
    }

    public void setRefusetext(String refusetext) {
        this.refusetext = refusetext;
    }

    public String getStatusrefused() {
        return this.statusrefused;
    }

    public Transfer statusrefused(String statusrefused) {
        this.setStatusrefused(statusrefused);
        return this;
    }

    public void setStatusrefused(String statusrefused) {
        this.statusrefused = statusrefused;
    }

    public String getBidadrsbook() {
        return this.bidadrsbook;
    }

    public Transfer bidadrsbook(String bidadrsbook) {
        this.setBidadrsbook(bidadrsbook);
        return this;
    }

    public void setBidadrsbook(String bidadrsbook) {
        this.bidadrsbook = bidadrsbook;
    }

    public String getPagenbrpaper() {
        return this.pagenbrpaper;
    }

    public Transfer pagenbrpaper(String pagenbrpaper) {
        this.setPagenbrpaper(pagenbrpaper);
        return this;
    }

    public void setPagenbrpaper(String pagenbrpaper) {
        this.pagenbrpaper = pagenbrpaper;
    }

    public String getFlagprint() {
        return this.flagprint;
    }

    public Transfer flagprint(String flagprint) {
        this.setFlagprint(flagprint);
        return this;
    }

    public void setFlagprint(String flagprint) {
        this.flagprint = flagprint;
    }

    public Instant getDateprint() {
        return this.dateprint;
    }

    public Transfer dateprint(Instant dateprint) {
        this.setDateprint(dateprint);
        return this;
    }

    public void setDateprint(Instant dateprint) {
        this.dateprint = dateprint;
    }

    public String getDatehjrprint() {
        return this.datehjrprint;
    }

    public Transfer datehjrprint(String datehjrprint) {
        this.setDatehjrprint(datehjrprint);
        return this;
    }

    public void setDatehjrprint(String datehjrprint) {
        this.datehjrprint = datehjrprint;
    }

    public Instant getDatejcdelete() {
        return this.datejcdelete;
    }

    public Transfer datejcdelete(Instant datejcdelete) {
        this.setDatejcdelete(datejcdelete);
        return this;
    }

    public void setDatejcdelete(Instant datejcdelete) {
        this.datejcdelete = datejcdelete;
    }

    public Instant getDatejcrevoke() {
        return this.datejcrevoke;
    }

    public Transfer datejcrevoke(Instant datejcrevoke) {
        this.setDatejcrevoke(datejcrevoke);
        return this;
    }

    public void setDatejcrevoke(Instant datejcrevoke) {
        this.datejcrevoke = datejcrevoke;
    }

    public String getDatehjrrevoke() {
        return this.datehjrrevoke;
    }

    public Transfer datehjrrevoke(String datehjrrevoke) {
        this.setDatehjrrevoke(datehjrrevoke);
        return this;
    }

    public void setDatehjrrevoke(String datehjrrevoke) {
        this.datehjrrevoke = datehjrrevoke;
    }

    public String getGabaritcontext() {
        return this.gabaritcontext;
    }

    public Transfer gabaritcontext(String gabaritcontext) {
        this.setGabaritcontext(gabaritcontext);
        return this;
    }

    public void setGabaritcontext(String gabaritcontext) {
        this.gabaritcontext = gabaritcontext;
    }

    public String getApprovedspeech() {
        return this.approvedspeech;
    }

    public Transfer approvedspeech(String approvedspeech) {
        this.setApprovedspeech(approvedspeech);
        return this;
    }

    public void setApprovedspeech(String approvedspeech) {
        this.approvedspeech = approvedspeech;
    }

    public Instant getDatejcapprovedspeech() {
        return this.datejcapprovedspeech;
    }

    public Transfer datejcapprovedspeech(Instant datejcapprovedspeech) {
        this.setDatejcapprovedspeech(datejcapprovedspeech);
        return this;
    }

    public void setDatejcapprovedspeech(Instant datejcapprovedspeech) {
        this.datejcapprovedspeech = datejcapprovedspeech;
    }

    public String getDatehjrapprovedspeech() {
        return this.datehjrapprovedspeech;
    }

    public Transfer datehjrapprovedspeech(String datehjrapprovedspeech) {
        this.setDatehjrapprovedspeech(datehjrapprovedspeech);
        return this;
    }

    public void setDatehjrapprovedspeech(String datehjrapprovedspeech) {
        this.datehjrapprovedspeech = datehjrapprovedspeech;
    }

    public String getConformitytask() {
        return this.conformitytask;
    }

    public Transfer conformitytask(String conformitytask) {
        this.setConformitytask(conformitytask);
        return this;
    }

    public void setConformitytask(String conformitytask) {
        this.conformitytask = conformitytask;
    }

    public String getUseradrsbook() {
        return this.useradrsbook;
    }

    public Transfer useradrsbook(String useradrsbook) {
        this.setUseradrsbook(useradrsbook);
        return this;
    }

    public void setUseradrsbook(String useradrsbook) {
        this.useradrsbook = useradrsbook;
    }

    public String getStepmaxwf() {
        return this.stepmaxwf;
    }

    public Transfer stepmaxwf(String stepmaxwf) {
        this.setStepmaxwf(stepmaxwf);
        return this;
    }

    public void setStepmaxwf(String stepmaxwf) {
        this.stepmaxwf = stepmaxwf;
    }

    public String getIncidenttransfer() {
        return this.incidenttransfer;
    }

    public Transfer incidenttransfer(String incidenttransfer) {
        this.setIncidenttransfer(incidenttransfer);
        return this;
    }

    public void setIncidenttransfer(String incidenttransfer) {
        this.incidenttransfer = incidenttransfer;
    }

    public String getQualificationincident() {
        return this.qualificationincident;
    }

    public Transfer qualificationincident(String qualificationincident) {
        this.setQualificationincident(qualificationincident);
        return this;
    }

    public void setQualificationincident(String qualificationincident) {
        this.qualificationincident = qualificationincident;
    }

    public String getCategorieincident() {
        return this.categorieincident;
    }

    public Transfer categorieincident(String categorieincident) {
        this.setCategorieincident(categorieincident);
        return this;
    }

    public void setCategorieincident(String categorieincident) {
        this.categorieincident = categorieincident;
    }

    public String getStatutincident() {
        return this.statutincident;
    }

    public Transfer statutincident(String statutincident) {
        this.setStatutincident(statutincident);
        return this;
    }

    public void setStatutincident(String statutincident) {
        this.statutincident = statutincident;
    }

    public String getCriticiteincident() {
        return this.criticiteincident;
    }

    public Transfer criticiteincident(String criticiteincident) {
        this.setCriticiteincident(criticiteincident);
        return this;
    }

    public void setCriticiteincident(String criticiteincident) {
        this.criticiteincident = criticiteincident;
    }

    public String getVoiceId() {
        return this.voiceId;
    }

    public Transfer voiceId(String voiceId) {
        this.setVoiceId(voiceId);
        return this;
    }

    public void setVoiceId(String voiceId) {
        this.voiceId = voiceId;
    }

    public String getFavoris() {
        return this.favoris;
    }

    public Transfer favoris(String favoris) {
        this.setFavoris(favoris);
        return this;
    }

    public void setFavoris(String favoris) {
        this.favoris = favoris;
    }

    public String getCheckinboxfavorite() {
        return this.checkinboxfavorite;
    }

    public Transfer checkinboxfavorite(String checkinboxfavorite) {
        this.setCheckinboxfavorite(checkinboxfavorite);
        return this;
    }

    public void setCheckinboxfavorite(String checkinboxfavorite) {
        this.checkinboxfavorite = checkinboxfavorite;
    }

    public String getCheckclosefavorite() {
        return this.checkclosefavorite;
    }

    public Transfer checkclosefavorite(String checkclosefavorite) {
        this.setCheckclosefavorite(checkclosefavorite);
        return this;
    }

    public void setCheckclosefavorite(String checkclosefavorite) {
        this.checkclosefavorite = checkclosefavorite;
    }

    public String getCheckfavorite() {
        return this.checkfavorite;
    }

    public Transfer checkfavorite(String checkfavorite) {
        this.setCheckfavorite(checkfavorite);
        return this;
    }

    public void setCheckfavorite(String checkfavorite) {
        this.checkfavorite = checkfavorite;
    }

    public String getTaskcategId() {
        return this.taskcategId;
    }

    public Transfer taskcategId(String taskcategId) {
        this.setTaskcategId(taskcategId);
        return this;
    }

    public void setTaskcategId(String taskcategId) {
        this.taskcategId = taskcategId;
    }

    public Boolean getPinrequired() {
        return this.pinrequired;
    }

    public Transfer pinrequired(Boolean pinrequired) {
        this.setPinrequired(pinrequired);
        return this;
    }

    public void setPinrequired(Boolean pinrequired) {
        this.pinrequired = pinrequired;
    }

    public String getCodePin() {
        return this.codePin;
    }

    public Transfer codePin(String codePin) {
        this.setCodePin(codePin);
        return this;
    }

    public void setCodePin(String codePin) {
        this.codePin = codePin;
    }

    public Boolean getSendwithmail() {
        return this.sendwithmail;
    }

    public Transfer sendwithmail(Boolean sendwithmail) {
        this.setSendwithmail(sendwithmail);
        return this;
    }

    public void setSendwithmail(Boolean sendwithmail) {
        this.sendwithmail = sendwithmail;
    }

    public Transfer getTransmoth() {
        return this.transmoth;
    }

    public void setTransmoth(Transfer transfer) {
        this.transmoth = transfer;
    }

    public Transfer transmoth(Transfer transfer) {
        this.setTransmoth(transfer);
        return this;
    }

    public Correspondencecopy getCorrespondencecopy() {
        return this.correspondencecopy;
    }

    public void setCorrespondencecopy(Correspondencecopy correspondencecopy) {
        this.correspondencecopy = correspondencecopy;
    }

    public Transfer correspondencecopy(Correspondencecopy correspondencecopy) {
        this.setCorrespondencecopy(correspondencecopy);
        return this;
    }

    public Correspondence getCorrespondence() {
        return this.correspondence;
    }

    public void setCorrespondence(Correspondence correspondence) {
        this.correspondence = correspondence;
    }

    public Transfer correspondence(Correspondence correspondence) {
        this.setCorrespondence(correspondence);
        return this;
    }

    public Deliverymode getDeliverymode() {
        return this.deliverymode;
    }

    public void setDeliverymode(Deliverymode deliverymode) {
        this.deliverymode = deliverymode;
    }

    public Transfer deliverymode(Deliverymode deliverymode) {
        this.setDeliverymode(deliverymode);
        return this;
    }

    public Employee getEmployee() {
        return this.employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }

    public Transfer employee(Employee employee) {
        this.setEmployee(employee);
        return this;
    }

    public Unit getUnit() {
        return this.unit;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }

    public Transfer unit(Unit unit) {
        this.setUnit(unit);
        return this;
    }

    public Action getAction() {
        return this.action;
    }

    public void setAction(Action action) {
        this.action = action;
    }

    public Transfer action(Action action) {
        this.setAction(action);
        return this;
    }

    public Employee getUserreceive() {
        return this.userreceive;
    }

    public void setUserreceive(Employee employee) {
        this.userreceive = employee;
    }

    public Transfer userreceive(Employee employee) {
        this.setUserreceive(employee);
        return this;
    }

    public Employee getUsertrans() {
        return this.usertrans;
    }

    public void setUsertrans(Employee employee) {
        this.usertrans = employee;
    }

    public Transfer usertrans(Employee employee) {
        this.setUsertrans(employee);
        return this;
    }

    public Employee getUsertransto() {
        return this.usertransto;
    }

    public void setUsertransto(Employee employee) {
        this.usertransto = employee;
    }

    public Transfer usertransto(Employee employee) {
        this.setUsertransto(employee);
        return this;
    }

    public Unit getUnittransto() {
        return this.unittransto;
    }

    public void setUnittransto(Unit unit) {
        this.unittransto = unit;
    }

    public Transfer unittransto(Unit unit) {
        this.setUnittransto(unit);
        return this;
    }

    public Employee getUserrevoke() {
        return this.userrevoke;
    }

    public void setUserrevoke(Employee employee) {
        this.userrevoke = employee;
    }

    public Transfer userrevoke(Employee employee) {
        this.setUserrevoke(employee);
        return this;
    }

    public Employee getUserreceiveto() {
        return this.userreceiveto;
    }

    public void setUserreceiveto(Employee employee) {
        this.userreceiveto = employee;
    }

    public Transfer userreceiveto(Employee employee) {
        this.setUserreceiveto(employee);
        return this;
    }

    public Employee getUseraction() {
        return this.useraction;
    }

    public void setUseraction(Employee employee) {
        this.useraction = employee;
    }

    public Transfer useraction(Employee employee) {
        this.setUseraction(employee);
        return this;
    }

    public Unit getFromdept() {
        return this.fromdept;
    }

    public void setFromdept(Unit unit) {
        this.fromdept = unit;
    }

    public Transfer fromdept(Unit unit) {
        this.setFromdept(unit);
        return this;
    }

    public Transfer getTransprincip() {
        return this.transprincip;
    }

    public void setTransprincip(Transfer transfer) {
        this.transprincip = transfer;
    }

    public Transfer transprincip(Transfer transfer) {
        this.setTransprincip(transfer);
        return this;
    }

    public Typecorrespondence getTypecorrespondence() {
        return this.typecorrespondence;
    }

    public void setTypecorrespondence(Typecorrespondence typecorrespondence) {
        this.typecorrespondence = typecorrespondence;
    }

    public Transfer typecorrespondence(Typecorrespondence typecorrespondence) {
        this.setTypecorrespondence(typecorrespondence);
        return this;
    }

    public Transfer getTransfer() {
        return this.transfer;
    }

    public void setTransfer(Transfer transfer) {
        if (this.transfer != null) {
            this.transfer.setTransmoth(null);
        }
        if (transfer != null) {
            transfer.setTransmoth(this);
        }
        this.transfer = transfer;
    }

    public Transfer transfer(Transfer transfer) {
        this.setTransfer(transfer);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Transfer)) {
            return false;
        }
        return getId() != null && getId().equals(((Transfer) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Transfer{" +
            "id=" + getId() +
            ", docyear='" + getDocyear() + "'" +
            ", texttransfer='" + getTexttransfer() + "'" +
            ", datejctransfer='" + getDatejctransfer() + "'" +
            ", datehjrtransfer='" + getDatehjrtransfer() + "'" +
            ", statustransfer='" + getStatustransfer() + "'" +
            ", datesendjctransfer='" + getDatesendjctransfer() + "'" +
            ", datesendhjrtransfer='" + getDatesendhjrtransfer() + "'" +
            ", savetransfer='" + getSavetransfer() + "'" +
            ", numcopy='" + getNumcopy() + "'" +
            ", highlevel='" + getHighlevel() + "'" +
            ", confidentiel='" + getConfidentiel() + "'" +
            ", priority='" + getPriority() + "'" +
            ", timeaction='" + getTimeaction() + "'" +
            ", deadline='" + getDeadline() + "'" +
            ", rappelnum='" + getRappelnum() + "'" +
            ", rappeltype='" + getRappeltype() + "'" +
            ", readrequest='" + getReadrequest() + "'" +
            ", typereceive='" + getTypereceive() + "'" +
            ", datejcreceive='" + getDatejcreceive() + "'" +
            ", datehjrreceive='" + getDatehjrreceive() + "'" +
            ", heurearch='" + getHeurearch() + "'" +
            ", actiontype='" + getActiontype() + "'" +
            ", comments='" + getComments() + "'" +
            ", datearch='" + getDatearch() + "'" +
            ", datearchhj='" + getDatearchhj() + "'" +
            ", lasttransserial='" + getLasttransserial() + "'" +
            ", adrsbooktransto='" + getAdrsbooktransto() + "'" +
            ", statusreceiveto='" + getStatusreceiveto() + "'" +
            ", commentsreceiveto='" + getCommentsreceiveto() + "'" +
            ", receivedatejcuserto='" + getReceivedatejcuserto() + "'" +
            ", receivedatehjruserto='" + getReceivedatehjruserto() + "'" +
            ", typetransfer='" + getTypetransfer() + "'" +
            ", transrecserial='" + getTransrecserial() + "'" +
            ", attach='" + getAttach() + "'" +
            ", transtype='" + getTranstype() + "'" +
            ", ordernbr='" + getOrdernbr() + "'" +
            ", heureaction='" + getHeureaction() + "'" +
            ", datejcaction='" + getDatejcaction() + "'" +
            ", datehjraction='" + getDatehjraction() + "'" +
            ", statusdenied='" + getStatusdenied() + "'" +
            ", subjectcorresp='" + getSubjectcorresp() + "'" +
            ", datejccorresp='" + getDatejccorresp() + "'" +
            ", datehjrcorresp='" + getDatehjrcorresp() + "'" +
            ", oldstatus='" + getOldstatus() + "'" +
            ", step='" + getStep() + "'" +
            ", typeprocess='" + getTypeprocess() + "'" +
            ", codetask='" + getCodetask() + "'" +
            ", refusetext='" + getRefusetext() + "'" +
            ", statusrefused='" + getStatusrefused() + "'" +
            ", bidadrsbook='" + getBidadrsbook() + "'" +
            ", pagenbrpaper='" + getPagenbrpaper() + "'" +
            ", flagprint='" + getFlagprint() + "'" +
            ", dateprint='" + getDateprint() + "'" +
            ", datehjrprint='" + getDatehjrprint() + "'" +
            ", datejcdelete='" + getDatejcdelete() + "'" +
            ", datejcrevoke='" + getDatejcrevoke() + "'" +
            ", datehjrrevoke='" + getDatehjrrevoke() + "'" +
            ", gabaritcontext='" + getGabaritcontext() + "'" +
            ", approvedspeech='" + getApprovedspeech() + "'" +
            ", datejcapprovedspeech='" + getDatejcapprovedspeech() + "'" +
            ", datehjrapprovedspeech='" + getDatehjrapprovedspeech() + "'" +
            ", conformitytask='" + getConformitytask() + "'" +
            ", useradrsbook='" + getUseradrsbook() + "'" +
            ", stepmaxwf='" + getStepmaxwf() + "'" +
            ", incidenttransfer='" + getIncidenttransfer() + "'" +
            ", qualificationincident='" + getQualificationincident() + "'" +
            ", categorieincident='" + getCategorieincident() + "'" +
            ", statutincident='" + getStatutincident() + "'" +
            ", criticiteincident='" + getCriticiteincident() + "'" +
            ", voiceId='" + getVoiceId() + "'" +
            ", favoris='" + getFavoris() + "'" +
            ", checkinboxfavorite='" + getCheckinboxfavorite() + "'" +
            ", checkclosefavorite='" + getCheckclosefavorite() + "'" +
            ", checkfavorite='" + getCheckfavorite() + "'" +
            ", taskcategId='" + getTaskcategId() + "'" +
            ", pinrequired='" + getPinrequired() + "'" +
            ", codePin='" + getCodePin() + "'" +
            ", sendwithmail='" + getSendwithmail() + "'" +
            "}";
    }
}
