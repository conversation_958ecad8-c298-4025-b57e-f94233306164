package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Employee.
 */
@Entity
@Table(name = "employee")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Employee implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Size(max = 15)
    @Column(name = "tel", length = 15)
    private String tel;

    @Size(max = 15)
    @Column(name = "fax", length = 15)
    private String fax;

    @Size(max = 100)
    @Column(name = "mail", length = 100)
    private String mail;

    @Size(max = 10)
    @Column(name = "confidentiel", length = 10)
    private String confidentiel;

    @Size(max = 15)
    @Column(name = "numidentity", length = 15)
    private String numidentity;

    @Size(max = 15)
    @Column(name = "empnumber", length = 15)
    private String empnumber;

    @Size(max = 1000)
    @Column(name = "fullname", length = 1000)
    private String fullname;

    @Size(max = 1000)
    @Column(name = "address", length = 1000)
    private String address;

    @Size(max = 10)
    @Column(name = "matricule", length = 10)
    private String matricule;

    @Size(max = 5)
    @Column(name = "upscale", length = 5)
    private String upscale;

    @Size(max = 10)
    @Column(name = "active", length = 10)
    private String active;

    @Size(max = 1)
    @Column(name = "statut", length = 1)
    private String statut;

    @Size(max = 15)
    @Column(name = "gender", length = 15)
    private String gender;

    @Size(max = 100)
    @Column(name = "avatar", length = 100)
    private String avatar;

    @Size(max = 255)
    @Column(name = "filenameparaf", length = 255)
    private String filenameparaf;

    @Size(max = 255)
    @Column(name = "filenamesign", length = 255)
    private String filenamesign;

    @Column(name = "coursier")
    private Boolean coursier;

    @OneToOne(fetch = FetchType.LAZY)
    @JoinColumn(unique = true)
    private User user;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "employee")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "employee" }, allowSetters = true)
    private Set<Employeelang> employeelangs = new HashSet<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "joblangs" }, allowSetters = true)
    private Job job;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "unitlangs", "employees", "company" }, allowSetters = true)
    private Unit unit;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Employee id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTel() {
        return this.tel;
    }

    public Employee tel(String tel) {
        this.setTel(tel);
        return this;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getFax() {
        return this.fax;
    }

    public Employee fax(String fax) {
        this.setFax(fax);
        return this;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getMail() {
        return this.mail;
    }

    public Employee mail(String mail) {
        this.setMail(mail);
        return this;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getConfidentiel() {
        return this.confidentiel;
    }

    public Employee confidentiel(String confidentiel) {
        this.setConfidentiel(confidentiel);
        return this;
    }

    public void setConfidentiel(String confidentiel) {
        this.confidentiel = confidentiel;
    }

    public String getNumidentity() {
        return this.numidentity;
    }

    public Employee numidentity(String numidentity) {
        this.setNumidentity(numidentity);
        return this;
    }

    public void setNumidentity(String numidentity) {
        this.numidentity = numidentity;
    }

    public String getEmpnumber() {
        return this.empnumber;
    }

    public Employee empnumber(String empnumber) {
        this.setEmpnumber(empnumber);
        return this;
    }

    public void setEmpnumber(String empnumber) {
        this.empnumber = empnumber;
    }

    public String getFullname() {
        return this.fullname;
    }

    public Employee fullname(String fullname) {
        this.setFullname(fullname);
        return this;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getAddress() {
        return this.address;
    }

    public Employee address(String address) {
        this.setAddress(address);
        return this;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getMatricule() {
        return this.matricule;
    }

    public Employee matricule(String matricule) {
        this.setMatricule(matricule);
        return this;
    }

    public void setMatricule(String matricule) {
        this.matricule = matricule;
    }

    public String getUpscale() {
        return this.upscale;
    }

    public Employee upscale(String upscale) {
        this.setUpscale(upscale);
        return this;
    }

    public void setUpscale(String upscale) {
        this.upscale = upscale;
    }

    public String getActive() {
        return this.active;
    }

    public Employee active(String active) {
        this.setActive(active);
        return this;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getStatut() {
        return this.statut;
    }

    public Employee statut(String statut) {
        this.setStatut(statut);
        return this;
    }

    public void setStatut(String statut) {
        this.statut = statut;
    }

    public String getGender() {
        return this.gender;
    }

    public Employee gender(String gender) {
        this.setGender(gender);
        return this;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getAvatar() {
        return this.avatar;
    }

    public Employee avatar(String avatar) {
        this.setAvatar(avatar);
        return this;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getFilenameparaf() {
        return this.filenameparaf;
    }

    public Employee filenameparaf(String filenameparaf) {
        this.setFilenameparaf(filenameparaf);
        return this;
    }

    public void setFilenameparaf(String filenameparaf) {
        this.filenameparaf = filenameparaf;
    }

    public String getFilenamesign() {
        return this.filenamesign;
    }

    public Employee filenamesign(String filenamesign) {
        this.setFilenamesign(filenamesign);
        return this;
    }

    public void setFilenamesign(String filenamesign) {
        this.filenamesign = filenamesign;
    }

    public Boolean getCoursier() {
        return this.coursier;
    }

    public Employee coursier(Boolean coursier) {
        this.setCoursier(coursier);
        return this;
    }

    public void setCoursier(Boolean coursier) {
        this.coursier = coursier;
    }

    public User getUser() {
        return this.user;
    }

    public void setUser(User user) {
        this.user = user;
    }

    public Employee user(User user) {
        this.setUser(user);
        return this;
    }

    public Set<Employeelang> getEmployeelangs() {
        return this.employeelangs;
    }

    public void setEmployeelangs(Set<Employeelang> employeelangs) {
        if (this.employeelangs != null) {
            this.employeelangs.forEach(i -> i.setEmployee(null));
        }
        if (employeelangs != null) {
            employeelangs.forEach(i -> i.setEmployee(this));
        }
        this.employeelangs = employeelangs;
    }

    public Employee employeelangs(Set<Employeelang> employeelangs) {
        this.setEmployeelangs(employeelangs);
        return this;
    }

    public Employee addEmployeelangs(Employeelang employeelang) {
        this.employeelangs.add(employeelang);
        employeelang.setEmployee(this);
        return this;
    }

    public Employee removeEmployeelangs(Employeelang employeelang) {
        this.employeelangs.remove(employeelang);
        employeelang.setEmployee(null);
        return this;
    }

    public Job getJob() {
        return this.job;
    }

    public void setJob(Job job) {
        this.job = job;
    }

    public Employee job(Job job) {
        this.setJob(job);
        return this;
    }

    public Unit getUnit() {
        return this.unit;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }

    public Employee unit(Unit unit) {
        this.setUnit(unit);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Employee)) {
            return false;
        }
        return getId() != null && getId().equals(((Employee) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Employee{" +
            "id=" + getId() +
            ", tel='" + getTel() + "'" +
            ", fax='" + getFax() + "'" +
            ", mail='" + getMail() + "'" +
            ", confidentiel='" + getConfidentiel() + "'" +
            ", numidentity='" + getNumidentity() + "'" +
            ", empnumber='" + getEmpnumber() + "'" +
            ", fullname='" + getFullname() + "'" +
            ", address='" + getAddress() + "'" +
            ", matricule='" + getMatricule() + "'" +
            ", upscale='" + getUpscale() + "'" +
            ", active='" + getActive() + "'" +
            ", statut='" + getStatut() + "'" +
            ", gender='" + getGender() + "'" +
            ", avatar='" + getAvatar() + "'" +
            ", filenameparaf='" + getFilenameparaf() + "'" +
            ", filenamesign='" + getFilenamesign() + "'" +
            ", coursier='" + getCoursier() + "'" +
            "}";
    }
}
