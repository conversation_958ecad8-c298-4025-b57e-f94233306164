package com.dq.lilas.domain;

import com.dq.lilas.domain.enumeration.PromotionStatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A DemandePromotion.
 */
@Entity
@Table(name = "demande_promotion")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class DemandePromotion implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "code_client")
    private String codeClient;

    @Column(name = "enseigne")
    private String enseigne;

    @Column(name = "action")
    private String action;

    @Column(name = "date_of_request")
    private Instant dateOfRequest;

    @Column(name = "period_promotion_start")
    private Instant periodPromotionStart;

    @Column(name = "period_promotion_end")
    private Instant periodPromotionEnd;

    @Column(name = "period_facturation_start")
    private Instant periodFacturationStart;

    @Column(name = "period_facturation_end")
    private Instant periodFacturationEnd;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private PromotionStatus status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee employee;

    @OneToMany(mappedBy = "demandePromotion", cascade = CascadeType.ALL, fetch = FetchType.LAZY)
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "demandePromotion" }, allowSetters = true)
    private Set<PromotionDetails> promotionDetails = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public DemandePromotion id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCodeClient() {
        return this.codeClient;
    }

    public DemandePromotion codeClient(String codeClient) {
        this.setCodeClient(codeClient);
        return this;
    }

    public void setCodeClient(String codeClient) {
        this.codeClient = codeClient;
    }

    public String getEnseigne() {
        return this.enseigne;
    }

    public DemandePromotion enseigne(String enseigne) {
        this.setEnseigne(enseigne);
        return this;
    }

    public void setEnseigne(String enseigne) {
        this.enseigne = enseigne;
    }

    public String getAction() {
        return this.action;
    }

    public DemandePromotion action(String action) {
        this.setAction(action);
        return this;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Instant getDateOfRequest() {
        return this.dateOfRequest;
    }

    public DemandePromotion dateOfRequest(Instant dateOfRequest) {
        this.setDateOfRequest(dateOfRequest);
        return this;
    }

    public void setDateOfRequest(Instant dateOfRequest) {
        this.dateOfRequest = dateOfRequest;
    }

    public Instant getPeriodPromotionStart() {
        return this.periodPromotionStart;
    }

    public DemandePromotion periodPromotionStart(Instant periodPromotionStart) {
        this.setPeriodPromotionStart(periodPromotionStart);
        return this;
    }

    public void setPeriodPromotionStart(Instant periodPromotionStart) {
        this.periodPromotionStart = periodPromotionStart;
    }

    public Instant getPeriodPromotionEnd() {
        return this.periodPromotionEnd;
    }

    public DemandePromotion periodPromotionEnd(Instant periodPromotionEnd) {
        this.setPeriodPromotionEnd(periodPromotionEnd);
        return this;
    }

    public void setPeriodPromotionEnd(Instant periodPromotionEnd) {
        this.periodPromotionEnd = periodPromotionEnd;
    }

    public Instant getPeriodFacturationStart() {
        return this.periodFacturationStart;
    }

    public DemandePromotion periodFacturationStart(Instant periodFacturationStart) {
        this.setPeriodFacturationStart(periodFacturationStart);
        return this;
    }

    public void setPeriodFacturationStart(Instant periodFacturationStart) {
        this.periodFacturationStart = periodFacturationStart;
    }

    public Instant getPeriodFacturationEnd() {
        return this.periodFacturationEnd;
    }

    public DemandePromotion periodFacturationEnd(Instant periodFacturationEnd) {
        this.setPeriodFacturationEnd(periodFacturationEnd);
        return this;
    }

    public void setPeriodFacturationEnd(Instant periodFacturationEnd) {
        this.periodFacturationEnd = periodFacturationEnd;
    }

    public PromotionStatus getStatus() {
        return this.status;
    }

    public DemandePromotion status(PromotionStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(PromotionStatus status) {
        this.status = status;
    }

    public Employee getEmployee() {
        return this.employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }

    public DemandePromotion employee(Employee employee) {
        this.setEmployee(employee);
        return this;
    }

    public Set<PromotionDetails> getPromotionDetails() {
        return this.promotionDetails;
    }

    public void setPromotionDetails(Set<PromotionDetails> promotionDetails) {
        this.promotionDetails = promotionDetails;
    }

    public DemandePromotion promotionDetails(Set<PromotionDetails> promotionDetails) {
        this.setPromotionDetails(promotionDetails);
        return this;
    }

    public DemandePromotion addPromotionDetails(PromotionDetails promotionDetails) {
        this.promotionDetails.add(promotionDetails);
        promotionDetails.setDemandePromotion(this);
        return this;
    }

    public DemandePromotion removePromotionDetails(PromotionDetails promotionDetails) {
        this.promotionDetails.remove(promotionDetails);
        promotionDetails.setDemandePromotion(null);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DemandePromotion)) {
            return false;
        }
        return getId() != null && getId().equals(((DemandePromotion) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "DemandePromotion{" +
            "id=" + getId() +
            ", codeClient='" + getCodeClient() + "'" +
            ", enseigne='" + getEnseigne() + "'" +
            ", action='" + getAction() + "'" +
            ", dateOfRequest='" + getDateOfRequest() + "'" +
            ", periodPromotionStart='" + getPeriodPromotionStart() + "'" +
            ", periodPromotionEnd='" + getPeriodPromotionEnd() + "'" +
            ", periodFacturationStart='" + getPeriodFacturationStart() + "'" +
            ", periodFacturationEnd='" + getPeriodFacturationEnd() + "'" +
            ", status='" + getStatus() + "'" +
            "}";
    }
}
