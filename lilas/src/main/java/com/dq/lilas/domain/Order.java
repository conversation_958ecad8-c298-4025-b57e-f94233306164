package com.dq.lilas.domain;

import com.dq.lilas.domain.enumeration.OrderStatus;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.io.Serializable;
import java.time.LocalDate;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Order.
 */
@Entity
@Table(name = "jhi_order")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Order implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "hearder_json", columnDefinition = "TEXT")
    private String hearderJson;

    @Column(name = "rank")
    private Integer rank;

    @Enumerated(EnumType.STRING)
    @Column(name = "status")
    private OrderStatus status;

    @Column(name = "locked")
    private Boolean locked;

    @Column(name = "order_number")
    private String orderNumber;

    @Column(name = "brand_id")
    private Long brandId;

    @Column(name = "footer_json", columnDefinition = "TEXT")
    private String footerJson;

    @Column(name = "company")
    private String company;

    @Column(name = "fiscale_id")
    private String fiscaleId;

    @Column(name = "delivery_location")
    private String deliveryLocation;

    @Column(name = "order_date")
    private LocalDate orderDate;

    @Column(name = "delivery_date")
    private LocalDate deliveryDate;

    @Column(name = "rayon")
    private String rayon;

    @Column(name = "gr_variantes")
    private String grVariantes;

    @Column(name = "client_name")
    private String clientName;

    @Column(name = "distinataire")
    private String distinataire;

    @Column(name = "line_count")
    private Integer lineCount;

    @Column(name = "packs_nb")
    private Integer packsNb;

    @Column(name = "purchase_amount")
    private Double purchaseAmount;

    @Column(name = "volume")
    private Double volume;

    @Column(name = "gross_weight")
    private Double grossWeight;

    @Column(name = "client_code")
    private String clientCode;

    @Column(name = "contract")
    private String contract;

    @Column(name = "sector")
    private String sector;

    @Column(name = "commissionar")
    private String commissionar;

    @Column(name = "observation", columnDefinition = "TEXT")
    private String observation;

    @Column(name = "billing_info", columnDefinition = "TEXT")
    private String billingInfo;

    @Column(name = "total_ttc")
    private Double totalTtc;

    @Column(name = "total_ht")
    private Double totalHt;

    @Column(name = "total_quantity")
    private Double totalQuantity;

    @Column(name = "tax_base")
    private Double taxBase;

    @Column(name = "tax_amount")
    private Double taxAmount;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "order")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "emailsNotifications", "order" }, allowSetters = true)
    private Set<OrderDetails> orderDetails = new HashSet<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "company" }, allowSetters = true)
    private DailyBatches dailyBatches;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "cadencier" }, allowSetters = true)
    private GmsClients gmsClients;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "employee" }, allowSetters = true)
    private TemplateConditions templateConditions;

    @ManyToOne(fetch = FetchType.LAZY)
    private Mails mails;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee employee;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Order id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getHearderJson() {
        return this.hearderJson;
    }

    public Order hearderJson(String hearderJson) {
        this.setHearderJson(hearderJson);
        return this;
    }

    public void setHearderJson(String hearderJson) {
        this.hearderJson = hearderJson;
    }

    public Integer getRank() {
        return this.rank;
    }

    public Order rank(Integer rank) {
        this.setRank(rank);
        return this;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public OrderStatus getStatus() {
        return this.status;
    }

    public Order status(OrderStatus status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    public Boolean getLocked() {
        return this.locked;
    }

    public Order locked(Boolean locked) {
        this.setLocked(locked);
        return this;
    }

    public void setLocked(Boolean locked) {
        this.locked = locked;
    }

    public String getOrderNumber() {
        return this.orderNumber;
    }

    public Order orderNumber(String orderNumber) {
        this.setOrderNumber(orderNumber);
        return this;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public Long getBrandId() {
        return this.brandId;
    }

    public Order brandId(Long brandId) {
        this.setBrandId(brandId);
        return this;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public String getFooterJson() {
        return this.footerJson;
    }

    public Order footerJson(String footerJson) {
        this.setFooterJson(footerJson);
        return this;
    }

    public void setFooterJson(String footerJson) {
        this.footerJson = footerJson;
    }

    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getFiscaleId() {
        return fiscaleId;
    }

    public void setFiscaleId(String fiscaleId) {
        this.fiscaleId = fiscaleId;
    }

    public String getDeliveryLocation() {
        return deliveryLocation;
    }

    public void setDeliveryLocation(String deliveryLocation) {
        this.deliveryLocation = deliveryLocation;
    }

    public LocalDate getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(LocalDate orderDate) {
        this.orderDate = orderDate;
    }

    public LocalDate getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(LocalDate deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getRayon() {
        return rayon;
    }

    public void setRayon(String rayon) {
        this.rayon = rayon;
    }

    public String getGrVariantes() {
        return grVariantes;
    }

    public void setGrVariantes(String grVariantes) {
        this.grVariantes = grVariantes;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getDistinataire() {
        return distinataire;
    }

    public void setDistinataire(String distinataire) {
        this.distinataire = distinataire;
    }

    public Integer getLineCount() {
        return lineCount;
    }

    public void setLineCount(Integer lineCount) {
        this.lineCount = lineCount;
    }

    public Integer getPacksNb() {
        return packsNb;
    }

    public void setPacksNb(Integer packsNb) {
        this.packsNb = packsNb;
    }

    public Double getPurchaseAmount() {
        return purchaseAmount;
    }

    public void setPurchaseAmount(Double purchaseAmount) {
        this.purchaseAmount = purchaseAmount;
    }

    public Double getVolume() {
        return volume;
    }

    public void setVolume(Double volume) {
        this.volume = volume;
    }

    public Double getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(Double grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract;
    }

    public String getSector() {
        return sector;
    }

    public void setSector(String sector) {
        this.sector = sector;
    }

    public String getCommissionar() {
        return commissionar;
    }

    public void setCommissionar(String commissionar) {
        this.commissionar = commissionar;
    }

    public String getObservation() {
        return observation;
    }

    public void setObservation(String observation) {
        this.observation = observation;
    }

    public String getBillingInfo() {
        return billingInfo;
    }

    public void setBillingInfo(String billingInfo) {
        this.billingInfo = billingInfo;
    }

    public Double getTotalTtc() {
        return totalTtc;
    }

    public void setTotalTtc(Double totalTtc) {
        this.totalTtc = totalTtc;
    }

    public Double getTotalHt() {
        return totalHt;
    }

    public void setTotalHt(Double totalHt) {
        this.totalHt = totalHt;
    }

    public Double getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Double totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public Double getTaxBase() {
        return taxBase;
    }

    public void setTaxBase(Double taxBase) {
        this.taxBase = taxBase;
    }

    public Double getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(Double taxAmount) {
        this.taxAmount = taxAmount;
    }

    public Set<OrderDetails> getOrderDetails() {
        return this.orderDetails;
    }

    public void setOrderDetails(Set<OrderDetails> orderDetails) {
        if (this.orderDetails != null) {
            this.orderDetails.forEach(i -> i.setOrder(null));
        }
        if (orderDetails != null) {
            orderDetails.forEach(i -> i.setOrder(this));
        }
        this.orderDetails = orderDetails;
    }

    public Order orderDetails(Set<OrderDetails> orderDetails) {
        this.setOrderDetails(orderDetails);
        return this;
    }

    public Order addOrderDetails(OrderDetails orderDetails) {
        this.orderDetails.add(orderDetails);
        orderDetails.setOrder(this);
        return this;
    }

    public Order removeOrderDetails(OrderDetails orderDetails) {
        this.orderDetails.remove(orderDetails);
        orderDetails.setOrder(null);
        return this;
    }

    public DailyBatches getDailyBatches() {
        return this.dailyBatches;
    }

    public void setDailyBatches(DailyBatches dailyBatches) {
        this.dailyBatches = dailyBatches;
    }

    public Order dailyBatches(DailyBatches dailyBatches) {
        this.setDailyBatches(dailyBatches);
        return this;
    }

    public GmsClients getGmsClients() {
        return this.gmsClients;
    }

    public void setGmsClients(GmsClients gmsClients) {
        this.gmsClients = gmsClients;
    }

    public Order gmsClients(GmsClients gmsClients) {
        this.setGmsClients(gmsClients);
        return this;
    }

    public TemplateConditions getTemplateConditions() {
        return this.templateConditions;
    }

    public void setTemplateConditions(TemplateConditions templateConditions) {
        this.templateConditions = templateConditions;
    }

    public Order templateConditions(TemplateConditions templateConditions) {
        this.setTemplateConditions(templateConditions);
        return this;
    }

    public Mails getMails() {
        return this.mails;
    }

    public void setMails(Mails mails) {
        this.mails = mails;
    }

    public Order mails(Mails mails) {
        this.setMails(mails);
        return this;
    }

    public Employee getEmployee() {
        return this.employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }

    public Order employee(Employee employee) {
        this.setEmployee(employee);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Order)) {
            return false;
        }
        return getId() != null && getId().equals(((Order) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Order{" +
            "id=" + getId() +
            ", hearderJson='" + getHearderJson() + "'" +
            ", rank=" + getRank() +
            ", status='" + getStatus() + "'" +
            ", locked='" + getLocked() + "'" +
            ", orderNumber='" + getOrderNumber() + "'" +
            ", brandId=" + getBrandId() +
            ", footerJson='" + getFooterJson() + "'" +
            ", company='" + getCompany() + "'" +
            ", fiscaleId='" + getFiscaleId() + "'" +
            ", deliveryLocation='" + getDeliveryLocation() + "'" +
            ", orderDate=" + getOrderDate() +
            ", deliveryDate=" + getDeliveryDate() +
            ", rayon='" + getRayon() + "'" +
            ", grVariantes='" + getGrVariantes() + "'" +
            ", clientName='" + getClientName() + "'" +
            ", distinataire='" + getDistinataire() + "'" +
            ", lineCount=" + getLineCount() +
            ", packsNb=" + getPacksNb() +
            ", purchaseAmount=" + getPurchaseAmount() +
            ", volume=" + getVolume() +
            ", grossWeight=" + getGrossWeight() +
            ", clientCode='" + getClientCode() + "'" +
            ", contract='" + getContract() + "'" +
            ", sector='" + getSector() + "'" +
            ", commissionar='" + getCommissionar() + "'" +
            ", observation='" + getObservation() + "'" +
            ", billingInfo='" + getBillingInfo() + "'" +
            ", totalTtc=" + getTotalTtc() +
            ", totalHt=" + getTotalHt() +
            ", totalQuantity=" + getTotalQuantity() +
            ", taxBase=" + getTaxBase() +
            ", taxAmount=" + getTaxAmount() +
            "}";
    }
}
