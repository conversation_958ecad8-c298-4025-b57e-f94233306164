package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Deliverymodelang.
 */
@Entity
@Table(name = "deliverymodelang")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Deliverymodelang implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Size(max = 250)
    @Column(name = "lbl", length = 250)
    private String lbl;

    @Size(max = 5)
    @Column(name = "lang", length = 5)
    private String lang;

    @Size(max = 10)
    @Column(name = "abrv", length = 10)
    private String abrv;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "deleverymodelangs" }, allowSetters = true)
    private Deliverymode deliverymode;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Deliverymodelang id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLbl() {
        return this.lbl;
    }

    public Deliverymodelang lbl(String lbl) {
        this.setLbl(lbl);
        return this;
    }

    public void setLbl(String lbl) {
        this.lbl = lbl;
    }

    public String getLang() {
        return this.lang;
    }

    public Deliverymodelang lang(String lang) {
        this.setLang(lang);
        return this;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getAbrv() {
        return this.abrv;
    }

    public Deliverymodelang abrv(String abrv) {
        this.setAbrv(abrv);
        return this;
    }

    public void setAbrv(String abrv) {
        this.abrv = abrv;
    }

    public Deliverymode getDeliverymode() {
        return this.deliverymode;
    }

    public void setDeliverymode(Deliverymode deliverymode) {
        this.deliverymode = deliverymode;
    }

    public Deliverymodelang deliverymode(Deliverymode deliverymode) {
        this.setDeliverymode(deliverymode);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Deliverymodelang)) {
            return false;
        }
        return getId() != null && getId().equals(((Deliverymodelang) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Deliverymodelang{" +
            "id=" + getId() +
            ", lbl='" + getLbl() + "'" +
            ", lang='" + getLang() + "'" +
            ", abrv='" + getAbrv() + "'" +
            "}";
    }
}
