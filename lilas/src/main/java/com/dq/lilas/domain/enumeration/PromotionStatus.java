package com.dq.lilas.domain.enumeration;

/**
 * The PromotionStatus enumeration.
 */
public enum PromotionStatus {
    /**
     * Status: Waiting for approval
     */
    Waiting,

    /**
     * Status: Approved
     */
    Approved,

    /**
     * Status: Rejected
     */
    Rejected;

    /**
     * Get the string value of the enum.
     * @return the string value
     */
    public String getValue() {
        return this.name();
    }

    /**
     * Find the enum value from its string representation.
     * This handles case-insensitive lookup.
     *
     * @param value the string value to convert
     * @return the matching enum value, or null if not found
     */
    public static PromotionStatus fromValue(String value) {
        if (value == null) {
            return null;
        }

        for (PromotionStatus status : PromotionStatus.values()) {
            if (status.name().equalsIgnoreCase(value)) {
                return status;
            }
        }

        throw new IllegalArgumentException("No enum constant " + PromotionStatus.class.getName() + "." + value);
    }
}
