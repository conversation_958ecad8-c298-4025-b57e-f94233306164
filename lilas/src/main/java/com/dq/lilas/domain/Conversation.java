package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;

import jakarta.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Iterator;
import java.util.Objects;
import java.util.Set;

/**
 * A Conversation.
 */
@Entity
@Table(name = "conversation")
public class Conversation extends AbstractAuditingEntity implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    private Long id;

    @Column(name = "content")
    private String content;

    @ManyToOne
    @JsonIgnoreProperties("")
    private Room room;

    @ManyToOne
    @JsonIgnoreProperties("")
    private User sender;

    @OneToMany(mappedBy = "conversation", cascade = CascadeType.ALL, orphanRemoval = true)
    private Set<ConversationReceiver> conversationReceivers = new HashSet<ConversationReceiver>();

    // jhipster-needle-entity-add-field - JHipster will add fields here, do not remove
    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public Conversation content(String content) {
        this.content = content;
        return this;
    }

    public Room getRoom() {
        return room;
    }

    public void setRoom(Room room) {
        this.room = room;
    }

    public Conversation room(Room room) {
        this.room = room;
        return this;
    }

    public User getSender() {
        return sender;
    }

    public void setSender(User sender) {
        this.sender = sender;
    }

    public Conversation sender(User user) {
        this.sender = user;
        return this;
    }

    public Set<ConversationReceiver> getConversationReceivers() {
        return conversationReceivers;
    }

    public void setConversationReceivers(Set<ConversationReceiver> conversationReceivers) {
        this.conversationReceivers = conversationReceivers;
    }

    public void addConversationReceiver(ConversationReceiver conversationReceiver) {
        conversationReceivers.add(conversationReceiver);
    }

    public void removeConversationReceiver(ConversationReceiver conversationReceiver) {
        for (Iterator<ConversationReceiver> iterator = conversationReceivers.iterator();
             iterator.hasNext(); ) {
            ConversationReceiver conversationReceiverIt = iterator.next();
            if (conversationReceiverIt.getConversation().equals(conversationReceiver.getConversation()) &&
                conversationReceiverIt.getReceiver().equals(conversationReceiver.getReceiver())) {
                iterator.remove();
                conversationReceiverIt.setConversation(null);
                conversationReceiverIt.setReceiver(null);
            }
        }
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here, do not remove

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        Conversation conversation = (Conversation) o;
        if (conversation.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), conversation.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "Conversation{" +
            "id=" + getId() +
            ", content='" + getContent() + "'" +
            "}";
    }
}
