package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Typecorrespondence.
 */
@Entity
@Table(name = "typecorrespondence")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Typecorrespondence implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "typecorresp")
    private String typecorresp;

    @Column(name = "typedecision")
    private String typedecision;

    @Column(name = "hosp")
    private String hosp;

    @Column(name = "lbl")
    private String lbl;

    @Column(name = "statut")
    private String statut;

    @Column(name = "abbreviated")
    private String abbreviated;

    @Column(name = "speech")
    private String speech;

    @Column(name = "nbrnotifbeforerec")
    private Long nbrnotifbeforerec;

    @Column(name = "nbrnotifafterrec")
    private Long nbrnotifafterrec;

    @Column(name = "archive_subjects_id")
    private String archiveSubjectsId;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "typecorrespondence")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "typecorrespondence" }, allowSetters = true)
    private Set<Typecorrespondencelang> typecorrespondences = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Typecorrespondence id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTypecorresp() {
        return this.typecorresp;
    }

    public Typecorrespondence typecorresp(String typecorresp) {
        this.setTypecorresp(typecorresp);
        return this;
    }

    public void setTypecorresp(String typecorresp) {
        this.typecorresp = typecorresp;
    }

    public String getTypedecision() {
        return this.typedecision;
    }

    public Typecorrespondence typedecision(String typedecision) {
        this.setTypedecision(typedecision);
        return this;
    }

    public void setTypedecision(String typedecision) {
        this.typedecision = typedecision;
    }

    public String getHosp() {
        return this.hosp;
    }

    public Typecorrespondence hosp(String hosp) {
        this.setHosp(hosp);
        return this;
    }

    public void setHosp(String hosp) {
        this.hosp = hosp;
    }

    public String getLbl() {
        return this.lbl;
    }

    public Typecorrespondence lbl(String lbl) {
        this.setLbl(lbl);
        return this;
    }

    public void setLbl(String lbl) {
        this.lbl = lbl;
    }

    public String getStatut() {
        return this.statut;
    }

    public Typecorrespondence statut(String statut) {
        this.setStatut(statut);
        return this;
    }

    public void setStatut(String statut) {
        this.statut = statut;
    }

    public String getAbbreviated() {
        return this.abbreviated;
    }

    public Typecorrespondence abbreviated(String abbreviated) {
        this.setAbbreviated(abbreviated);
        return this;
    }

    public void setAbbreviated(String abbreviated) {
        this.abbreviated = abbreviated;
    }

    public String getSpeech() {
        return this.speech;
    }

    public Typecorrespondence speech(String speech) {
        this.setSpeech(speech);
        return this;
    }

    public void setSpeech(String speech) {
        this.speech = speech;
    }

    public Long getNbrnotifbeforerec() {
        return this.nbrnotifbeforerec;
    }

    public Typecorrespondence nbrnotifbeforerec(Long nbrnotifbeforerec) {
        this.setNbrnotifbeforerec(nbrnotifbeforerec);
        return this;
    }

    public void setNbrnotifbeforerec(Long nbrnotifbeforerec) {
        this.nbrnotifbeforerec = nbrnotifbeforerec;
    }

    public Long getNbrnotifafterrec() {
        return this.nbrnotifafterrec;
    }

    public Typecorrespondence nbrnotifafterrec(Long nbrnotifafterrec) {
        this.setNbrnotifafterrec(nbrnotifafterrec);
        return this;
    }

    public void setNbrnotifafterrec(Long nbrnotifafterrec) {
        this.nbrnotifafterrec = nbrnotifafterrec;
    }

    public String getArchiveSubjectsId() {
        return this.archiveSubjectsId;
    }

    public Typecorrespondence archiveSubjectsId(String archiveSubjectsId) {
        this.setArchiveSubjectsId(archiveSubjectsId);
        return this;
    }

    public void setArchiveSubjectsId(String archiveSubjectsId) {
        this.archiveSubjectsId = archiveSubjectsId;
    }

    public Set<Typecorrespondencelang> getTypecorrespondences() {
        return this.typecorrespondences;
    }

    public void setTypecorrespondences(Set<Typecorrespondencelang> typecorrespondencelangs) {
        if (this.typecorrespondences != null) {
            this.typecorrespondences.forEach(i -> i.setTypecorrespondence(null));
        }
        if (typecorrespondencelangs != null) {
            typecorrespondencelangs.forEach(i -> i.setTypecorrespondence(this));
        }
        this.typecorrespondences = typecorrespondencelangs;
    }

    public Typecorrespondence typecorrespondences(Set<Typecorrespondencelang> typecorrespondencelangs) {
        this.setTypecorrespondences(typecorrespondencelangs);
        return this;
    }

    public Typecorrespondence addTypecorrespondence(Typecorrespondencelang typecorrespondencelang) {
        this.typecorrespondences.add(typecorrespondencelang);
        typecorrespondencelang.setTypecorrespondence(this);
        return this;
    }

    public Typecorrespondence removeTypecorrespondence(Typecorrespondencelang typecorrespondencelang) {
        this.typecorrespondences.remove(typecorrespondencelang);
        typecorrespondencelang.setTypecorrespondence(null);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Typecorrespondence)) {
            return false;
        }
        return getId() != null && getId().equals(((Typecorrespondence) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Typecorrespondence{" +
            "id=" + getId() +
            ", typecorresp='" + getTypecorresp() + "'" +
            ", typedecision='" + getTypedecision() + "'" +
            ", hosp='" + getHosp() + "'" +
            ", lbl='" + getLbl() + "'" +
            ", statut='" + getStatut() + "'" +
            ", abbreviated='" + getAbbreviated() + "'" +
            ", speech='" + getSpeech() + "'" +
            ", nbrnotifbeforerec=" + getNbrnotifbeforerec() +
            ", nbrnotifafterrec=" + getNbrnotifafterrec() +
            ", archiveSubjectsId='" + getArchiveSubjectsId() + "'" +
            "}";
    }
}
