package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import java.io.Serializable;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A EmployeeGmsBrands.
 */
@Entity
@Table(name = "employee_gms_brands")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class EmployeeGmsBrands implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Column(name = "assignment_date")
    private Instant assignmentDate;

    @Column(name = "status")
    private Boolean status;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee employee;

    @ManyToOne(fetch = FetchType.LAZY)
    private GmsBrands gmsBrands;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public EmployeeGmsBrands id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Instant getAssignmentDate() {
        return this.assignmentDate;
    }

    public EmployeeGmsBrands assignmentDate(Instant assignmentDate) {
        this.setAssignmentDate(assignmentDate);
        return this;
    }

    public void setAssignmentDate(Instant assignmentDate) {
        this.assignmentDate = assignmentDate;
    }

    public Boolean getStatus() {
        return this.status;
    }

    public EmployeeGmsBrands status(Boolean status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public Employee getEmployee() {
        return this.employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }

    public EmployeeGmsBrands employee(Employee employee) {
        this.setEmployee(employee);
        return this;
    }

    public GmsBrands getGmsBrands() {
        return this.gmsBrands;
    }

    public void setGmsBrands(GmsBrands gmsBrands) {
        this.gmsBrands = gmsBrands;
    }

    public EmployeeGmsBrands gmsBrands(GmsBrands gmsBrands) {
        this.setGmsBrands(gmsBrands);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof EmployeeGmsBrands)) {
            return false;
        }
        return getId() != null && getId().equals(((EmployeeGmsBrands) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "EmployeeGmsBrands{" +
            "id=" + getId() +
            ", assignmentDate='" + getAssignmentDate() + "'" +
            ", status='" + getStatus() + "'" +
            "}";
    }
}
