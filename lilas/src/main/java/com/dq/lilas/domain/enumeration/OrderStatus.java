package com.dq.lilas.domain.enumeration;

/**
 * The OrderStatus enumeration.
 */
public enum OrderStatus {
    /**
     * Order is waiting for processing
     */
    WAITING,

    /**
     * Order has been verified
     */
    VERIFIED,

    /**
     * Order has been validated
     */
    VALIDATED,

    /**
     * Order has been injected into the system
     */
    INJECTED,

    /**
     * Order has been delivered
     */
    DELIVERED,

    /**
     * Order has been paid
     */
    PAID,

    /**
     * Order has been cancelled
     */
    CANCELLED
}
