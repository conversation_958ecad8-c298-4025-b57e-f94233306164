package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Action.
 */
@Entity
@Table(name = "action")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Action implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Size(max = 1)
    @Column(name = "regulation", length = 1)
    private String regulation;

    @Size(max = 1)
    @Column(name = "circular", length = 1)
    private String circular;

    @Column(name = "app_order")
    private Integer appOrder;

    @Size(max = 1)
    @Column(name = "statut", length = 1)
    private String statut;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "action")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "action" }, allowSetters = true)
    private Set<Actionlang> actionlangs = new HashSet<>();

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Action id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getRegulation() {
        return this.regulation;
    }

    public Action regulation(String regulation) {
        this.setRegulation(regulation);
        return this;
    }

    public void setRegulation(String regulation) {
        this.regulation = regulation;
    }

    public String getCircular() {
        return this.circular;
    }

    public Action circular(String circular) {
        this.setCircular(circular);
        return this;
    }

    public void setCircular(String circular) {
        this.circular = circular;
    }

    public Integer getAppOrder() {
        return this.appOrder;
    }

    public Action appOrder(Integer appOrder) {
        this.setAppOrder(appOrder);
        return this;
    }

    public void setAppOrder(Integer appOrder) {
        this.appOrder = appOrder;
    }

    public String getStatut() {
        return this.statut;
    }

    public Action statut(String statut) {
        this.setStatut(statut);
        return this;
    }

    public void setStatut(String statut) {
        this.statut = statut;
    }

    public Set<Actionlang> getActionlangs() {
        return this.actionlangs;
    }

    public void setActionlangs(Set<Actionlang> actionlangs) {
        if (this.actionlangs != null) {
            this.actionlangs.forEach(i -> i.setAction(null));
        }
        if (actionlangs != null) {
            actionlangs.forEach(i -> i.setAction(this));
        }
        this.actionlangs = actionlangs;
    }

    public Action actionlangs(Set<Actionlang> actionlangs) {
        this.setActionlangs(actionlangs);
        return this;
    }

    public Action addActionlangs(Actionlang actionlang) {
        this.actionlangs.add(actionlang);
        actionlang.setAction(this);
        return this;
    }

    public Action removeActionlangs(Actionlang actionlang) {
        this.actionlangs.remove(actionlang);
        actionlang.setAction(null);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Action)) {
            return false;
        }
        return getId() != null && getId().equals(((Action) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Action{" +
            "id=" + getId() +
            ", regulation='" + getRegulation() + "'" +
            ", circular='" + getCircular() + "'" +
            ", appOrder=" + getAppOrder() +
            ", statut='" + getStatut() + "'" +
            "}";
    }
}
