package com.dq.lilas.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A StockData.
 */
@Entity
@Table(name = "stock_data", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"code_article", "sheet_type", "company", "date_snapshot"}))
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class StockData implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @NotNull
    @Size(max = 50)
    @Column(name = "code_article", length = 50, nullable = false)
    private String codeArticle;

    @Lob
    @Column(name = "description")
    private String description;

    @Size(max = 20)
    @Column(name = "colisage", length = 20)
    private String colisage;

    @Column(name = "stock_quantity", precision = 15, scale = 2)
    private BigDecimal stockQuantity = BigDecimal.ZERO;

    @Size(max = 50)
    @Column(name = "stock_type", length = 50)
    private String stockType;

    @Column(name = "date_snapshot")
    private LocalDate dateSnapshot;

    @Size(max = 200)
    @Column(name = "source_file", length = 200)
    private String sourceFile;

    @Size(max = 100)
    @Column(name = "sheet_type", length = 100)
    private String sheetType;

    @NotNull
    @Size(max = 10)
    @Column(name = "company", length = 10, nullable = false)
    private String company;

    @Column(name = "created_at")
    private Instant createdAt;

    @Column(name = "updated_at")
    private Instant updatedAt;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    @PrePersist
    public void prePersist() {
        if (this.createdAt == null) {
            this.createdAt = Instant.now();
        }
        this.updatedAt = Instant.now();
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = Instant.now();
    }

    public Long getId() {
        return this.id;
    }

    public StockData id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCodeArticle() {
        return this.codeArticle;
    }

    public StockData codeArticle(String codeArticle) {
        this.setCodeArticle(codeArticle);
        return this;
    }

    public void setCodeArticle(String codeArticle) {
        this.codeArticle = codeArticle;
    }

    public String getDescription() {
        return this.description;
    }

    public StockData description(String description) {
        this.setDescription(description);
        return this;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getColisage() {
        return this.colisage;
    }

    public StockData colisage(String colisage) {
        this.setColisage(colisage);
        return this;
    }

    public void setColisage(String colisage) {
        this.colisage = colisage;
    }

    public BigDecimal getStockQuantity() {
        return this.stockQuantity;
    }

    public StockData stockQuantity(BigDecimal stockQuantity) {
        this.setStockQuantity(stockQuantity);
        return this;
    }

    public void setStockQuantity(BigDecimal stockQuantity) {
        this.stockQuantity = stockQuantity;
    }

    public String getStockType() {
        return this.stockType;
    }

    public StockData stockType(String stockType) {
        this.setStockType(stockType);
        return this;
    }

    public void setStockType(String stockType) {
        this.stockType = stockType;
    }

    public LocalDate getDateSnapshot() {
        return this.dateSnapshot;
    }

    public StockData dateSnapshot(LocalDate dateSnapshot) {
        this.setDateSnapshot(dateSnapshot);
        return this;
    }

    public void setDateSnapshot(LocalDate dateSnapshot) {
        this.dateSnapshot = dateSnapshot;
    }

    public String getSourceFile() {
        return this.sourceFile;
    }

    public StockData sourceFile(String sourceFile) {
        this.setSourceFile(sourceFile);
        return this;
    }

    public void setSourceFile(String sourceFile) {
        this.sourceFile = sourceFile;
    }

    public String getSheetType() {
        return this.sheetType;
    }

    public StockData sheetType(String sheetType) {
        this.setSheetType(sheetType);
        return this;
    }

    public void setSheetType(String sheetType) {
        this.sheetType = sheetType;
    }

    public String getCompany() {
        return this.company;
    }

    public StockData company(String company) {
        this.setCompany(company);
        return this;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public StockData createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public StockData updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof StockData)) {
            return false;
        }
        return getId() != null && getId().equals(((StockData) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "StockData{" +
            "id=" + getId() +
            ", codeArticle='" + getCodeArticle() + "'" +
            ", description='" + getDescription() + "'" +
            ", colisage='" + getColisage() + "'" +
            ", stockQuantity=" + getStockQuantity() +
            ", stockType='" + getStockType() + "'" +
            ", dateSnapshot='" + getDateSnapshot() + "'" +
            ", sourceFile='" + getSourceFile() + "'" +
            ", sheetType='" + getSheetType() + "'" +
            ", company='" + getCompany() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            "}";
    }
}
