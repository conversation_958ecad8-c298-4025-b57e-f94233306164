package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.HashSet;
import java.util.Set;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Unit.
 */
@Entity
@Table(name = "unit")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Unit implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Size(max = 15)
    @Column(name = "parentid", length = 15)
    private String parentid;

    @Size(max = 15)
    @Column(name = "tel", length = 15)
    private String tel;

    @Size(max = 15)
    @Column(name = "fax", length = 15)
    private String fax;

    @Column(name = "jhi_order")
    private Integer order;

    @Size(max = 1)
    @Column(name = "deanstatus", length = 1)
    private String deanstatus;

    @Size(max = 1000)
    @Column(name = "address", length = 1000)
    private String address;

    @Size(max = 15)
    @Column(name = "nbr", length = 15)
    private String nbr;

    @Size(max = 1000)
    @Column(name = "name", length = 1000)
    private String name;

    @Size(max = 5)
    @Column(name = "lang", length = 5)
    private String lang;

    @Size(max = 15)
    @Column(name = "abbreviated", length = 15)
    private String abbreviated;

    @Size(max = 100)
    @Column(name = "mail", length = 100)
    private String mail;

    @Size(max = 20)
    @Column(name = "checkenabled", length = 20)
    private String checkenabled;

    @Size(max = 20)
    @Column(name = "activate", length = 20)
    private String activate;

    @Size(max = 20)
    @Column(name = "active", length = 20)
    private String active;

    @Size(max = 20)
    @Column(name = "level", length = 20)
    private String level;

    @Size(max = 20)
    @Column(name = "grp", length = 20)
    private String grp;

    @Size(max = 20)
    @Column(name = "compid", length = 20)
    private String compid;

    @Size(max = 20)
    @Column(name = "branch", length = 20)
    private String branch;

    @Size(max = 20)
    @Column(name = "regoffice", length = 20)
    private String regoffice;

    @Size(max = 20)
    @Column(name = "unitgroup", length = 20)
    private String unitgroup;

    @Size(max = 20)
    @Column(name = "grdparent", length = 20)
    private String grdparent;

    @Size(max = 1)
    @Column(name = "status", length = 1)
    private String status;

    @Column(name = "category")
    private String category;

    @Column(name = "function_unit")
    private String functionUnit;

    @Column(name = "position")
    private String position;

    @Column(name = "section")
    private String section;

    @Column(name = "categdir")
    private String categdir;

    @Column(name = "categ_unit")
    private String categUnit;

    @Column(name = "function")
    private String function;

    @Size(max = 15)
    @Column(name = "responsible", length = 15)
    private String responsible;

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "unit")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "unit" }, allowSetters = true)
    private Set<Unitlang> unitlangs = new HashSet<>();

    @OneToMany(fetch = FetchType.LAZY, mappedBy = "unit")
    @Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Set<Employee> employees = new HashSet<>();

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "units" }, allowSetters = true)
    private Company company;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Unit id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getParentid() {
        return this.parentid;
    }

    public Unit parentid(String parentid) {
        this.setParentid(parentid);
        return this;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }

    public String getTel() {
        return this.tel;
    }

    public Unit tel(String tel) {
        this.setTel(tel);
        return this;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getFax() {
        return this.fax;
    }

    public Unit fax(String fax) {
        this.setFax(fax);
        return this;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public Integer getOrder() {
        return this.order;
    }

    public Unit order(Integer order) {
        this.setOrder(order);
        return this;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getDeanstatus() {
        return this.deanstatus;
    }

    public Unit deanstatus(String deanstatus) {
        this.setDeanstatus(deanstatus);
        return this;
    }

    public void setDeanstatus(String deanstatus) {
        this.deanstatus = deanstatus;
    }

    public String getAddress() {
        return this.address;
    }

    public Unit address(String address) {
        this.setAddress(address);
        return this;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNbr() {
        return this.nbr;
    }

    public Unit nbr(String nbr) {
        this.setNbr(nbr);
        return this;
    }

    public void setNbr(String nbr) {
        this.nbr = nbr;
    }

    public String getName() {
        return this.name;
    }

    public Unit name(String name) {
        this.setName(name);
        return this;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLang() {
        return this.lang;
    }

    public Unit lang(String lang) {
        this.setLang(lang);
        return this;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getAbbreviated() {
        return this.abbreviated;
    }

    public Unit abbreviated(String abbreviated) {
        this.setAbbreviated(abbreviated);
        return this;
    }

    public void setAbbreviated(String abbreviated) {
        this.abbreviated = abbreviated;
    }

    public String getMail() {
        return this.mail;
    }

    public Unit mail(String mail) {
        this.setMail(mail);
        return this;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getCheckenabled() {
        return this.checkenabled;
    }

    public Unit checkenabled(String checkenabled) {
        this.setCheckenabled(checkenabled);
        return this;
    }

    public void setCheckenabled(String checkenabled) {
        this.checkenabled = checkenabled;
    }

    public String getActivate() {
        return this.activate;
    }

    public Unit activate(String activate) {
        this.setActivate(activate);
        return this;
    }

    public void setActivate(String activate) {
        this.activate = activate;
    }

    public String getActive() {
        return this.active;
    }

    public Unit active(String active) {
        this.setActive(active);
        return this;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getLevel() {
        return this.level;
    }

    public Unit level(String level) {
        this.setLevel(level);
        return this;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getGrp() {
        return this.grp;
    }

    public Unit grp(String grp) {
        this.setGrp(grp);
        return this;
    }

    public void setGrp(String grp) {
        this.grp = grp;
    }

    public String getCompid() {
        return this.compid;
    }

    public Unit compid(String compid) {
        this.setCompid(compid);
        return this;
    }

    public void setCompid(String compid) {
        this.compid = compid;
    }

    public String getBranch() {
        return this.branch;
    }

    public Unit branch(String branch) {
        this.setBranch(branch);
        return this;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getRegoffice() {
        return this.regoffice;
    }

    public Unit regoffice(String regoffice) {
        this.setRegoffice(regoffice);
        return this;
    }

    public void setRegoffice(String regoffice) {
        this.regoffice = regoffice;
    }

    public String getUnitgroup() {
        return this.unitgroup;
    }

    public Unit unitgroup(String unitgroup) {
        this.setUnitgroup(unitgroup);
        return this;
    }

    public void setUnitgroup(String unitgroup) {
        this.unitgroup = unitgroup;
    }

    public String getGrdparent() {
        return this.grdparent;
    }

    public Unit grdparent(String grdparent) {
        this.setGrdparent(grdparent);
        return this;
    }

    public void setGrdparent(String grdparent) {
        this.grdparent = grdparent;
    }

    public String getStatus() {
        return this.status;
    }

    public Unit status(String status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCategory() {
        return this.category;
    }

    public Unit category(String category) {
        this.setCategory(category);
        return this;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getFunctionUnit() {
        return this.functionUnit;
    }

    public Unit functionUnit(String functionUnit) {
        this.setFunctionUnit(functionUnit);
        return this;
    }

    public void setFunctionUnit(String functionUnit) {
        this.functionUnit = functionUnit;
    }

    public String getPosition() {
        return this.position;
    }

    public Unit position(String position) {
        this.setPosition(position);
        return this;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getSection() {
        return this.section;
    }

    public Unit section(String section) {
        this.setSection(section);
        return this;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public String getCategdir() {
        return this.categdir;
    }

    public Unit categdir(String categdir) {
        this.setCategdir(categdir);
        return this;
    }

    public void setCategdir(String categdir) {
        this.categdir = categdir;
    }

    public String getCategUnit() {
        return this.categUnit;
    }

    public Unit categUnit(String categUnit) {
        this.setCategUnit(categUnit);
        return this;
    }

    public void setCategUnit(String categUnit) {
        this.categUnit = categUnit;
    }

    public String getFunction() {
        return this.function;
    }

    public Unit function(String function) {
        this.setFunction(function);
        return this;
    }

    public void setFunction(String function) {
        this.function = function;
    }

    public String getResponsible() {
        return this.responsible;
    }

    public Unit responsible(String responsible) {
        this.setResponsible(responsible);
        return this;
    }

    public void setResponsible(String responsible) {
        this.responsible = responsible;
    }

    public Set<Unitlang> getUnitlangs() {
        return this.unitlangs;
    }

    public void setUnitlangs(Set<Unitlang> unitlangs) {
        if (this.unitlangs != null) {
            this.unitlangs.forEach(i -> i.setUnit(null));
        }
        if (unitlangs != null) {
            unitlangs.forEach(i -> i.setUnit(this));
        }
        this.unitlangs = unitlangs;
    }

    public Unit unitlangs(Set<Unitlang> unitlangs) {
        this.setUnitlangs(unitlangs);
        return this;
    }

    public Unit addUnitlangs(Unitlang unitlang) {
        this.unitlangs.add(unitlang);
        unitlang.setUnit(this);
        return this;
    }

    public Unit removeUnitlangs(Unitlang unitlang) {
        this.unitlangs.remove(unitlang);
        unitlang.setUnit(null);
        return this;
    }

    public Set<Employee> getEmployees() {
        return this.employees;
    }

    public void setEmployees(Set<Employee> employees) {
        if (this.employees != null) {
            this.employees.forEach(i -> i.setUnit(null));
        }
        if (employees != null) {
            employees.forEach(i -> i.setUnit(this));
        }
        this.employees = employees;
    }

    public Unit employees(Set<Employee> employees) {
        this.setEmployees(employees);
        return this;
    }

    public Unit addEmployees(Employee employee) {
        this.employees.add(employee);
        employee.setUnit(this);
        return this;
    }

    public Unit removeEmployees(Employee employee) {
        this.employees.remove(employee);
        employee.setUnit(null);
        return this;
    }

    public Company getCompany() {
        return this.company;
    }

    public void setCompany(Company company) {
        this.company = company;
    }

    public Unit company(Company company) {
        this.setCompany(company);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Unit)) {
            return false;
        }
        return getId() != null && getId().equals(((Unit) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Unit{" +
            "id=" + getId() +
            ", parentid='" + getParentid() + "'" +
            ", tel='" + getTel() + "'" +
            ", fax='" + getFax() + "'" +
            ", order=" + getOrder() +
            ", deanstatus='" + getDeanstatus() + "'" +
            ", address='" + getAddress() + "'" +
            ", nbr='" + getNbr() + "'" +
            ", name='" + getName() + "'" +
            ", lang='" + getLang() + "'" +
            ", abbreviated='" + getAbbreviated() + "'" +
            ", mail='" + getMail() + "'" +
            ", checkenabled='" + getCheckenabled() + "'" +
            ", activate='" + getActivate() + "'" +
            ", active='" + getActive() + "'" +
            ", level='" + getLevel() + "'" +
            ", grp='" + getGrp() + "'" +
            ", compid='" + getCompid() + "'" +
            ", branch='" + getBranch() + "'" +
            ", regoffice='" + getRegoffice() + "'" +
            ", unitgroup='" + getUnitgroup() + "'" +
            ", grdparent='" + getGrdparent() + "'" +
            ", status='" + getStatus() + "'" +
            ", category='" + getCategory() + "'" +
            ", functionUnit='" + getFunctionUnit() + "'" +
            ", position='" + getPosition() + "'" +
            ", section='" + getSection() + "'" +
            ", categdir='" + getCategdir() + "'" +
            ", categUnit='" + getCategUnit() + "'" +
            ", function='" + getFunction() + "'" +
            ", responsible='" + getResponsible() + "'" +
            "}";
    }
}
