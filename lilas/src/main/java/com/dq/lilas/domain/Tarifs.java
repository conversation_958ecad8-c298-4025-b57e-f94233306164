package com.dq.lilas.domain;

import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.math.BigDecimal;
import java.time.Instant;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Tarifs.
 */
@Entity
@Table(name = "tarifs", 
       uniqueConstraints = @UniqueConstraint(columnNames = {"codes_articles", "codes_client", "pieces", "company"}))
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Tarifs implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @NotNull
    @Size(max = 50)
    @Column(name = "codes_articles", length = 50, nullable = false)
    private String codesArticles;

    @NotNull
    @Size(max = 10)
    @Column(name = "pieces", length = 10, nullable = false)
    private String pieces;

    @NotNull
    @Size(max = 50)
    @Column(name = "codes_client", length = 50, nullable = false)
    private String codesClient;

    @Column(name = "prix", precision = 10, scale = 2)
    private BigDecimal prix = BigDecimal.ZERO;

    @Column(name = "remise", precision = 10, scale = 2)
    private BigDecimal remise = BigDecimal.ZERO;

    @Column(name = "prix_avec_remise", precision = 10, scale = 2)
    private BigDecimal prixAvecRemise = BigDecimal.ZERO;

    @NotNull
    @Size(max = 10)
    @Column(name = "company", length = 10, nullable = false)
    private String company;

    @Column(name = "created_at")
    private Instant createdAt;

    @Column(name = "updated_at")
    private Instant updatedAt;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    @PrePersist
    public void prePersist() {
        if (this.createdAt == null) {
            this.createdAt = Instant.now();
        }
        this.updatedAt = Instant.now();
    }

    @PreUpdate
    public void preUpdate() {
        this.updatedAt = Instant.now();
    }

    public Long getId() {
        return this.id;
    }

    public Tarifs id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCodesArticles() {
        return this.codesArticles;
    }

    public Tarifs codesArticles(String codesArticles) {
        this.setCodesArticles(codesArticles);
        return this;
    }

    public void setCodesArticles(String codesArticles) {
        this.codesArticles = codesArticles;
    }

    public String getPieces() {
        return this.pieces;
    }

    public Tarifs pieces(String pieces) {
        this.setPieces(pieces);
        return this;
    }

    public void setPieces(String pieces) {
        this.pieces = pieces;
    }

    public String getCodesClient() {
        return this.codesClient;
    }

    public Tarifs codesClient(String codesClient) {
        this.setCodesClient(codesClient);
        return this;
    }

    public void setCodesClient(String codesClient) {
        this.codesClient = codesClient;
    }

    public BigDecimal getPrix() {
        return this.prix;
    }

    public Tarifs prix(BigDecimal prix) {
        this.setPrix(prix);
        return this;
    }

    public void setPrix(BigDecimal prix) {
        this.prix = prix;
    }

    public BigDecimal getRemise() {
        return this.remise;
    }

    public Tarifs remise(BigDecimal remise) {
        this.setRemise(remise);
        return this;
    }

    public void setRemise(BigDecimal remise) {
        this.remise = remise;
    }

    public BigDecimal getPrixAvecRemise() {
        return this.prixAvecRemise;
    }

    public Tarifs prixAvecRemise(BigDecimal prixAvecRemise) {
        this.setPrixAvecRemise(prixAvecRemise);
        return this;
    }

    public void setPrixAvecRemise(BigDecimal prixAvecRemise) {
        this.prixAvecRemise = prixAvecRemise;
    }

    public String getCompany() {
        return this.company;
    }

    public Tarifs company(String company) {
        this.setCompany(company);
        return this;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public Instant getCreatedAt() {
        return this.createdAt;
    }

    public Tarifs createdAt(Instant createdAt) {
        this.setCreatedAt(createdAt);
        return this;
    }

    public void setCreatedAt(Instant createdAt) {
        this.createdAt = createdAt;
    }

    public Instant getUpdatedAt() {
        return this.updatedAt;
    }

    public Tarifs updatedAt(Instant updatedAt) {
        this.setUpdatedAt(updatedAt);
        return this;
    }

    public void setUpdatedAt(Instant updatedAt) {
        this.updatedAt = updatedAt;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Tarifs)) {
            return false;
        }
        return getId() != null && getId().equals(((Tarifs) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Tarifs{" +
            "id=" + getId() +
            ", codesArticles='" + getCodesArticles() + "'" +
            ", pieces='" + getPieces() + "'" +
            ", codesClient='" + getCodesClient() + "'" +
            ", prix=" + getPrix() +
            ", remise=" + getRemise() +
            ", prixAvecRemise=" + getPrixAvecRemise() +
            ", company='" + getCompany() + "'" +
            ", createdAt='" + getCreatedAt() + "'" +
            ", updatedAt='" + getUpdatedAt() + "'" +
            "}";
    }
}
