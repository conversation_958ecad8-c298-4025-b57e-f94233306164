package com.dq.lilas.domain;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import jakarta.persistence.*;
import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.ZonedDateTime;
import org.hibernate.annotations.Cache;
import org.hibernate.annotations.CacheConcurrencyStrategy;

/**
 * A Correspondence.
 */
@Entity
@Table(name = "correspondence")
@Cache(usage = CacheConcurrencyStrategy.READ_WRITE)
@SuppressWarnings("common-java:DuplicatedBlocks")
public class Correspondence implements Serializable {

    private static final long serialVersionUID = 1L;

    @Id
    @GeneratedValue(strategy = GenerationType.SEQUENCE, generator = "sequenceGenerator")
    @SequenceGenerator(name = "sequenceGenerator")
    @Column(name = "id")
    private Long id;

    @Size(max = 50)
    @Column(name = "ordernbr", length = 50)
    private String ordernbr;

    @Size(max = 15)
    @Column(name = "numcopy", length = 15)
    private String numcopy;

    @Size(max = 500)
    @Column(name = "doclink", length = 500)
    private String doclink;

    @Size(max = 200)
    @Column(name = "pagenbr", length = 200)
    private String pagenbr;

    @Size(max = 5)
    @Column(name = "highlevel", length = 5)
    private String highlevel;

    @Size(max = 10)
    @Column(name = "confidentiel", length = 10)
    private String confidentiel;

    @Size(max = 5)
    @Column(name = "priority", length = 5)
    private String priority;

    @Column(name = "obs")
    private String obs;

    @NotNull
    @Size(max = 5)
    @Column(name = "category", length = 5, nullable = false)
    private String category;

    @Size(max = 5)
    @Column(name = "status", length = 5)
    private String status;

    @Column(name = "datejc")
    private ZonedDateTime datejc;

    @Column(name = "datejcsend")
    private ZonedDateTime datejcsend;

    @Size(max = 1)
    @Column(name = "typereceive", length = 1)
    private String typereceive;

    @Size(max = 15)
    @Column(name = "typecopy", length = 15)
    private String typecopy;

    @Size(max = 500)
    @Column(name = "refsnd", length = 500)
    private String refsnd;

    @Size(max = 4000)
    @Column(name = "text", length = 4000)
    private String text;

    @Size(max = 4)
    @Column(name = "docyear", length = 4)
    private String docyear;

    @Size(max = 10)
    @Column(name = "datehjrsave", length = 10)
    private String datehjrsave;

    @Column(name = "datejcsave")
    private ZonedDateTime datejcsave;

    @Column(name = "old_order_number")
    private String oldOrderNumber;

    @NotNull
    @Size(max = 1000)
    @Column(name = "subject", length = 1000, nullable = false)
    private String subject;

    @Size(max = 200)
    @Column(name = "attach", length = 200)
    private String attach;

    @Size(max = 50)
    @Column(name = "ordernbradrsbook", length = 50)
    private String ordernbradrsbook;

    @Column(name = "seqkeyadrsbook")
    private Integer seqkeyadrsbook;

    @Size(max = 500)
    @Column(name = "useradrsbook", length = 500)
    private String useradrsbook;

    @Size(max = 15)
    @Column(name = "cityzenncard", length = 15)
    private String cityzenncard;

    @Size(max = 15)
    @Column(name = "cityzenphone", length = 15)
    private String cityzenphone;

    @Size(max = 1)
    @Column(name = "sms", length = 1)
    private String sms;

    @Size(max = 5)
    @Column(name = "incident", length = 5)
    private String incident;

    @Size(max = 1)
    @Column(name = "check_favorite", length = 1)
    private String checkFavorite;

    @Column(name = "company_id")
    private String companyId;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "user", "employeelangs", "job", "unit" }, allowSetters = true)
    private Employee employee;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "unitlangs", "employees", "company" }, allowSetters = true)
    private Unit unit;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "deleverymodelangs" }, allowSetters = true)
    private Deliverymode deliverymode;

    @ManyToOne(fetch = FetchType.LAZY)
    @JsonIgnoreProperties(value = { "typecorrespondences" }, allowSetters = true)
    private Typecorrespondence typecorrespondence;

    // jhipster-needle-entity-add-field - JHipster will add fields here

    public Long getId() {
        return this.id;
    }

    public Correspondence id(Long id) {
        this.setId(id);
        return this;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrdernbr() {
        return this.ordernbr;
    }

    public Correspondence ordernbr(String ordernbr) {
        this.setOrdernbr(ordernbr);
        return this;
    }

    public void setOrdernbr(String ordernbr) {
        this.ordernbr = ordernbr;
    }

    public String getNumcopy() {
        return this.numcopy;
    }

    public Correspondence numcopy(String numcopy) {
        this.setNumcopy(numcopy);
        return this;
    }

    public void setNumcopy(String numcopy) {
        this.numcopy = numcopy;
    }

    public String getDoclink() {
        return this.doclink;
    }

    public Correspondence doclink(String doclink) {
        this.setDoclink(doclink);
        return this;
    }

    public void setDoclink(String doclink) {
        this.doclink = doclink;
    }

    public String getPagenbr() {
        return this.pagenbr;
    }

    public Correspondence pagenbr(String pagenbr) {
        this.setPagenbr(pagenbr);
        return this;
    }

    public void setPagenbr(String pagenbr) {
        this.pagenbr = pagenbr;
    }

    public String getHighlevel() {
        return this.highlevel;
    }

    public Correspondence highlevel(String highlevel) {
        this.setHighlevel(highlevel);
        return this;
    }

    public void setHighlevel(String highlevel) {
        this.highlevel = highlevel;
    }

    public String getConfidentiel() {
        return this.confidentiel;
    }

    public Correspondence confidentiel(String confidentiel) {
        this.setConfidentiel(confidentiel);
        return this;
    }

    public void setConfidentiel(String confidentiel) {
        this.confidentiel = confidentiel;
    }

    public String getPriority() {
        return this.priority;
    }

    public Correspondence priority(String priority) {
        this.setPriority(priority);
        return this;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getObs() {
        return this.obs;
    }

    public Correspondence obs(String obs) {
        this.setObs(obs);
        return this;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }

    public String getCategory() {
        return this.category;
    }

    public Correspondence category(String category) {
        this.setCategory(category);
        return this;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getStatus() {
        return this.status;
    }

    public Correspondence status(String status) {
        this.setStatus(status);
        return this;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public ZonedDateTime getDatejc() {
        return this.datejc;
    }

    public Correspondence datejc(ZonedDateTime datejc) {
        this.setDatejc(datejc);
        return this;
    }

    public void setDatejc(ZonedDateTime datejc) {
        this.datejc = datejc;
    }

    public ZonedDateTime getDatejcsend() {
        return this.datejcsend;
    }

    public Correspondence datejcsend(ZonedDateTime datejcsend) {
        this.setDatejcsend(datejcsend);
        return this;
    }

    public void setDatejcsend(ZonedDateTime datejcsend) {
        this.datejcsend = datejcsend;
    }

    public String getTypereceive() {
        return this.typereceive;
    }

    public Correspondence typereceive(String typereceive) {
        this.setTypereceive(typereceive);
        return this;
    }

    public void setTypereceive(String typereceive) {
        this.typereceive = typereceive;
    }

    public String getTypecopy() {
        return this.typecopy;
    }

    public Correspondence typecopy(String typecopy) {
        this.setTypecopy(typecopy);
        return this;
    }

    public void setTypecopy(String typecopy) {
        this.typecopy = typecopy;
    }

    public String getRefsnd() {
        return this.refsnd;
    }

    public Correspondence refsnd(String refsnd) {
        this.setRefsnd(refsnd);
        return this;
    }

    public void setRefsnd(String refsnd) {
        this.refsnd = refsnd;
    }

    public String getText() {
        return this.text;
    }

    public Correspondence text(String text) {
        this.setText(text);
        return this;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getDocyear() {
        return this.docyear;
    }

    public Correspondence docyear(String docyear) {
        this.setDocyear(docyear);
        return this;
    }

    public void setDocyear(String docyear) {
        this.docyear = docyear;
    }

    public String getDatehjrsave() {
        return this.datehjrsave;
    }

    public Correspondence datehjrsave(String datehjrsave) {
        this.setDatehjrsave(datehjrsave);
        return this;
    }

    public void setDatehjrsave(String datehjrsave) {
        this.datehjrsave = datehjrsave;
    }

    public ZonedDateTime getDatejcsave() {
        return this.datejcsave;
    }

    public Correspondence datejcsave(ZonedDateTime datejcsave) {
        this.setDatejcsave(datejcsave);
        return this;
    }

    public void setDatejcsave(ZonedDateTime datejcsave) {
        this.datejcsave = datejcsave;
    }

    public String getOldOrderNumber() {
        return this.oldOrderNumber;
    }

    public Correspondence oldOrderNumber(String oldOrderNumber) {
        this.setOldOrderNumber(oldOrderNumber);
        return this;
    }

    public void setOldOrderNumber(String oldOrderNumber) {
        this.oldOrderNumber = oldOrderNumber;
    }

    public String getSubject() {
        return this.subject;
    }

    public Correspondence subject(String subject) {
        this.setSubject(subject);
        return this;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getAttach() {
        return this.attach;
    }

    public Correspondence attach(String attach) {
        this.setAttach(attach);
        return this;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }

    public String getOrdernbradrsbook() {
        return this.ordernbradrsbook;
    }

    public Correspondence ordernbradrsbook(String ordernbradrsbook) {
        this.setOrdernbradrsbook(ordernbradrsbook);
        return this;
    }

    public void setOrdernbradrsbook(String ordernbradrsbook) {
        this.ordernbradrsbook = ordernbradrsbook;
    }

    public Integer getSeqkeyadrsbook() {
        return this.seqkeyadrsbook;
    }

    public Correspondence seqkeyadrsbook(Integer seqkeyadrsbook) {
        this.setSeqkeyadrsbook(seqkeyadrsbook);
        return this;
    }

    public void setSeqkeyadrsbook(Integer seqkeyadrsbook) {
        this.seqkeyadrsbook = seqkeyadrsbook;
    }

    public String getUseradrsbook() {
        return this.useradrsbook;
    }

    public Correspondence useradrsbook(String useradrsbook) {
        this.setUseradrsbook(useradrsbook);
        return this;
    }

    public void setUseradrsbook(String useradrsbook) {
        this.useradrsbook = useradrsbook;
    }

    public String getCityzenncard() {
        return this.cityzenncard;
    }

    public Correspondence cityzenncard(String cityzenncard) {
        this.setCityzenncard(cityzenncard);
        return this;
    }

    public void setCityzenncard(String cityzenncard) {
        this.cityzenncard = cityzenncard;
    }

    public String getCityzenphone() {
        return this.cityzenphone;
    }

    public Correspondence cityzenphone(String cityzenphone) {
        this.setCityzenphone(cityzenphone);
        return this;
    }

    public void setCityzenphone(String cityzenphone) {
        this.cityzenphone = cityzenphone;
    }

    public String getSms() {
        return this.sms;
    }

    public Correspondence sms(String sms) {
        this.setSms(sms);
        return this;
    }

    public void setSms(String sms) {
        this.sms = sms;
    }

    public String getIncident() {
        return this.incident;
    }

    public Correspondence incident(String incident) {
        this.setIncident(incident);
        return this;
    }

    public void setIncident(String incident) {
        this.incident = incident;
    }

    public String getCheckFavorite() {
        return this.checkFavorite;
    }

    public Correspondence checkFavorite(String checkFavorite) {
        this.setCheckFavorite(checkFavorite);
        return this;
    }

    public void setCheckFavorite(String checkFavorite) {
        this.checkFavorite = checkFavorite;
    }

    public String getCompanyId() {
        return this.companyId;
    }

    public Correspondence companyId(String companyId) {
        this.setCompanyId(companyId);
        return this;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public Employee getEmployee() {
        return this.employee;
    }

    public void setEmployee(Employee employee) {
        this.employee = employee;
    }

    public Correspondence employee(Employee employee) {
        this.setEmployee(employee);
        return this;
    }

    public Unit getUnit() {
        return this.unit;
    }

    public void setUnit(Unit unit) {
        this.unit = unit;
    }

    public Correspondence unit(Unit unit) {
        this.setUnit(unit);
        return this;
    }

    public Deliverymode getDeliverymode() {
        return this.deliverymode;
    }

    public void setDeliverymode(Deliverymode deliverymode) {
        this.deliverymode = deliverymode;
    }

    public Correspondence deliverymode(Deliverymode deliverymode) {
        this.setDeliverymode(deliverymode);
        return this;
    }

    public Typecorrespondence getTypecorrespondence() {
        return this.typecorrespondence;
    }

    public void setTypecorrespondence(Typecorrespondence typecorrespondence) {
        this.typecorrespondence = typecorrespondence;
    }

    public Correspondence typecorrespondence(Typecorrespondence typecorrespondence) {
        this.setTypecorrespondence(typecorrespondence);
        return this;
    }

    // jhipster-needle-entity-add-getters-setters - JHipster will add getters and setters here

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof Correspondence)) {
            return false;
        }
        return getId() != null && getId().equals(((Correspondence) o).getId());
    }

    @Override
    public int hashCode() {
        // see https://vladmihalcea.com/how-to-implement-equals-and-hashcode-using-the-jpa-entity-identifier/
        return getClass().hashCode();
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "Correspondence{" +
            "id=" + getId() +
            ", ordernbr='" + getOrdernbr() + "'" +
            ", numcopy='" + getNumcopy() + "'" +
            ", doclink='" + getDoclink() + "'" +
            ", pagenbr='" + getPagenbr() + "'" +
            ", highlevel='" + getHighlevel() + "'" +
            ", confidentiel='" + getConfidentiel() + "'" +
            ", priority='" + getPriority() + "'" +
            ", obs='" + getObs() + "'" +
            ", category='" + getCategory() + "'" +
            ", status='" + getStatus() + "'" +
            ", datejc='" + getDatejc() + "'" +
            ", datejcsend='" + getDatejcsend() + "'" +
            ", typereceive='" + getTypereceive() + "'" +
            ", typecopy='" + getTypecopy() + "'" +
            ", refsnd='" + getRefsnd() + "'" +
            ", text='" + getText() + "'" +
            ", docyear='" + getDocyear() + "'" +
            ", datehjrsave='" + getDatehjrsave() + "'" +
            ", datejcsave='" + getDatejcsave() + "'" +
            ", oldOrderNumber='" + getOldOrderNumber() + "'" +
            ", subject='" + getSubject() + "'" +
            ", attach='" + getAttach() + "'" +
            ", ordernbradrsbook='" + getOrdernbradrsbook() + "'" +
            ", seqkeyadrsbook=" + getSeqkeyadrsbook() +
            ", useradrsbook='" + getUseradrsbook() + "'" +
            ", cityzenncard='" + getCityzenncard() + "'" +
            ", cityzenphone='" + getCityzenphone() + "'" +
            ", sms='" + getSms() + "'" +
            ", incident='" + getIncident() + "'" +
            ", checkFavorite='" + getCheckFavorite() + "'" +
            ", companyId='" + getCompanyId() + "'" +
            "}";
    }
}
