package com.dq.lilas.web.rest;

import com.dq.lilas.repository.AttachementRepository;
import com.dq.lilas.service.AttachementService;
import com.dq.lilas.service.dto.AttachementDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Attachement}.
 */
@RestController
@RequestMapping("/api/attachements")
public class AttachementResource {

    private static final Logger LOG = LoggerFactory.getLogger(AttachementResource.class);

    private static final String ENTITY_NAME = "attachement";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final AttachementService attachementService;

    private final AttachementRepository attachementRepository;

    public AttachementResource(AttachementService attachementService, AttachementRepository attachementRepository) {
        this.attachementService = attachementService;
        this.attachementRepository = attachementRepository;
    }

    /**
     * {@code POST  /attachements} : Create a new attachement.
     *
     * @param attachementDTO the attachementDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new attachementDTO, or with status {@code 400 (Bad Request)} if the attachement has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<AttachementDTO> createAttachement(@Valid @RequestBody AttachementDTO attachementDTO) throws URISyntaxException {
        LOG.debug("REST request to save Attachement : {}", attachementDTO);
        if (attachementDTO.getId() != null) {
            throw new BadRequestAlertException("A new attachement cannot already have an ID", ENTITY_NAME, "idexists");
        }
        attachementDTO = attachementService.save(attachementDTO);
        return ResponseEntity.created(new URI("/api/attachements/" + attachementDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, attachementDTO.getId().toString()))
            .body(attachementDTO);
    }

    /**
     * {@code PUT  /attachements/:id} : Updates an existing attachement.
     *
     * @param id the id of the attachementDTO to save.
     * @param attachementDTO the attachementDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated attachementDTO,
     * or with status {@code 400 (Bad Request)} if the attachementDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the attachementDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<AttachementDTO> updateAttachement(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody AttachementDTO attachementDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Attachement : {}, {}", id, attachementDTO);
        if (attachementDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, attachementDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!attachementRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        attachementDTO = attachementService.update(attachementDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, attachementDTO.getId().toString()))
            .body(attachementDTO);
    }

    /**
     * {@code PATCH  /attachements/:id} : Partial updates given fields of an existing attachement, field will ignore if it is null
     *
     * @param id the id of the attachementDTO to save.
     * @param attachementDTO the attachementDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated attachementDTO,
     * or with status {@code 400 (Bad Request)} if the attachementDTO is not valid,
     * or with status {@code 404 (Not Found)} if the attachementDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the attachementDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<AttachementDTO> partialUpdateAttachement(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody AttachementDTO attachementDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Attachement partially : {}, {}", id, attachementDTO);
        if (attachementDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, attachementDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!attachementRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<AttachementDTO> result = attachementService.partialUpdate(attachementDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, attachementDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /attachements} : get all the attachements.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of attachements in body.
     */
    @GetMapping("")
    public ResponseEntity<List<AttachementDTO>> getAllAttachements(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of Attachements");
        Page<AttachementDTO> page = attachementService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /attachements/:id} : get the "id" attachement.
     *
     * @param id the id of the attachementDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the attachementDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<AttachementDTO> getAttachement(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Attachement : {}", id);
        Optional<AttachementDTO> attachementDTO = attachementService.findOne(id);
        return ResponseUtil.wrapOrNotFound(attachementDTO);
    }

    /**
     * {@code DELETE  /attachements/:id} : delete the "id" attachement.
     *
     * @param id the id of the attachementDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteAttachement(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Attachement : {}", id);
        attachementService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
