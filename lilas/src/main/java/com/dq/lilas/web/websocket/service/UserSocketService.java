package com.dq.lilas.web.websocket.service;

import com.dq.lilas.web.websocket.dto.UserSocketDTO;
import com.dq.lilas.web.websocket.enumeration.UserCallState;
import com.dq.lilas.web.websocket.enumeration.UserConnexionState;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.user.SimpSession;
import org.springframework.messaging.simp.user.SimpUser;
import org.springframework.messaging.simp.user.SimpUserRegistry;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

@Service
public class UserSocketService {

    // In memory data
    private final Set<UserSocketDTO> userSocketDTOS = new HashSet<>();

    @Autowired
    SimpUserRegistry userRegistry;

    public Set<UserSocketDTO> getUserSocketList() {
        return userSocketDTOS;
    }

    public UserSocketDTO findUserSocketByUsername(String username) {
        return
            userSocketDTOS.stream()
                .filter(dto -> dto.getUsername().equals(username))
                .findAny()
                .orElse(null);
    }

    public void updateUserSocketConnexionState(String username, UserConnexionState userConnexionState) {
        UserSocketDTO userSocketDTO = findUserSocketByUsername(username);
        if (userSocketDTO != null) {
            if (userConnexionState != null) {
                userSocketDTO.setUserConnexionState(userConnexionState);
            }
        } else {
            userSocketDTOS.add(new UserSocketDTO(username, userConnexionState));
        }
    }

    public void updateUserSocketCallState(String username, UserCallState userCallState) {
        UserSocketDTO userSocketDTO = findUserSocketByUsername(username);
        if (userSocketDTO != null) {
            userSocketDTO.setUserCallState(userCallState);
        }
    }

    public void removeByUsername(String username) {
        userSocketDTOS.removeIf(userSocketDTO -> userSocketDTO.getUsername().equals(username));
    }

    public List<UserSocketDTO> getSubscribedSocketUsersByDestination(String destination) {
        return userRegistry.findSubscriptions(subscription -> subscription.getDestination().equals(destination))
            .stream()
            .map(simpSubscription -> {
                SimpSession simpSession = simpSubscription.getSession();
                SimpUser simpUser = simpSession.getUser();
                UserSocketDTO userSocketDTO = findUserSocketByUsername(simpUser.getName());
                if (userSocketDTO != null) {
                    return userSocketDTO;
                }
                return new UserSocketDTO(simpUser.getName(), UserConnexionState.ONLINE);
            })
            .collect(Collectors.toList());
    }
}
