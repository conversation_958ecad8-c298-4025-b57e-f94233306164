package com.dq.lilas.web.rest;

import com.dq.lilas.repository.DeliverymodeRepository;
import com.dq.lilas.service.DeliverymodeService;
import com.dq.lilas.service.dto.DeliverymodeDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Deliverymode}.
 */
@RestController
@RequestMapping("/api/deliverymodes")
public class DeliverymodeResource {

    private static final Logger LOG = LoggerFactory.getLogger(DeliverymodeResource.class);

    private static final String ENTITY_NAME = "deliverymode";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final DeliverymodeService deliverymodeService;

    private final DeliverymodeRepository deliverymodeRepository;

    public DeliverymodeResource(DeliverymodeService deliverymodeService, DeliverymodeRepository deliverymodeRepository) {
        this.deliverymodeService = deliverymodeService;
        this.deliverymodeRepository = deliverymodeRepository;
    }

    /**
     * {@code POST  /deliverymodes} : Create a new deliverymode.
     *
     * @param deliverymodeDTO the deliverymodeDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new deliverymodeDTO, or with status {@code 400 (Bad Request)} if the deliverymode has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<DeliverymodeDTO> createDeliverymode(@Valid @RequestBody DeliverymodeDTO deliverymodeDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save Deliverymode : {}", deliverymodeDTO);
        if (deliverymodeDTO.getId() != null) {
            throw new BadRequestAlertException("A new deliverymode cannot already have an ID", ENTITY_NAME, "idexists");
        }
        deliverymodeDTO = deliverymodeService.save(deliverymodeDTO);
        return ResponseEntity.created(new URI("/api/deliverymodes/" + deliverymodeDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, deliverymodeDTO.getId().toString()))
            .body(deliverymodeDTO);
    }

    /**
     * {@code PUT  /deliverymodes/:id} : Updates an existing deliverymode.
     *
     * @param id the id of the deliverymodeDTO to save.
     * @param deliverymodeDTO the deliverymodeDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated deliverymodeDTO,
     * or with status {@code 400 (Bad Request)} if the deliverymodeDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the deliverymodeDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<DeliverymodeDTO> updateDeliverymode(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody DeliverymodeDTO deliverymodeDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Deliverymode : {}, {}", id, deliverymodeDTO);
        if (deliverymodeDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, deliverymodeDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!deliverymodeRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        deliverymodeDTO = deliverymodeService.update(deliverymodeDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, deliverymodeDTO.getId().toString()))
            .body(deliverymodeDTO);
    }

    /**
     * {@code PATCH  /deliverymodes/:id} : Partial updates given fields of an existing deliverymode, field will ignore if it is null
     *
     * @param id the id of the deliverymodeDTO to save.
     * @param deliverymodeDTO the deliverymodeDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated deliverymodeDTO,
     * or with status {@code 400 (Bad Request)} if the deliverymodeDTO is not valid,
     * or with status {@code 404 (Not Found)} if the deliverymodeDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the deliverymodeDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<DeliverymodeDTO> partialUpdateDeliverymode(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody DeliverymodeDTO deliverymodeDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Deliverymode partially : {}, {}", id, deliverymodeDTO);
        if (deliverymodeDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, deliverymodeDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!deliverymodeRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<DeliverymodeDTO> result = deliverymodeService.partialUpdate(deliverymodeDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, deliverymodeDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /deliverymodes} : get all the deliverymodes.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of deliverymodes in body.
     */
    @GetMapping("")
    public ResponseEntity<List<DeliverymodeDTO>> getAllDeliverymodes(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of Deliverymodes");
        Page<DeliverymodeDTO> page = deliverymodeService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /deliverymodes/:id} : get the "id" deliverymode.
     *
     * @param id the id of the deliverymodeDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the deliverymodeDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<DeliverymodeDTO> getDeliverymode(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Deliverymode : {}", id);
        Optional<DeliverymodeDTO> deliverymodeDTO = deliverymodeService.findOne(id);
        return ResponseUtil.wrapOrNotFound(deliverymodeDTO);
    }

    /**
     * {@code DELETE  /deliverymodes/:id} : delete the "id" deliverymode.
     *
     * @param id the id of the deliverymodeDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDeliverymode(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Deliverymode : {}", id);
        deliverymodeService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
