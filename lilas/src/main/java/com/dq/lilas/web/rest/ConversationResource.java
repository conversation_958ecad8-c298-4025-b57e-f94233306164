package com.dq.lilas.web.rest;

import com.dq.lilas.domain.enumeration.ConversationState;
import com.dq.lilas.service.ConversationService;
import com.dq.lilas.service.dto.ConversationDTO;
import com.dq.lilas.service.dto.UserDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import io.micrometer.core.annotation.Timed;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.*;

/**
 * REST controller for managing Conversation.
 */
@RestController
@RequestMapping("/api")
public class ConversationResource {

    private static final String ENTITY_NAME = "conversation";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final Logger log = LoggerFactory.getLogger(ConversationResource.class);
    private final ConversationService conversationService;

    public ConversationResource(ConversationService conversationService) {
        this.conversationService = conversationService;
    }

    /**
     * POST  /conversations : Create a new conversation.
     *
     * @param conversationDTO the conversationDTO to create
     * @return the ResponseEntity with status 201 (Created) and with body the new conversationDTO, or with status 400 (Bad Request) if the conversation has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/conversations")
    @Timed
    public ResponseEntity<ConversationDTO> createConversation(@RequestBody ConversationDTO conversationDTO) throws URISyntaxException {
        log.debug("REST request to save Conversation : {}", conversationDTO);
        if (conversationDTO.getId() != null) {
            throw new BadRequestAlertException("A new conversation cannot already have an ID", ENTITY_NAME, "idexists");
        }
        if (Objects.isNull(conversationDTO.getSenderId())) {
            throw new BadRequestAlertException("Invalid association value provided", ENTITY_NAME, "null");
        }
        Long receiverId = conversationDTO.getReceiverId();
        if (Objects.isNull(receiverId)) {
            throw new BadRequestAlertException("Invalid association value provided", ENTITY_NAME, "null");
        }
        Set<UserDTO> receivers = new HashSet<>(Collections.singletonList(new UserDTO(receiverId)));
        ConversationDTO result = conversationService.save(conversationDTO, receivers);
        return ResponseEntity.created(new URI("/api/conversations/" + result.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, result.getId().toString(), ENTITY_NAME))
            .body(result);
    }

    /**
     * PUT  /conversations : Updates an existing conversation.
     *
     * @param conversationDTO the conversationDTO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated conversationDTO,
     * or with status 400 (Bad Request) if the conversationDTO is not valid,
     * or with status 500 (Internal Server Error) if the conversationDTO couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/conversations")
    @Timed
    public ResponseEntity<ConversationDTO> updateConversation(@RequestBody ConversationDTO conversationDTO) throws URISyntaxException {
        log.debug("REST request to update Conversation : {}", conversationDTO);
        if (conversationDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (Objects.isNull(conversationDTO.getReceiverId())) {
            throw new BadRequestAlertException("Invalid association value provided", ENTITY_NAME, "null");
        }
        Set<UserDTO> receivers = new HashSet<>(Collections.singletonList(new UserDTO(conversationDTO.getReceiverId())));
        ConversationDTO result = conversationService.save(conversationDTO, receivers);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, conversationDTO.getId().toString()))
            .body(result);
    }


    /**
     * GET  /conversations : get all the conversations.
     *
     * @param pageable the pagination information
     * @return the ResponseEntity with status 200 (OK) and the list of conversations in body
     */
    @GetMapping("/conversations")
    @Timed
    public ResponseEntity<List<ConversationDTO>> getAllConversations(Pageable pageable) {
        log.debug("REST request to get a page of Conversations");
        Page<ConversationDTO> page = conversationService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * GET  /conversations/:id : get the "id" conversation.
     *
     * @param id the id of the conversationDTO to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the conversationDTO, or with status 404 (Not Found)
     */
    @GetMapping("/conversations/{id}")
    @Timed
    public ResponseEntity<ConversationDTO> getConversation(@PathVariable Long id) {
        log.debug("REST request to get Conversation : {}", id);
        Optional<ConversationDTO> conversationDTO = conversationService.findOne(id);
        return ResponseUtil.wrapOrNotFound(conversationDTO);
    }

    /**
     * {@code GET  /conversations/:roomId} : get all the conversations of Room.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of conversations in body.
     */
    @GetMapping("/conversations/room/{roomId}")
    @Timed
    public Page<ConversationDTO> getAllConversationsByRoom(@PathVariable Long roomId, Pageable pageable) {
        log.debug("REST request to get all Conversations of Room: {}", roomId);
        return conversationService.findAllByRoom(pageable, roomId);
    }

    /**
     * {@code GET  /conversations/logged/room:roomId} : get all the conversations by logged user of Room after join date.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of conversations in body.
     */
    @GetMapping("/conversations/logged/room/{roomId}")
    @Timed
    public Page<ConversationDTO> findAllByLoggedUserOfRoomAfterJoinDate(@PathVariable Long roomId, Pageable pageable) {
        log.debug("REST request to get all the conversations by logged user of Room after join date: {}", roomId);
        return conversationService.findAllByLoggedUserOfRoomAfterJoinDate(pageable, roomId);
    }

    /**
     * {@code GET  /conversations/logged/delivered/by-room/count} : Get current User Conversations Count By State grouped by Room
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and count.
     */
    @GetMapping("/conversations/logged/delivered/by-room/count")
    @Timed
    public List<?> getCurrentUserConversationsCountByStateGroupedByRoom() {
        log.debug("REST request to get current user conversations count by state grouped by room");
        return conversationService.getCurrentUserConversationsCountByStateGroupedByRoom(ConversationState.DELIVERED);
    }

    /**
     * {@code GET  /conversations/logged/delivered/by-sender/count} : Get current User Conversations Count By State grouped by Sender
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and count.
     */
    @GetMapping("/conversations/logged/delivered/by-sender/count")
    @Timed
    public List<?> getCurrentUserConversationsCountByStateGroupedBySender() {
        log.debug("REST request to get current user conversations count by state grouped by sender");
        return conversationService.getCurrentUserConversationsCountByStateGroupedBySender(ConversationState.DELIVERED);
    }

    /**
     * {@code GET  /conversations/logged/room/{id} : Update current user conversation state by room
     */
    @PutMapping("/conversations/logged/room/{id}")
    @Timed
    public void updateConversationStateByRoom(@PathVariable Long id) {
        log.debug("REST request to update conversation state by room");
        conversationService.updateConversationStateByRoom(ConversationState.SEEN, id);
    }

    /**
     * {@code GET  /conversations/logged/delivered/room/{id}/count} : Get current User Conversation Count By State and Room
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and count.
     */
    @GetMapping("/conversations/logged/delivered/room/{id}/count")
    @Timed
    public int getCurrentUserConversationCountByRoomAndState(@PathVariable Long id) {
        log.debug("REST request to get current user conversation count by room and state");
        return conversationService.getCurrentUserConversationCountByRoomAndReceiverAndState(ConversationState.DELIVERED, id);
    }

    /**
     * DELETE  /conversations/:id : delete the "id" conversation.
     *
     * @param id the id of the conversationDTO to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/conversations/{id}")
    @Timed
    public ResponseEntity<Void> deleteConversation(@PathVariable Long id) {
        log.debug("REST request to delete Conversation : {}", id);
        conversationService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString())).build();
    }
}
