package com.dq.lilas.web.rest;

import com.dq.lilas.service.RoomService;
import com.dq.lilas.service.dto.RoomDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import io.micrometer.core.annotation.Timed;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;

/**
 * REST controller for managing Room.
 */
@RestController
@RequestMapping("/api")
public class RoomResource {

    private static final String ENTITY_NAME = "room";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final Logger log = LoggerFactory.getLogger(RoomResource.class);
    private final RoomService roomService;

    public RoomResource(RoomService roomService) {
        this.roomService = roomService;
    }

    /**
     * POST  /rooms : Create a new room.
     *
     * @param roomDTO the roomDTO to create
     * @return the ResponseEntity with status 201 (Created) and with body the new roomDTO, or with status 400 (Bad Request) if the room has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/rooms")
    @Timed
    public ResponseEntity<RoomDTO> createRoom(@RequestBody RoomDTO roomDTO) throws URISyntaxException {
        log.debug("REST request to save Room : {}", roomDTO);
        if (roomDTO.getId() != null) {
            throw new BadRequestAlertException("A new room cannot already have an ID", ENTITY_NAME, "idexists");
        }
        RoomDTO result = roomService.save(roomDTO);
        return ResponseEntity.created(new URI("/api/rooms/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(ENTITY_NAME, false, applicationName, result.getId().toString()))
                .body(result);
    }

    /**
     * PUT  /rooms : Updates an existing room.
     *
     * @param roomDTO the roomDTO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated roomDTO,
     * or with status 400 (Bad Request) if the roomDTO is not valid,
     * or with status 500 (Internal Server Error) if the roomDTO couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/rooms")
    @Timed
    public ResponseEntity<RoomDTO> updateRoom(@RequestBody RoomDTO roomDTO) throws URISyntaxException {
        log.debug("REST request to update Room : {}", roomDTO);
        if (roomDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        RoomDTO result = roomService.save(roomDTO);
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, roomDTO.getId().toString()))
                .body(result);
    }

    /**
     * GET  /rooms : get all the rooms.
     *
     * @param pageable  the pagination information
     * @param eagerload flag to eager load entities from relationships (This is applicable for many-to-many)
     * @return the ResponseEntity with status 200 (OK) and the list of rooms in body
     */
    @GetMapping("/rooms")
    @Timed
    public ResponseEntity<List<RoomDTO>> getRooms(Pageable pageable, @RequestParam(required = false, defaultValue = "false") boolean eagerload) {
        log.debug("REST request to get a page of Rooms");
        Page<RoomDTO> page;
        if (eagerload) {
            page = roomService.findAllWithEagerRelationships(pageable);
        } else {
            page = roomService.findAllByPage(pageable);
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /rooms/all} : get all the rooms.
     *
     * @param eagerload flag to eager load entities from relationships (This is applicable for many-to-many).
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of rooms in body.
     */
    @GetMapping("/rooms/all")
    public List<RoomDTO> getAllRooms(@RequestParam(required = false, defaultValue = "false") boolean eagerload) {
        log.debug("REST request to get all Rooms");
        return roomService.findAll();
    }

    /**
     * {@code POST  /rooms/group} : Create First Room in Group
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of rooms in body.
     */
    @PostMapping("/rooms/group")
    public void createFirstRoomInGroup(@RequestBody RoomDTO roomDTO) throws URISyntaxException {
        log.debug("REST request to save First Room In Group: {}", roomDTO);
        Long groupId = roomDTO.getGroupId();
        if (groupId == null) {
            throw new BadRequestAlertException("The Group in the Room must not be Null", ENTITY_NAME, "null");
        }
        roomService.createFirstRoomInGroup(roomDTO);
    }


    /**
     * {@code GET  /rooms/logged/private/user/:id} : get the "id" room.
     *
     * @param id the id of the other user in the same room.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the roomDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/rooms/logged/private/user/{id}")
    public ResponseEntity<RoomDTO> getPrivateRoomByLoggedUserAndSelectedUser(@PathVariable Long id) {
        log.debug("REST request to get Room : {}", id);
        RoomDTO roomDTO = roomService.getPrivateRoomByLoggedUserAndSelectedUser(id);
        return ResponseUtil.wrapOrNotFound(Optional.of(roomDTO));
    }

    /**
     * {@code GET  /rooms/group/:groupId}.
     *
     * @param groupId the id of the group.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the roomDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/rooms/group/{groupId}")
    public List<RoomDTO> findAllByGroup(@PathVariable Long groupId) {
        log.debug("REST request to get all Rooms of group");
        return roomService.findAllByGroup(groupId);
    }

    /**
     * {@code GET  /rooms/logged/group/:groupId}.
     *
     * @param groupId the id of the group.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the roomDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/rooms/logged/group/{groupId}")
    public List<RoomDTO> findAllOfGroupJoinedByLoggedUser(@PathVariable Long groupId) {
        log.debug("REST request to get all rooms of a group joined by the logged user");
        return roomService.findAllOfGroupJoinedByLoggedUser(groupId);
    }

    /**
     * {@code GET  /rooms/logged} : get all the rooms joined by logged user.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of rooms in body.
     */
    @GetMapping("/rooms/logged")
    public List<RoomDTO> getAllRoomsJoinedByLoggedUser() {
        log.debug("REST request to get all Rooms Joined by logged user");
        return roomService.findAllByLoggedUser();
    }

    /**
     * GET  /rooms/:id : get the "id" room.
     *
     * @param id the id of the roomDTO to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the roomDTO, or with status 404 (Not Found)
     */
    @GetMapping("/rooms/{id}")
    @Timed
    public ResponseEntity<RoomDTO> getRoom(@PathVariable Long id) {
        log.debug("REST request to get Room : {}", id);
        Optional<RoomDTO> roomDTO = roomService.findOne(id);
        return ResponseUtil.wrapOrNotFound(roomDTO);
    }

    /**
     * PUT  /rooms/logged/:name/group/:groupId/remove : remove logged user from room by name and group.
     *
     * @param groupId the group entity id.
     * @param name the room entity name.
     */
    @PutMapping("/rooms/logged/{name}/group/{groupId}/remove")
    @Timed
    void removeLoggedUserFromRoomByNameAndGroup(@PathVariable Long groupId, @PathVariable String name) {
        log.debug("REST request to remove logged user from room by name and group");
        roomService.updateLoggedUserFromRoomByNameAndGroup(groupId, name, false);
    }

    /**
     * PUT  /rooms/logged/:name/group/:groupId/add : REST request to add logged user to room by name and group.
     *
     * @param groupId the group entity id.
     * @param name the room entity name.
     */
    @PutMapping("/rooms/logged/{name}/group/{groupId}/add")
    @Timed
    void addLoggedUserFromRoomByNameAndGroup(@PathVariable Long groupId, @PathVariable String name) {
        log.debug("REST request to add logged user to room by name and group");
        roomService.updateLoggedUserFromRoomByNameAndGroup(groupId, name, true);
    }

    /**
     * DELETE  /rooms/:id : delete the "id" room.
     *
     * @param id the id of the roomDTO to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/rooms/{id}")
    @Timed
    public ResponseEntity<Void> deleteRoom(@PathVariable Long id) {
        log.debug("REST request to delete Room : {}", id);
        roomService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString())).build();
    }
}
