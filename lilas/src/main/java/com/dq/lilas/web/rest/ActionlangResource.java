package com.dq.lilas.web.rest;

import com.dq.lilas.repository.ActionlangRepository;
import com.dq.lilas.service.ActionlangService;
import com.dq.lilas.service.dto.ActionlangDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Actionlang}.
 */
@RestController
@RequestMapping("/api/actionlangs")
public class ActionlangResource {

    private static final Logger LOG = LoggerFactory.getLogger(ActionlangResource.class);

    private static final String ENTITY_NAME = "actionlang";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final ActionlangService actionlangService;

    private final ActionlangRepository actionlangRepository;

    public ActionlangResource(ActionlangService actionlangService, ActionlangRepository actionlangRepository) {
        this.actionlangService = actionlangService;
        this.actionlangRepository = actionlangRepository;
    }

    /**
     * {@code POST  /actionlangs} : Create a new actionlang.
     *
     * @param actionlangDTO the actionlangDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new actionlangDTO, or with status {@code 400 (Bad Request)} if the actionlang has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<ActionlangDTO> createActionlang(@Valid @RequestBody ActionlangDTO actionlangDTO) throws URISyntaxException {
        LOG.debug("REST request to save Actionlang : {}", actionlangDTO);
        if (actionlangDTO.getId() != null) {
            throw new BadRequestAlertException("A new actionlang cannot already have an ID", ENTITY_NAME, "idexists");
        }
        actionlangDTO = actionlangService.save(actionlangDTO);
        return ResponseEntity.created(new URI("/api/actionlangs/" + actionlangDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, actionlangDTO.getId().toString()))
            .body(actionlangDTO);
    }

    /**
     * {@code PUT  /actionlangs/:id} : Updates an existing actionlang.
     *
     * @param id the id of the actionlangDTO to save.
     * @param actionlangDTO the actionlangDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated actionlangDTO,
     * or with status {@code 400 (Bad Request)} if the actionlangDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the actionlangDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<ActionlangDTO> updateActionlang(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody ActionlangDTO actionlangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Actionlang : {}, {}", id, actionlangDTO);
        if (actionlangDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, actionlangDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!actionlangRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        actionlangDTO = actionlangService.update(actionlangDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, actionlangDTO.getId().toString()))
            .body(actionlangDTO);
    }

    /**
     * {@code PATCH  /actionlangs/:id} : Partial updates given fields of an existing actionlang, field will ignore if it is null
     *
     * @param id the id of the actionlangDTO to save.
     * @param actionlangDTO the actionlangDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated actionlangDTO,
     * or with status {@code 400 (Bad Request)} if the actionlangDTO is not valid,
     * or with status {@code 404 (Not Found)} if the actionlangDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the actionlangDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<ActionlangDTO> partialUpdateActionlang(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody ActionlangDTO actionlangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Actionlang partially : {}, {}", id, actionlangDTO);
        if (actionlangDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, actionlangDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!actionlangRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<ActionlangDTO> result = actionlangService.partialUpdate(actionlangDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, actionlangDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /actionlangs} : get all the actionlangs.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of actionlangs in body.
     */
    @GetMapping("")
    public ResponseEntity<List<ActionlangDTO>> getAllActionlangs(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of Actionlangs");
        Page<ActionlangDTO> page = actionlangService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /actionlangs/:id} : get the "id" actionlang.
     *
     * @param id the id of the actionlangDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the actionlangDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<ActionlangDTO> getActionlang(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Actionlang : {}", id);
        Optional<ActionlangDTO> actionlangDTO = actionlangService.findOne(id);
        return ResponseUtil.wrapOrNotFound(actionlangDTO);
    }

    /**
     * {@code DELETE  /actionlangs/:id} : delete the "id" actionlang.
     *
     * @param id the id of the actionlangDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteActionlang(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Actionlang : {}", id);
        actionlangService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
