package com.dq.lilas.web.rest;

import com.dq.lilas.repository.EmailsNotificationsRepository;
import com.dq.lilas.service.EmailsNotificationsService;
import com.dq.lilas.service.dto.EmailsNotificationsDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.EmailsNotifications}.
 */
@RestController
@RequestMapping("/api/emails-notifications")
public class EmailsNotificationsResource {

    private static final Logger LOG = LoggerFactory.getLogger(EmailsNotificationsResource.class);

    private static final String ENTITY_NAME = "emailsNotifications";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final EmailsNotificationsService emailsNotificationsService;

    private final EmailsNotificationsRepository emailsNotificationsRepository;

    public EmailsNotificationsResource(
        EmailsNotificationsService emailsNotificationsService,
        EmailsNotificationsRepository emailsNotificationsRepository
    ) {
        this.emailsNotificationsService = emailsNotificationsService;
        this.emailsNotificationsRepository = emailsNotificationsRepository;
    }

    /**
     * {@code POST  /emails-notifications} : Create a new emailsNotifications.
     *
     * @param emailsNotificationsDTO the emailsNotificationsDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new emailsNotificationsDTO, or with status {@code 400 (Bad Request)} if the emailsNotifications has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<EmailsNotificationsDTO> createEmailsNotifications(@RequestBody EmailsNotificationsDTO emailsNotificationsDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save EmailsNotifications : {}", emailsNotificationsDTO);
        if (emailsNotificationsDTO.getId() != null) {
            throw new BadRequestAlertException("A new emailsNotifications cannot already have an ID", ENTITY_NAME, "idexists");
        }
        emailsNotificationsDTO = emailsNotificationsService.save(emailsNotificationsDTO);
        return ResponseEntity.created(new URI("/api/emails-notifications/" + emailsNotificationsDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, emailsNotificationsDTO.getId().toString()))
            .body(emailsNotificationsDTO);
    }

    /**
     * {@code PUT  /emails-notifications/:id} : Updates an existing emailsNotifications.
     *
     * @param id the id of the emailsNotificationsDTO to save.
     * @param emailsNotificationsDTO the emailsNotificationsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated emailsNotificationsDTO,
     * or with status {@code 400 (Bad Request)} if the emailsNotificationsDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the emailsNotificationsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<EmailsNotificationsDTO> updateEmailsNotifications(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody EmailsNotificationsDTO emailsNotificationsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update EmailsNotifications : {}, {}", id, emailsNotificationsDTO);
        if (emailsNotificationsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, emailsNotificationsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!emailsNotificationsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        emailsNotificationsDTO = emailsNotificationsService.update(emailsNotificationsDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, emailsNotificationsDTO.getId().toString()))
            .body(emailsNotificationsDTO);
    }

    /**
     * {@code PATCH  /emails-notifications/:id} : Partial updates given fields of an existing emailsNotifications, field will ignore if it is null
     *
     * @param id the id of the emailsNotificationsDTO to save.
     * @param emailsNotificationsDTO the emailsNotificationsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated emailsNotificationsDTO,
     * or with status {@code 400 (Bad Request)} if the emailsNotificationsDTO is not valid,
     * or with status {@code 404 (Not Found)} if the emailsNotificationsDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the emailsNotificationsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<EmailsNotificationsDTO> partialUpdateEmailsNotifications(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody EmailsNotificationsDTO emailsNotificationsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update EmailsNotifications partially : {}, {}", id, emailsNotificationsDTO);
        if (emailsNotificationsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, emailsNotificationsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!emailsNotificationsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<EmailsNotificationsDTO> result = emailsNotificationsService.partialUpdate(emailsNotificationsDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, emailsNotificationsDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /emails-notifications} : get all the emailsNotifications.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of emailsNotifications in body.
     */
    @GetMapping("")
    public ResponseEntity<List<EmailsNotificationsDTO>> getAllEmailsNotifications(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of EmailsNotifications");
        Page<EmailsNotificationsDTO> page = emailsNotificationsService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /emails-notifications/:id} : get the "id" emailsNotifications.
     *
     * @param id the id of the emailsNotificationsDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the emailsNotificationsDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<EmailsNotificationsDTO> getEmailsNotifications(@PathVariable("id") Long id) {
        LOG.debug("REST request to get EmailsNotifications : {}", id);
        Optional<EmailsNotificationsDTO> emailsNotificationsDTO = emailsNotificationsService.findOne(id);
        return ResponseUtil.wrapOrNotFound(emailsNotificationsDTO);
    }

    /**
     * {@code DELETE  /emails-notifications/:id} : delete the "id" emailsNotifications.
     *
     * @param id the id of the emailsNotificationsDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEmailsNotifications(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete EmailsNotifications : {}", id);
        emailsNotificationsService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
