package com.dq.lilas.web.rest;

import com.dq.lilas.repository.TypecorrespondencelangRepository;
import com.dq.lilas.service.TypecorrespondencelangService;
import com.dq.lilas.service.dto.TypecorrespondencelangDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Typecorrespondencelang}.
 */
@RestController
@RequestMapping("/api/typecorrespondencelangs")
public class TypecorrespondencelangResource {

    private static final Logger LOG = LoggerFactory.getLogger(TypecorrespondencelangResource.class);

    private static final String ENTITY_NAME = "typecorrespondencelang";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final TypecorrespondencelangService typecorrespondencelangService;

    private final TypecorrespondencelangRepository typecorrespondencelangRepository;

    public TypecorrespondencelangResource(
        TypecorrespondencelangService typecorrespondencelangService,
        TypecorrespondencelangRepository typecorrespondencelangRepository
    ) {
        this.typecorrespondencelangService = typecorrespondencelangService;
        this.typecorrespondencelangRepository = typecorrespondencelangRepository;
    }

    /**
     * {@code POST  /typecorrespondencelangs} : Create a new typecorrespondencelang.
     *
     * @param typecorrespondencelangDTO the typecorrespondencelangDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new typecorrespondencelangDTO, or with status {@code 400 (Bad Request)} if the typecorrespondencelang has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<TypecorrespondencelangDTO> createTypecorrespondencelang(
        @RequestBody TypecorrespondencelangDTO typecorrespondencelangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to save Typecorrespondencelang : {}", typecorrespondencelangDTO);
        if (typecorrespondencelangDTO.getId() != null) {
            throw new BadRequestAlertException("A new typecorrespondencelang cannot already have an ID", ENTITY_NAME, "idexists");
        }
        typecorrespondencelangDTO = typecorrespondencelangService.save(typecorrespondencelangDTO);
        return ResponseEntity.created(new URI("/api/typecorrespondencelangs/" + typecorrespondencelangDTO.getId()))
            .headers(
                HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, typecorrespondencelangDTO.getId().toString())
            )
            .body(typecorrespondencelangDTO);
    }

    /**
     * {@code PUT  /typecorrespondencelangs/:id} : Updates an existing typecorrespondencelang.
     *
     * @param id the id of the typecorrespondencelangDTO to save.
     * @param typecorrespondencelangDTO the typecorrespondencelangDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated typecorrespondencelangDTO,
     * or with status {@code 400 (Bad Request)} if the typecorrespondencelangDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the typecorrespondencelangDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<TypecorrespondencelangDTO> updateTypecorrespondencelang(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody TypecorrespondencelangDTO typecorrespondencelangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Typecorrespondencelang : {}, {}", id, typecorrespondencelangDTO);
        if (typecorrespondencelangDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, typecorrespondencelangDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!typecorrespondencelangRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        typecorrespondencelangDTO = typecorrespondencelangService.update(typecorrespondencelangDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, typecorrespondencelangDTO.getId().toString()))
            .body(typecorrespondencelangDTO);
    }

    /**
     * {@code PATCH  /typecorrespondencelangs/:id} : Partial updates given fields of an existing typecorrespondencelang, field will ignore if it is null
     *
     * @param id the id of the typecorrespondencelangDTO to save.
     * @param typecorrespondencelangDTO the typecorrespondencelangDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated typecorrespondencelangDTO,
     * or with status {@code 400 (Bad Request)} if the typecorrespondencelangDTO is not valid,
     * or with status {@code 404 (Not Found)} if the typecorrespondencelangDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the typecorrespondencelangDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<TypecorrespondencelangDTO> partialUpdateTypecorrespondencelang(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody TypecorrespondencelangDTO typecorrespondencelangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Typecorrespondencelang partially : {}, {}", id, typecorrespondencelangDTO);
        if (typecorrespondencelangDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, typecorrespondencelangDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!typecorrespondencelangRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<TypecorrespondencelangDTO> result = typecorrespondencelangService.partialUpdate(typecorrespondencelangDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, typecorrespondencelangDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /typecorrespondencelangs} : get all the typecorrespondencelangs.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of typecorrespondencelangs in body.
     */
    @GetMapping("")
    public ResponseEntity<List<TypecorrespondencelangDTO>> getAllTypecorrespondencelangs(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of Typecorrespondencelangs");
        Page<TypecorrespondencelangDTO> page = typecorrespondencelangService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /typecorrespondencelangs/:id} : get the "id" typecorrespondencelang.
     *
     * @param id the id of the typecorrespondencelangDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the typecorrespondencelangDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<TypecorrespondencelangDTO> getTypecorrespondencelang(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Typecorrespondencelang : {}", id);
        Optional<TypecorrespondencelangDTO> typecorrespondencelangDTO = typecorrespondencelangService.findOne(id);
        return ResponseUtil.wrapOrNotFound(typecorrespondencelangDTO);
    }

    /**
     * {@code DELETE  /typecorrespondencelangs/:id} : delete the "id" typecorrespondencelang.
     *
     * @param id the id of the typecorrespondencelangDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTypecorrespondencelang(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Typecorrespondencelang : {}", id);
        typecorrespondencelangService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
