package com.dq.lilas.web.rest;

import com.dq.lilas.repository.EmployeeCompanyPermissionRepository;
import com.dq.lilas.service.EmployeeCompanyPermissionService;
import com.dq.lilas.service.dto.EmployeeCompanyPermissionDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.EmployeeCompanyPermission}.
 */
@RestController
@RequestMapping("/api/employee-company-permissions")
public class EmployeeCompanyPermissionResource {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeCompanyPermissionResource.class);

    private static final String ENTITY_NAME = "employeeCompanyPermission";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final EmployeeCompanyPermissionService employeeCompanyPermissionService;

    private final EmployeeCompanyPermissionRepository employeeCompanyPermissionRepository;

    public EmployeeCompanyPermissionResource(
        EmployeeCompanyPermissionService employeeCompanyPermissionService,
        EmployeeCompanyPermissionRepository employeeCompanyPermissionRepository
    ) {
        this.employeeCompanyPermissionService = employeeCompanyPermissionService;
        this.employeeCompanyPermissionRepository = employeeCompanyPermissionRepository;
    }

    /**
     * {@code POST  /employee-company-permissions} : Create a new employeeCompanyPermission.
     *
     * @param employeeCompanyPermissionDTO the employeeCompanyPermissionDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new employeeCompanyPermissionDTO, or with status {@code 400 (Bad Request)} if the employeeCompanyPermission has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<EmployeeCompanyPermissionDTO> createEmployeeCompanyPermission(
        @RequestBody EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to save EmployeeCompanyPermission : {}", employeeCompanyPermissionDTO);
        if (employeeCompanyPermissionDTO.getId() != null) {
            throw new BadRequestAlertException("A new employeeCompanyPermission cannot already have an ID", ENTITY_NAME, "idexists");
        }
        employeeCompanyPermissionDTO = employeeCompanyPermissionService.save(employeeCompanyPermissionDTO);
        return ResponseEntity.created(new URI("/api/employee-company-permissions/" + employeeCompanyPermissionDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, employeeCompanyPermissionDTO.getId().toString()))
            .body(employeeCompanyPermissionDTO);
    }

    /**
     * {@code PUT  /employee-company-permissions/:id} : Updates an existing employeeCompanyPermission.
     *
     * @param id the id of the employeeCompanyPermissionDTO to save.
     * @param employeeCompanyPermissionDTO the employeeCompanyPermissionDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeCompanyPermissionDTO,
     * or with status {@code 400 (Bad Request)} if the employeeCompanyPermissionDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the employeeCompanyPermissionDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<EmployeeCompanyPermissionDTO> updateEmployeeCompanyPermission(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update EmployeeCompanyPermission : {}, {}", id, employeeCompanyPermissionDTO);
        if (employeeCompanyPermissionDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, employeeCompanyPermissionDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!employeeCompanyPermissionRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        employeeCompanyPermissionDTO = employeeCompanyPermissionService.update(employeeCompanyPermissionDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, employeeCompanyPermissionDTO.getId().toString()))
            .body(employeeCompanyPermissionDTO);
    }

    /**
     * {@code PATCH  /employee-company-permissions/:id} : Partial updates given fields of an existing employeeCompanyPermission, field will ignore if it is null
     *
     * @param id the id of the employeeCompanyPermissionDTO to save.
     * @param employeeCompanyPermissionDTO the employeeCompanyPermissionDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeCompanyPermissionDTO,
     * or with status {@code 400 (Bad Request)} if the employeeCompanyPermissionDTO is not valid,
     * or with status {@code 404 (Not Found)} if the employeeCompanyPermissionDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the employeeCompanyPermissionDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<EmployeeCompanyPermissionDTO> partialUpdateEmployeeCompanyPermission(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update EmployeeCompanyPermission partially : {}, {}", id, employeeCompanyPermissionDTO);
        if (employeeCompanyPermissionDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, employeeCompanyPermissionDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!employeeCompanyPermissionRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<EmployeeCompanyPermissionDTO> result = employeeCompanyPermissionService.partialUpdate(employeeCompanyPermissionDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, employeeCompanyPermissionDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /employee-company-permissions} : get all the employeeCompanyPermissions.
     *
     * @param eagerload flag to eager load entities from relationships (This is applicable for many-to-many).
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of employeeCompanyPermissions in body.
     */
    @GetMapping("")
    public List<EmployeeCompanyPermissionDTO> getAllEmployeeCompanyPermissions(
        @RequestParam(name = "eagerload", required = false, defaultValue = "true") boolean eagerload
    ) {
        LOG.debug("REST request to get all EmployeeCompanyPermissions");
        return employeeCompanyPermissionService.findAll();
    }

    /**
     * {@code GET  /employee-company-permissions/:id} : get the "id" employeeCompanyPermission.
     *
     * @param id the id of the employeeCompanyPermissionDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the employeeCompanyPermissionDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<EmployeeCompanyPermissionDTO> getEmployeeCompanyPermission(@PathVariable("id") Long id) {
        LOG.debug("REST request to get EmployeeCompanyPermission : {}", id);
        Optional<EmployeeCompanyPermissionDTO> employeeCompanyPermissionDTO = employeeCompanyPermissionService.findOne(id);
        return ResponseUtil.wrapOrNotFound(employeeCompanyPermissionDTO);
    }

    /**
     * {@code DELETE  /employee-company-permissions/:id} : delete the "id" employeeCompanyPermission.
     *
     * @param id the id of the employeeCompanyPermissionDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEmployeeCompanyPermission(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete EmployeeCompanyPermission : {}", id);
        employeeCompanyPermissionService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
