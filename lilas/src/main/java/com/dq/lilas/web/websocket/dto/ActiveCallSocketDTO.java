package com.dq.lilas.web.websocket.dto;

import java.util.HashSet;
import java.util.Set;

public class ActiveCallSocketDTO {
    private String caller;
    private Long room;
    private Set<String> users = new HashSet<>();

    public ActiveCallSocketDTO(String caller, Long room, Set<String> users) {
        this.caller = caller;
        this.room = room;
        this.users = users;
    }

    public String getCaller() {
        return caller;
    }

    public void setCaller(String caller) {
        this.caller = caller;
    }

    public Long getRoom() {
        return room;
    }

    public void setRoom(Long room) {
        this.room = room;
    }

    public Set<String> getUsers() {
        return users;
    }

    public void setUsers(Set<String> users) {
        this.users = users;
    }

    public void addUser(String user) {
        this.users.add(user);
    }

    public void removeUser(String user) {
        this.users.removeIf(s -> s.equals(user));
    }
}
