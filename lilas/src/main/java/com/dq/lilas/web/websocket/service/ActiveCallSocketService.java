package com.dq.lilas.web.websocket.service;

import com.dq.lilas.web.websocket.dto.ActiveCallSocketDTO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.messaging.simp.user.SimpUserRegistry;
import org.springframework.stereotype.Service;

import java.util.HashSet;
import java.util.Set;

@Service
public class ActiveCallSocketService {

    // In memory data
    private final Set<ActiveCallSocketDTO> activeCallSocketDTOS = new HashSet<>();

    @Autowired
    SimpUserRegistry userRegistry;

    public Set<ActiveCallSocketDTO> getActiveCallSocketList() {
        return activeCallSocketDTOS;
    }

    public ActiveCallSocketDTO findActiveCallSocketByCaller(String username) {
        return
            activeCallSocketDTOS.stream()
                .filter(dto -> dto.getCaller().equals(username))
                .findAny()
                .orElse(null);
    }

    public ActiveCallSocketDTO findActiveCallSocketByRoom(Long room) {
        return
            activeCallSocketDTOS.stream()
                .filter(dto -> dto.getRoom().equals(room))
                .findAny()
                .orElse(null);
    }

    public void updateActiveCallSocketDTOUsers(Long room, String user, boolean isAdd) {
        ActiveCallSocketDTO activeCallSocketDTO = findActiveCallSocketByRoom(room);
        if(isAdd) {
            activeCallSocketDTO.addUser(user);
        } else {
            activeCallSocketDTO.removeUser(user);
        }
    }

    public int countActiveCallUsers(Long room) {
        ActiveCallSocketDTO activeCallSocketDTO = findActiveCallSocketByRoom(room);
        return activeCallSocketDTO
            .getUsers()
            .size();
    }

    public boolean checkIfUserInActiveCallByRoom(Long room, String username) {
        return activeCallSocketDTOS.stream()
            .anyMatch(dto ->
                dto.getRoom().equals(room) &&
                    dto.getUsers().contains(username)
            );
    }

    public void removeByRoom(Long room) {
        activeCallSocketDTOS.removeIf(callSocketDTO -> callSocketDTO.getRoom().equals(room));
    }

    public void create(Long roomIdLong, String caller, Set<String> users) {
        removeByRoom(roomIdLong);
        activeCallSocketDTOS.add(
            new ActiveCallSocketDTO(
                caller,
                roomIdLong,
                users
            )
        );
    }

    public void removeUser(String username) {
        activeCallSocketDTOS.forEach(dto -> {
            if(dto.getCaller().equals(username)) {
                dto.setCaller(null);
            }
            dto.getUsers().removeIf(s -> s.equals(username));
        });
    }
}
