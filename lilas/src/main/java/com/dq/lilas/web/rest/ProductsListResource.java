package com.dq.lilas.web.rest;

import com.dq.lilas.repository.ProductsListRepository;
import com.dq.lilas.service.ProductsListService;
import com.dq.lilas.service.dto.ProductsListDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.ProductsList}.
 */
@RestController
@RequestMapping("/api/products-lists")
public class ProductsListResource {

    private static final Logger LOG = LoggerFactory.getLogger(ProductsListResource.class);

    private static final String ENTITY_NAME = "productsList";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final ProductsListService productsListService;

    private final ProductsListRepository productsListRepository;

    public ProductsListResource(ProductsListService productsListService, ProductsListRepository productsListRepository) {
        this.productsListService = productsListService;
        this.productsListRepository = productsListRepository;
    }

    /**
     * {@code POST  /products-lists} : Create a new productsList.
     *
     * @param productsListDTO the productsListDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new productsListDTO, or with status {@code 400 (Bad Request)} if the productsList has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<ProductsListDTO> createProductsList(@RequestBody ProductsListDTO productsListDTO) throws URISyntaxException {
        LOG.debug("REST request to save ProductsList : {}", productsListDTO);
        if (productsListDTO.getId() != null) {
            throw new BadRequestAlertException("A new productsList cannot already have an ID", ENTITY_NAME, "idexists");
        }
        productsListDTO = productsListService.save(productsListDTO);
        return ResponseEntity.created(new URI("/api/products-lists/" + productsListDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, productsListDTO.getId().toString()))
            .body(productsListDTO);
    }

    /**
     * {@code PUT  /products-lists/:id} : Updates an existing productsList.
     *
     * @param id the id of the productsListDTO to save.
     * @param productsListDTO the productsListDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated productsListDTO,
     * or with status {@code 400 (Bad Request)} if the productsListDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the productsListDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<ProductsListDTO> updateProductsList(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody ProductsListDTO productsListDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update ProductsList : {}, {}", id, productsListDTO);
        if (productsListDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, productsListDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!productsListRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        productsListDTO = productsListService.update(productsListDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, productsListDTO.getId().toString()))
            .body(productsListDTO);
    }

    /**
     * {@code PATCH  /products-lists/:id} : Partial updates given fields of an existing productsList, field will ignore if it is null
     *
     * @param id the id of the productsListDTO to save.
     * @param productsListDTO the productsListDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated productsListDTO,
     * or with status {@code 400 (Bad Request)} if the productsListDTO is not valid,
     * or with status {@code 404 (Not Found)} if the productsListDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the productsListDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<ProductsListDTO> partialUpdateProductsList(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody ProductsListDTO productsListDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update ProductsList partially : {}, {}", id, productsListDTO);
        if (productsListDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, productsListDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!productsListRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<ProductsListDTO> result = productsListService.partialUpdate(productsListDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, productsListDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /products-lists} : get all the productsLists.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of productsLists in body.
     */
    @GetMapping("")
    public ResponseEntity<List<ProductsListDTO>> getAllProductsLists(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of ProductsLists");
        Page<ProductsListDTO> page = productsListService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /products-lists/:id} : get the "id" productsList.
     *
     * @param id the id of the productsListDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the productsListDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<ProductsListDTO> getProductsList(@PathVariable("id") Long id) {
        LOG.debug("REST request to get ProductsList : {}", id);
        Optional<ProductsListDTO> productsListDTO = productsListService.findOne(id);
        return ResponseUtil.wrapOrNotFound(productsListDTO);
    }

    /**
     * {@code DELETE  /products-lists/:id} : delete the "id" productsList.
     *
     * @param id the id of the productsListDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteProductsList(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete ProductsList : {}", id);
        productsListService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
