package com.dq.lilas.web.rest;

import com.dq.lilas.repository.UnitlangRepository;
import com.dq.lilas.service.UnitlangService;
import com.dq.lilas.service.dto.UnitlangDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Unitlang}.
 */
@RestController
@RequestMapping("/api/unitlangs")
public class UnitlangResource {

    private static final Logger LOG = LoggerFactory.getLogger(UnitlangResource.class);

    private static final String ENTITY_NAME = "unitlang";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final UnitlangService unitlangService;

    private final UnitlangRepository unitlangRepository;

    public UnitlangResource(UnitlangService unitlangService, UnitlangRepository unitlangRepository) {
        this.unitlangService = unitlangService;
        this.unitlangRepository = unitlangRepository;
    }

    /**
     * {@code POST  /unitlangs} : Create a new unitlang.
     *
     * @param unitlangDTO the unitlangDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new unitlangDTO, or with status {@code 400 (Bad Request)} if the unitlang has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<UnitlangDTO> createUnitlang(@Valid @RequestBody UnitlangDTO unitlangDTO) throws URISyntaxException {
        LOG.debug("REST request to save Unitlang : {}", unitlangDTO);
        if (unitlangDTO.getId() != null) {
            throw new BadRequestAlertException("A new unitlang cannot already have an ID", ENTITY_NAME, "idexists");
        }
        unitlangDTO = unitlangService.save(unitlangDTO);
        return ResponseEntity.created(new URI("/api/unitlangs/" + unitlangDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, unitlangDTO.getId().toString()))
            .body(unitlangDTO);
    }

    /**
     * {@code PUT  /unitlangs/:id} : Updates an existing unitlang.
     *
     * @param id the id of the unitlangDTO to save.
     * @param unitlangDTO the unitlangDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated unitlangDTO,
     * or with status {@code 400 (Bad Request)} if the unitlangDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the unitlangDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<UnitlangDTO> updateUnitlang(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody UnitlangDTO unitlangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Unitlang : {}, {}", id, unitlangDTO);
        if (unitlangDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, unitlangDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!unitlangRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        unitlangDTO = unitlangService.update(unitlangDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, unitlangDTO.getId().toString()))
            .body(unitlangDTO);
    }

    /**
     * {@code PATCH  /unitlangs/:id} : Partial updates given fields of an existing unitlang, field will ignore if it is null
     *
     * @param id the id of the unitlangDTO to save.
     * @param unitlangDTO the unitlangDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated unitlangDTO,
     * or with status {@code 400 (Bad Request)} if the unitlangDTO is not valid,
     * or with status {@code 404 (Not Found)} if the unitlangDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the unitlangDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<UnitlangDTO> partialUpdateUnitlang(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody UnitlangDTO unitlangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Unitlang partially : {}, {}", id, unitlangDTO);
        if (unitlangDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, unitlangDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!unitlangRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<UnitlangDTO> result = unitlangService.partialUpdate(unitlangDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, unitlangDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /unitlangs} : get all the unitlangs.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of unitlangs in body.
     */
    @GetMapping("")
    public ResponseEntity<List<UnitlangDTO>> getAllUnitlangs(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of Unitlangs");
        Page<UnitlangDTO> page = unitlangService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /unitlangs/:id} : get the "id" unitlang.
     *
     * @param id the id of the unitlangDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the unitlangDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<UnitlangDTO> getUnitlang(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Unitlang : {}", id);
        Optional<UnitlangDTO> unitlangDTO = unitlangService.findOne(id);
        return ResponseUtil.wrapOrNotFound(unitlangDTO);
    }

    /**
     * {@code DELETE  /unitlangs/:id} : delete the "id" unitlang.
     *
     * @param id the id of the unitlangDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteUnitlang(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Unitlang : {}", id);
        unitlangService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
