package com.dq.lilas.web.websocket.dto;

import com.dq.lilas.web.websocket.enumeration.NotificationSocketType;

public class NotificationSocketDTO {

    private String content;
    private NotificationSocketType type;
    private String metadata;

    public String getContent() {
        return content;
    }

    public void setContent(String content) {
        this.content = content;
    }

    public NotificationSocketType getType() {
        return type;
    }

    public void setType(NotificationSocketType type) {
        this.type = type;
    }

    public String getMetadata() {
        return metadata;
    }

    public void setMetadata(String metadata) {
        this.metadata = metadata;
    }
}
