package com.dq.lilas.web.rest;

import com.dq.lilas.repository.EmployeelangRepository;
import com.dq.lilas.service.EmployeelangService;
import com.dq.lilas.service.dto.EmployeelangDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Employeelang}.
 */
@RestController
@RequestMapping("/api/employeelangs")
public class EmployeelangResource {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeelangResource.class);

    private static final String ENTITY_NAME = "employeelang";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final EmployeelangService employeelangService;

    private final EmployeelangRepository employeelangRepository;

    public EmployeelangResource(EmployeelangService employeelangService, EmployeelangRepository employeelangRepository) {
        this.employeelangService = employeelangService;
        this.employeelangRepository = employeelangRepository;
    }

    /**
     * {@code POST  /employeelangs} : Create a new employeelang.
     *
     * @param employeelangDTO the employeelangDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new employeelangDTO, or with status {@code 400 (Bad Request)} if the employeelang has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<EmployeelangDTO> createEmployeelang(@Valid @RequestBody EmployeelangDTO employeelangDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save Employeelang : {}", employeelangDTO);
        if (employeelangDTO.getId() != null) {
            throw new BadRequestAlertException("A new employeelang cannot already have an ID", ENTITY_NAME, "idexists");
        }
        employeelangDTO = employeelangService.save(employeelangDTO);
        return ResponseEntity.created(new URI("/api/employeelangs/" + employeelangDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, employeelangDTO.getId().toString()))
            .body(employeelangDTO);
    }

    /**
     * {@code PUT  /employeelangs/:id} : Updates an existing employeelang.
     *
     * @param id the id of the employeelangDTO to save.
     * @param employeelangDTO the employeelangDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeelangDTO,
     * or with status {@code 400 (Bad Request)} if the employeelangDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the employeelangDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<EmployeelangDTO> updateEmployeelang(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody EmployeelangDTO employeelangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Employeelang : {}, {}", id, employeelangDTO);
        if (employeelangDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, employeelangDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!employeelangRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        employeelangDTO = employeelangService.update(employeelangDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, employeelangDTO.getId().toString()))
            .body(employeelangDTO);
    }

    /**
     * {@code PATCH  /employeelangs/:id} : Partial updates given fields of an existing employeelang, field will ignore if it is null
     *
     * @param id the id of the employeelangDTO to save.
     * @param employeelangDTO the employeelangDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeelangDTO,
     * or with status {@code 400 (Bad Request)} if the employeelangDTO is not valid,
     * or with status {@code 404 (Not Found)} if the employeelangDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the employeelangDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<EmployeelangDTO> partialUpdateEmployeelang(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody EmployeelangDTO employeelangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Employeelang partially : {}, {}", id, employeelangDTO);
        if (employeelangDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, employeelangDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!employeelangRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<EmployeelangDTO> result = employeelangService.partialUpdate(employeelangDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, employeelangDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /employeelangs} : get all the employeelangs.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of employeelangs in body.
     */
    @GetMapping("")
    public ResponseEntity<List<EmployeelangDTO>> getAllEmployeelangs(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of Employeelangs");
        Page<EmployeelangDTO> page = employeelangService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /employeelangs/:id} : get the "id" employeelang.
     *
     * @param id the id of the employeelangDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the employeelangDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<EmployeelangDTO> getEmployeelang(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Employeelang : {}", id);
        Optional<EmployeelangDTO> employeelangDTO = employeelangService.findOne(id);
        return ResponseUtil.wrapOrNotFound(employeelangDTO);
    }

    /**
     * {@code DELETE  /employeelangs/:id} : delete the "id" employeelang.
     *
     * @param id the id of the employeelangDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEmployeelang(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Employeelang : {}", id);
        employeelangService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
