package com.dq.lilas.web.rest;

import com.dq.lilas.repository.PromotionDetailsRepository;
import com.dq.lilas.service.PromotionDetailsService;
import com.dq.lilas.service.dto.PromotionDetailsDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.PromotionDetails}.
 */
@RestController
@RequestMapping("/api/promotion-details")
public class PromotionDetailsResource {

    private static final Logger LOG = LoggerFactory.getLogger(PromotionDetailsResource.class);

    private static final String ENTITY_NAME = "promotionDetails";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final PromotionDetailsService promotionDetailsService;

    private final PromotionDetailsRepository promotionDetailsRepository;

    public PromotionDetailsResource(
        PromotionDetailsService promotionDetailsService,
        PromotionDetailsRepository promotionDetailsRepository
    ) {
        this.promotionDetailsService = promotionDetailsService;
        this.promotionDetailsRepository = promotionDetailsRepository;
    }

    /**
     * {@code POST  /promotion-details} : Create a new promotionDetails.
     *
     * @param promotionDetailsDTO the promotionDetailsDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new promotionDetailsDTO, or with status {@code 400 (Bad Request)} if the promotionDetails has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<PromotionDetailsDTO> createPromotionDetails(@RequestBody PromotionDetailsDTO promotionDetailsDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save PromotionDetails : {}", promotionDetailsDTO);
        if (promotionDetailsDTO.getId() != null) {
            throw new BadRequestAlertException("A new promotionDetails cannot already have an ID", ENTITY_NAME, "idexists");
        }
        promotionDetailsDTO = promotionDetailsService.save(promotionDetailsDTO);
        return ResponseEntity.created(new URI("/api/promotion-details/" + promotionDetailsDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, promotionDetailsDTO.getId().toString()))
            .body(promotionDetailsDTO);
    }

    /**
     * {@code PUT  /promotion-details/:id} : Updates an existing promotionDetails.
     *
     * @param id the id of the promotionDetailsDTO to save.
     * @param promotionDetailsDTO the promotionDetailsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated promotionDetailsDTO,
     * or with status {@code 400 (Bad Request)} if the promotionDetailsDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the promotionDetailsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<PromotionDetailsDTO> updatePromotionDetails(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody PromotionDetailsDTO promotionDetailsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update PromotionDetails : {}, {}", id, promotionDetailsDTO);
        if (promotionDetailsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, promotionDetailsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!promotionDetailsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        promotionDetailsDTO = promotionDetailsService.update(promotionDetailsDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, promotionDetailsDTO.getId().toString()))
            .body(promotionDetailsDTO);
    }

    /**
     * {@code PATCH  /promotion-details/:id} : Partial updates given fields of an existing promotionDetails, field will ignore if it is null
     *
     * @param id the id of the promotionDetailsDTO to save.
     * @param promotionDetailsDTO the promotionDetailsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated promotionDetailsDTO,
     * or with status {@code 400 (Bad Request)} if the promotionDetailsDTO is not valid,
     * or with status {@code 404 (Not Found)} if the promotionDetailsDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the promotionDetailsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<PromotionDetailsDTO> partialUpdatePromotionDetails(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody PromotionDetailsDTO promotionDetailsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update PromotionDetails partially : {}, {}", id, promotionDetailsDTO);
        if (promotionDetailsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, promotionDetailsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!promotionDetailsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<PromotionDetailsDTO> result = promotionDetailsService.partialUpdate(promotionDetailsDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, promotionDetailsDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /promotion-details} : get all the promotionDetails.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of promotionDetails in body.
     */
    @GetMapping("")
    public ResponseEntity<List<PromotionDetailsDTO>> getAllPromotionDetails(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of PromotionDetails");
        Page<PromotionDetailsDTO> page = promotionDetailsService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /promotion-details/:id} : get the "id" promotionDetails.
     *
     * @param id the id of the promotionDetailsDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the promotionDetailsDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<PromotionDetailsDTO> getPromotionDetails(@PathVariable("id") Long id) {
        LOG.debug("REST request to get PromotionDetails : {}", id);
        Optional<PromotionDetailsDTO> promotionDetailsDTO = promotionDetailsService.findOne(id);
        return ResponseUtil.wrapOrNotFound(promotionDetailsDTO);
    }

    /**
     * {@code DELETE  /promotion-details/:id} : delete the "id" promotionDetails.
     *
     * @param id the id of the promotionDetailsDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deletePromotionDetails(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete PromotionDetails : {}", id);
        promotionDetailsService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
