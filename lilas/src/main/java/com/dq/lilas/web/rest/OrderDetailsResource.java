package com.dq.lilas.web.rest;

import com.dq.lilas.repository.OrderDetailsRepository;
import com.dq.lilas.service.OrderDetailsService;
import com.dq.lilas.service.dto.OrderDetailsDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.OrderDetails}.
 */
@RestController
@RequestMapping("/api/order-details")
public class OrderDetailsResource {

    private static final Logger LOG = LoggerFactory.getLogger(OrderDetailsResource.class);

    private static final String ENTITY_NAME = "orderDetails";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final OrderDetailsService orderDetailsService;

    private final OrderDetailsRepository orderDetailsRepository;

    public OrderDetailsResource(OrderDetailsService orderDetailsService, OrderDetailsRepository orderDetailsRepository) {
        this.orderDetailsService = orderDetailsService;
        this.orderDetailsRepository = orderDetailsRepository;
    }

    /**
     * {@code POST  /order-details} : Create a new orderDetails.
     *
     * @param orderDetailsDTO the orderDetailsDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new orderDetailsDTO, or with status {@code 400 (Bad Request)} if the orderDetails has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<OrderDetailsDTO> createOrderDetails(@RequestBody OrderDetailsDTO orderDetailsDTO) throws URISyntaxException {
        LOG.debug("REST request to save OrderDetails : {}", orderDetailsDTO);
        if (orderDetailsDTO.getId() != null) {
            throw new BadRequestAlertException("A new orderDetails cannot already have an ID", ENTITY_NAME, "idexists");
        }
        orderDetailsDTO = orderDetailsService.save(orderDetailsDTO);
        return ResponseEntity.created(new URI("/api/order-details/" + orderDetailsDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, orderDetailsDTO.getId().toString()))
            .body(orderDetailsDTO);
    }

    /**
     * {@code PUT  /order-details/:id} : Updates an existing orderDetails.
     *
     * @param id the id of the orderDetailsDTO to save.
     * @param orderDetailsDTO the orderDetailsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated orderDetailsDTO,
     * or with status {@code 400 (Bad Request)} if the orderDetailsDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the orderDetailsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<OrderDetailsDTO> updateOrderDetails(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody OrderDetailsDTO orderDetailsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update OrderDetails : {}, {}", id, orderDetailsDTO);
        if (orderDetailsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, orderDetailsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!orderDetailsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        orderDetailsDTO = orderDetailsService.update(orderDetailsDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, orderDetailsDTO.getId().toString()))
            .body(orderDetailsDTO);
    }

    /**
     * {@code PATCH  /order-details/:id} : Partial updates given fields of an existing orderDetails, field will ignore if it is null
     *
     * @param id the id of the orderDetailsDTO to save.
     * @param orderDetailsDTO the orderDetailsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated orderDetailsDTO,
     * or with status {@code 400 (Bad Request)} if the orderDetailsDTO is not valid,
     * or with status {@code 404 (Not Found)} if the orderDetailsDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the orderDetailsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<OrderDetailsDTO> partialUpdateOrderDetails(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody OrderDetailsDTO orderDetailsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update OrderDetails partially : {}, {}", id, orderDetailsDTO);
        if (orderDetailsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, orderDetailsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!orderDetailsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<OrderDetailsDTO> result = orderDetailsService.partialUpdate(orderDetailsDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, orderDetailsDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /order-details} : get all the orderDetails.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of orderDetails in body.
     */
    @GetMapping("")
    public ResponseEntity<List<OrderDetailsDTO>> getAllOrderDetails(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of OrderDetails");
        Page<OrderDetailsDTO> page = orderDetailsService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /order-details/:id} : get the "id" orderDetails.
     *
     * @param id the id of the orderDetailsDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the orderDetailsDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<OrderDetailsDTO> getOrderDetails(@PathVariable("id") Long id) {
        LOG.debug("REST request to get OrderDetails : {}", id);
        Optional<OrderDetailsDTO> orderDetailsDTO = orderDetailsService.findOne(id);
        return ResponseUtil.wrapOrNotFound(orderDetailsDTO);
    }

    /**
     * {@code DELETE  /order-details/:id} : delete the "id" orderDetails.
     *
     * @param id the id of the orderDetailsDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteOrderDetails(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete OrderDetails : {}", id);
        orderDetailsService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * {@code PATCH  /order-details/cancel/:id} : cancel the "id" orderDetails.
     *
     * @param id the id of the orderDetailsDTO to cancel.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @PatchMapping("/cancel/{id}")
    public ResponseEntity<OrderDetailsDTO> deleteOrderLine(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete OrderDetails : {}", id);
        OrderDetailsDTO orderDetailsDTO = orderDetailsService.deleteOrderLine(id);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, orderDetailsDTO.getId().toString()))
            .body(orderDetailsDTO);
    }

    /**
     * {@code PATCH  /order-details/validate/:id} : validate the "id" orderDetails.
     *
     * @param id the id of the orderDetailsDTO to validate.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @PatchMapping("/validate/{id}")
    public ResponseEntity<OrderDetailsDTO> validateOrderLine(@PathVariable("id") Long id) {
        LOG.debug("REST request to validate OrderDetails : {}", id);
        OrderDetailsDTO orderDetailsDTO = orderDetailsService.validateOrderLine(id);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, orderDetailsDTO.getId().toString()))
            .body(orderDetailsDTO);
    }
}
