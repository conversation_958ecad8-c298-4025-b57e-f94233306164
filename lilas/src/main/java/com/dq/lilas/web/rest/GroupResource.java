package com.dq.lilas.web.rest;

import com.dq.lilas.service.GroupService;
import com.dq.lilas.service.dto.GroupDTO;
import com.dq.lilas.service.dto.UserDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import io.micrometer.core.annotation.Timed;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * REST controller for managing Group.
 */
@RestController
@RequestMapping("/api")
public class GroupResource {

    private final Logger log = LoggerFactory.getLogger(GroupResource.class);

    private static final String ENTITY_NAME = "group";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final GroupService groupService;

    public GroupResource(GroupService groupService) {
        this.groupService = groupService;
    }

    /**
     * POST  /groups : Create a new group.
     *
     * @param groupDTO the groupDTO to create
     * @return the ResponseEntity with status 201 (Created) and with body the new groupDTO, or with status 400 (Bad Request) if the group has already an ID
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PostMapping("/groups")
    @Timed
    public ResponseEntity<GroupDTO> createGroup(@RequestBody GroupDTO groupDTO) throws URISyntaxException {
        log.debug("REST request to save Group : {}", groupDTO);
        if (groupDTO.getId() != null) {
            throw new BadRequestAlertException("A new group cannot already have an ID", ENTITY_NAME, "idexists");
        }
        GroupDTO result = groupService.save(groupDTO);
        return ResponseEntity.created(new URI("/api/groups/" + result.getId()))
                .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, result.getId().toString()))
                .body(result);
    }

    @GetMapping("/groups/logged/joined/{groupName}")
    @Timed
    public boolean checkIfLoggedUserJoinedGroupByName(@PathVariable String groupName) throws URISyntaxException {
        log.debug("REST request to check if  User in Group");
        if (groupName == null) {
            throw new BadRequestAlertException("Group Name Is NULL", ENTITY_NAME, "null");
        }
        return groupService.checkIfUserIsInGroupByName(groupName);
    }

    /**
     * POST: Add Logged User from group By Name.
     *
     * @param name      the group entity name
     * @return GroupDTO
     */
    @PostMapping("/groups/logged/{name}/add")
    @Timed
    public ResponseEntity<GroupDTO> addLoggedUserToGroup(@PathVariable String name) throws URISyntaxException {
        log.debug("REST request to add User to Group : {}", name);
        if (name == null) {
            throw new BadRequestAlertException("Group Name Is NULL", ENTITY_NAME, "null");
        }
        GroupDTO groupDTO = groupService.addOrRemoveLoggedUserFromGroup(name, true);
        return ResponseUtil.wrapOrNotFound(Optional.of(groupDTO));
    }

    /**
     * POST: Remove Logged User from group By Name.
     *
     * @param name      the group entity name
     * @return GroupDTO
     */
    @PostMapping("/groups/logged/{name}/remove")
    @Timed
    public ResponseEntity<GroupDTO> removeLoggedUserFromGroup(@PathVariable String name) throws URISyntaxException {
        log.debug("REST request to remove User from Group : {}", name);
        if (name == null) {
            throw new BadRequestAlertException("Group Name Is NULL", ENTITY_NAME, "null");
        }
        GroupDTO groupDTO = groupService.addOrRemoveLoggedUserFromGroup(name, false);
        return ResponseUtil.wrapOrNotFound(Optional.of(groupDTO));
    }

    /**
     * PUT  /groups : Updates an existing group.
     *
     * @param groupDTO the groupDTO to update
     * @return the ResponseEntity with status 200 (OK) and with body the updated groupDTO,
     * or with status 400 (Bad Request) if the groupDTO is not valid,
     * or with status 500 (Internal Server Error) if the groupDTO couldn't be updated
     * @throws URISyntaxException if the Location URI syntax is incorrect
     */
    @PutMapping("/groups")
    @Timed
    public ResponseEntity<GroupDTO> updateGroup(@RequestBody GroupDTO groupDTO) throws URISyntaxException {
        log.debug("REST request to update Group : {}", groupDTO);
        if (groupDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        GroupDTO result = groupService.save(groupDTO);
        return ResponseEntity.ok()
                .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, groupDTO.getId().toString()))
                .body(result);
    }

    /**
     * GET  /groups : get all the groups.
     *
     * @param pageable the pagination information
     * @param eagerload flag to eager load entities from relationships (This is applicable for many-to-many)
     * @return the ResponseEntity with status 200 (OK) and the list of groups in body
     */
    @GetMapping("/groups")
    @Timed
    public ResponseEntity<List<GroupDTO>> getAllGroups(Pageable pageable, @RequestParam(required = false, defaultValue = "false") boolean eagerload) {
        log.debug("REST request to get a page of Groups");
        Page<GroupDTO> page;
        if (eagerload) {
            page = groupService.findAllWithEagerRelationships(pageable);
        } else {
            page = groupService.findAll(pageable);
        }
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }


    /**
     * {@code GET  /groups/all} : get all the groups.
     *
     * @param eagerload flag to eager load entities from relationships (This is applicable for many-to-many).
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of groups in body.
     */
    @GetMapping("/groups/all")
    public List<GroupDTO> getAllGroups(@RequestParam(required = false, defaultValue = "false") boolean eagerload) {
        log.debug("REST request to get all Groups");
        return groupService.findAll();
    }

    /**
     * {@code GET  /groups/logged/all} : get all the groups joined by logged user.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of groups in body.
     */
    @GetMapping("/groups/logged/all")
    public List<GroupDTO> getAllGroupsJoinedByLoggedUser() {
        log.debug("REST request to get all Groups Joined by logged user");
        return groupService.findGroupsByLoggedUser();
    }

    /**
     * {@code GET  /groups/logged/users} : get all users joined to same groups as the logged user.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of users in body.
     */
    @GetMapping("/groups/logged/users")
    public List<UserDTO> getUsersJoinedToSameGroupsAsLoggedUser() {
        log.debug("REST request to get all Groups Joined by current user");
        return groupService.getUsersJoinedToSameGroupsAsLoggedUser();
    }

    /**
     * {@code GET  /groups/{id}/users : get all users of groups.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of groups in body.
     */
    @GetMapping("/groups/{id}/users")
    public Set<UserDTO> getUsersInGroup(@PathVariable Long id) {
        log.debug("REST request to get users of a group");
        return groupService.getUsersJoinedToGroup(id);
    }


    /**
     * GET  /groups/:id : get the "id" group.
     *
     * @param id the id of the groupDTO to retrieve
     * @return the ResponseEntity with status 200 (OK) and with body the groupDTO, or with status 404 (Not Found)
     */
    @GetMapping("/groups/{id}")
    @Timed
    public ResponseEntity<GroupDTO> getGroup(@PathVariable Long id) {
        log.debug("REST request to get Group : {}", id);
        Optional<GroupDTO> groupDTO = groupService.findOne(id);
        return ResponseUtil.wrapOrNotFound(groupDTO);
    }

    /**
     * DELETE  /groups/:id : delete the "id" group.
     *
     * @param id the id of the groupDTO to delete
     * @return the ResponseEntity with status 200 (OK)
     */
    @DeleteMapping("/groups/{id}")
    @Timed
    public ResponseEntity<Void> deleteGroup(@PathVariable Long id) {
        log.debug("REST request to delete Group : {}", id);
        groupService.delete(id);
        return ResponseEntity.ok().headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString())).build();
    }


    /**
     * {@code SEARCH  /groups/{id}/users/_search?query=:query} : search for the users corresponding
     * to the query by group.
     *
     * @param id the id of the group
     * @param query the query.
     * @return the result of the search.
     */
    @GetMapping("/groups/{id}/users/_search")
    @Timed
    public ResponseEntity<Set<UserDTO>> searchUsers(@RequestParam String query, @PathVariable Long id) {
        log.debug("REST request to search for users for query by group {}", query);
        final Set<UserDTO> userDTOS = groupService.getUsersJoinedToGroupByNameIsLike(id, query);
        return new ResponseEntity<>(userDTOS, HttpStatus.OK);
    }

}
