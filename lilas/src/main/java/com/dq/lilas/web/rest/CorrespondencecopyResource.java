package com.dq.lilas.web.rest;

import com.dq.lilas.repository.CorrespondencecopyRepository;
import com.dq.lilas.service.CorrespondencecopyService;
import com.dq.lilas.service.dto.CorrespondencecopyDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Correspondencecopy}.
 */
@RestController
@RequestMapping("/api/correspondencecopies")
public class CorrespondencecopyResource {

    private static final Logger LOG = LoggerFactory.getLogger(CorrespondencecopyResource.class);

    private static final String ENTITY_NAME = "correspondencecopy";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final CorrespondencecopyService correspondencecopyService;

    private final CorrespondencecopyRepository correspondencecopyRepository;

    public CorrespondencecopyResource(
        CorrespondencecopyService correspondencecopyService,
        CorrespondencecopyRepository correspondencecopyRepository
    ) {
        this.correspondencecopyService = correspondencecopyService;
        this.correspondencecopyRepository = correspondencecopyRepository;
    }

    /**
     * {@code POST  /correspondencecopies} : Create a new correspondencecopy.
     *
     * @param correspondencecopyDTO the correspondencecopyDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new correspondencecopyDTO, or with status {@code 400 (Bad Request)} if the correspondencecopy has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<CorrespondencecopyDTO> createCorrespondencecopy(@Valid @RequestBody CorrespondencecopyDTO correspondencecopyDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save Correspondencecopy : {}", correspondencecopyDTO);
        if (correspondencecopyDTO.getId() != null) {
            throw new BadRequestAlertException("A new correspondencecopy cannot already have an ID", ENTITY_NAME, "idexists");
        }
        correspondencecopyDTO = correspondencecopyService.save(correspondencecopyDTO);
        return ResponseEntity.created(new URI("/api/correspondencecopies/" + correspondencecopyDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, correspondencecopyDTO.getId().toString()))
            .body(correspondencecopyDTO);
    }

    /**
     * {@code PUT  /correspondencecopies/:id} : Updates an existing correspondencecopy.
     *
     * @param id the id of the correspondencecopyDTO to save.
     * @param correspondencecopyDTO the correspondencecopyDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated correspondencecopyDTO,
     * or with status {@code 400 (Bad Request)} if the correspondencecopyDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the correspondencecopyDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<CorrespondencecopyDTO> updateCorrespondencecopy(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody CorrespondencecopyDTO correspondencecopyDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Correspondencecopy : {}, {}", id, correspondencecopyDTO);
        if (correspondencecopyDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, correspondencecopyDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!correspondencecopyRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        correspondencecopyDTO = correspondencecopyService.update(correspondencecopyDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, correspondencecopyDTO.getId().toString()))
            .body(correspondencecopyDTO);
    }

    /**
     * {@code PATCH  /correspondencecopies/:id} : Partial updates given fields of an existing correspondencecopy, field will ignore if it is null
     *
     * @param id the id of the correspondencecopyDTO to save.
     * @param correspondencecopyDTO the correspondencecopyDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated correspondencecopyDTO,
     * or with status {@code 400 (Bad Request)} if the correspondencecopyDTO is not valid,
     * or with status {@code 404 (Not Found)} if the correspondencecopyDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the correspondencecopyDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<CorrespondencecopyDTO> partialUpdateCorrespondencecopy(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody CorrespondencecopyDTO correspondencecopyDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Correspondencecopy partially : {}, {}", id, correspondencecopyDTO);
        if (correspondencecopyDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, correspondencecopyDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!correspondencecopyRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<CorrespondencecopyDTO> result = correspondencecopyService.partialUpdate(correspondencecopyDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, correspondencecopyDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /correspondencecopies} : get all the correspondencecopies.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of correspondencecopies in body.
     */
    @GetMapping("")
    public ResponseEntity<List<CorrespondencecopyDTO>> getAllCorrespondencecopies(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of Correspondencecopies");
        Page<CorrespondencecopyDTO> page = correspondencecopyService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /correspondencecopies/:id} : get the "id" correspondencecopy.
     *
     * @param id the id of the correspondencecopyDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the correspondencecopyDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<CorrespondencecopyDTO> getCorrespondencecopy(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Correspondencecopy : {}", id);
        Optional<CorrespondencecopyDTO> correspondencecopyDTO = correspondencecopyService.findOne(id);
        return ResponseUtil.wrapOrNotFound(correspondencecopyDTO);
    }

    /**
     * {@code DELETE  /correspondencecopies/:id} : delete the "id" correspondencecopy.
     *
     * @param id the id of the correspondencecopyDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCorrespondencecopy(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Correspondencecopy : {}", id);
        correspondencecopyService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
