package com.dq.lilas.web.rest;

import com.dq.lilas.repository.CorrespondenceRepository;
import com.dq.lilas.service.CorrespondenceService;
import com.dq.lilas.service.dto.CorrespondenceDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Correspondence}.
 */
@RestController
@RequestMapping("/api/correspondences")
public class CorrespondenceResource {

    private static final Logger LOG = LoggerFactory.getLogger(CorrespondenceResource.class);

    private static final String ENTITY_NAME = "correspondence";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final CorrespondenceService correspondenceService;

    private final CorrespondenceRepository correspondenceRepository;

    public CorrespondenceResource(CorrespondenceService correspondenceService, CorrespondenceRepository correspondenceRepository) {
        this.correspondenceService = correspondenceService;
        this.correspondenceRepository = correspondenceRepository;
    }

    /**
     * {@code POST  /correspondences} : Create a new correspondence.
     *
     * @param correspondenceDTO the correspondenceDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new correspondenceDTO, or with status {@code 400 (Bad Request)} if the correspondence has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<CorrespondenceDTO> createCorrespondence(@Valid @RequestBody CorrespondenceDTO correspondenceDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save Correspondence : {}", correspondenceDTO);
        if (correspondenceDTO.getId() != null) {
            throw new BadRequestAlertException("A new correspondence cannot already have an ID", ENTITY_NAME, "idexists");
        }
        correspondenceDTO = correspondenceService.save(correspondenceDTO);
        return ResponseEntity.created(new URI("/api/correspondences/" + correspondenceDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, correspondenceDTO.getId().toString()))
            .body(correspondenceDTO);
    }

    /**
     * {@code PUT  /correspondences/:id} : Updates an existing correspondence.
     *
     * @param id the id of the correspondenceDTO to save.
     * @param correspondenceDTO the correspondenceDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated correspondenceDTO,
     * or with status {@code 400 (Bad Request)} if the correspondenceDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the correspondenceDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<CorrespondenceDTO> updateCorrespondence(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody CorrespondenceDTO correspondenceDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Correspondence : {}, {}", id, correspondenceDTO);
        if (correspondenceDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, correspondenceDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!correspondenceRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        correspondenceDTO = correspondenceService.update(correspondenceDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, correspondenceDTO.getId().toString()))
            .body(correspondenceDTO);
    }

    /**
     * {@code PATCH  /correspondences/:id} : Partial updates given fields of an existing correspondence, field will ignore if it is null
     *
     * @param id the id of the correspondenceDTO to save.
     * @param correspondenceDTO the correspondenceDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated correspondenceDTO,
     * or with status {@code 400 (Bad Request)} if the correspondenceDTO is not valid,
     * or with status {@code 404 (Not Found)} if the correspondenceDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the correspondenceDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<CorrespondenceDTO> partialUpdateCorrespondence(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody CorrespondenceDTO correspondenceDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Correspondence partially : {}, {}", id, correspondenceDTO);
        if (correspondenceDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, correspondenceDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!correspondenceRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<CorrespondenceDTO> result = correspondenceService.partialUpdate(correspondenceDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, correspondenceDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /correspondences} : get all the correspondences.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of correspondences in body.
     */
    @GetMapping("")
    public ResponseEntity<List<CorrespondenceDTO>> getAllCorrespondences(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of Correspondences");
        Page<CorrespondenceDTO> page = correspondenceService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /correspondences/:id} : get the "id" correspondence.
     *
     * @param id the id of the correspondenceDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the correspondenceDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<CorrespondenceDTO> getCorrespondence(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Correspondence : {}", id);
        Optional<CorrespondenceDTO> correspondenceDTO = correspondenceService.findOne(id);
        return ResponseUtil.wrapOrNotFound(correspondenceDTO);
    }

    /**
     * {@code DELETE  /correspondences/:id} : delete the "id" correspondence.
     *
     * @param id the id of the correspondenceDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCorrespondence(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Correspondence : {}", id);
        correspondenceService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
