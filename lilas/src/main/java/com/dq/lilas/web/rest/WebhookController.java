package com.dq.lilas.web.rest;

import com.dq.lilas.config.GraphClientConfiguration;
import com.dq.lilas.domain.enumeration.AlfrescoSite;
import com.dq.lilas.domain.enumeration.MailType;
import com.dq.lilas.domain.enumeration.OrderStatus;
import com.dq.lilas.security.SecurityUtils;
import com.dq.lilas.service.AttachementService;
import com.dq.lilas.service.GraphMessageService;
import com.dq.lilas.service.MailsService;
import com.dq.lilas.service.OrderService;
import com.dq.lilas.service.dto.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsoft.graph.models.*;
import com.microsoft.kiota.serialization.KiotaJsonSerialization;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.util.*;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/notifications")
@RequiredArgsConstructor
public class WebhookController {

    private static final Logger log = LoggerFactory.getLogger(WebhookController.class);

    private final GraphMessageService messageService;
    private final GraphClientConfiguration.GraphProperties props;
    private final MailsService mailsService;
    private final AttachementService attachementService;
    private final OrderService orderService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${ocr.url}")
    String ocrUrl;
    @Value("${alfresco.url}")
    String alfUrl;
    @Value("${alfresco.directory-path}")
    String alfDirectoryPath;
    @Value("${alfresco.default-model-id}")
    String alfModelId;

    /**
     * <p>
     * This method handles the initial
     * <a href="https://docs.microsoft.com/graph/webhooks#notification-endpoint-validation">endpoint
     * validation request sent</a> by Microsoft Graph when the subscription is created.
     *
     * @param validationToken A validation token provided as a query parameter
     * @return a 200 OK response with the validationToken in the text/plain body
     */
    @PostMapping(headers = {"content-type=text/plain"})
    @ResponseBody
    public ResponseEntity<String> handleValidation(
        @RequestParam(value = "validationToken") final String validationToken) {
        return ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN).body(validationToken);
    }

    @PostMapping
    public ResponseEntity<String> handleNotifications(@RequestBody @NonNull final String jsonPayload) {
        try {
            ChangeNotificationCollection notifications = KiotaJsonSerialization.deserialize(
                jsonPayload, ChangeNotificationCollection::createFromDiscriminatorValue);

            if (notifications.getValue() == null) {
                return ResponseEntity.accepted().body("");
            }

            for (ChangeNotification change : notifications.getValue()) {
                if (!isValidClientState(change)) {
                    log.warn("Invalid clientState for notification: {}", change.getId());
                    continue;
                }

                Optional<ResourceIdentifiers> identifiers = extractIdentifiers(Objects.requireNonNull(change.getResource()));
                if (identifiers.isEmpty()) {
                    log.warn("Invalid resource format: {}", change.getResource());
                    continue;
                }

                String userId = identifiers.get().userId();
                String messageId = identifiers.get().messageId();

                log.info("Processing notification - User: {}, Message: {}", userId, messageId);
                processMessage(userId, messageId);
            }

            return ResponseEntity.ok().build();

        } catch (IOException e) {
            log.error("Failed to deserialize notification payload: {}", e.getMessage(), e);
        }

        return ResponseEntity.accepted().body("");
    }

    private boolean isValidClientState(ChangeNotification change) {
        return props.getWebhook().getNotificationSecret().equals(change.getClientState());
    }

    private Optional<ResourceIdentifiers> extractIdentifiers(String resource) {
        String[] parts = resource.split("/");
        if (parts.length >= 4) {
            return Optional.of(new ResourceIdentifiers(parts[1], parts[3]));
        }
        return Optional.empty();
    }

    private void processMessage(String userId, String messageId) {
        try {
            Message message = messageService.getMessageById(userId, messageId);
            log.info("Processing message - Subject: {}, Sender: {}",
                message.getSubject(),
                Objects.requireNonNull(Objects.requireNonNull(message.getSender()).getEmailAddress()).getAddress());

            if (!hasValidAttachments(message)) {
                log.info("Message {} has no attachments, skipping processing", messageId);
                return;
            }

            MailsDTO savedMail = saveMail(message);
            processAttachments(userId, messageId, savedMail);

        } catch (Exception e) {
            log.error("Error processing message {} for user {}: {}", messageId, userId, e.getMessage(), e);
        }
    }

    private boolean hasValidAttachments(Message message) {
        return Boolean.TRUE.equals(message.getHasAttachments());
    }

    private MailsDTO saveMail(Message message) {
        MailsDTO mailsDTO = new MailsDTO();
        mailsDTO.setSubject(message.getSubject());
        mailsDTO.setSender(Objects.requireNonNull(Objects.requireNonNull(message.getSender()).getEmailAddress()).getAddress());
        mailsDTO.setMaildate(Instant.now());

        if (message.getBody() != null && message.getBody().getContent() != null) {
            mailsDTO.setMailbody(message.getBody().getContent());
        }

        if (!CollectionUtils.isEmpty(message.getToRecipients())) {
            mailsDTO.setRecipient(Objects.requireNonNull(message.getToRecipients().get(0).getEmailAddress()).getAddress());
        }

        MailsDTO savedMail = mailsService.save(mailsDTO);
        log.info("Saved mail with ID: {}", savedMail.getId());
        return savedMail;
    }

    private void processAttachments(String userId, String messageId, MailsDTO savedMail) {
        try {
            List<Attachment> attachments = messageService.getMessageAttachments(userId, messageId);
            if (CollectionUtils.isEmpty(attachments)) {
                log.warn("Message {} reported having attachments but none were found", messageId);
                updateMailType(savedMail, MailType.NOT_BC);
                return;
            }

            boolean hasBCAttachment = false;
            for (Attachment attachment : attachments) {
                // upload attachment to alfresco
                String docExternalId = uploadAttachment(attachment);

                // save attachment with proper context
                AttachementDTO savedAttachment = saveAttachment(attachment, docExternalId, userId, savedMail);

                boolean isBC = processBCAttachment(attachment, savedMail, savedAttachment);
                hasBCAttachment |= isBC;
            }

            if (!hasBCAttachment) {
                updateMailType(savedMail, MailType.NOT_BC);
            }

        } catch (Exception e) {
            log.error("Error processing attachments for message {}: {}", messageId, e.getMessage());
        }
    }

    private AttachementDTO saveAttachment(Attachment attachment, String docExternalId, String userId, MailsDTO savedMail) {
        AttachementDTO attachementDTO = new AttachementDTO();

        // Basic attachment information
        attachementDTO.setFilenameattachment(attachment.getName());
        attachementDTO.setSizeAttachement(attachment.getSize() != null ? attachment.getSize().toString() : null);
        attachementDTO.setDatejcattachment(Instant.now());
        attachementDTO.setIdDocAttachment(docExternalId);

        // Set user information - try to get current authenticated user first, fallback to userId parameter
        String currentUser = SecurityUtils.getCurrentUserLogin().orElse(userId);
        if (currentUser != null && !currentUser.trim().isEmpty()) {
            attachementDTO.setUserattachment(currentUser);
        } else {
            log.warn("No user information available for attachment {}", attachment.getName());
        }

        // Note: Mail association will be established through the order when it's created
        // The attachment will be linked to the order via the setOrder() method later

        // Set attachment type based on file extension
        String fileName = attachment.getName();
        if (fileName != null) {
            String fileExtension = getFileExtension(fileName);
            attachementDTO.setTypeAtach(fileExtension.toUpperCase());

            // Set label based on file type
            if (isPDFFile(fileName)) {
                attachementDTO.setLblAttachment("BC Document - " + fileName);
            } else {
                attachementDTO.setLblAttachment("Email Attachment - " + fileName);
            }
        }

        // Set path information (this could be the Alfresco path or similar)
        if (docExternalId != null) {
            attachementDTO.setPathFile("alfresco://" + docExternalId);
        }

        AttachementDTO savedAttachment = attachementService.save(attachementDTO);
        log.info("Saved attachment with ID: {} for user: {} linked to mail: {}",
                savedAttachment.getId(), currentUser, savedMail != null ? savedMail.getId() : "none");
        return savedAttachment;
    }

    /**
     * Get file extension from filename
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "UNKNOWN";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    /**
     * Check if file is a PDF
     */
    private boolean isPDFFile(String fileName) {
        if (fileName == null) {
            return false;
        }
        return fileName.toLowerCase().endsWith(".pdf");
    }

    private String uploadAttachment(Attachment attachment) {
        // Define the API endpoint
        String url = String.format("%s?modelId=%s&site=%s&path=%s", alfUrl, alfModelId, AlfrescoSite.gms.name(), alfDirectoryPath);

        // Prepare headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        // Prepare form data
        MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();

        // Add file
        byte[] fileContent = ((FileAttachment) attachment).getContentBytes();
        formData.add("file", new ByteArrayResource(Objects.requireNonNull(fileContent)) {
            @Override
            public String getFilename() {
                return attachment.getName();
            }
        });

        // Add properties as JSON
        Map<String, Object> properties = new HashMap<>();
        properties.put("category", "test");
        properties.put("sender", "test");
        properties.put("unity", "test");
        properties.put("createdby", "test");
        properties.put("corresptype", "test");
        properties.put("subject", "test");

        formData.add("properties", properties);

        // Create request entity
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(formData, headers);

        // Make the POST request
        try {
            log.info("Upload attachment to Alfresco with ID: {}", attachment.getId());
            return restTemplate.postForObject(url, requestEntity, String.class);
        } catch (Exception e) {
            throw new RuntimeException("Failed to upload document: " + e.getMessage(), e);
        }
    }

    private boolean processBCAttachment(Attachment attachment, MailsDTO savedMail, AttachementDTO savedAttachment) throws IOException {
        byte[] file = ((FileAttachment) attachment).getContentBytes();
        BCCheckResponse bcCheckResponse = checkBCViaExternalAPI(attachment.getName(), file);

        if (bcCheckResponse != null && "success".equals(bcCheckResponse.getStatus())) {


            createOrder(savedMail, bcCheckResponse);
            updateMailType(savedMail, MailType.BC);

            if (checkRedundancy(savedMail)) {
                updateMailType(savedMail, MailType.REDUNDANT);
            }
            return true;
        }
        return false;
    }

    /**
     * Create order with details from BC response data
     */
    private void createOrder(MailsDTO savedMail, BCCheckResponse bcCheckResponse) {
        try {
            if (savedMail == null) {
                log.error("Cannot create order: savedMail is null");
                return;
            }
            OrderDTO orderDTO = createOrderWithDetails(savedMail, bcCheckResponse);
            if (orderDTO == null) {
                log.error("Failed to create OrderDTO for mail: {}", savedMail.getId());
                return;
            }
            if (orderDTO.getOrderDetails() == null || orderDTO.getOrderDetails().isEmpty()) {
                Set<OrderDetailsDTO> orderDetails = new HashSet<>();
                orderDetails.add(createBasicOrderDetail(savedMail, bcCheckResponse));
                orderDTO.setOrderDetails(orderDetails);
            }
            OrderDTO savedOrder = orderService.saveWithDetails(orderDTO);
            if (savedOrder != null && savedOrder.getId() != null) {
                log.info("Created order ID: {} with {} details for mail: {}",
                    savedOrder.getId(), savedOrder.getOrderDetails().size(), savedMail.getId());
            } else {
                log.error("Order creation failed for mail: {}", savedMail.getId());
            }

        } catch (Exception e) {
            log.error("Error creating order for mail {}: {}", savedMail.getId(), e.getMessage());
        }
    }

    private OrderDTO createOrderWithDetails(MailsDTO savedMail, BCCheckResponse bcCheckResponse) {
        log.debug("Creating OrderDTO with details for mail: {} using BC response data", savedMail.getId());

        try {
            OrderDTO orderDTO = new OrderDTO();

        orderDTO.setMails(savedMail);
        orderDTO.setStatus(OrderStatus.WAITING);  // Service will set default if null
        orderDTO.setLocked(false);                // Service will set default if null
        orderDTO.setRank(1);                      // Service will set default if null
            if (orderDTO.getLineCount() == null) {
                orderDTO.setLineCount(0); // Will be updated later
            }
            if (orderDTO.getPacksNb() == null) {
                orderDTO.setPacksNb(0); // Will be updated later
            }
            if (orderDTO.getTotalQuantity() == null) {
                orderDTO.setTotalQuantity(0.0); // Will be updated later
            }
        String company = extractCompanyFromBCResponse(bcCheckResponse, savedMail);
        orderDTO.setCompany(company);
        orderDTO.setOrderNumber(generateOrderNumber(savedMail));
        orderDTO.setOrderDate(java.time.LocalDate.now());
        String clientName = extractClientNameFromBCResponse(bcCheckResponse, savedMail);
        String clientCode = generateClientCodeFromBCResponse(bcCheckResponse, savedMail);
        orderDTO.setClientName(clientName);
        orderDTO.setClientCode(clientCode);
        if (bcCheckResponse != null && bcCheckResponse.getDeliveryAddress() != null && !bcCheckResponse.getDeliveryAddress().trim().isEmpty()) {
            orderDTO.setDeliveryLocation(bcCheckResponse.getDeliveryAddress());
        }
        Map<String, Object> headerInfo = createOrderHeaderInfo(savedMail, company, orderDTO.getOrderNumber(), bcCheckResponse);
        try {
            orderDTO.setHearderJson(objectMapper.writeValueAsString(headerInfo));
        } catch (Exception e) {
            log.warn("Could not serialize header info for order: {}", e.getMessage());
            orderDTO.setHearderJson("{\"created_from\":\"webhook_bc_processing\",\"error\":\"serialization_failed\"}");
        }
        Set<OrderDetailsDTO> orderDetails = createOrderDetailsFromBCResponse(bcCheckResponse, savedMail);
        orderDTO.setOrderDetails(orderDetails);
        orderDTO.setLineCount(orderDetails.size());
        orderDTO.setPacksNb(calculateTotalPacks(orderDetails));
        orderDTO.setTotalQuantity(calculateTotalQuantity(orderDetails));

            log.debug("Created OrderDTO with {} details for mail {}: company={}, orderNumber={}",
                orderDetails.size(), savedMail.getId(), company, orderDTO.getOrderNumber());

            return orderDTO;

        } catch (Exception e) {
            log.error("Error creating OrderDTO for mail {}: {}", savedMail.getId(), e.getMessage(), e);
            return null;
        }
    }
    private Map<String, Object> createOrderHeaderInfo(MailsDTO savedMail, String company, String orderNumber, BCCheckResponse bcCheckResponse) {
        Map<String, Object> headerInfo = new HashMap<>();
        headerInfo.put("company", company);
        headerInfo.put("order_number", orderNumber);
        headerInfo.put("sender", savedMail.getSender());
        headerInfo.put("subject", savedMail.getSubject());
        headerInfo.put("recipient", savedMail.getRecipient());
        headerInfo.put("mail_date", savedMail.getMaildate() != null ? savedMail.getMaildate().toString() : null);
        headerInfo.put("created_from", "webhook_bc_processing");
        headerInfo.put("mail_id", savedMail.getId());
        headerInfo.put("mail_type", savedMail.getMailType() != null ? savedMail.getMailType().toString() : "BC");
        headerInfo.put("processing_timestamp", java.time.Instant.now().toString());
        if (bcCheckResponse != null) {
            headerInfo.put("bc_status", bcCheckResponse.getStatus());
            headerInfo.put("bc_company", bcCheckResponse.getCompany());
            headerInfo.put("bc_tax_number", bcCheckResponse.getTaxNumber());
            headerInfo.put("bc_delivery_address", bcCheckResponse.getDeliveryAddress());

            // Add extracted data if available
            if (bcCheckResponse.getExtractedData() != null) {
                BCCheckResponse.ExtractedData extractedData = bcCheckResponse.getExtractedData();
                headerInfo.put("bc_header_info", extractedData.getHeaderInfo());
                headerInfo.put("bc_delivery_info", extractedData.getDeliveryInfo());
                headerInfo.put("bc_footer_info", extractedData.getFooterInfo());
                // Note: table_info will be used for order details, not header
            }
        }

        return headerInfo;
    }

    private OrderDetailsDTO createBasicOrderDetail(MailsDTO savedMail, BCCheckResponse bcCheckResponse) {
        log.debug("Creating basic order detail for BC mail: {} with BC response data", savedMail.getId());

        OrderDetailsDTO detailDTO = new OrderDetailsDTO();
        detailDTO.setQuantity(BigDecimal.ONE);
        detailDTO.setUnitPrice(0.0); // Will be updated later when BC is processed
        detailDTO.setAvailability(true);
        String productName = generateProductNameFromBCResponse(bcCheckResponse, savedMail);
        detailDTO.setProductName(productName);
        detailDTO.setInternalCode("BC-" + savedMail.getId());
        detailDTO.setRef("BC-REF-" + savedMail.getId());
        detailDTO.setPackNb(1);
        detailDTO.setProductUnit("UNIT");
        detailDTO.setTva(0.0); // Default VAT, can be updated later
        if (detailDTO.getQuantity() == null) {
            detailDTO.setQuantity(BigDecimal.ONE);
        }
        if (detailDTO.getUnitPrice() == null) {
            detailDTO.setUnitPrice(0.0);
        }
        Map<String, Object> lineInfo = createOrderLineInfo(savedMail);
        try {
            detailDTO.setOrderLineJson(objectMapper.writeValueAsString(lineInfo));
        } catch (Exception e) {
            log.warn("Could not serialize order line info for mail {}: {}", savedMail.getId(), e.getMessage());
            detailDTO.setOrderLineJson("{\"type\":\"bc_document\",\"error\":\"serialization_failed\"}");
        }
        detailDTO.setDiscountStatus(null);
        detailDTO.setPriceStatus(null);
        detailDTO.setQuantityStatus(null);
        detailDTO.setProductStatus(null);

        log.debug("Created order detail: productName={}, internalCode={}, ref={}",
            detailDTO.getProductName(), detailDTO.getInternalCode(), detailDTO.getRef());

        return detailDTO;
    }
    private String generateProductNameFromBCResponse(BCCheckResponse bcCheckResponse, MailsDTO savedMail) {
        // Try to extract product name from BC response
        if (bcCheckResponse != null && bcCheckResponse.getExtractedData() != null) {
            BCCheckResponse.ExtractedData extractedData = bcCheckResponse.getExtractedData();

            // Check header info for document title or subject
            if (extractedData.getHeaderInfo() != null) {
                Map<String, String> headerInfo = extractedData.getHeaderInfo();
                String docTitle = headerInfo.get("document_title");
                if (docTitle == null) docTitle = headerInfo.get("title");
                if (docTitle == null) docTitle = headerInfo.get("subject");
                if (docTitle == null) docTitle = headerInfo.get("objet");

                if (docTitle != null && !docTitle.trim().isEmpty()) {
                    return "BC - " + (docTitle.length() > 40 ? docTitle.substring(0, 37) + "..." : docTitle);
                }
            }
            if (bcCheckResponse.getCompany() != null && !bcCheckResponse.getCompany().trim().isEmpty()) {
                return "BC - " + bcCheckResponse.getCompany() + " - " + savedMail.getId();
            }
        }
        return generateProductName(savedMail);
    }

    private String generateProductName(MailsDTO savedMail) {
        String subject = savedMail.getSubject();
        if (subject != null && subject.length() > 50) {
            subject = subject.substring(0, 47) + "...";
        }
        return "BC Document - " + (subject != null ? subject : "Mail " + savedMail.getId());
    }
    private Map<String, Object> createOrderLineInfo(MailsDTO savedMail) {
        Map<String, Object> lineInfo = new HashMap<>();
        lineInfo.put("type", "bc_document");
        lineInfo.put("mail_id", savedMail.getId());
        lineInfo.put("mail_subject", savedMail.getSubject());
        lineInfo.put("mail_sender", savedMail.getSender());
        lineInfo.put("mail_recipient", savedMail.getRecipient());
        lineInfo.put("mail_date", savedMail.getMaildate() != null ? savedMail.getMaildate().toString() : null);
        lineInfo.put("created_from", "webhook_processing");
        lineInfo.put("processing_timestamp", java.time.Instant.now().toString());
        lineInfo.put("mail_type", savedMail.getMailType() != null ? savedMail.getMailType().toString() : "BC");
        return lineInfo;
    }
    private String extractCompanyFromBCResponse(BCCheckResponse bcCheckResponse, MailsDTO savedMail) {
        log.debug("Extracting company from BC response for mail: {}", savedMail.getId());

        if (bcCheckResponse == null) {
            log.debug("BC response is null, falling back to mail extraction");
            return extractCompanyFromMail(savedMail);
        }

        if (bcCheckResponse.getCompany() != null && !bcCheckResponse.getCompany().trim().isEmpty()) {
            String company = bcCheckResponse.getCompany().trim();
            log.debug("Found company from direct field: {}", company);
            return company;
        }

        String company = extractCompanyFromExtractedData(bcCheckResponse);
        if (company != null && !company.trim().isEmpty()) {
            log.debug("Found company from extracted data: {}", company);
            return company.trim();
        }

        company = extractCompanyFromRawResponse(bcCheckResponse);
        if (company != null && !company.trim().isEmpty()) {
            log.debug("Found company from raw response parsing: {}", company);
            return company.trim();
        }

        log.debug("No company found in BC response, falling back to mail extraction");
        return extractCompanyFromMail(savedMail);
    }
    private String extractCompanyFromExtractedData(BCCheckResponse bcCheckResponse) {
        if (bcCheckResponse.getExtractedData() == null) {
            return null;
        }

        BCCheckResponse.ExtractedData extractedData = bcCheckResponse.getExtractedData();
        String company = extractValueFromMap(extractedData.getHeaderInfo(),
            "company", "société", "societe", "entreprise", "client", "customer");
        if (company != null) return company;
        company = extractValueFromMap(extractedData.getDeliveryInfo(),
            "company", "société", "societe", "entreprise", "client", "customer");
        if (company != null) return company;
        company = extractValueFromMap(extractedData.getFooterInfo(),
            "company", "société", "societe", "entreprise", "client", "customer");
        if (company != null) return company;

        return null;
    }
    private String extractCompanyFromRawResponse(BCCheckResponse bcCheckResponse) {
        try {
            String jsonString = objectMapper.writeValueAsString(bcCheckResponse);
            if (jsonString.contains("\"Société\"")) {
                int startIndex = jsonString.indexOf("\"Société\":");
                if (startIndex != -1) {
                    startIndex = jsonString.indexOf("\"", startIndex + 10);
                    if (startIndex != -1) {
                        int endIndex = jsonString.indexOf("\"", startIndex + 1);
                        if (endIndex != -1) {
                            return jsonString.substring(startIndex + 1, endIndex);
                        }
                    }
                }
            }
        } catch (Exception e) {
            log.debug("Failed to extract company from raw response: {}", e.getMessage());
        }
        return null;
    }
    private String extractValueFromMap(Map<String, String> map, String... possibleKeys) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        for (String key : possibleKeys) {
            String value = map.get(key);
            if (value != null && !value.trim().isEmpty()) {
                return value.trim();
            }
        }
        for (String targetKey : possibleKeys) {
            for (Map.Entry<String, String> entry : map.entrySet()) {
                if (entry.getKey() != null && entry.getKey().equalsIgnoreCase(targetKey)) {
                    String value = entry.getValue();
                    if (value != null && !value.trim().isEmpty()) {
                        return value.trim();
                    }
                }
            }
        }

        return null;
    }
    private String extractCompanyFromMail(MailsDTO savedMail) {
        String sender = savedMail.getSender();
        if (sender != null && sender.contains("@")) {
            String domain = sender.substring(sender.indexOf("@") + 1);
            // Map common domains to company names
            if (domain.contains("azur") || domain.contains("cosmetique")) {
                return "AZUR COSMETIQUE";
            } else if (domain.contains("mg") || domain.contains("monoprix")) {
                return "MG";
            } else if (domain.contains("geant")) {
                return "GEANT";
            }
            return domain.toUpperCase();
        }
        return "UNKNOWN_COMPANY";
    }
    private String generateOrderNumber(MailsDTO savedMail) {
        return "BC-" + savedMail.getId() + "-" + System.currentTimeMillis();
    }
    private String extractClientNameFromEmail(String email) {
        if (email != null && email.contains("@")) {
            String localPart = email.substring(0, email.indexOf("@"));
            return localPart.replace(".", " ").replace("_", " ").toUpperCase();
        }
        return "UNKNOWN_CLIENT";
    }
    private String generateClientCode(String email) {
        if (email != null && email.contains("@")) {
            String localPart = email.substring(0, email.indexOf("@"));
            return "CLI-" + localPart.toUpperCase().replace(".", "").replace("_", "");
        }
        return "CLI-UNKNOWN";
    }
    private String extractClientNameFromBCResponse(BCCheckResponse bcCheckResponse, MailsDTO savedMail) {
        log.debug("Extracting client name from BC response for mail: {}", savedMail.getId());

        if (bcCheckResponse == null) {
            log.debug("BC response is null, falling back to email extraction");
            return extractClientNameFromEmail(savedMail.getSender());
        }
        String clientName = extractClientNameFromExtractedData(bcCheckResponse);
        if (clientName != null && !clientName.trim().isEmpty()) {
            log.debug("Found client name from extracted data: {}", clientName);
            return clientName.trim();
        }
        if (bcCheckResponse.getDeliveryAddress() != null && !bcCheckResponse.getDeliveryAddress().trim().isEmpty()) {
            String deliveryAddress = bcCheckResponse.getDeliveryAddress().trim();
            // Extract potential client name from delivery address
            String extractedName = extractClientNameFromAddress(deliveryAddress);
            if (extractedName != null) {
                log.debug("Found client name from delivery address: {}", extractedName);
                return extractedName;
            }
        }
        if (bcCheckResponse.getCompany() != null && !bcCheckResponse.getCompany().trim().isEmpty()) {
            log.debug("Using company name as client name: {}", bcCheckResponse.getCompany());
            return bcCheckResponse.getCompany().trim();
        }

        log.debug("No client name found in BC response, falling back to email extraction");
        return extractClientNameFromEmail(savedMail.getSender());
    }
    private String extractClientNameFromExtractedData(BCCheckResponse bcCheckResponse) {
        if (bcCheckResponse.getExtractedData() == null) {
            return null;
        }

        BCCheckResponse.ExtractedData extractedData = bcCheckResponse.getExtractedData();

        // Try header info first
        String clientName = extractValueFromMap(extractedData.getHeaderInfo(),
            "client_name", "customer_name", "buyer_name", "nom_client", "client", "customer");
        if (clientName != null) return clientName;

        // Try delivery info
        clientName = extractValueFromMap(extractedData.getDeliveryInfo(),
            "client_name", "customer_name", "buyer_name", "nom_client", "client", "customer");
        if (clientName != null) return clientName;

        // Try footer info
        clientName = extractValueFromMap(extractedData.getFooterInfo(),
            "client_name", "customer_name", "buyer_name", "nom_client", "client", "customer");
        if (clientName != null) return clientName;

        return null;
    }
    private String extractClientNameFromAddress(String deliveryAddress) {
        if (deliveryAddress == null || deliveryAddress.trim().isEmpty()) {
            return null;
        }
        String[] lines = deliveryAddress.split("\n");
        if (lines.length > 0) {
            String firstLine = lines[0].trim();
            if (!firstLine.isEmpty() && firstLine.length() > 2) {
                return firstLine;
            }
        }

        return null;
    }
    private String generateClientCodeFromBCResponse(BCCheckResponse bcCheckResponse, MailsDTO savedMail) {
        // Try to extract client code from BC response
        if (bcCheckResponse != null && bcCheckResponse.getTaxNumber() != null && !bcCheckResponse.getTaxNumber().trim().isEmpty()) {
            return "CLI-" + bcCheckResponse.getTaxNumber().trim().replace("/", "").replace("-", "");
        }

        // Fallback to generating from mail
        return generateClientCode(savedMail.getSender());
    }
    private Set<OrderDetailsDTO> createOrderDetailsFromBCResponse(BCCheckResponse bcCheckResponse, MailsDTO savedMail) {
        Set<OrderDetailsDTO> orderDetails = new HashSet<>();

        // Try to extract order details from BC response table info
        if (bcCheckResponse != null && bcCheckResponse.getExtractedData() != null
            && bcCheckResponse.getExtractedData().getTableInfo() != null) {

            Map<String, List<Map<String, String>>> tableInfo = bcCheckResponse.getExtractedData().getTableInfo();

            // Find table data - try BC API keys first, then generic keys
            List<Map<String, String>> items = null;
            String[] possibleTableKeys = {"Page 1", "Page 2", "Page 3", "items", "products", "articles"};

            for (String tableKey : possibleTableKeys) {
                items = tableInfo.get(tableKey);
                if (items != null && !items.isEmpty()) break;
            }

            // If no predefined keys work, try the first available table
            if (items == null && !tableInfo.isEmpty()) {
                String firstKey = tableInfo.keySet().iterator().next();
                items = tableInfo.get(firstKey);
            }

            if (items != null && !items.isEmpty()) {
                for (Map<String, String> item : items) {
                    OrderDetailsDTO detailDTO = createOrderDetailFromBCItem(item, savedMail);
                    if (detailDTO != null) {
                        orderDetails.add(detailDTO);
                    }
                }
            }
        } else {
            log.warn("No table info available in BC response for mail: {}", savedMail.getId());
        }

        // If no details were extracted from BC response, create a basic detail
        if (orderDetails.isEmpty()) {
            log.info("No order details extracted from BC response, creating basic detail for mail: {}", savedMail.getId());
            orderDetails.add(createBasicOrderDetail(savedMail, bcCheckResponse));
        }

        return orderDetails;
    }

    /**
     * Create an order detail from a BC item
     */
    private OrderDetailsDTO createOrderDetailFromBCItem(Map<String, String> item, MailsDTO savedMail) {
        if (item == null || item.isEmpty()) {
            return null;
        }

        OrderDetailsDTO detailDTO = new OrderDetailsDTO();

        // Extract product information from BC response
        String productName = getItemValue(item, "EAN principal", "Libellé", "product_name", "name", "description");
        String productCode = getItemValue(item, "N° article", "EAN principal", "product_code", "code", "reference");
        String quantityStr = getItemValue(item, "Qté", "Nb colis", "quantity", "qty");
        String priceStr = getItemValue(item, "Prix achat en DT", "Montant promo DT", "unit_price", "price");

        // Set product information with fallbacks
        detailDTO.setProductName(productName != null && !productName.trim().isEmpty() ?
            productName : "BC Product - " + savedMail.getId());
        detailDTO.setInternalCode(productCode != null && !productCode.trim().isEmpty() ?
            productCode : "BC-" + savedMail.getId());
        detailDTO.setRef(productCode != null && !productCode.trim().isEmpty() ?
            productCode : "BC-REF-" + savedMail.getId());

        // Set quantity
        try {
            BigDecimal quantity = quantityStr != null ? new BigDecimal(quantityStr.replaceAll("[^0-9.,]", "").replace(",", ".")) : BigDecimal.ONE;
            detailDTO.setQuantity(quantity);
        } catch (NumberFormatException e) {
            detailDTO.setQuantity(BigDecimal.ONE);
        }

        // Set price
        try {
            Double price = priceStr != null ? Double.parseDouble(priceStr.replaceAll("[^0-9.,]", "").replace(",", ".")) : 0.0;
            detailDTO.setUnitPrice(price);
        } catch (NumberFormatException e) {
            detailDTO.setUnitPrice(0.0);
        }

        // Extract and set additional fields from BC response
        String pcbStr = getItemValue(item, "PCB", "pcb");
        String tvaStr = getItemValue(item, "TVA", "tva");
        String packNbStr = getItemValue(item, "Nb colis", "pack_nb");

        detailDTO.setAvailability(true);
        detailDTO.setProductUnit("UNIT");

        // Set PCB
        try {
            detailDTO.setPcb(pcbStr != null ? Double.parseDouble(pcbStr.trim()) : 1.0);
        } catch (NumberFormatException e) {
            detailDTO.setPcb(1.0);
        }

        // Set Pack Number
        try {
            detailDTO.setPackNb(packNbStr != null ? Integer.parseInt(packNbStr.trim().split("\\.")[0]) : 1);
        } catch (NumberFormatException e) {
            detailDTO.setPackNb(1);
        }

        // Set TVA
        try {
            detailDTO.setTva(tvaStr != null ? Double.parseDouble(tvaStr.trim()) : 0.0);
        } catch (NumberFormatException e) {
            detailDTO.setTva(0.0);
        }

        // Ensure required fields are not null
        if (detailDTO.getQuantity() == null) {
            detailDTO.setQuantity(BigDecimal.ONE);
        }
        if (detailDTO.getUnitPrice() == null) {
            detailDTO.setUnitPrice(0.0);
        }

        // Create order line JSON with item information
        try {
            Map<String, Object> lineInfo = new HashMap<>(item);
            lineInfo.put("type", "bc_extracted_item");
            lineInfo.put("mail_id", savedMail.getId());
            lineInfo.put("processing_timestamp", java.time.Instant.now().toString());
            detailDTO.setOrderLineJson(objectMapper.writeValueAsString(lineInfo));
        } catch (Exception e) {
            log.warn("Could not serialize order line info for BC item: {}", e.getMessage());
            detailDTO.setOrderLineJson("{\"type\":\"bc_extracted_item\",\"error\":\"serialization_failed\"}");
        }

        // Set status fields to null
        detailDTO.setDiscountStatus(null);
        detailDTO.setPriceStatus(null);
        detailDTO.setQuantityStatus(null);
        detailDTO.setProductStatus(null);

        return detailDTO;
    }
    private String getItemValue(Map<String, String> item, String... keys) {
        // First try exact match
        for (String key : keys) {
            String value = item.get(key);
            if (value != null && !value.trim().isEmpty()) {
                return value.trim();
            }
        }

        // Then try case-insensitive match
        for (String targetKey : keys) {
            for (Map.Entry<String, String> entry : item.entrySet()) {
                if (entry.getKey() != null && entry.getKey().equalsIgnoreCase(targetKey)) {
                    String value = entry.getValue();
                    if (value != null && !value.trim().isEmpty()) {
                        return value.trim();
                    }
                }
            }
        }

        // Finally try partial match (contains)
        for (String targetKey : keys) {
            for (Map.Entry<String, String> entry : item.entrySet()) {
                if (entry.getKey() != null &&
                    (entry.getKey().toLowerCase().contains(targetKey.toLowerCase()) ||
                     targetKey.toLowerCase().contains(entry.getKey().toLowerCase()))) {
                    String value = entry.getValue();
                    if (value != null && !value.trim().isEmpty()) {
                        return value.trim();
                    }
                }
            }
        }

        return null;
    }
    private Integer calculateTotalPacks(Set<OrderDetailsDTO> orderDetails) {
        if (orderDetails == null || orderDetails.isEmpty()) {
            return 1;
        }
        return orderDetails.stream()
            .mapToInt(detail -> detail.getPackNb() != null ? detail.getPackNb() : 1)
            .sum();
    }
    private Double calculateTotalQuantity(Set<OrderDetailsDTO> orderDetails) {
        if (orderDetails == null || orderDetails.isEmpty()) {
            return 1.0;
        }
        return orderDetails.stream()
            .mapToDouble(detail -> detail.getQuantity() != null ? detail.getQuantity().doubleValue() : 1.0)
            .sum();
    }



    /**
     * Updates the mail type for a given mail in the database.
     *
     * @param mail The mail DTO to update
     * @param mailType The new mail type to set
     */
    private void updateMailType(MailsDTO mail, MailType mailType) {
        try {
            if (mail == null || mail.getId() == null) {
                log.warn("Cannot update mail type: mail or mail ID is null");
                return;
            }

            if (mailType == null) {
                log.warn("Cannot update mail type: mailType is null for mail {}", mail.getId());
                return;
            }

            // Check if the mail type is already set to the desired value
            if (mailType.equals(mail.getMailType())) {
                log.debug("Mail {} already has mail type '{}', skipping update", mail.getId(), mailType);
                return;
            }

            log.info("Updating mail {} from type '{}' to '{}'", mail.getId(), mail.getMailType(), mailType);

            // Create a partial update DTO with only the ID and new mail type
            MailsDTO updateDTO = new MailsDTO();
            updateDTO.setId(mail.getId());
            updateDTO.setMailType(mailType);

            // Use partial update for better performance
            Optional<MailsDTO> updatedMail = mailsService.partialUpdate(updateDTO);

            if (updatedMail.isPresent()) {
                // Update the local mail object to reflect the change
                mail.setMailType(mailType);
                log.info("Successfully updated mail {} to type '{}'", mail.getId(), mailType);
            } else {
                log.error("Failed to update mail type for mail {}: mail not found", mail.getId());
            }

        } catch (Exception e) {
            log.error("Error updating mail type for mail {} to '{}': {}",
                mail != null ? mail.getId() : "null", mailType, e.getMessage(), e);
        }
    }

    /**
     * Check if the mail is a Bon de Commande (BC) via external API using RestTemplate
     *
     * @param name      The attachment name
     * @param fileBytes The attachment bytes
     * @return BCCheckResponse
     */
    private BCCheckResponse checkBCViaExternalAPI(String name, byte[] fileBytes) throws IOException {
        validateInput(name, fileBytes);

        Path tempFile = null;
        try {
            // Create and write to temporary file
            tempFile = Files.createTempFile("bc_check_", name);
            Files.write(tempFile, fileBytes);

            // Make POST request
            HttpResponse<String> response = Unirest.post(ocrUrl)
                .field("file_uploaded", tempFile.toFile(), "application/octet-stream")
                .asString();

            return processResponse(response);
        } catch (Exception e) {
            log.error("Failed to process BC check for file: {}", name, e);
            throw new IOException("BC check failed: " + e.getMessage(), e);
        } finally {
            cleanupTempFile(tempFile);
        }
    }

    private void validateInput(String name, byte[] fileBytes) {
        if (fileBytes == null || fileBytes.length == 0) {
            throw new IllegalArgumentException("File bytes cannot be null or empty");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("File name cannot be null or empty");
        }
    }

    private BCCheckResponse processResponse(HttpResponse<String> response) throws IOException {
        int statusCode = response.getStatus();
        String responseBody = response.getBody();

        if (statusCode >= 200 && statusCode < 300) {
            try {
                log.debug("=== RAW BC API RESPONSE ===");
                log.debug("Response Body: {}", responseBody);

                BCCheckResponse bcResponse = objectMapper.readValue(responseBody, BCCheckResponse.class);

                // CRITICAL: Log what was actually deserialized
                log.debug("=== DESERIALIZED BC RESPONSE ===");
                log.debug("Status: {}", bcResponse.getStatus());
                log.debug("Company: {}", bcResponse.getCompany());
                log.debug("Tax Number: {}", bcResponse.getTaxNumber());
                log.debug("Delivery Address: {}", bcResponse.getDeliveryAddress());

                if (bcResponse.getExtractedData() != null) {
                    log.debug("ExtractedData is not null");
                    if (bcResponse.getExtractedData().getTableInfo() != null) {
                        log.debug("TableInfo keys: {}", bcResponse.getExtractedData().getTableInfo().keySet());
                        bcResponse.getExtractedData().getTableInfo().forEach((key, value) -> {
                            log.debug("  Table[{}]: {} items", key, value != null ? value.size() : 0);
                        });
                    } else {
                        log.debug("TableInfo is null");
                    }
                } else {
                    log.debug("ExtractedData is null");
                }
                log.debug("=== END DESERIALIZED BC RESPONSE ===");

                return bcResponse;
            } catch (Exception e) {
                log.error("JSON Deserialization failed for response: {}", responseBody);
                throw new IOException("Failed to parse response: " + responseBody, e);
            }
        }
        throw new IOException("Upload failed with status: " + statusCode + ", Body: " + responseBody);
    }

    private void cleanupTempFile(Path tempFile) {
        if (tempFile != null) {
            try {
                Files.deleteIfExists(tempFile);
            } catch (IOException e) {
                log.warn("Failed to delete temporary file: {}", tempFile, e);
            }
        }
    }

    /**
     * Check if the mail is redundant
     *
     * @param mail The saved mail DTO to check
     * @return true if it's redundant, false otherwise
     */
    private boolean checkRedundancy(MailsDTO mail) {
        try {
            // TODO: Inject and call the redundancy check service when it's implemented
            // Example: return redundancyCheckService.isRedundant(mail);

            log.info("Redundancy check service not yet implemented for mail {}", mail.getId());
            return true;

        } catch (Exception e) {
            log.error("Unexpected error during redundancy check for mail {}: {}", mail.getId(), e.getMessage());
            return false;
        }
    }

    private record ResourceIdentifiers(String userId, String messageId) {
    }
}
