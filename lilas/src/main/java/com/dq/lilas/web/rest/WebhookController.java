package com.dq.lilas.web.rest;

import com.dq.lilas.config.GraphClientConfiguration;
import com.dq.lilas.domain.enumeration.AlfrescoSite;
import com.dq.lilas.domain.enumeration.MailType;
import com.dq.lilas.domain.enumeration.OrderStatus;
import com.dq.lilas.security.SecurityUtils;
import com.dq.lilas.service.AttachementService;
import com.dq.lilas.service.GraphMessageService;
import com.dq.lilas.service.MailsService;
import com.dq.lilas.service.OrderService;
import com.dq.lilas.service.dto.*;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.microsoft.graph.models.*;
import com.microsoft.kiota.serialization.KiotaJsonSerialization;
import kong.unirest.HttpResponse;
import kong.unirest.Unirest;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ByteArrayResource;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.lang.NonNull;
import org.springframework.util.CollectionUtils;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;

import java.io.IOException;
import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Path;
import java.time.Instant;
import java.util.*;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/api/notifications")
@RequiredArgsConstructor
public class WebhookController {

    private final GraphMessageService messageService;
    private final GraphClientConfiguration.GraphProperties props;
    private final MailsService mailsService;
    private final AttachementService attachementService;
    private final OrderService orderService;
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;

    @Value("${ocr.url}")
    String ocrUrl;
    @Value("${alfresco.url}")
    String alfUrl;
    @Value("${alfresco.directory-path}")
    String alfDirectoryPath;
    @Value("${alfresco.default-model-id}")
    String alfModelId;

    /**
     * <p>
     * This method handles the initial
     * <a href="https://docs.microsoft.com/graph/webhooks#notification-endpoint-validation">endpoint
     * validation request sent</a> by Microsoft Graph when the subscription is created.
     *
     * @param validationToken A validation token provided as a query parameter
     * @return a 200 OK response with the validationToken in the text/plain body
     */
    @PostMapping(headers = {"content-type=text/plain"})
    @ResponseBody
    public ResponseEntity<String> handleValidation(
        @RequestParam(value = "validationToken") final String validationToken) {
        return ResponseEntity.ok().contentType(MediaType.TEXT_PLAIN).body(validationToken);
    }

    @PostMapping
    public ResponseEntity<String> handleNotifications(@RequestBody @NonNull final String jsonPayload) {
        try {
            ChangeNotificationCollection notifications = KiotaJsonSerialization.deserialize(
                jsonPayload, ChangeNotificationCollection::createFromDiscriminatorValue);

            if (notifications.getValue() == null) {
                return ResponseEntity.accepted().body("");
            }

            for (ChangeNotification change : notifications.getValue()) {
                if (!isValidClientState(change)) {
                    log.warn("Invalid clientState for notification: {}", change.getId());
                    continue;
                }

                Optional<ResourceIdentifiers> identifiers = extractIdentifiers(Objects.requireNonNull(change.getResource()));
                if (identifiers.isEmpty()) {
                    log.warn("Invalid resource format: {}", change.getResource());
                    continue;
                }

                String userId = identifiers.get().userId();
                String messageId = identifiers.get().messageId();

                log.info("Processing notification - User: {}, Message: {}", userId, messageId);
                processMessage(userId, messageId);
            }

            return ResponseEntity.ok().build();

        } catch (IOException e) {
            log.error("Failed to deserialize notification payload: {}", e.getMessage(), e);
        }

        return ResponseEntity.accepted().body("");
    }

    private boolean isValidClientState(ChangeNotification change) {
        return props.getWebhook().getNotificationSecret().equals(change.getClientState());
    }

    private Optional<ResourceIdentifiers> extractIdentifiers(String resource) {
        String[] parts = resource.split("/");
        if (parts.length >= 4) {
            return Optional.of(new ResourceIdentifiers(parts[1], parts[3]));
        }
        return Optional.empty();
    }

    private void processMessage(String userId, String messageId) {
        try {
            Message message = messageService.getMessageById(userId, messageId);
            log.info("Processing message - Subject: {}, Sender: {}",
                message.getSubject(),
                Objects.requireNonNull(Objects.requireNonNull(message.getSender()).getEmailAddress()).getAddress());

            if (!hasValidAttachments(message)) {
                log.info("Message {} has no attachments, skipping processing", messageId);
                return;
            }

            MailsDTO savedMail = saveMail(message);
            processAttachments(userId, messageId, savedMail);

        } catch (Exception e) {
            log.error("Error processing message {} for user {}: {}", messageId, userId, e.getMessage(), e);
        }
    }

    private boolean hasValidAttachments(Message message) {
        return Boolean.TRUE.equals(message.getHasAttachments());
    }

    private MailsDTO saveMail(Message message) {
        MailsDTO mailsDTO = new MailsDTO();
        mailsDTO.setSubject(message.getSubject());
        mailsDTO.setSender(Objects.requireNonNull(Objects.requireNonNull(message.getSender()).getEmailAddress()).getAddress());
        mailsDTO.setMaildate(Instant.now());

        if (message.getBody() != null && message.getBody().getContent() != null) {
            mailsDTO.setMailbody(message.getBody().getContent());
        }

        if (!CollectionUtils.isEmpty(message.getToRecipients())) {
            mailsDTO.setRecipient(Objects.requireNonNull(message.getToRecipients().get(0).getEmailAddress()).getAddress());
        }

        MailsDTO savedMail = mailsService.save(mailsDTO);
        log.info("Saved mail with ID: {}", savedMail.getId());
        return savedMail;
    }

    private void processAttachments(String userId, String messageId, MailsDTO savedMail) {
        try {
            List<Attachment> attachments = messageService.getMessageAttachments(userId, messageId);
            if (CollectionUtils.isEmpty(attachments)) {
                log.warn("Message {} reported having attachments but none were found", messageId);
                updateMailType(savedMail, MailType.NOT_BC);
                return;
            }

            boolean hasBCAttachment = false;
            for (Attachment attachment : attachments) {
                // upload attachment to alfresco
                String docExternalId = uploadAttachment(attachment);

                // save attachment with proper context
                AttachementDTO savedAttachment = saveAttachment(attachment, docExternalId, userId, savedMail);

                boolean isBC = processBCAttachment(attachment, savedMail, savedAttachment);
                hasBCAttachment |= isBC;
            }

            if (!hasBCAttachment) {
                updateMailType(savedMail, MailType.NOT_BC);
            }

        } catch (Exception e) {
            log.error("Error processing attachments for message {}: {}", messageId, e.getMessage());
        }
    }

    private AttachementDTO saveAttachment(Attachment attachment, String docExternalId, String userId, MailsDTO savedMail) {
        AttachementDTO attachementDTO = new AttachementDTO();

        // Basic attachment information
        attachementDTO.setFilenameattachment(attachment.getName());
        attachementDTO.setSizeAttachement(attachment.getSize() != null ? attachment.getSize().toString() : null);
        attachementDTO.setDatejcattachment(Instant.now());
        attachementDTO.setIdDocAttachment(docExternalId);

        // Set user information - try to get current authenticated user first, fallback to userId parameter
        String currentUser = SecurityUtils.getCurrentUserLogin().orElse(userId);
        if (currentUser != null && !currentUser.trim().isEmpty()) {
            attachementDTO.setUserattachment(currentUser);
        } else {
            log.warn("No user information available for attachment {}", attachment.getName());
        }

        // Note: Mail association will be established through the order when it's created
        // The attachment will be linked to the order via the setOrder() method later

        // Set attachment type based on file extension
        String fileName = attachment.getName();
        if (fileName != null) {
            String fileExtension = getFileExtension(fileName);
            attachementDTO.setTypeAtach(fileExtension.toUpperCase());

            // Set label based on file type
            if (isPDFFile(fileName)) {
                attachementDTO.setLblAttachment("BC Document - " + fileName);
            } else {
                attachementDTO.setLblAttachment("Email Attachment - " + fileName);
            }
        }

        // Set path information (this could be the Alfresco path or similar)
        if (docExternalId != null) {
            attachementDTO.setPathFile("alfresco://" + docExternalId);
        }

        AttachementDTO savedAttachment = attachementService.save(attachementDTO);
        log.info("Saved attachment with ID: {} for user: {} linked to mail: {}",
                savedAttachment.getId(), currentUser, savedMail != null ? savedMail.getId() : "none");
        return savedAttachment;
    }

    /**
     * Get file extension from filename
     */
    private String getFileExtension(String fileName) {
        if (fileName == null || !fileName.contains(".")) {
            return "UNKNOWN";
        }
        return fileName.substring(fileName.lastIndexOf(".") + 1);
    }

    /**
     * Check if file is a PDF
     */
    private boolean isPDFFile(String fileName) {
        if (fileName == null) {
            return false;
        }
        return fileName.toLowerCase().endsWith(".pdf");
    }

    private String uploadAttachment(Attachment attachment) {
        // Define the API endpoint
        String url = String.format("%s?modelId=%s&site=%s&path=%s", alfUrl, alfModelId, AlfrescoSite.gms.name(), alfDirectoryPath);

        // Prepare headers
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.MULTIPART_FORM_DATA);
        headers.setAccept(Collections.singletonList(MediaType.APPLICATION_JSON));

        // Prepare form data
        MultiValueMap<String, Object> formData = new LinkedMultiValueMap<>();

        // Add file
        byte[] fileContent = ((FileAttachment) attachment).getContentBytes();
        formData.add("file", new ByteArrayResource(Objects.requireNonNull(fileContent)) {
            @Override
            public String getFilename() {
                return attachment.getName();
            }
        });

        // Add properties as JSON
        Map<String, Object> properties = new HashMap<>();
        properties.put("category", "test");
        properties.put("sender", "test");
        properties.put("unity", "test");
        properties.put("createdby", "test");
        properties.put("corresptype", "test");
        properties.put("subject", "test");

        formData.add("properties", properties);

        // Create request entity
        HttpEntity<MultiValueMap<String, Object>> requestEntity = new HttpEntity<>(formData, headers);

        // Make the POST request
        try {
            log.info("Upload attachment to Alfresco with ID: {}", attachment.getId());
            return restTemplate.postForObject(url, requestEntity, String.class);
        } catch (Exception e) {
            throw new RuntimeException("Failed to upload document: " + e.getMessage(), e);
        }
    }

    private boolean processBCAttachment(Attachment attachment, MailsDTO savedMail, AttachementDTO savedAttachment) throws IOException {
        byte[] file = ((FileAttachment) attachment).getContentBytes();
        BCCheckResponse bcCheckResponse = checkBCViaExternalAPI(attachment.getName(), file);

        if (bcCheckResponse != null && "success".equals(bcCheckResponse.getStatus())) {
            createOrder(savedMail, bcCheckResponse);
            updateMailType(savedMail, MailType.BC);

            if (checkRedundancy(savedMail)) {
                updateMailType(savedMail, MailType.REDUNDANT);
            }
            return true;
        }
        return false;
    }

    /**
     * Creates an order with order details for a BC mail using the integrated order service.
     * This method leverages the OrderService.saveWithDetails method which properly handles
     * the circular reference between Order and OrderDetails entities.
     *
     * @param savedMail The mail DTO containing BC information
     * @param bcCheckResponse The BC check response containing extracted data from the BC document
     */
    private void createOrder(MailsDTO savedMail, BCCheckResponse bcCheckResponse) {
        try {
            log.debug("Creating order with details for BC mail: {} using BC response data", savedMail.getId());

            // Validate input parameters
            if (savedMail == null) {
                log.error("Cannot create order: savedMail is null");
                return;
            }

            if (bcCheckResponse == null) {
                log.warn("BC response is null for mail: {}, proceeding with mail data only", savedMail.getId());
            } else {
                log.debug("BC response status: {}, company: {}", bcCheckResponse.getStatus(), bcCheckResponse.getCompany());
            }

            // Create comprehensive OrderDTO with details using BC response data
            OrderDTO orderDTO = createOrderWithDetails(savedMail, bcCheckResponse);

            if (orderDTO == null) {
                log.error("Failed to create OrderDTO for mail: {}", savedMail.getId());
                createSimpleOrderFallback(savedMail, bcCheckResponse);
                return;
            }

            // Validate order data before saving
            if (orderDTO.getOrderDetails() == null || orderDTO.getOrderDetails().isEmpty()) {
                log.warn("Order for mail {} has no details, creating with basic detail", savedMail.getId());
                // Ensure at least one order detail exists
                Set<OrderDetailsDTO> orderDetails = new HashSet<>();
                orderDetails.add(createBasicOrderDetail(savedMail, bcCheckResponse));
                orderDTO.setOrderDetails(orderDetails);
            }

            // Use the service's saveWithDetails method which handles:
            // - Circular reference resolution between Order and OrderDetails
            // - Proper entity mapping and persistence
            // - Transaction management
            // - Default value setting for required fields
            // Log detailed order information before saving
            log.debug("About to save order for mail: {} with details:", savedMail.getId());
            log.debug("  - Company: {}", orderDTO.getCompany());
            log.debug("  - Order Number: {}", orderDTO.getOrderNumber());
            log.debug("  - Status: {}", orderDTO.getStatus());
            log.debug("  - Order Details Count: {}", orderDTO.getOrderDetails() != null ? orderDTO.getOrderDetails().size() : 0);
            log.debug("  - Mails ID: {}", orderDTO.getMails() != null ? orderDTO.getMails().getId() : "null");

            log.debug("Calling orderService.saveWithDetails for mail: {}", savedMail.getId());
            OrderDTO savedOrder = orderService.saveWithDetails(orderDTO);
            log.debug("orderService.saveWithDetails returned: {}", savedOrder != null ? "OrderDTO with ID " + savedOrder.getId() : "null");

            // Verify the order was saved successfully with details
            if (savedOrder != null && savedOrder.getId() != null) {
                int detailsCount = savedOrder.getOrderDetails() != null ? savedOrder.getOrderDetails().size() : 0;
                log.info("Successfully created order with ID: {} and {} details for BC mail: {}",
                    savedOrder.getId(), detailsCount, savedMail.getId());

                // Log additional order information for tracking
                log.debug("Created order details: orderNumber={}, company={}, status={}",
                    savedOrder.getOrderNumber(), savedOrder.getCompany(), savedOrder.getStatus());
            } else {
                log.error("Order creation returned null or invalid result for mail: {}", savedMail.getId());
                createSimpleOrderFallback(savedMail, bcCheckResponse);
            }

        } catch (Exception e) {
            log.error("Error creating order with details for mail {}: {}", savedMail.getId(), e.getMessage(), e);
            // Fallback to simple order creation without details
            createSimpleOrderFallback(savedMail, bcCheckResponse);
        }
    }

    /**
     * Create a comprehensive OrderDTO with details based on mail information and BC response data.
     * This method prepares the order data structure that will be processed by
     * OrderService.saveWithDetails() which handles the proper persistence logic.
     *
     * @param savedMail The mail containing BC information
     * @param bcCheckResponse The BC check response containing extracted data from the BC document
     * @return OrderDTO with associated order details ready for persistence
     */
    private OrderDTO createOrderWithDetails(MailsDTO savedMail, BCCheckResponse bcCheckResponse) {
        log.debug("Creating OrderDTO with details for mail: {} using BC response data", savedMail.getId());

        try {
            OrderDTO orderDTO = new OrderDTO();

        // Set basic order properties - these will be validated by the service
        orderDTO.setMails(savedMail);
        orderDTO.setStatus(OrderStatus.WAITING);  // Service will set default if null
        orderDTO.setLocked(false);                // Service will set default if null
        orderDTO.setRank(1);                      // Service will set default if null

            // Set additional required fields that might be needed
            if (orderDTO.getLineCount() == null) {
                orderDTO.setLineCount(0); // Will be updated later
            }
            if (orderDTO.getPacksNb() == null) {
                orderDTO.setPacksNb(0); // Will be updated later
            }
            if (orderDTO.getTotalQuantity() == null) {
                orderDTO.setTotalQuantity(0.0); // Will be updated later
            }

        // Extract and set order information from BC response (preferred) or mail fallback
        String company = extractCompanyFromBCResponse(bcCheckResponse, savedMail);
        orderDTO.setCompany(company);
        orderDTO.setOrderNumber(generateOrderNumber(savedMail));

        // Set dates
        orderDTO.setOrderDate(java.time.LocalDate.now());

        // Set client information from BC response or mail fallback
        String clientName = extractClientNameFromBCResponse(bcCheckResponse, savedMail);
        String clientCode = generateClientCodeFromBCResponse(bcCheckResponse, savedMail);
        orderDTO.setClientName(clientName);
        orderDTO.setClientCode(clientCode);

        // Set delivery location from BC response if available
        if (bcCheckResponse != null && bcCheckResponse.getDeliveryAddress() != null && !bcCheckResponse.getDeliveryAddress().trim().isEmpty()) {
            orderDTO.setDeliveryLocation(bcCheckResponse.getDeliveryAddress());
        }

        // Create header JSON with comprehensive mail and BC response information
        Map<String, Object> headerInfo = createOrderHeaderInfo(savedMail, company, orderDTO.getOrderNumber(), bcCheckResponse);
        try {
            orderDTO.setHearderJson(objectMapper.writeValueAsString(headerInfo));
        } catch (Exception e) {
            log.warn("Could not serialize header info for order: {}", e.getMessage());
            orderDTO.setHearderJson("{\"created_from\":\"webhook_bc_processing\",\"error\":\"serialization_failed\"}");
        }

        // Create order details from BC response data - the service will handle the circular reference
        Set<OrderDetailsDTO> orderDetails = createOrderDetailsFromBCResponse(bcCheckResponse, savedMail);
        orderDTO.setOrderDetails(orderDetails);

        // Set summary information based on order details
        orderDTO.setLineCount(orderDetails.size());
        orderDTO.setPacksNb(calculateTotalPacks(orderDetails));
        orderDTO.setTotalQuantity(calculateTotalQuantity(orderDetails));

            log.debug("Created OrderDTO with {} details for mail {}: company={}, orderNumber={}",
                orderDetails.size(), savedMail.getId(), company, orderDTO.getOrderNumber());

            return orderDTO;

        } catch (Exception e) {
            log.error("Error creating OrderDTO for mail {}: {}", savedMail.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * Fallback method to create a simple order when the main method fails
     */
    private void createSimpleOrderFallback(MailsDTO savedMail, BCCheckResponse bcCheckResponse) {
        try {
            log.warn("Creating simple fallback order for mail: {}", savedMail.getId());

            // Create a minimal OrderDTO
            OrderDTO simpleOrder = new OrderDTO();
            simpleOrder.setMails(savedMail);
            simpleOrder.setStatus(OrderStatus.WAITING);
            simpleOrder.setLocked(false);
            simpleOrder.setRank(1);
            simpleOrder.setCompany(extractCompanyFromBCResponse(bcCheckResponse, savedMail));
            simpleOrder.setOrderNumber(generateOrderNumber(savedMail));
            simpleOrder.setOrderDate(java.time.LocalDate.now());
            simpleOrder.setClientName("BC Client - " + savedMail.getId());
            simpleOrder.setClientCode("BC-" + savedMail.getId());
            simpleOrder.setLineCount(1);
            simpleOrder.setPacksNb(1);
            simpleOrder.setTotalQuantity(1.0);
            simpleOrder.setHearderJson("{\"type\":\"fallback_order\",\"mail_id\":" + savedMail.getId() + "}");

            // Create one simple order detail
            Set<OrderDetailsDTO> orderDetails = new HashSet<>();
            OrderDetailsDTO detail = new OrderDetailsDTO();
            detail.setProductName("BC Document - " + savedMail.getId());
            detail.setInternalCode("BC-FALLBACK-" + savedMail.getId());
            detail.setRef("BC-FALLBACK-REF-" + savedMail.getId());
            detail.setQuantity(BigDecimal.ONE);
            detail.setUnitPrice(0.0);
            detail.setPackNb(1);
            detail.setProductUnit("UNIT");
            detail.setTva(0.0);
            detail.setAvailability(true);
            detail.setOrderLineJson("{\"type\":\"fallback_detail\"}");
            orderDetails.add(detail);
            simpleOrder.setOrderDetails(orderDetails);

            // Try to save the simple order
            OrderDTO savedOrder = orderService.saveWithDetails(simpleOrder);
            if (savedOrder != null && savedOrder.getId() != null) {
                log.info("Successfully created fallback order with ID: {} for mail: {}", savedOrder.getId(), savedMail.getId());
            } else {
                log.error("Failed to create even the fallback order for mail: {}", savedMail.getId());
            }

        } catch (Exception e) {
            log.error("Error creating fallback order for mail {}: {}", savedMail.getId(), e.getMessage(), e);
        }
    }

    /**
     * Create comprehensive header information for the order including BC response data
     */
    private Map<String, Object> createOrderHeaderInfo(MailsDTO savedMail, String company, String orderNumber, BCCheckResponse bcCheckResponse) {
        Map<String, Object> headerInfo = new HashMap<>();
        headerInfo.put("company", company);
        headerInfo.put("order_number", orderNumber);
        headerInfo.put("sender", savedMail.getSender());
        headerInfo.put("subject", savedMail.getSubject());
        headerInfo.put("recipient", savedMail.getRecipient());
        headerInfo.put("mail_date", savedMail.getMaildate() != null ? savedMail.getMaildate().toString() : null);
        headerInfo.put("created_from", "webhook_bc_processing");
        headerInfo.put("mail_id", savedMail.getId());
        headerInfo.put("mail_type", savedMail.getMailType() != null ? savedMail.getMailType().toString() : "BC");
        headerInfo.put("processing_timestamp", java.time.Instant.now().toString());

        // Add BC response data to header
        if (bcCheckResponse != null) {
            headerInfo.put("bc_status", bcCheckResponse.getStatus());
            headerInfo.put("bc_company", bcCheckResponse.getCompany());
            headerInfo.put("bc_tax_number", bcCheckResponse.getTaxNumber());
            headerInfo.put("bc_delivery_address", bcCheckResponse.getDeliveryAddress());

            // Add extracted data if available
            if (bcCheckResponse.getExtractedData() != null) {
                BCCheckResponse.ExtractedData extractedData = bcCheckResponse.getExtractedData();
                headerInfo.put("bc_header_info", extractedData.getHeaderInfo());
                headerInfo.put("bc_delivery_info", extractedData.getDeliveryInfo());
                headerInfo.put("bc_footer_info", extractedData.getFooterInfo());
                // Note: table_info will be used for order details, not header
            }
        }

        return headerInfo;
    }

    /**
     * Create a basic order detail for BC processing.
     * This method creates an OrderDetailsDTO that will be properly linked to its parent order
     * by the OrderService.saveWithDetails() method.
     *
     * @param savedMail The mail containing BC information
     * @param bcCheckResponse The BC check response containing extracted data from the BC document
     * @return OrderDetailsDTO ready for persistence via the service layer
     */
    private OrderDetailsDTO createBasicOrderDetail(MailsDTO savedMail, BCCheckResponse bcCheckResponse) {
        log.debug("Creating basic order detail for BC mail: {} with BC response data", savedMail.getId());

        OrderDetailsDTO detailDTO = new OrderDetailsDTO();

        // Set basic properties for BC order detail
        detailDTO.setQuantity(BigDecimal.ONE);
        detailDTO.setUnitPrice(0.0); // Will be updated later when BC is processed
        detailDTO.setAvailability(true);

        // Generate meaningful product information using BC response if available
        String productName = generateProductNameFromBCResponse(bcCheckResponse, savedMail);
        detailDTO.setProductName(productName);
        detailDTO.setInternalCode("BC-" + savedMail.getId());
        detailDTO.setRef("BC-REF-" + savedMail.getId());

        // Set additional product details
        detailDTO.setPackNb(1);
        detailDTO.setProductUnit("UNIT");
        detailDTO.setTva(0.0); // Default VAT, can be updated later

        // Ensure required fields are not null
        if (detailDTO.getQuantity() == null) {
            detailDTO.setQuantity(BigDecimal.ONE);
        }
        if (detailDTO.getUnitPrice() == null) {
            detailDTO.setUnitPrice(0.0);
        }

        // Create comprehensive order line JSON with mail information
        Map<String, Object> lineInfo = createOrderLineInfo(savedMail);
        try {
            detailDTO.setOrderLineJson(objectMapper.writeValueAsString(lineInfo));
        } catch (Exception e) {
            log.warn("Could not serialize order line info for mail {}: {}", savedMail.getId(), e.getMessage());
            detailDTO.setOrderLineJson("{\"type\":\"bc_document\",\"error\":\"serialization_failed\"}");
        }

        // Set status fields to null to avoid database constraint errors
        // These will be managed by the business logic later
        detailDTO.setDiscountStatus(null);
        detailDTO.setPriceStatus(null);
        detailDTO.setQuantityStatus(null);
        detailDTO.setProductStatus(null);

        // Note: We don't set the order reference here as OrderService.saveWithDetails()
        // will handle the circular reference properly

        log.debug("Created order detail: productName={}, internalCode={}, ref={}",
            detailDTO.getProductName(), detailDTO.getInternalCode(), detailDTO.getRef());

        return detailDTO;
    }

    /**
     * Generate a meaningful product name from BC response with mail fallback
     */
    private String generateProductNameFromBCResponse(BCCheckResponse bcCheckResponse, MailsDTO savedMail) {
        // Try to extract product name from BC response
        if (bcCheckResponse != null && bcCheckResponse.getExtractedData() != null) {
            BCCheckResponse.ExtractedData extractedData = bcCheckResponse.getExtractedData();

            // Check header info for document title or subject
            if (extractedData.getHeaderInfo() != null) {
                Map<String, String> headerInfo = extractedData.getHeaderInfo();
                String docTitle = headerInfo.get("document_title");
                if (docTitle == null) docTitle = headerInfo.get("title");
                if (docTitle == null) docTitle = headerInfo.get("subject");
                if (docTitle == null) docTitle = headerInfo.get("objet");

                if (docTitle != null && !docTitle.trim().isEmpty()) {
                    return "BC - " + (docTitle.length() > 40 ? docTitle.substring(0, 37) + "..." : docTitle);
                }
            }

            // Check if we have company info to create a meaningful name
            if (bcCheckResponse.getCompany() != null && !bcCheckResponse.getCompany().trim().isEmpty()) {
                return "BC - " + bcCheckResponse.getCompany() + " - " + savedMail.getId();
            }
        }

        // Fallback to generating from mail
        return generateProductName(savedMail);
    }

    /**
     * Generate a meaningful product name from mail information
     */
    private String generateProductName(MailsDTO savedMail) {
        String subject = savedMail.getSubject();
        if (subject != null && subject.length() > 50) {
            subject = subject.substring(0, 47) + "...";
        }
        return "BC Document - " + (subject != null ? subject : "Mail " + savedMail.getId());
    }

    /**
     * Create comprehensive order line information
     */
    private Map<String, Object> createOrderLineInfo(MailsDTO savedMail) {
        Map<String, Object> lineInfo = new HashMap<>();
        lineInfo.put("type", "bc_document");
        lineInfo.put("mail_id", savedMail.getId());
        lineInfo.put("mail_subject", savedMail.getSubject());
        lineInfo.put("mail_sender", savedMail.getSender());
        lineInfo.put("mail_recipient", savedMail.getRecipient());
        lineInfo.put("mail_date", savedMail.getMaildate() != null ? savedMail.getMaildate().toString() : null);
        lineInfo.put("created_from", "webhook_processing");
        lineInfo.put("processing_timestamp", java.time.Instant.now().toString());
        lineInfo.put("mail_type", savedMail.getMailType() != null ? savedMail.getMailType().toString() : "BC");
        return lineInfo;
    }

    /**
     * Extract company name from BC response with mail fallback
     */
    private String extractCompanyFromBCResponse(BCCheckResponse bcCheckResponse, MailsDTO savedMail) {
        // First try to get company from BC response
        if (bcCheckResponse != null && bcCheckResponse.getCompany() != null && !bcCheckResponse.getCompany().trim().isEmpty()) {
            return bcCheckResponse.getCompany().trim();
        }

        // Fallback to extracting from mail
        return extractCompanyFromMail(savedMail);
    }

    /**
     * Extract company name from mail information
     */
    private String extractCompanyFromMail(MailsDTO savedMail) {
        String sender = savedMail.getSender();
        if (sender != null && sender.contains("@")) {
            String domain = sender.substring(sender.indexOf("@") + 1);
            // Map common domains to company names
            if (domain.contains("azur") || domain.contains("cosmetique")) {
                return "AZUR COSMETIQUE";
            } else if (domain.contains("mg") || domain.contains("monoprix")) {
                return "MG";
            } else if (domain.contains("geant")) {
                return "GEANT";
            }
            // Default to domain name
            return domain.toUpperCase();
        }
        return "UNKNOWN_COMPANY";
    }

    /**
     * Generate order number based on mail information
     */
    private String generateOrderNumber(MailsDTO savedMail) {
        return "BC-" + savedMail.getId() + "-" + System.currentTimeMillis();
    }

    /**
     * Extract client name from email address
     */
    private String extractClientNameFromEmail(String email) {
        if (email != null && email.contains("@")) {
            String localPart = email.substring(0, email.indexOf("@"));
            return localPart.replace(".", " ").replace("_", " ").toUpperCase();
        }
        return "UNKNOWN_CLIENT";
    }

    /**
     * Generate client code from email
     */
    private String generateClientCode(String email) {
        if (email != null && email.contains("@")) {
            String localPart = email.substring(0, email.indexOf("@"));
            return "CLI-" + localPart.toUpperCase().replace(".", "").replace("_", "");
        }
        return "CLI-UNKNOWN";
    }

    /**
     * Extract client name from BC response with mail fallback
     */
    private String extractClientNameFromBCResponse(BCCheckResponse bcCheckResponse, MailsDTO savedMail) {
        // Try to extract client name from BC response header info
        if (bcCheckResponse != null && bcCheckResponse.getExtractedData() != null
            && bcCheckResponse.getExtractedData().getHeaderInfo() != null) {
            Map<String, String> headerInfo = bcCheckResponse.getExtractedData().getHeaderInfo();

            // Look for common client name fields in header
            String clientName = headerInfo.get("client_name");
            if (clientName == null) clientName = headerInfo.get("customer_name");
            if (clientName == null) clientName = headerInfo.get("buyer_name");
            if (clientName == null) clientName = headerInfo.get("nom_client");

            if (clientName != null && !clientName.trim().isEmpty()) {
                return clientName.trim();
            }
        }

        // Fallback to extracting from mail
        return extractClientNameFromEmail(savedMail.getSender());
    }

    /**
     * Generate client code from BC response with mail fallback
     */
    private String generateClientCodeFromBCResponse(BCCheckResponse bcCheckResponse, MailsDTO savedMail) {
        // Try to extract client code from BC response
        if (bcCheckResponse != null && bcCheckResponse.getTaxNumber() != null && !bcCheckResponse.getTaxNumber().trim().isEmpty()) {
            return "CLI-" + bcCheckResponse.getTaxNumber().trim().replace("/", "").replace("-", "");
        }

        // Fallback to generating from mail
        return generateClientCode(savedMail.getSender());
    }

    /**
     * Create order details from BC response table data
     */
    private Set<OrderDetailsDTO> createOrderDetailsFromBCResponse(BCCheckResponse bcCheckResponse, MailsDTO savedMail) {
        Set<OrderDetailsDTO> orderDetails = new HashSet<>();

        // Try to extract order details from BC response table info
        if (bcCheckResponse != null && bcCheckResponse.getExtractedData() != null
            && bcCheckResponse.getExtractedData().getTableInfo() != null) {

            Map<String, List<Map<String, String>>> tableInfo = bcCheckResponse.getExtractedData().getTableInfo();

            // Look for product/item tables
            List<Map<String, String>> items = tableInfo.get("items");
            if (items == null) items = tableInfo.get("products");
            if (items == null) items = tableInfo.get("articles");
            if (items == null) items = tableInfo.get("lignes");

            if (items != null && !items.isEmpty()) {
                for (Map<String, String> item : items) {
                    OrderDetailsDTO detailDTO = createOrderDetailFromBCItem(item, savedMail);
                    if (detailDTO != null) {
                        orderDetails.add(detailDTO);
                    }
                }
            }
        }

        // If no details were extracted from BC response, create a basic detail
        if (orderDetails.isEmpty()) {
            orderDetails.add(createBasicOrderDetail(savedMail, bcCheckResponse));
        }

        return orderDetails;
    }

    /**
     * Create an order detail from a BC item
     */
    private OrderDetailsDTO createOrderDetailFromBCItem(Map<String, String> item, MailsDTO savedMail) {
        if (item == null || item.isEmpty()) {
            return null;
        }

        OrderDetailsDTO detailDTO = new OrderDetailsDTO();

        // Extract product information
        String productName = getItemValue(item, "product_name", "nom_produit", "designation", "description");
        String productCode = getItemValue(item, "product_code", "code_produit", "reference", "ref");
        String quantityStr = getItemValue(item, "quantity", "quantite", "qty", "qte");
        String priceStr = getItemValue(item, "unit_price", "prix_unitaire", "price", "prix");

        // Set product information
        detailDTO.setProductName(productName != null ? productName : "BC Product - " + savedMail.getId());
        detailDTO.setInternalCode(productCode != null ? productCode : "BC-" + savedMail.getId());
        detailDTO.setRef(productCode != null ? productCode : "BC-REF-" + savedMail.getId());

        // Set quantity
        try {
            BigDecimal quantity = quantityStr != null ? new BigDecimal(quantityStr.replaceAll("[^0-9.,]", "").replace(",", ".")) : BigDecimal.ONE;
            detailDTO.setQuantity(quantity);
        } catch (NumberFormatException e) {
            detailDTO.setQuantity(BigDecimal.ONE);
        }

        // Set price
        try {
            Double price = priceStr != null ? Double.parseDouble(priceStr.replaceAll("[^0-9.,]", "").replace(",", ".")) : 0.0;
            detailDTO.setUnitPrice(price);
        } catch (NumberFormatException e) {
            detailDTO.setUnitPrice(0.0);
        }

        // Set other required properties
        detailDTO.setAvailability(true);
        detailDTO.setPackNb(1);
        detailDTO.setProductUnit("UNIT");
        detailDTO.setTva(0.0);

        // Ensure required fields are not null
        if (detailDTO.getQuantity() == null) {
            detailDTO.setQuantity(BigDecimal.ONE);
        }
        if (detailDTO.getUnitPrice() == null) {
            detailDTO.setUnitPrice(0.0);
        }

        // Create order line JSON with item information
        try {
            Map<String, Object> lineInfo = new HashMap<>(item);
            lineInfo.put("type", "bc_extracted_item");
            lineInfo.put("mail_id", savedMail.getId());
            lineInfo.put("processing_timestamp", java.time.Instant.now().toString());
            detailDTO.setOrderLineJson(objectMapper.writeValueAsString(lineInfo));
        } catch (Exception e) {
            log.warn("Could not serialize order line info for BC item: {}", e.getMessage());
            detailDTO.setOrderLineJson("{\"type\":\"bc_extracted_item\",\"error\":\"serialization_failed\"}");
        }

        // Set status fields to null
        detailDTO.setDiscountStatus(null);
        detailDTO.setPriceStatus(null);
        detailDTO.setQuantityStatus(null);
        detailDTO.setProductStatus(null);

        return detailDTO;
    }

    /**
     * Helper method to get item value from multiple possible keys
     */
    private String getItemValue(Map<String, String> item, String... keys) {
        for (String key : keys) {
            String value = item.get(key);
            if (value != null && !value.trim().isEmpty()) {
                return value.trim();
            }
        }
        return null;
    }

    /**
     * Calculate total packs from order details
     */
    private Integer calculateTotalPacks(Set<OrderDetailsDTO> orderDetails) {
        if (orderDetails == null || orderDetails.isEmpty()) {
            return 1;
        }
        return orderDetails.stream()
            .mapToInt(detail -> detail.getPackNb() != null ? detail.getPackNb() : 1)
            .sum();
    }

    /**
     * Calculate total quantity from order details
     */
    private Double calculateTotalQuantity(Set<OrderDetailsDTO> orderDetails) {
        if (orderDetails == null || orderDetails.isEmpty()) {
            return 1.0;
        }
        return orderDetails.stream()
            .mapToDouble(detail -> detail.getQuantity() != null ? detail.getQuantity().doubleValue() : 1.0)
            .sum();
    }

    /**
     * Fallback method to create simple order when detailed creation fails.
     * This method creates a minimal order without order details as a last resort.
     *
     * @param savedMail The mail for which to create a fallback order
     * @param bcCheckResponse The BC check response (may be null)
     */
    private void createSimpleOrderFallback(MailsDTO savedMail, BCCheckResponse bcCheckResponse) {
        try {
            log.warn("Creating fallback simple order for mail: {}", savedMail.getId());

            OrderDTO orderDTO = new OrderDTO();
            orderDTO.setMails(savedMail);
            orderDTO.setStatus(OrderStatus.WAITING);
            orderDTO.setLocked(false);
            orderDTO.setRank(1);

            // Set basic order information even in fallback, using BC response if available
            orderDTO.setCompany(extractCompanyFromBCResponse(bcCheckResponse, savedMail));
            orderDTO.setOrderNumber(generateOrderNumber(savedMail) + "-FALLBACK");
            orderDTO.setOrderDate(java.time.LocalDate.now());

            // Set minimal header info with BC response data if available
            Map<String, Object> fallbackHeader = new HashMap<>();
            fallbackHeader.put("created_from", "webhook_fallback");
            fallbackHeader.put("mail_id", savedMail.getId());
            fallbackHeader.put("reason", "detailed_creation_failed");

            if (bcCheckResponse != null) {
                fallbackHeader.put("bc_status", bcCheckResponse.getStatus());
                fallbackHeader.put("bc_company", bcCheckResponse.getCompany());
            }

            try {
                orderDTO.setHearderJson(objectMapper.writeValueAsString(fallbackHeader));
            } catch (Exception e) {
                orderDTO.setHearderJson("{\"created_from\":\"webhook_fallback\",\"error\":\"serialization_failed\"}");
            }

            // Use the simple save method (without details)
            OrderDTO savedOrder = orderService.save(orderDTO);

            if (savedOrder != null && savedOrder.getId() != null) {
                log.info("Created simple fallback order with ID: {} for BC mail: {}",
                    savedOrder.getId(), savedMail.getId());
            } else {
                log.error("Fallback order creation returned null result for mail: {}", savedMail.getId());
            }

        } catch (Exception e) {
            log.error("Simple order creation also failed for mail {}: {}",
                savedMail.getId(), e.getMessage(), e);
            // At this point, we've exhausted all options for order creation
            // The mail type should remain as BC, but no order will be created
        }
    }

    /**
     * Updates the mail type for a given mail in the database.
     *
     * @param mail The mail DTO to update
     * @param mailType The new mail type to set
     */
    private void updateMailType(MailsDTO mail, MailType mailType) {
        try {
            if (mail == null || mail.getId() == null) {
                log.warn("Cannot update mail type: mail or mail ID is null");
                return;
            }

            if (mailType == null) {
                log.warn("Cannot update mail type: mailType is null for mail {}", mail.getId());
                return;
            }

            // Check if the mail type is already set to the desired value
            if (mailType.equals(mail.getMailType())) {
                log.debug("Mail {} already has mail type '{}', skipping update", mail.getId(), mailType);
                return;
            }

            log.info("Updating mail {} from type '{}' to '{}'", mail.getId(), mail.getMailType(), mailType);

            // Create a partial update DTO with only the ID and new mail type
            MailsDTO updateDTO = new MailsDTO();
            updateDTO.setId(mail.getId());
            updateDTO.setMailType(mailType);

            // Use partial update for better performance
            Optional<MailsDTO> updatedMail = mailsService.partialUpdate(updateDTO);

            if (updatedMail.isPresent()) {
                // Update the local mail object to reflect the change
                mail.setMailType(mailType);
                log.info("Successfully updated mail {} to type '{}'", mail.getId(), mailType);
            } else {
                log.error("Failed to update mail type for mail {}: mail not found", mail.getId());
            }

        } catch (Exception e) {
            log.error("Error updating mail type for mail {} to '{}': {}",
                mail != null ? mail.getId() : "null", mailType, e.getMessage(), e);
        }
    }

    /**
     * Check if the mail is a Bon de Commande (BC) via external API using RestTemplate
     *
     * @param name      The attachment name
     * @param fileBytes The attachment bytes
     * @return BCCheckResponse
     */
    private BCCheckResponse checkBCViaExternalAPI(String name, byte[] fileBytes) throws IOException {
        validateInput(name, fileBytes);

        Path tempFile = null;
        try {
            // Create and write to temporary file
            tempFile = Files.createTempFile("bc_check_", name);
            Files.write(tempFile, fileBytes);

            // Make POST request
            HttpResponse<String> response = Unirest.post(ocrUrl)
                .field("file_uploaded", tempFile.toFile(), "application/octet-stream")
                .asString();

            return processResponse(response);
        } catch (Exception e) {
            log.error("Failed to process BC check for file: {}", name, e);
            throw new IOException("BC check failed: " + e.getMessage(), e);
        } finally {
            cleanupTempFile(tempFile);
        }
    }

    private void validateInput(String name, byte[] fileBytes) {
        if (fileBytes == null || fileBytes.length == 0) {
            throw new IllegalArgumentException("File bytes cannot be null or empty");
        }
        if (name == null || name.trim().isEmpty()) {
            throw new IllegalArgumentException("File name cannot be null or empty");
        }
    }

    private BCCheckResponse processResponse(HttpResponse<String> response) throws IOException {
        int statusCode = response.getStatus();
        String responseBody = response.getBody();

        if (statusCode >= 200 && statusCode < 300) {
            try {
                return objectMapper.readValue(responseBody, BCCheckResponse.class);
            } catch (Exception e) {
                throw new IOException("Failed to parse response: " + responseBody, e);
            }
        }
        throw new IOException("Upload failed with status: " + statusCode + ", Body: " + responseBody);
    }

    private void cleanupTempFile(Path tempFile) {
        if (tempFile != null) {
            try {
                Files.deleteIfExists(tempFile);
            } catch (IOException e) {
                log.warn("Failed to delete temporary file: {}", tempFile, e);
            }
        }
    }

    /**
     * Check if the mail is redundant
     *
     * @param mail The saved mail DTO to check
     * @return true if it's redundant, false otherwise
     */
    private boolean checkRedundancy(MailsDTO mail) {
        try {
            // TODO: Inject and call the redundancy check service when it's implemented
            // Example: return redundancyCheckService.isRedundant(mail);

            log.info("Redundancy check service not yet implemented for mail {}", mail.getId());
            return true;

        } catch (Exception e) {
            log.error("Unexpected error during redundancy check for mail {}: {}", mail.getId(), e.getMessage());
            return false;
        }
    }

    private record ResourceIdentifiers(String userId, String messageId) {
    }
}
