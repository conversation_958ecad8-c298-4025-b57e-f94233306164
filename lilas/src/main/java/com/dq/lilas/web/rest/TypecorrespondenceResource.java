package com.dq.lilas.web.rest;

import com.dq.lilas.repository.TypecorrespondenceRepository;
import com.dq.lilas.service.TypecorrespondenceService;
import com.dq.lilas.service.dto.TypecorrespondenceDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Typecorrespondence}.
 */
@RestController
@RequestMapping("/api/typecorrespondences")
public class TypecorrespondenceResource {

    private static final Logger LOG = LoggerFactory.getLogger(TypecorrespondenceResource.class);

    private static final String ENTITY_NAME = "typecorrespondence";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final TypecorrespondenceService typecorrespondenceService;

    private final TypecorrespondenceRepository typecorrespondenceRepository;

    public TypecorrespondenceResource(
        TypecorrespondenceService typecorrespondenceService,
        TypecorrespondenceRepository typecorrespondenceRepository
    ) {
        this.typecorrespondenceService = typecorrespondenceService;
        this.typecorrespondenceRepository = typecorrespondenceRepository;
    }

    /**
     * {@code POST  /typecorrespondences} : Create a new typecorrespondence.
     *
     * @param typecorrespondenceDTO the typecorrespondenceDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new typecorrespondenceDTO, or with status {@code 400 (Bad Request)} if the typecorrespondence has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<TypecorrespondenceDTO> createTypecorrespondence(@RequestBody TypecorrespondenceDTO typecorrespondenceDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save Typecorrespondence : {}", typecorrespondenceDTO);
        if (typecorrespondenceDTO.getId() != null) {
            throw new BadRequestAlertException("A new typecorrespondence cannot already have an ID", ENTITY_NAME, "idexists");
        }
        typecorrespondenceDTO = typecorrespondenceService.save(typecorrespondenceDTO);
        return ResponseEntity.created(new URI("/api/typecorrespondences/" + typecorrespondenceDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, typecorrespondenceDTO.getId().toString()))
            .body(typecorrespondenceDTO);
    }

    /**
     * {@code PUT  /typecorrespondences/:id} : Updates an existing typecorrespondence.
     *
     * @param id the id of the typecorrespondenceDTO to save.
     * @param typecorrespondenceDTO the typecorrespondenceDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated typecorrespondenceDTO,
     * or with status {@code 400 (Bad Request)} if the typecorrespondenceDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the typecorrespondenceDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<TypecorrespondenceDTO> updateTypecorrespondence(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody TypecorrespondenceDTO typecorrespondenceDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Typecorrespondence : {}, {}", id, typecorrespondenceDTO);
        if (typecorrespondenceDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, typecorrespondenceDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!typecorrespondenceRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        typecorrespondenceDTO = typecorrespondenceService.update(typecorrespondenceDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, typecorrespondenceDTO.getId().toString()))
            .body(typecorrespondenceDTO);
    }

    /**
     * {@code PATCH  /typecorrespondences/:id} : Partial updates given fields of an existing typecorrespondence, field will ignore if it is null
     *
     * @param id the id of the typecorrespondenceDTO to save.
     * @param typecorrespondenceDTO the typecorrespondenceDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated typecorrespondenceDTO,
     * or with status {@code 400 (Bad Request)} if the typecorrespondenceDTO is not valid,
     * or with status {@code 404 (Not Found)} if the typecorrespondenceDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the typecorrespondenceDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<TypecorrespondenceDTO> partialUpdateTypecorrespondence(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody TypecorrespondenceDTO typecorrespondenceDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Typecorrespondence partially : {}, {}", id, typecorrespondenceDTO);
        if (typecorrespondenceDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, typecorrespondenceDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!typecorrespondenceRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<TypecorrespondenceDTO> result = typecorrespondenceService.partialUpdate(typecorrespondenceDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, typecorrespondenceDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /typecorrespondences} : get all the typecorrespondences.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of typecorrespondences in body.
     */
    @GetMapping("")
    public ResponseEntity<List<TypecorrespondenceDTO>> getAllTypecorrespondences(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of Typecorrespondences");
        Page<TypecorrespondenceDTO> page = typecorrespondenceService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /typecorrespondences/:id} : get the "id" typecorrespondence.
     *
     * @param id the id of the typecorrespondenceDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the typecorrespondenceDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<TypecorrespondenceDTO> getTypecorrespondence(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Typecorrespondence : {}", id);
        Optional<TypecorrespondenceDTO> typecorrespondenceDTO = typecorrespondenceService.findOne(id);
        return ResponseUtil.wrapOrNotFound(typecorrespondenceDTO);
    }

    /**
     * {@code DELETE  /typecorrespondences/:id} : delete the "id" typecorrespondence.
     *
     * @param id the id of the typecorrespondenceDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTypecorrespondence(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Typecorrespondence : {}", id);
        typecorrespondenceService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
