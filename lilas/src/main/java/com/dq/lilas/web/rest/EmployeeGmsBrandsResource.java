package com.dq.lilas.web.rest;

import com.dq.lilas.domain.EmployeeGmsBrands;
import com.dq.lilas.repository.EmployeeGmsBrandsRepository;
import com.dq.lilas.service.EmployeeGmsBrandsService;
import com.dq.lilas.service.dto.EmployeeGmsBrandsDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.EmployeeGmsBrands}.
 */
@RestController
@RequestMapping("/api/employee-gms-brands")
public class EmployeeGmsBrandsResource {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeGmsBrandsResource.class);

    private static final String ENTITY_NAME = "employeeGmsBrands";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final EmployeeGmsBrandsService employeeGmsBrandsService;

    private final EmployeeGmsBrandsRepository employeeGmsBrandsRepository;

    public EmployeeGmsBrandsResource(
        EmployeeGmsBrandsService employeeGmsBrandsService,
        EmployeeGmsBrandsRepository employeeGmsBrandsRepository
    ) {
        this.employeeGmsBrandsService = employeeGmsBrandsService;
        this.employeeGmsBrandsRepository = employeeGmsBrandsRepository;
    }

    /**
     * {@code POST  /employee-gms-brands} : Create a new employeeGmsBrands.
     *
     * @param employeeGmsBrandsDTO the employeeGmsBrandsDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new employeeGmsBrandsDTO, or with status {@code 400 (Bad Request)} if the employeeGmsBrands has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<EmployeeGmsBrandsDTO> createEmployeeGmsBrands(@RequestBody EmployeeGmsBrandsDTO employeeGmsBrandsDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save EmployeeGmsBrands : {}", employeeGmsBrandsDTO);
        if (employeeGmsBrandsDTO.getId() != null) {
            throw new BadRequestAlertException("A new employeeGmsBrands cannot already have an ID", ENTITY_NAME, "idexists");
        }
        employeeGmsBrandsDTO = employeeGmsBrandsService.save(employeeGmsBrandsDTO);
        return ResponseEntity.created(new URI("/api/employee-gms-brands/" + employeeGmsBrandsDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, employeeGmsBrandsDTO.getId().toString()))
            .body(employeeGmsBrandsDTO);
    }

    /**
     * {@code PUT  /employee-gms-brands/:id} : Updates an existing employeeGmsBrands.
     *
     * @param id the id of the employeeGmsBrandsDTO to save.
     * @param employeeGmsBrandsDTO the employeeGmsBrandsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeGmsBrandsDTO,
     * or with status {@code 400 (Bad Request)} if the employeeGmsBrandsDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the employeeGmsBrandsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<EmployeeGmsBrandsDTO> updateEmployeeGmsBrands(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody EmployeeGmsBrandsDTO employeeGmsBrandsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update EmployeeGmsBrands : {}, {}", id, employeeGmsBrandsDTO);
        if (employeeGmsBrandsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, employeeGmsBrandsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!employeeGmsBrandsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        employeeGmsBrandsDTO = employeeGmsBrandsService.update(employeeGmsBrandsDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, employeeGmsBrandsDTO.getId().toString()))
            .body(employeeGmsBrandsDTO);
    }

    /**
     * {@code PATCH  /employee-gms-brands/:id} : Partial updates given fields of an existing employeeGmsBrands, field will ignore if it is null
     *
     * @param id the id of the employeeGmsBrandsDTO to save.
     * @param employeeGmsBrandsDTO the employeeGmsBrandsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated employeeGmsBrandsDTO,
     * or with status {@code 400 (Bad Request)} if the employeeGmsBrandsDTO is not valid,
     * or with status {@code 404 (Not Found)} if the employeeGmsBrandsDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the employeeGmsBrandsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<EmployeeGmsBrandsDTO> partialUpdateEmployeeGmsBrands(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody EmployeeGmsBrandsDTO employeeGmsBrandsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update EmployeeGmsBrands partially : {}, {}", id, employeeGmsBrandsDTO);
        if (employeeGmsBrandsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, employeeGmsBrandsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!employeeGmsBrandsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<EmployeeGmsBrandsDTO> result = employeeGmsBrandsService.partialUpdate(employeeGmsBrandsDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, employeeGmsBrandsDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /employee-gms-brands} : get all the employeeGmsBrands.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of employeeGmsBrands in body.
     */
    @GetMapping("")
    public ResponseEntity<List<EmployeeGmsBrandsDTO>> getAllEmployeeGmsBrands(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of EmployeeGmsBrands");
        Page<EmployeeGmsBrandsDTO> page = employeeGmsBrandsService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /employee-gms-brands/:id} : get the "id" employeeGmsBrands.
     *
     * @param id the id of the employeeGmsBrandsDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the employeeGmsBrandsDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<EmployeeGmsBrandsDTO> getEmployeeGmsBrands(@PathVariable("id") Long id) {
        LOG.debug("REST request to get EmployeeGmsBrands : {}", id);
        Optional<EmployeeGmsBrandsDTO> employeeGmsBrandsDTO = employeeGmsBrandsService.findOne(id);
        return ResponseUtil.wrapOrNotFound(employeeGmsBrandsDTO);
    }

    /**
     * {@code DELETE  /employee-gms-brands/:id} : delete the "id" employeeGmsBrands.
     *
     * @param id the id of the employeeGmsBrandsDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteEmployeeGmsBrands(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete EmployeeGmsBrands : {}", id);
        employeeGmsBrandsService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }


    /**
     * {@code GET  /employee-gms-brands/employee/:employeeId} : get all the employeeGmsBrands for a specific employee.
     *
     * @param employeeId the id of the employee to retrieve employeeGmsBrands for.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of employeeGmsBrands in body.
     */
    @GetMapping("/employee/{employeeId}")
    public List<EmployeeGmsBrands> findByEmployee(@PathVariable long employeeId){
        LOG.debug("REST request to get EmployeeGmsBrands by employeeId : {}", employeeId);
        return employeeGmsBrandsService.findByEmployee(employeeId);
    }

    /**
     * {@code GET  /employee-gms-brands/brands/employee/:employeeId} : get all the brands associated with a specific employee.
     *
     * @param employeeId the id of the employee to retrieve brands for.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of brand IDs in body.
     */
    @GetMapping("/brands/employee/{employeeId}")
    public List<Long> findBrandsByEmployee(@PathVariable long employeeId) {
        LOG.debug("REST request to get brands by employeeId : {}", employeeId);
        return employeeGmsBrandsService.findBrandsByEmployee(employeeId);
    }

    /**
     * {@code POST  /employee-gms-brands/assign/:employeeId} : Assign brands to an employee.
     *
     * @param employeeId the id of the employee to assign brands to.
     * @param brandIds the list of brand IDs to assign to the employee.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of assigned EmployeeGmsBrandsDTOs in body.
     */
    @PostMapping("/assign/{employeeId}")
    public ResponseEntity<List<EmployeeGmsBrandsDTO>> assignEmployeeBrands(
        @PathVariable Long employeeId,
        @RequestBody List<Long> brandIds
    ) {
        LOG.debug("REST request to assign brands to employee {} with brands {}", employeeId, brandIds);
        if (brandIds == null || brandIds.isEmpty()) {
            LOG.error("Brand IDs list is null or empty");
            throw new BadRequestAlertException("Brand IDs list cannot be null or empty", ENTITY_NAME, "brandidsnull");
        }
        List<EmployeeGmsBrandsDTO> assignedBrands = employeeGmsBrandsService.assignEmployeeBrands(employeeId, brandIds);
        return ResponseEntity.ok().body(assignedBrands);
    }
}
