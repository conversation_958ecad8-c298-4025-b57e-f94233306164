package com.dq.lilas.web.rest;

import com.dq.lilas.service.GraphWebhookService;
import com.microsoft.graph.models.Subscription;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RestController
@RequestMapping("/api/subscriptions")
@RequiredArgsConstructor
public class SubscriptionController {

    private final GraphWebhookService webhookService;

    @PostMapping
    public Subscription createSubscription(@RequestParam String userId) {
        return webhookService.createMessageSubscription(userId);
    }

    @DeleteMapping("/{subscriptionId}")
    public void deleteSubscription(@PathVariable String subscriptionId) {
        webhookService.deleteSubscription(subscriptionId);
    }
}
