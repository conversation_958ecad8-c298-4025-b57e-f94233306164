package com.dq.lilas.web.websocket.dto;

import com.dq.lilas.web.websocket.enumeration.UserCallState;
import com.dq.lilas.web.websocket.enumeration.UserConnexionState;

import java.io.Serializable;

public class UserSocketDTO implements Serializable {
    private String username;
    private UserConnexionState userConnexionState;
    private UserCallState userCallState;

    public UserSocketDTO(String username, UserConnexionState userConnexionState) {
        this.username = username;
        this.userConnexionState = userConnexionState;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public UserConnexionState getUserConnexionState() {
        return userConnexionState;
    }

    public void setUserConnexionState(UserConnexionState userConnexionState) {
        this.userConnexionState = userConnexionState;
    }

    public UserCallState getUserCallState() {
        return userCallState;
    }

    public void setUserCallState(UserCallState userCallState) {
        this.userCallState = userCallState;
    }
}
