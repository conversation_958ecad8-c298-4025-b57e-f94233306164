package com.dq.lilas.web.rest;

import com.dq.lilas.repository.DailyStockRepository;
import com.dq.lilas.service.DailyStockService;
import com.dq.lilas.service.dto.DailyStockDTO;
import com.dq.lilas.service.dto.UpdateStockRequestDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.time.LocalDate;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.DailyStock}.
 */
@RestController
@RequestMapping("/api/daily-stocks")
public class DailyStockResource {

    private static final Logger LOG = LoggerFactory.getLogger(DailyStockResource.class);

    private static final String ENTITY_NAME = "dailyStock";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final DailyStockService dailyStockService;

    private final DailyStockRepository dailyStockRepository;

    public DailyStockResource(DailyStockService dailyStockService, DailyStockRepository dailyStockRepository) {
        this.dailyStockService = dailyStockService;
        this.dailyStockRepository = dailyStockRepository;
    }

    /**
     * {@code POST  /daily-stocks} : Create a new dailyStock.
     *
     * @param dailyStockDTO the dailyStockDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new dailyStockDTO, or with status {@code 400 (Bad Request)} if the dailyStock has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<DailyStockDTO> createDailyStock(@RequestBody DailyStockDTO dailyStockDTO) throws URISyntaxException {
        LOG.debug("REST request to save DailyStock : {}", dailyStockDTO);
        if (dailyStockDTO.getId() != null) {
            throw new BadRequestAlertException("A new dailyStock cannot already have an ID", ENTITY_NAME, "idexists");
        }
        dailyStockDTO = dailyStockService.save(dailyStockDTO);
        return ResponseEntity.created(new URI("/api/daily-stocks/" + dailyStockDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, dailyStockDTO.getId().toString()))
            .body(dailyStockDTO);
    }

    /**
     * {@code PUT  /daily-stocks/:id} : Updates an existing dailyStock.
     *
     * @param id the id of the dailyStockDTO to save.
     * @param dailyStockDTO the dailyStockDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated dailyStockDTO,
     * or with status {@code 400 (Bad Request)} if the dailyStockDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the dailyStockDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<DailyStockDTO> updateDailyStock(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody DailyStockDTO dailyStockDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update DailyStock : {}, {}", id, dailyStockDTO);
        if (dailyStockDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, dailyStockDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!dailyStockRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        dailyStockDTO = dailyStockService.update(dailyStockDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, dailyStockDTO.getId().toString()))
            .body(dailyStockDTO);
    }

    /**
     * {@code PATCH  /daily-stocks/:id} : Partial updates given fields of an existing dailyStock, field will ignore if it is null
     *
     * @param id the id of the dailyStockDTO to save.
     * @param dailyStockDTO the dailyStockDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated dailyStockDTO,
     * or with status {@code 400 (Bad Request)} if the dailyStockDTO is not valid,
     * or with status {@code 404 (Not Found)} if the dailyStockDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the dailyStockDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<DailyStockDTO> partialUpdateDailyStock(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody DailyStockDTO dailyStockDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update DailyStock partially : {}, {}", id, dailyStockDTO);
        if (dailyStockDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, dailyStockDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!dailyStockRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<DailyStockDTO> result = dailyStockService.partialUpdate(dailyStockDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, dailyStockDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /daily-stocks} : get all the dailyStocks.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of dailyStocks in body.
     */
    @GetMapping("")
    public ResponseEntity<List<DailyStockDTO>> getAllDailyStocks(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of DailyStocks");
        Page<DailyStockDTO> page = dailyStockService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /daily-stocks/:id} : get the "id" dailyStock.
     *
     * @param id the id of the dailyStockDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the dailyStockDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<DailyStockDTO> getDailyStock(@PathVariable("id") Long id) {
        LOG.debug("REST request to get DailyStock : {}", id);
        Optional<DailyStockDTO> dailyStockDTO = dailyStockService.findOne(id);
        return ResponseUtil.wrapOrNotFound(dailyStockDTO);
    }

    /**
     * {@code DELETE  /daily-stocks/:id} : delete the "id" dailyStock.
     *
     * @param id the id of the dailyStockDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDailyStock(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete DailyStock : {}", id);
        dailyStockService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * {@code UPDATE  /daily-stocks/update/internal-code/:internalCode/stock-date/:stockDate} : Updates the remining stock of an existing dailyStock by internal code and stock date.
     *
     * @param internalCode the internal code of the daily stock.
     * @param stockDate the stock date of the daily stock.
     * @param request the request containing the quantity ordered and updated to update stock.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated dailyStockDTO,
     * or with status {@code 400 (Bad Request)} if the dailyStockDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the dailyStockDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/update/internal-code/{internalCode}/stock-date/{stockDate}")
        public ResponseEntity<DailyStockDTO> updateDailyStockByInternalCodeAndDate(
        @PathVariable String internalCode,
        @PathVariable LocalDate stockDate,
        @RequestBody UpdateStockRequestDTO request
    ) throws URISyntaxException {
        LOG.debug("REST request to update remining stock of daily stock by internal code and date : {}, {}", internalCode, stockDate);

        if (internalCode == null || stockDate == null) {
            throw new BadRequestAlertException("Invalid parameters", ENTITY_NAME, "params null");
        }

        DailyStockDTO dailyStock = dailyStockService.updateByInternalCodeAndDate(internalCode, stockDate, request.getQuantityOrdered());

        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, dailyStock.getId().toString()))
            .body(dailyStock);
    }

    /**
     * {@code UPDATE  /daily-stocks/restore/internal-code/:internalCode/stock-date/:stockDate} : Restore the remining stock of an existing dailyStock by internal code and stock date.
     *
     * @param internalCode the internal code of the daily stock.
     * @param stockDate the stock date of the daily stock.
     * @param request the request containing the quantity ordered and updated to restore stock.
     *
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated dailyStockDTO,
     * or with status {@code 400 (Bad Request)} if the dailyStockDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the dailyStockDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/restore/internal-code/{internalCode}/stock-date/{stockDate}")
    public ResponseEntity<DailyStockDTO> restoreStockByInternalCodeAndDate(
        @PathVariable String internalCode,
        @PathVariable LocalDate stockDate,
        @RequestBody UpdateStockRequestDTO request
    ) throws URISyntaxException {
        LOG.debug("REST request to restore remining stock of daily stock by internal code and date : {}, {}", internalCode, stockDate);

        if (internalCode == null || stockDate == null) {
            throw new BadRequestAlertException("Invalid parameters", ENTITY_NAME, "params null");
        }
        if (request == null) {
            throw new BadRequestAlertException("Request body is missing", ENTITY_NAME, "requestnull");
        }

        DailyStockDTO dailyStock = dailyStockService.restoreStockByInternalCodeAndDate(
            internalCode, stockDate, request.getQuantityOrdered(), request.getQuantityUpdated()
        );

        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, dailyStock.getId().toString()))
            .body(dailyStock);
    }

    /**
     * {@code GET  /daily-stocks/remining/internal-code/:internalCode/stock-date/:stockDate} : Get the remaining quantity of a dailyStock by internal code and stock date.
     *
     * @param internalCode the internal code of the daily stock.
     * @param stockDate the stock date of the daily stock.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the remaining quantity, or with status {@code 404 (Not Found)} if not found.
     */
    @GetMapping("/remining/internal-code/{internalCode}/stock-date/{stockDate}")
    public ResponseEntity<Long> getRemainingQuantityByInternalCodeAndDate(
        @PathVariable String internalCode,
        @PathVariable LocalDate stockDate
    ) {
        LOG.debug("REST request to get remaining quantity of daily stock by internal code and date : {}, {}", internalCode, stockDate);

        if (internalCode == null || stockDate == null) {
            throw new BadRequestAlertException("Invalid parameters", ENTITY_NAME, "params null");
        }

        Long remainingQuantity = dailyStockService.getRemainingQuantityByInternalCodeAndDate(internalCode, stockDate);
        return ResponseUtil.wrapOrNotFound(Optional.of(remainingQuantity));
    }

}
