package com.dq.lilas.web.rest;

import com.dq.lilas.repository.MailsRepository;
import com.dq.lilas.service.MailsService;
import com.dq.lilas.service.dto.MailsDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Mails}.
 */
@RestController
@RequestMapping("/api/mails")
public class MailsResource {

    private static final Logger LOG = LoggerFactory.getLogger(MailsResource.class);

    private static final String ENTITY_NAME = "mails";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final MailsService mailsService;

    private final MailsRepository mailsRepository;

    public MailsResource(MailsService mailsService, MailsRepository mailsRepository) {
        this.mailsService = mailsService;
        this.mailsRepository = mailsRepository;
    }

    /**
     * {@code POST  /mails} : Create a new mails.
     *
     * @param mailsDTO the mailsDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new mailsDTO, or with status {@code 400 (Bad Request)} if the mails has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<MailsDTO> createMails(@RequestBody MailsDTO mailsDTO) throws URISyntaxException {
        LOG.debug("REST request to save Mails : {}", mailsDTO);
        if (mailsDTO.getId() != null) {
            throw new BadRequestAlertException("A new mails cannot already have an ID", ENTITY_NAME, "idexists");
        }
        mailsDTO = mailsService.save(mailsDTO);
        return ResponseEntity.created(new URI("/api/mails/" + mailsDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, mailsDTO.getId().toString()))
            .body(mailsDTO);
    }

    /**
     * {@code PUT  /mails/:id} : Updates an existing mails.
     *
     * @param id the id of the mailsDTO to save.
     * @param mailsDTO the mailsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated mailsDTO,
     * or with status {@code 400 (Bad Request)} if the mailsDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the mailsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<MailsDTO> updateMails(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody MailsDTO mailsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Mails : {}, {}", id, mailsDTO);
        if (mailsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, mailsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!mailsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        mailsDTO = mailsService.update(mailsDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, mailsDTO.getId().toString()))
            .body(mailsDTO);
    }

    /**
     * {@code PATCH  /mails/:id} : Partial updates given fields of an existing mails, field will ignore if it is null
     *
     * @param id the id of the mailsDTO to save.
     * @param mailsDTO the mailsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated mailsDTO,
     * or with status {@code 400 (Bad Request)} if the mailsDTO is not valid,
     * or with status {@code 404 (Not Found)} if the mailsDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the mailsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<MailsDTO> partialUpdateMails(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody MailsDTO mailsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Mails partially : {}, {}", id, mailsDTO);
        if (mailsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, mailsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!mailsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<MailsDTO> result = mailsService.partialUpdate(mailsDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, mailsDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /mails} : get all the mails.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of mails in body.
     */
    @GetMapping("")
    public ResponseEntity<List<MailsDTO>> getAllMails(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of Mails");
        Page<MailsDTO> page = mailsService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /mails/:id} : get the "id" mails.
     *
     * @param id the id of the mailsDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the mailsDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<MailsDTO> getMails(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Mails : {}", id);
        Optional<MailsDTO> mailsDTO = mailsService.findOne(id);
        return ResponseUtil.wrapOrNotFound(mailsDTO);
    }

    /**
     * {@code DELETE  /mails/:id} : delete the "id" mails.
     *
     * @param id the id of the mailsDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteMails(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Mails : {}", id);
        mailsService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
