package com.dq.lilas.web.rest;

import com.dq.lilas.repository.TemplateConditionsRepository;
import com.dq.lilas.service.TemplateConditionsService;
import com.dq.lilas.service.dto.TemplateConditionsDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.TemplateConditions}.
 */
@RestController
@RequestMapping("/api/template-conditions")
public class TemplateConditionsResource {

    private static final Logger LOG = LoggerFactory.getLogger(TemplateConditionsResource.class);

    private static final String ENTITY_NAME = "templateConditions";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final TemplateConditionsService templateConditionsService;

    private final TemplateConditionsRepository templateConditionsRepository;

    public TemplateConditionsResource(
        TemplateConditionsService templateConditionsService,
        TemplateConditionsRepository templateConditionsRepository
    ) {
        this.templateConditionsService = templateConditionsService;
        this.templateConditionsRepository = templateConditionsRepository;
    }

    /**
     * {@code POST  /template-conditions} : Create a new templateConditions.
     *
     * @param templateConditionsDTO the templateConditionsDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new templateConditionsDTO, or with status {@code 400 (Bad Request)} if the templateConditions has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<TemplateConditionsDTO> createTemplateConditions(@RequestBody TemplateConditionsDTO templateConditionsDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save TemplateConditions : {}", templateConditionsDTO);
        if (templateConditionsDTO.getId() != null) {
            throw new BadRequestAlertException("A new templateConditions cannot already have an ID", ENTITY_NAME, "idexists");
        }
        templateConditionsDTO = templateConditionsService.save(templateConditionsDTO);
        return ResponseEntity.created(new URI("/api/template-conditions/" + templateConditionsDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, templateConditionsDTO.getId().toString()))
            .body(templateConditionsDTO);
    }

    /**
     * {@code PUT  /template-conditions/:id} : Updates an existing templateConditions.
     *
     * @param id the id of the templateConditionsDTO to save.
     * @param templateConditionsDTO the templateConditionsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated templateConditionsDTO,
     * or with status {@code 400 (Bad Request)} if the templateConditionsDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the templateConditionsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<TemplateConditionsDTO> updateTemplateConditions(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody TemplateConditionsDTO templateConditionsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update TemplateConditions : {}, {}", id, templateConditionsDTO);
        if (templateConditionsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, templateConditionsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!templateConditionsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        templateConditionsDTO = templateConditionsService.update(templateConditionsDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, templateConditionsDTO.getId().toString()))
            .body(templateConditionsDTO);
    }

    /**
     * {@code PATCH  /template-conditions/:id} : Partial updates given fields of an existing templateConditions, field will ignore if it is null
     *
     * @param id the id of the templateConditionsDTO to save.
     * @param templateConditionsDTO the templateConditionsDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated templateConditionsDTO,
     * or with status {@code 400 (Bad Request)} if the templateConditionsDTO is not valid,
     * or with status {@code 404 (Not Found)} if the templateConditionsDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the templateConditionsDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<TemplateConditionsDTO> partialUpdateTemplateConditions(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody TemplateConditionsDTO templateConditionsDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update TemplateConditions partially : {}, {}", id, templateConditionsDTO);
        if (templateConditionsDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, templateConditionsDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!templateConditionsRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<TemplateConditionsDTO> result = templateConditionsService.partialUpdate(templateConditionsDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, templateConditionsDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /template-conditions} : get all the templateConditions.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of templateConditions in body.
     */
    @GetMapping("")
    public ResponseEntity<List<TemplateConditionsDTO>> getAllTemplateConditions(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of TemplateConditions");
        Page<TemplateConditionsDTO> page = templateConditionsService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /template-conditions/:id} : get the "id" templateConditions.
     *
     * @param id the id of the templateConditionsDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the templateConditionsDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<TemplateConditionsDTO> getTemplateConditions(@PathVariable("id") Long id) {
        LOG.debug("REST request to get TemplateConditions : {}", id);
        Optional<TemplateConditionsDTO> templateConditionsDTO = templateConditionsService.findOne(id);
        return ResponseUtil.wrapOrNotFound(templateConditionsDTO);
    }

    /**
     * {@code DELETE  /template-conditions/:id} : delete the "id" templateConditions.
     *
     * @param id the id of the templateConditionsDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteTemplateConditions(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete TemplateConditions : {}", id);
        templateConditionsService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
