package com.dq.lilas.web.rest;

import com.dq.lilas.domain.DailyBatches;
import com.dq.lilas.repository.DailyBatchesRepository;
import com.dq.lilas.service.DailyBatchesService;
import com.dq.lilas.service.EmployeeCompanyPermissionService;
import com.dq.lilas.service.dto.DailyBatchesDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.DailyBatches}.
 */
@RestController
@RequestMapping("/api/daily-batches")
public class DailyBatchesResource {

    private static final Logger LOG = LoggerFactory.getLogger(DailyBatchesResource.class);

    private static final String ENTITY_NAME = "dailyBatches";
    private final EmployeeCompanyPermissionService employeeCompanyPermissionService;

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final DailyBatchesService dailyBatchesService;

    private final DailyBatchesRepository dailyBatchesRepository;

    public DailyBatchesResource(DailyBatchesService dailyBatchesService, DailyBatchesRepository dailyBatchesRepository, EmployeeCompanyPermissionService employeeCompanyPermissionService) {
        this.dailyBatchesService = dailyBatchesService;
        this.dailyBatchesRepository = dailyBatchesRepository;
        this.employeeCompanyPermissionService = employeeCompanyPermissionService;
    }

    /**
     * {@code POST  /daily-batches} : Create a new dailyBatches.
     *
     * @param dailyBatchesDTO the dailyBatchesDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new dailyBatchesDTO, or with status {@code 400 (Bad Request)} if the dailyBatches has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<DailyBatchesDTO> createDailyBatches(@RequestBody DailyBatchesDTO dailyBatchesDTO) throws URISyntaxException {
        LOG.debug("REST request to save DailyBatches : {}", dailyBatchesDTO);
        if (dailyBatchesDTO.getId() != null) {
            throw new BadRequestAlertException("A new dailyBatches cannot already have an ID", ENTITY_NAME, "idexists");
        }
        dailyBatchesDTO = dailyBatchesService.save(dailyBatchesDTO);
        return ResponseEntity.created(new URI("/api/daily-batches/" + dailyBatchesDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, dailyBatchesDTO.getId().toString()))
            .body(dailyBatchesDTO);
    }

    /**
     * {@code PUT  /daily-batches/:id} : Updates an existing dailyBatches.
     *
     * @param id the id of the dailyBatchesDTO to save.
     * @param dailyBatchesDTO the dailyBatchesDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated dailyBatchesDTO,
     * or with status {@code 400 (Bad Request)} if the dailyBatchesDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the dailyBatchesDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<DailyBatchesDTO> updateDailyBatches(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody DailyBatchesDTO dailyBatchesDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update DailyBatches : {}, {}", id, dailyBatchesDTO);
        if (dailyBatchesDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, dailyBatchesDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!dailyBatchesRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        dailyBatchesDTO = dailyBatchesService.update(dailyBatchesDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, dailyBatchesDTO.getId().toString()))
            .body(dailyBatchesDTO);
    }

    /**
     * {@code PATCH  /daily-batches/:id} : Partial updates given fields of an existing dailyBatches, field will ignore if it is null
     *
     * @param id the id of the dailyBatchesDTO to save.
     * @param dailyBatchesDTO the dailyBatchesDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated dailyBatchesDTO,
     * or with status {@code 400 (Bad Request)} if the dailyBatchesDTO is not valid,
     * or with status {@code 404 (Not Found)} if the dailyBatchesDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the dailyBatchesDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<DailyBatchesDTO> partialUpdateDailyBatches(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody DailyBatchesDTO dailyBatchesDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update DailyBatches partially : {}, {}", id, dailyBatchesDTO);
        if (dailyBatchesDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, dailyBatchesDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!dailyBatchesRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<DailyBatchesDTO> result = dailyBatchesService.partialUpdate(dailyBatchesDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, dailyBatchesDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /daily-batches} : get all the dailyBatches.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of dailyBatches in body.
     */
    @GetMapping("")
    public ResponseEntity<List<DailyBatchesDTO>> getAllDailyBatches(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of DailyBatches");
        Page<DailyBatchesDTO> page = dailyBatchesService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET /daily-batches/by-date} : retrieve daily batches of specific date
     *
     * @param batchDate the batchDate of the dailyBatchDTO to retrieve
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of dailyBatches in body.
     */
    @GetMapping("/by-date")
    public ResponseEntity<List<DailyBatchesDTO>> retrieveDailyBatchesByDate(@RequestParam String batchDate) {
        LOG.debug("REST request to get DailyBatches by date : {}", batchDate);
        List<DailyBatchesDTO> dailyBatchesDTOs = dailyBatchesService.findByBatchDate(batchDate);
        return ResponseEntity.ok().body(dailyBatchesDTOs);
    }

    /**
     * {@code GET  /daily-batches/:id} : get the "id" dailyBatches.
     *
     * @param id the id of the dailyBatchesDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the dailyBatchesDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<DailyBatchesDTO> getDailyBatches(@PathVariable("id") Long id) {
        LOG.debug("REST request to get DailyBatches : {}", id);
        Optional<DailyBatchesDTO> dailyBatchesDTO = dailyBatchesService.findOne(id);
        return ResponseUtil.wrapOrNotFound(dailyBatchesDTO);
    }

    /**
     * {@code DELETE  /daily-batches/:id} : delete the "id" dailyBatches.
     *
     * @param id the id of the dailyBatchesDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDailyBatches(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete DailyBatches : {}", id);
        dailyBatchesService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }

    /**
     * {@code GET  /daily-batches/company/:companyId} : get all daily batches by company ID.
     *
     * @param companyIds the ID of the company to filter batches.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of daily batches in body.
     */
    @GetMapping("/company")
    public List<DailyBatches> findBatchesByCompanies(@RequestParam List<Long> companyIds) {
        LOG.debug("REST request to find DailyBatches by company ID : {}", companyIds);

        dailyBatchesService.findBatchesByCompanies(companyIds);
        return  dailyBatchesRepository.findBatchesByCompanies(companyIds);
    }

    /**
     * {@code GET  /daily-batches/employee/:employeeId} : get all daily batches by employee ID.
     *
     * @param employeeId the ID of the employee to filter batches.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of daily batches in body.
     */
    @GetMapping("/employee/{employeeId}")
    public List<DailyBatches> findBatchesByEmployee(@PathVariable long employeeId) {
        LOG.debug("REST request to find DailyBatches by company ID : {}", employeeId);

        List<Long> companyIds = employeeCompanyPermissionService.findCompanyIdsByEmployeeId(employeeId);
        return  dailyBatchesRepository.findBatchesByCompanies(companyIds);
    }

}
