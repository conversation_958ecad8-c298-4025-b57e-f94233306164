package com.dq.lilas.web.rest;

import com.dq.lilas.repository.JoblangRepository;
import com.dq.lilas.service.JoblangService;
import com.dq.lilas.service.dto.JoblangDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Joblang}.
 */
@RestController
@RequestMapping("/api/joblangs")
public class JoblangResource {

    private static final Logger LOG = LoggerFactory.getLogger(JoblangResource.class);

    private static final String ENTITY_NAME = "joblang";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final JoblangService joblangService;

    private final JoblangRepository joblangRepository;

    public JoblangResource(JoblangService joblangService, JoblangRepository joblangRepository) {
        this.joblangService = joblangService;
        this.joblangRepository = joblangRepository;
    }

    /**
     * {@code POST  /joblangs} : Create a new joblang.
     *
     * @param joblangDTO the joblangDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new joblangDTO, or with status {@code 400 (Bad Request)} if the joblang has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<JoblangDTO> createJoblang(@Valid @RequestBody JoblangDTO joblangDTO) throws URISyntaxException {
        LOG.debug("REST request to save Joblang : {}", joblangDTO);
        if (joblangDTO.getId() != null) {
            throw new BadRequestAlertException("A new joblang cannot already have an ID", ENTITY_NAME, "idexists");
        }
        joblangDTO = joblangService.save(joblangDTO);
        return ResponseEntity.created(new URI("/api/joblangs/" + joblangDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, joblangDTO.getId().toString()))
            .body(joblangDTO);
    }

    /**
     * {@code PUT  /joblangs/:id} : Updates an existing joblang.
     *
     * @param id the id of the joblangDTO to save.
     * @param joblangDTO the joblangDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated joblangDTO,
     * or with status {@code 400 (Bad Request)} if the joblangDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the joblangDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<JoblangDTO> updateJoblang(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody JoblangDTO joblangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Joblang : {}, {}", id, joblangDTO);
        if (joblangDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, joblangDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!joblangRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        joblangDTO = joblangService.update(joblangDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, joblangDTO.getId().toString()))
            .body(joblangDTO);
    }

    /**
     * {@code PATCH  /joblangs/:id} : Partial updates given fields of an existing joblang, field will ignore if it is null
     *
     * @param id the id of the joblangDTO to save.
     * @param joblangDTO the joblangDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated joblangDTO,
     * or with status {@code 400 (Bad Request)} if the joblangDTO is not valid,
     * or with status {@code 404 (Not Found)} if the joblangDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the joblangDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<JoblangDTO> partialUpdateJoblang(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody JoblangDTO joblangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Joblang partially : {}, {}", id, joblangDTO);
        if (joblangDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, joblangDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!joblangRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<JoblangDTO> result = joblangService.partialUpdate(joblangDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, joblangDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /joblangs} : get all the joblangs.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of joblangs in body.
     */
    @GetMapping("")
    public ResponseEntity<List<JoblangDTO>> getAllJoblangs(@org.springdoc.core.annotations.ParameterObject Pageable pageable) {
        LOG.debug("REST request to get a page of Joblangs");
        Page<JoblangDTO> page = joblangService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /joblangs/:id} : get the "id" joblang.
     *
     * @param id the id of the joblangDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the joblangDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<JoblangDTO> getJoblang(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Joblang : {}", id);
        Optional<JoblangDTO> joblangDTO = joblangService.findOne(id);
        return ResponseUtil.wrapOrNotFound(joblangDTO);
    }

    /**
     * {@code DELETE  /joblangs/:id} : delete the "id" joblang.
     *
     * @param id the id of the joblangDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteJoblang(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Joblang : {}", id);
        joblangService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
