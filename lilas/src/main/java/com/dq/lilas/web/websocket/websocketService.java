package com.dq.lilas.web.websocket;

import com.dq.lilas.domain.User;
import com.dq.lilas.service.ConversationService;
import com.dq.lilas.service.RoomService;
import com.dq.lilas.service.UserService;
import com.dq.lilas.service.dto.ConversationDTO;
import com.dq.lilas.service.dto.RoomDTO;
import com.dq.lilas.service.dto.UserDTO;
import com.dq.lilas.web.websocket.domain.NotificationSocket;
import com.dq.lilas.web.websocket.dto.ActiveCallSocketDTO;
import com.dq.lilas.web.websocket.dto.NotificationSocketDTO;
import com.dq.lilas.web.websocket.dto.UserSocketDTO;
import com.dq.lilas.web.websocket.enumeration.NotificationSocketType;
import com.dq.lilas.web.websocket.enumeration.UserCallState;
import com.dq.lilas.web.websocket.enumeration.UserConnexionState;
import com.dq.lilas.web.websocket.service.ActiveCallSocketService;
import com.dq.lilas.web.websocket.service.UserSocketService;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.gson.Gson;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.messaging.handler.annotation.MessageMapping;
import org.springframework.messaging.handler.annotation.Payload;
import org.springframework.messaging.simp.SimpMessageSendingOperations;
import org.springframework.stereotype.Controller;

import java.io.IOException;
import java.security.Principal;
import java.time.Instant;
import java.util.*;
import java.util.stream.Collectors;

@Controller
public class websocketService {

    private static final Logger log = LoggerFactory.getLogger(websocketService.class);

    private final SimpMessageSendingOperations messagingTemplate;
    private final RoomService roomService;
    private final ConversationService conversationService;
    private final UserSocketService userSocketService;
    private final ActiveCallSocketService activeCallSocketService;
    private final UserService userService;

    public websocketService(SimpMessageSendingOperations messagingTemplate, RoomService roomService, ConversationService conversationService, UserSocketService userSocketService, ActiveCallSocketService activeCallSocketService, UserService userService) {
        this.messagingTemplate = messagingTemplate;
        this.roomService = roomService;
        this.conversationService = conversationService;
        this.userSocketService = userSocketService;
        this.activeCallSocketService = activeCallSocketService;
        this.userService = userService;
    }

    @MessageMapping("/online-users")
    public void getOnlineUsers(Principal principal) {
        NotificationSocket notificationSocket = new NotificationSocket();
        notificationSocket.setTime(Instant.now());
        notificationSocket.setType(NotificationSocketType.ONLINE_USERS);
        List<UserSocketDTO> userSocketDTOS = userSocketService.getSubscribedSocketUsersByDestination("/topic/public");
        userSocketDTOS.removeIf(userConnexionState -> userConnexionState.getUsername().equals(principal.getName()));
        notificationSocket.addToMetadata("USERS", String.join(", ", new Gson().toJson(userSocketDTOS)));
        messagingTemplate.convertAndSendToUser(principal.getName()
            , "/queue/messages", notificationSocket);
    }

    @MessageMapping("/update-user-state")
    public void updateUserStateAndNotifySubscribers(@Payload String state, Principal principal) {
        if (state != null) {
            UserConnexionState userConnexionState = UserConnexionState.valueOf(state);
            NotificationSocket notificationSocket = new NotificationSocket();
            notificationSocket.setTime(Instant.now());
            notificationSocket.setType(NotificationSocketType.USER_STATE);
            notificationSocket.addToMetadata("USER", principal.getName());
            notificationSocket.addToMetadata("STATE", userConnexionState.name());
            userSocketService.updateUserSocketConnexionState(principal.getName(), userConnexionState);
            sendToSubscribers("/topic/public", "/queue/messages", principal.getName(), notificationSocket);
        }
    }

    @MessageMapping("/socket-notification")
    public void notificationWorkflow(@Payload NotificationSocketDTO notificationSocketDTO, Principal principal) {
        NotificationSocket notificationSocket = new NotificationSocket();
        notificationSocket.setTime(Instant.now());
        notificationSocket.setType(notificationSocketDTO.getType());
        ObjectMapper mapper = new ObjectMapper();
        Map<String, String> metadata = new HashMap<>();
        String loggedUser = principal.getName();
        Optional<User> optionalUser = userService.findUserByLogin(loggedUser);
        String roomIdStr = null;
        if (optionalUser.isPresent()) {
            User sender = optionalUser.get();
            UserDTO senderDTO = new UserDTO(sender.getLogin(), sender.getFirstName(), sender.getLastName());
            notificationSocket.addToMetadata("USER", new Gson().toJson(senderDTO));
            try {
                metadata = mapper.readValue(notificationSocketDTO.getMetadata(), Map.class);
                roomIdStr = metadata.get("ROOM");
                notificationSocket.addToMetadata("ROOM", metadata.get("ROOM"));
            } catch (IOException e) {
                e.printStackTrace();
            }
            if (roomIdStr != null) {
                Long roomIdLong = Long.parseLong(roomIdStr);
                Set<UserDTO> userDTOS = getUsersInSameRoom(roomIdLong);
                // create conversation object
                ConversationDTO conversationDTO = new ConversationDTO();
                conversationDTO.setRoomId(roomIdLong);
                conversationDTO.setSenderId(sender.getId());
                // handle notification by type
                handleNotification(notificationSocketDTO, notificationSocket, metadata, sender, roomIdLong, userDTOS, conversationDTO);
            }
        }
    }

    private Set<UserDTO> getUsersInSameRoom(Long roomId) {
        Optional<RoomDTO> optionalRoomDTO = roomService.findOne(roomId);
        if (optionalRoomDTO.isPresent()) {
            RoomDTO roomDTO = optionalRoomDTO.get();
            return roomDTO.getUsers();
        }
        return new HashSet<>();
    }

    private void handleNotification(NotificationSocketDTO notificationSocketDTO, NotificationSocket notificationSocket, Map<String, String> metadata, User sender, Long roomIdLong, Set<UserDTO> userDTOS, ConversationDTO conversationDTO) {
        switch (notificationSocketDTO.getType()) {
            case INCOMING_CALL:
                handleIncomingCall(notificationSocket, sender, userDTOS, roomIdLong);
                break;
            case ACCEPTED_CALL:
                handleAcceptedCall(notificationSocket, sender, roomIdLong);
                break;
            case CANCELLED_CALL:
                handleCancelledCall(notificationSocket, sender, roomIdLong, userDTOS);
                break;
            case REJECTED_CALL:
                handleRejectedCall(notificationSocket, sender, roomIdLong);
                break;
            case INCOMING_MESSAGE:
                handleIncomingMessage(notificationSocket, metadata, sender, userDTOS, conversationDTO);
                break;
        }
    }

    private void handleIncomingCall(NotificationSocket notificationSocket, User sender, Set<UserDTO> userDTOS, Long roomIdLong) {
        notificationSocket.setContent("New call from," + sender.getFullName());
        // init users set object for active call room
        Set<String> activeUsers = new HashSet<>(Collections.singleton(sender.getLogin()));
        // update user call state of available users in call room to PENDING_CALL and send notification to all except the caller
        userDTOS
            .stream()
            .map(UserDTO::getLogin)
            .forEach(username -> {
                if(username.equals(sender.getLogin())) {
                    // set caller call state to PENDING
                    userSocketService.updateUserSocketCallState(username, UserCallState.PENDING_CALL);
                } else {
                    UserSocketDTO userSocketDTO = userSocketService.findUserSocketByUsername(username);
                    // check if user socket session is active
                    if (userSocketDTO != null) {
                        // check if user available for the call
                        boolean isUserAvailableForCall = !userSocketDTO.getUserConnexionState().equals(UserConnexionState.OFFLINE) && userSocketDTO.getUserCallState() == null;
                        if (isUserAvailableForCall) {
                            activeUsers.add(username);
                            userSocketService.updateUserSocketCallState(username, UserCallState.PENDING_CALL);
                            messagingTemplate.convertAndSendToUser(username, "/queue/messages", notificationSocket);
                        }
                    }
                }
            });
        // create active room call with available users and remove if exist
        activeCallSocketService.create(roomIdLong, sender.getLogin(), activeUsers);
    }

    private void handleAcceptedCall(NotificationSocket notificationSocket, User sender, Long roomIdLong) {
        notificationSocket.setContent("The call has been accepted by," + sender.getFullName());
        // update user call state of the sender (logged user) to IN_CALL && send notification to caller if still waiting for first response
        userSocketService.updateUserSocketCallState(sender.getLogin(), UserCallState.IN_CALL);
        ActiveCallSocketDTO activeCallSocketDTO = activeCallSocketService.findActiveCallSocketByRoom(roomIdLong);
        if(activeCallSocketDTO != null) {
            String caller = activeCallSocketDTO.getCaller();
            if (caller != null) {
                UserSocketDTO userSocketDTO = userSocketService.findUserSocketByUsername(caller);
                // update caller call state to IN_CALL && send notification to him
                if(userSocketDTO.getUserCallState().equals(UserCallState.PENDING_CALL)) {
                    userSocketService.updateUserSocketCallState(caller, UserCallState.IN_CALL);
                    messagingTemplate.convertAndSendToUser(caller, "/queue/messages", notificationSocket);
                }
            }
        }
    }

    private void handleCancelledCall(NotificationSocket notificationSocket, User sender, Long roomIdLong, Set<UserDTO> userDTOS) {
        notificationSocket.setContent("The call has been canceled by," + sender.getFullName());
        // update user call state for each user in a room to null when caller cancel the call
        userDTOS.
            stream().
            map(UserDTO::getLogin).
            forEach(username -> {
                boolean isUserInRoomCall = activeCallSocketService.checkIfUserInActiveCallByRoom(roomIdLong, username);
                if(isUserInRoomCall) {
                    userSocketService.updateUserSocketCallState(username, null);
                    // notify users exclude caller
                    if(!username.equals(sender.getLogin())) {
                        messagingTemplate.convertAndSendToUser(username, "/queue/messages", notificationSocket);
                    }
                }
            });
        // remove active room
        activeCallSocketService.removeByRoom(roomIdLong);
    }

    private void handleRejectedCall(NotificationSocket notificationSocket, User sender, Long roomIdLong) {
        notificationSocket.setContent("The call has been rejected by," + sender.getFullName());
        // update user call state of the sender (logged user) to null and send notification to caller
        userSocketService.updateUserSocketCallState(sender.getLogin(), null);
        // remove sender from active call users
        activeCallSocketService.updateActiveCallSocketDTOUsers(roomIdLong, sender.getLogin(), false);
        // remove room and send notification to the caller only if no other user in the call
        if(activeCallSocketService.countActiveCallUsers(roomIdLong) == 1) {
            ActiveCallSocketDTO activeCallSocketDTO = activeCallSocketService.findActiveCallSocketByRoom(roomIdLong);
            Optional<String> stringOptional = activeCallSocketDTO.getUsers().stream().findFirst();
            if(stringOptional.isPresent()) {
                String user = stringOptional.get();
                userSocketService.updateUserSocketCallState(user, null);
                messagingTemplate.convertAndSendToUser(user, "/queue/messages", notificationSocket);
            }
            activeCallSocketService.removeByRoom(roomIdLong);
        } else if(activeCallSocketService.countActiveCallUsers(roomIdLong) == 0) {
            activeCallSocketService.removeByRoom(roomIdLong);
        }
    }

    private void handleIncomingMessage(NotificationSocket notificationSocket, Map<String, String> metadata, User sender, Set<UserDTO> userDTOS, ConversationDTO conversationDTO) {
        notificationSocket.setContent("You received new message from," + sender.getFullName());
        String message = metadata.get("MESSAGE");
        notificationSocket.addToMetadata("MESSAGE", message);
        conversationDTO.setContent(message);
        userDTOS = userDTOS.
            stream()
            .filter(user -> !user.getId().equals(sender.getId()))
            .collect(Collectors.toSet());
        this.conversationService.save(conversationDTO, userDTOS);
        // send message to each subscriber except the sender
        userDTOS.forEach(receiver -> messagingTemplate.convertAndSendToUser(receiver.getLogin(), "/queue/messages", notificationSocket));
    }

    private void sendToSubscribers(String subscribersDestination, String destination, String loggedUser, Object payload) {
        userSocketService.getSubscribedSocketUsersByDestination(subscribersDestination)
            .stream()
            .map(UserSocketDTO::getUsername)
            .filter(name -> !loggedUser.equals(name))
            .forEach(subscriber -> messagingTemplate.convertAndSendToUser(subscriber, destination, payload));
    }

}
