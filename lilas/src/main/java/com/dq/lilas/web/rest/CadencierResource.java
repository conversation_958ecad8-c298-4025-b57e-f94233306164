package com.dq.lilas.web.rest;

import com.dq.lilas.repository.CadencierRepository;
import com.dq.lilas.service.CadencierService;
import com.dq.lilas.service.dto.CadencierDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Cadencier}.
 */
@RestController
@RequestMapping("/api/cadenciers")
public class CadencierResource {

    private static final Logger LOG = LoggerFactory.getLogger(CadencierResource.class);

    private static final String ENTITY_NAME = "cadencier";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final CadencierService cadencierService;

    private final CadencierRepository cadencierRepository;

    public CadencierResource(CadencierService cadencierService, CadencierRepository cadencierRepository) {
        this.cadencierService = cadencierService;
        this.cadencierRepository = cadencierRepository;
    }

    /**
     * {@code POST  /cadenciers} : Create a new cadencier.
     *
     * @param cadencierDTO the cadencierDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new cadencierDTO, or with status {@code 400 (Bad Request)} if the cadencier has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<CadencierDTO> createCadencier(@RequestBody CadencierDTO cadencierDTO) throws URISyntaxException {
        LOG.debug("REST request to save Cadencier : {}", cadencierDTO);
        if (cadencierDTO.getId() != null) {
            throw new BadRequestAlertException("A new cadencier cannot already have an ID", ENTITY_NAME, "idexists");
        }
        cadencierDTO = cadencierService.save(cadencierDTO);
        return ResponseEntity.created(new URI("/api/cadenciers/" + cadencierDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, cadencierDTO.getId().toString()))
            .body(cadencierDTO);
    }

    /**
     * {@code PUT  /cadenciers/:id} : Updates an existing cadencier.
     *
     * @param id the id of the cadencierDTO to save.
     * @param cadencierDTO the cadencierDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated cadencierDTO,
     * or with status {@code 400 (Bad Request)} if the cadencierDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the cadencierDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<CadencierDTO> updateCadencier(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody CadencierDTO cadencierDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Cadencier : {}, {}", id, cadencierDTO);
        if (cadencierDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, cadencierDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!cadencierRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        cadencierDTO = cadencierService.update(cadencierDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, cadencierDTO.getId().toString()))
            .body(cadencierDTO);
    }

    /**
     * {@code PATCH  /cadenciers/:id} : Partial updates given fields of an existing cadencier, field will ignore if it is null
     *
     * @param id the id of the cadencierDTO to save.
     * @param cadencierDTO the cadencierDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated cadencierDTO,
     * or with status {@code 400 (Bad Request)} if the cadencierDTO is not valid,
     * or with status {@code 404 (Not Found)} if the cadencierDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the cadencierDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<CadencierDTO> partialUpdateCadencier(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody CadencierDTO cadencierDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Cadencier partially : {}, {}", id, cadencierDTO);
        if (cadencierDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, cadencierDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!cadencierRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<CadencierDTO> result = cadencierService.partialUpdate(cadencierDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, cadencierDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /cadenciers} : get all the cadenciers.
     *
     * @param pageable the pagination information.
     * @param filter the filter of the request.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of cadenciers in body.
     */
    @GetMapping("")
    public ResponseEntity<List<CadencierDTO>> getAllCadenciers(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable,
        @RequestParam(name = "filter", required = false) String filter
    ) {
        if ("gmsclients-is-null".equals(filter)) {
            LOG.debug("REST request to get all Cadenciers where gmsClients is null");
            return new ResponseEntity<>(cadencierService.findAllWhereGmsClientsIsNull(), HttpStatus.OK);
        }
        LOG.debug("REST request to get a page of Cadenciers");
        Page<CadencierDTO> page = cadencierService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /cadenciers/:id} : get the "id" cadencier.
     *
     * @param id the id of the cadencierDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the cadencierDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<CadencierDTO> getCadencier(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Cadencier : {}", id);
        Optional<CadencierDTO> cadencierDTO = cadencierService.findOne(id);
        return ResponseUtil.wrapOrNotFound(cadencierDTO);
    }

    /**
     * {@code DELETE  /cadenciers/:id} : delete the "id" cadencier.
     *
     * @param id the id of the cadencierDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteCadencier(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Cadencier : {}", id);
        cadencierService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
