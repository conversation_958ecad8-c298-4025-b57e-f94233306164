package com.dq.lilas.web.rest;

import com.dq.lilas.repository.DeliverymodelangRepository;
import com.dq.lilas.service.DeliverymodelangService;
import com.dq.lilas.service.dto.DeliverymodelangDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import jakarta.validation.Valid;
import jakarta.validation.constraints.NotNull;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.Deliverymodelang}.
 */
@RestController
@RequestMapping("/api/deliverymodelangs")
public class DeliverymodelangResource {

    private static final Logger LOG = LoggerFactory.getLogger(DeliverymodelangResource.class);

    private static final String ENTITY_NAME = "deliverymodelang";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final DeliverymodelangService deliverymodelangService;

    private final DeliverymodelangRepository deliverymodelangRepository;

    public DeliverymodelangResource(
        DeliverymodelangService deliverymodelangService,
        DeliverymodelangRepository deliverymodelangRepository
    ) {
        this.deliverymodelangService = deliverymodelangService;
        this.deliverymodelangRepository = deliverymodelangRepository;
    }

    /**
     * {@code POST  /deliverymodelangs} : Create a new deliverymodelang.
     *
     * @param deliverymodelangDTO the deliverymodelangDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new deliverymodelangDTO, or with status {@code 400 (Bad Request)} if the deliverymodelang has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<DeliverymodelangDTO> createDeliverymodelang(@Valid @RequestBody DeliverymodelangDTO deliverymodelangDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save Deliverymodelang : {}", deliverymodelangDTO);
        if (deliverymodelangDTO.getId() != null) {
            throw new BadRequestAlertException("A new deliverymodelang cannot already have an ID", ENTITY_NAME, "idexists");
        }
        deliverymodelangDTO = deliverymodelangService.save(deliverymodelangDTO);
        return ResponseEntity.created(new URI("/api/deliverymodelangs/" + deliverymodelangDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, deliverymodelangDTO.getId().toString()))
            .body(deliverymodelangDTO);
    }

    /**
     * {@code PUT  /deliverymodelangs/:id} : Updates an existing deliverymodelang.
     *
     * @param id the id of the deliverymodelangDTO to save.
     * @param deliverymodelangDTO the deliverymodelangDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated deliverymodelangDTO,
     * or with status {@code 400 (Bad Request)} if the deliverymodelangDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the deliverymodelangDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<DeliverymodelangDTO> updateDeliverymodelang(
        @PathVariable(value = "id", required = false) final Long id,
        @Valid @RequestBody DeliverymodelangDTO deliverymodelangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update Deliverymodelang : {}, {}", id, deliverymodelangDTO);
        if (deliverymodelangDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, deliverymodelangDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!deliverymodelangRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        deliverymodelangDTO = deliverymodelangService.update(deliverymodelangDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, deliverymodelangDTO.getId().toString()))
            .body(deliverymodelangDTO);
    }

    /**
     * {@code PATCH  /deliverymodelangs/:id} : Partial updates given fields of an existing deliverymodelang, field will ignore if it is null
     *
     * @param id the id of the deliverymodelangDTO to save.
     * @param deliverymodelangDTO the deliverymodelangDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated deliverymodelangDTO,
     * or with status {@code 400 (Bad Request)} if the deliverymodelangDTO is not valid,
     * or with status {@code 404 (Not Found)} if the deliverymodelangDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the deliverymodelangDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<DeliverymodelangDTO> partialUpdateDeliverymodelang(
        @PathVariable(value = "id", required = false) final Long id,
        @NotNull @RequestBody DeliverymodelangDTO deliverymodelangDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update Deliverymodelang partially : {}, {}", id, deliverymodelangDTO);
        if (deliverymodelangDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, deliverymodelangDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!deliverymodelangRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<DeliverymodelangDTO> result = deliverymodelangService.partialUpdate(deliverymodelangDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, deliverymodelangDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /deliverymodelangs} : get all the deliverymodelangs.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of deliverymodelangs in body.
     */
    @GetMapping("")
    public ResponseEntity<List<DeliverymodelangDTO>> getAllDeliverymodelangs(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of Deliverymodelangs");
        Page<DeliverymodelangDTO> page = deliverymodelangService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /deliverymodelangs/:id} : get the "id" deliverymodelang.
     *
     * @param id the id of the deliverymodelangDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the deliverymodelangDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<DeliverymodelangDTO> getDeliverymodelang(@PathVariable("id") Long id) {
        LOG.debug("REST request to get Deliverymodelang : {}", id);
        Optional<DeliverymodelangDTO> deliverymodelangDTO = deliverymodelangService.findOne(id);
        return ResponseUtil.wrapOrNotFound(deliverymodelangDTO);
    }

    /**
     * {@code DELETE  /deliverymodelangs/:id} : delete the "id" deliverymodelang.
     *
     * @param id the id of the deliverymodelangDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDeliverymodelang(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete Deliverymodelang : {}", id);
        deliverymodelangService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
