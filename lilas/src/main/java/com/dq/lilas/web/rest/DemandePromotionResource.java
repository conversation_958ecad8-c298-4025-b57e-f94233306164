package com.dq.lilas.web.rest;

import com.dq.lilas.repository.DemandePromotionRepository;
import com.dq.lilas.service.DemandePromotionService;
import com.dq.lilas.service.dto.DemandePromotionDTO;
import com.dq.lilas.web.rest.errors.BadRequestAlertException;
import java.net.URI;
import java.net.URISyntaxException;
import java.util.List;
import java.util.Objects;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.http.HttpHeaders;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.support.ServletUriComponentsBuilder;
import tech.jhipster.web.util.HeaderUtil;
import tech.jhipster.web.util.PaginationUtil;
import tech.jhipster.web.util.ResponseUtil;

/**
 * REST controller for managing {@link com.dq.lilas.domain.DemandePromotion}.
 */
@RestController
@RequestMapping("/api/demande-promotions")
public class DemandePromotionResource {

    private static final Logger LOG = LoggerFactory.getLogger(DemandePromotionResource.class);

    private static final String ENTITY_NAME = "demandePromotion";

    @Value("${jhipster.clientApp.name}")
    private String applicationName;

    private final DemandePromotionService demandePromotionService;

    private final DemandePromotionRepository demandePromotionRepository;

    public DemandePromotionResource(
        DemandePromotionService demandePromotionService,
        DemandePromotionRepository demandePromotionRepository
    ) {
        this.demandePromotionService = demandePromotionService;
        this.demandePromotionRepository = demandePromotionRepository;
    }

    /**
     * {@code POST  /demande-promotions} : Create a new demandePromotion.
     *
     * @param demandePromotionDTO the demandePromotionDTO to create.
     * @return the {@link ResponseEntity} with status {@code 201 (Created)} and with body the new demandePromotionDTO, or with status {@code 400 (Bad Request)} if the demandePromotion has already an ID.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PostMapping("")
    public ResponseEntity<DemandePromotionDTO> createDemandePromotion(@RequestBody DemandePromotionDTO demandePromotionDTO)
        throws URISyntaxException {
        LOG.debug("REST request to save DemandePromotion : {}", demandePromotionDTO);
        if (demandePromotionDTO.getId() != null) {
            throw new BadRequestAlertException("A new demandePromotion cannot already have an ID", ENTITY_NAME, "idexists");
        }
        demandePromotionDTO = demandePromotionService.save(demandePromotionDTO);
        return ResponseEntity.created(new URI("/api/demande-promotions/" + demandePromotionDTO.getId()))
            .headers(HeaderUtil.createEntityCreationAlert(applicationName, false, ENTITY_NAME, demandePromotionDTO.getId().toString()))
            .body(demandePromotionDTO);
    }

    /**
     * {@code PUT  /demande-promotions/:id} : Updates an existing demandePromotion.
     *
     * @param id the id of the demandePromotionDTO to save.
     * @param demandePromotionDTO the demandePromotionDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated demandePromotionDTO,
     * or with status {@code 400 (Bad Request)} if the demandePromotionDTO is not valid,
     * or with status {@code 500 (Internal Server Error)} if the demandePromotionDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PutMapping("/{id}")
    public ResponseEntity<DemandePromotionDTO> updateDemandePromotion(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody DemandePromotionDTO demandePromotionDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to update DemandePromotion : {}, {}", id, demandePromotionDTO);
        if (demandePromotionDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, demandePromotionDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!demandePromotionRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        demandePromotionDTO = demandePromotionService.update(demandePromotionDTO);
        return ResponseEntity.ok()
            .headers(HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, demandePromotionDTO.getId().toString()))
            .body(demandePromotionDTO);
    }

    /**
     * {@code PATCH  /demande-promotions/:id} : Partial updates given fields of an existing demandePromotion, field will ignore if it is null
     *
     * @param id the id of the demandePromotionDTO to save.
     * @param demandePromotionDTO the demandePromotionDTO to update.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the updated demandePromotionDTO,
     * or with status {@code 400 (Bad Request)} if the demandePromotionDTO is not valid,
     * or with status {@code 404 (Not Found)} if the demandePromotionDTO is not found,
     * or with status {@code 500 (Internal Server Error)} if the demandePromotionDTO couldn't be updated.
     * @throws URISyntaxException if the Location URI syntax is incorrect.
     */
    @PatchMapping(value = "/{id}", consumes = { "application/json", "application/merge-patch+json" })
    public ResponseEntity<DemandePromotionDTO> partialUpdateDemandePromotion(
        @PathVariable(value = "id", required = false) final Long id,
        @RequestBody DemandePromotionDTO demandePromotionDTO
    ) throws URISyntaxException {
        LOG.debug("REST request to partial update DemandePromotion partially : {}, {}", id, demandePromotionDTO);
        if (demandePromotionDTO.getId() == null) {
            throw new BadRequestAlertException("Invalid id", ENTITY_NAME, "idnull");
        }
        if (!Objects.equals(id, demandePromotionDTO.getId())) {
            throw new BadRequestAlertException("Invalid ID", ENTITY_NAME, "idinvalid");
        }

        if (!demandePromotionRepository.existsById(id)) {
            throw new BadRequestAlertException("Entity not found", ENTITY_NAME, "idnotfound");
        }

        Optional<DemandePromotionDTO> result = demandePromotionService.partialUpdate(demandePromotionDTO);

        return ResponseUtil.wrapOrNotFound(
            result,
            HeaderUtil.createEntityUpdateAlert(applicationName, false, ENTITY_NAME, demandePromotionDTO.getId().toString())
        );
    }

    /**
     * {@code GET  /demande-promotions} : get all the demandePromotions for the current user.
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of demandePromotions in body.
     */
    @GetMapping("")
    public ResponseEntity<List<DemandePromotionDTO>> getAllDemandePromotions(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of DemandePromotions for current user");
        Page<DemandePromotionDTO> page = demandePromotionService.findAllForCurrentUser(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /demande-promotions/all} : get ALL demandePromotions (admin only).
     *
     * @param pageable the pagination information.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and the list of all demandePromotions in body.
     */
    @GetMapping("/all")
    public ResponseEntity<List<DemandePromotionDTO>> getAllDemandePromotionsAdmin(
        @org.springdoc.core.annotations.ParameterObject Pageable pageable
    ) {
        LOG.debug("REST request to get a page of ALL DemandePromotions (admin)");
        Page<DemandePromotionDTO> page = demandePromotionService.findAll(pageable);
        HttpHeaders headers = PaginationUtil.generatePaginationHttpHeaders(ServletUriComponentsBuilder.fromCurrentRequest(), page);
        return ResponseEntity.ok().headers(headers).body(page.getContent());
    }

    /**
     * {@code GET  /demande-promotions/:id} : get the "id" demandePromotion.
     *
     * @param id the id of the demandePromotionDTO to retrieve.
     * @return the {@link ResponseEntity} with status {@code 200 (OK)} and with body the demandePromotionDTO, or with status {@code 404 (Not Found)}.
     */
    @GetMapping("/{id}")
    public ResponseEntity<DemandePromotionDTO> getDemandePromotion(@PathVariable("id") Long id) {
        LOG.debug("REST request to get DemandePromotion : {}", id);
        Optional<DemandePromotionDTO> demandePromotionDTO = demandePromotionService.findOne(id);
        return ResponseUtil.wrapOrNotFound(demandePromotionDTO);
    }

    /**
     * {@code DELETE  /demande-promotions/:id} : delete the "id" demandePromotion.
     *
     * @param id the id of the demandePromotionDTO to delete.
     * @return the {@link ResponseEntity} with status {@code 204 (NO_CONTENT)}.
     */
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteDemandePromotion(@PathVariable("id") Long id) {
        LOG.debug("REST request to delete DemandePromotion : {}", id);
        demandePromotionService.delete(id);
        return ResponseEntity.noContent()
            .headers(HeaderUtil.createEntityDeletionAlert(applicationName, false, ENTITY_NAME, id.toString()))
            .build();
    }
}
