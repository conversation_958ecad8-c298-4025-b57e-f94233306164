package com.dq.lilas.web.websocket.service;

import com.dq.lilas.web.websocket.dto.ActiveCallSocketDTO;
import com.dq.lilas.web.websocket.dto.UserSocketDTO;
import io.micrometer.core.annotation.Timed;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

@RestController
@RequestMapping("/check")
public class checkService {

    private final ActiveCallSocketService activeCallSocketService;
    private final UserSocketService userSocketService;

    public checkService(ActiveCallSocketService activeCallSocketService, UserSocketService userSocketService) {
        this.activeCallSocketService = activeCallSocketService;
        this.userSocketService = userSocketService;
    }

    @GetMapping("/active-call")
    @Timed
    public Set<ActiveCallSocketDTO> getActiveCall() {
        return activeCallSocketService.getActiveCallSocketList();
    }

    @GetMapping("/active-user")
    @Timed
    public Set<UserSocketDTO> getActiveUsers() {
        return userSocketService.getUserSocketList();
    }

}
