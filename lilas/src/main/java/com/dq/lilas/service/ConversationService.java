package com.dq.lilas.service;

import com.dq.lilas.domain.enumeration.ConversationState;
import com.dq.lilas.service.dto.ConversationDTO;
import com.dq.lilas.service.dto.UserDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service Interface for managing Conversation.
 */
public interface ConversationService {

    /**
     * Save a conversation.
     *
     * @param conversationDTO the entity to save
     * @param receivers the receivers
     * @return the persisted entity
     */
    ConversationDTO save(ConversationDTO conversationDTO, Set<UserDTO> receivers);

    /**
     * Update Current User Conversation State By Room
     **/
    void updateConversationStateByRoom(ConversationState conversationState, Long roomId);

    /**
     * Get all the conversations.
     *
     * @param pageable the pagination information
     * @return the list of entities
     */
    Page<ConversationDTO> findAll(Pageable pageable);


    /**
     * Get the "id" conversation.
     *
     * @param id the id of the entity
     * @return the entity
     */
    Optional<ConversationDTO> findOne(Long id);

    /**
     * Get Current User Conversations Count By State and Room
     **/
    int getCurrentUserConversationCountByRoomAndReceiverAndState(ConversationState conversationState, Long roomId);

    /**
     * Get Current User Conversations Count By State grouped by Sender
     **/
    List<?> getCurrentUserConversationsCountByStateGroupedBySender(ConversationState conversationState);

    /**
     * Get Current User Conversations Count By State grouped by Room
     **/
    List<?> getCurrentUserConversationsCountByStateGroupedByRoom(ConversationState conversationState);

    /**
     * Get all the conversations Of Room.
     *
     * @return the list of entities.
     */
    Page<ConversationDTO> findAllByRoom(Pageable pageable, long roomId);

    /**
     * Get all the conversations by logged user of Room after join date.
     *
     * @return the list of entities.
     */
    Page<ConversationDTO> findAllByLoggedUserOfRoomAfterJoinDate(Pageable pageable, long roomId);

    /**
     * Delete the "id" conversation.
     *
     * @param id the id of the entity
     */
    void delete(Long id);
}
