package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Employee;
import com.dq.lilas.domain.EmployeeGmsBrands;
import com.dq.lilas.domain.GmsBrands;
import com.dq.lilas.repository.EmployeeGmsBrandsRepository;
import com.dq.lilas.repository.EmployeeRepository;
import com.dq.lilas.repository.GmsBrandsRepository;
import com.dq.lilas.service.EmployeeGmsBrandsService;
import com.dq.lilas.service.dto.EmployeeGmsBrandsDTO;
import com.dq.lilas.service.mapper.EmployeeGmsBrandsMapper;

import java.time.Instant;
import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;

import jakarta.persistence.EntityNotFoundException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.EmployeeGmsBrands}.
 */
@Service
@Transactional
public class EmployeeGmsBrandsServiceImpl implements EmployeeGmsBrandsService {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeGmsBrandsServiceImpl.class);


    private final EmployeeGmsBrandsRepository employeeGmsBrandsRepository;
    private final GmsBrandsRepository gmsBrandsRepository;
    private final EmployeeRepository employeeRepository;

    private final EmployeeGmsBrandsMapper employeeGmsBrandsMapper;

    public EmployeeGmsBrandsServiceImpl(
        EmployeeGmsBrandsRepository employeeGmsBrandsRepository, GmsBrandsRepository gmsBrandsRepository, EmployeeRepository employeeRepository,
        EmployeeGmsBrandsMapper employeeGmsBrandsMapper
    ) {
        this.employeeGmsBrandsRepository = employeeGmsBrandsRepository;
        this.gmsBrandsRepository = gmsBrandsRepository;
        this.employeeRepository = employeeRepository;
        this.employeeGmsBrandsMapper = employeeGmsBrandsMapper;
    }

    @Override
    public EmployeeGmsBrandsDTO save(EmployeeGmsBrandsDTO employeeGmsBrandsDTO) {
        LOG.debug("Request to save EmployeeGmsBrands : {}", employeeGmsBrandsDTO);
        EmployeeGmsBrands employeeGmsBrands = employeeGmsBrandsMapper.toEntity(employeeGmsBrandsDTO);
        employeeGmsBrands = employeeGmsBrandsRepository.save(employeeGmsBrands);
        return employeeGmsBrandsMapper.toDto(employeeGmsBrands);
    }

    @Override
    public EmployeeGmsBrandsDTO update(EmployeeGmsBrandsDTO employeeGmsBrandsDTO) {
        LOG.debug("Request to update EmployeeGmsBrands : {}", employeeGmsBrandsDTO);
        EmployeeGmsBrands employeeGmsBrands = employeeGmsBrandsMapper.toEntity(employeeGmsBrandsDTO);
        employeeGmsBrands = employeeGmsBrandsRepository.save(employeeGmsBrands);
        return employeeGmsBrandsMapper.toDto(employeeGmsBrands);
    }

    @Override
    public Optional<EmployeeGmsBrandsDTO> partialUpdate(EmployeeGmsBrandsDTO employeeGmsBrandsDTO) {
        LOG.debug("Request to partially update EmployeeGmsBrands : {}", employeeGmsBrandsDTO);

        return employeeGmsBrandsRepository
            .findById(employeeGmsBrandsDTO.getId())
            .map(existingEmployeeGmsBrands -> {
                employeeGmsBrandsMapper.partialUpdate(existingEmployeeGmsBrands, employeeGmsBrandsDTO);

                return existingEmployeeGmsBrands;
            })
            .map(employeeGmsBrandsRepository::save)
            .map(employeeGmsBrandsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EmployeeGmsBrandsDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all EmployeeGmsBrands");
        return employeeGmsBrandsRepository.findAll(pageable).map(employeeGmsBrandsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EmployeeGmsBrandsDTO> findOne(Long id) {
        LOG.debug("Request to get EmployeeGmsBrands : {}", id);
        return employeeGmsBrandsRepository.findById(id).map(employeeGmsBrandsMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete EmployeeGmsBrands : {}", id);
        employeeGmsBrandsRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EmployeeGmsBrands> findByEmployee(Long employeeId){
        LOG.debug("Request to get EmployeeGmsBrands by employeeId : {}", employeeId);
        return employeeGmsBrandsRepository.findByEmployee(employeeId);
    }

    @Override
    public List<Long> findBrandsByEmployee(Long employeeId){
        LOG.debug("Request to get brand by employeeId : {}", employeeId);
        return employeeGmsBrandsRepository.findBrandByEmployee(employeeId);
    }

    @Override
    @Transactional
    public List<EmployeeGmsBrandsDTO> assignEmployeeBrands(Long employeeId, List<Long> brandIds) {
        LOG.debug("Request to set brands for employee {} with brands {}", employeeId, brandIds);
        if (brandIds == null || brandIds.isEmpty()) {
            LOG.error("Brand IDs list is null or empty");
            throw new IllegalArgumentException("Brand IDs cannot be null or empty");
        }
        if (employeeId == null) {
            LOG.error("Employee ID is null");
            throw new IllegalArgumentException("Employee ID cannot be null");
        }

        Employee employee = employeeRepository.findById(employeeId)
            .orElseThrow(() -> new EntityNotFoundException("Employee not found with id: " + employeeId));

        List<EmployeeGmsBrands> existingAssociations = employeeGmsBrandsRepository.findByEmployee(employeeId);
        Set<Long> existingBrandIds = existingAssociations.stream()
            .map(egb -> egb.getGmsBrands().getId())
            .collect(Collectors.toSet());

        List<EmployeeGmsBrands> newAssociations = new ArrayList<>();
        for (Long brandId : brandIds) {
            // Check if brand exists
            Optional<GmsBrands> brandOptional = gmsBrandsRepository.findById(brandId);
            if (brandOptional.isEmpty()) {
                LOG.warn("Brand with id {} not found, skipping", brandId);
                continue;
            }

            // Check if association already exists
            if (existingBrandIds.contains(brandId)) {
                LOG.debug("Association already exists for employee {} and brand {}", employeeId, brandId);
                continue;
            }

            // Create new association
            EmployeeGmsBrands newAssociation = new EmployeeGmsBrands();
            newAssociation.setEmployee(employee);
            newAssociation.setGmsBrands(brandOptional.get());
            newAssociation.setAssignmentDate(Instant.now());
            newAssociation.setStatus(true);

            EmployeeGmsBrands savedAssociation = employeeGmsBrandsRepository.save(newAssociation);
            newAssociations.add(savedAssociation);
            LOG.debug("Created new association for employee {} and brand {}", employeeId, brandId);
        }

        return newAssociations.stream()
            .map(employeeGmsBrandsMapper::toDto)
            .collect(Collectors.toList());
    }




}
