package com.dq.lilas.service;

import com.dq.lilas.service.dto.MailsDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Mails}.
 */
public interface MailsService {
    /**
     * Save a mails.
     *
     * @param mailsDTO the entity to save.
     * @return the persisted entity.
     */
    MailsDTO save(MailsDTO mailsDTO);

    /**
     * Updates a mails.
     *
     * @param mailsDTO the entity to update.
     * @return the persisted entity.
     */
    MailsDTO update(MailsDTO mailsDTO);

    /**
     * Partially updates a mails.
     *
     * @param mailsDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<MailsDTO> partialUpdate(MailsDTO mailsDTO);

    /**
     * Get all the mails.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<MailsDTO> findAll(Pageable pageable);

    /**
     * Get the "id" mails.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<MailsDTO> findOne(Long id);

    /**
     * Delete the "id" mails.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
