package com.dq.lilas.service;

import com.dq.lilas.service.dto.CadencierDTO;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Cadencier}.
 */
public interface CadencierService {
    /**
     * Save a cadencier.
     *
     * @param cadencierDTO the entity to save.
     * @return the persisted entity.
     */
    CadencierDTO save(CadencierDTO cadencierDTO);

    /**
     * Updates a cadencier.
     *
     * @param cadencierDTO the entity to update.
     * @return the persisted entity.
     */
    CadencierDTO update(CadencierDTO cadencierDTO);

    /**
     * Partially updates a cadencier.
     *
     * @param cadencierDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<CadencierDTO> partialUpdate(CadencierDTO cadencierDTO);

    /**
     * Get all the cadenciers.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<CadencierDTO> findAll(Pageable pageable);

    /**
     * Get all the CadencierDTO where GmsC<PERSON>s is {@code null}.
     *
     * @return the {@link List} of entities.
     */
    List<CadencierDTO> findAllWhereGmsClientsIsNull();

    /**
     * Get the "id" cadencier.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<CadencierDTO> findOne(Long id);

    /**
     * Delete the "id" cadencier.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
