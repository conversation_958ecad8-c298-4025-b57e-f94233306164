package com.dq.lilas.service.impl;

import com.dq.lilas.domain.TemplateConditions;
import com.dq.lilas.repository.TemplateConditionsRepository;
import com.dq.lilas.service.TemplateConditionsService;
import com.dq.lilas.service.dto.TemplateConditionsDTO;
import com.dq.lilas.service.mapper.TemplateConditionsMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.TemplateConditions}.
 */
@Service
@Transactional
public class TemplateConditionsServiceImpl implements TemplateConditionsService {

    private static final Logger LOG = LoggerFactory.getLogger(TemplateConditionsServiceImpl.class);

    private final TemplateConditionsRepository templateConditionsRepository;

    private final TemplateConditionsMapper templateConditionsMapper;

    public TemplateConditionsServiceImpl(
        TemplateConditionsRepository templateConditionsRepository,
        TemplateConditionsMapper templateConditionsMapper
    ) {
        this.templateConditionsRepository = templateConditionsRepository;
        this.templateConditionsMapper = templateConditionsMapper;
    }

    @Override
    public TemplateConditionsDTO save(TemplateConditionsDTO templateConditionsDTO) {
        LOG.debug("Request to save TemplateConditions : {}", templateConditionsDTO);
        TemplateConditions templateConditions = templateConditionsMapper.toEntity(templateConditionsDTO);
        templateConditions = templateConditionsRepository.save(templateConditions);
        return templateConditionsMapper.toDto(templateConditions);
    }

    @Override
    public TemplateConditionsDTO update(TemplateConditionsDTO templateConditionsDTO) {
        LOG.debug("Request to update TemplateConditions : {}", templateConditionsDTO);
        TemplateConditions templateConditions = templateConditionsMapper.toEntity(templateConditionsDTO);
        templateConditions = templateConditionsRepository.save(templateConditions);
        return templateConditionsMapper.toDto(templateConditions);
    }

    @Override
    public Optional<TemplateConditionsDTO> partialUpdate(TemplateConditionsDTO templateConditionsDTO) {
        LOG.debug("Request to partially update TemplateConditions : {}", templateConditionsDTO);

        return templateConditionsRepository
            .findById(templateConditionsDTO.getId())
            .map(existingTemplateConditions -> {
                templateConditionsMapper.partialUpdate(existingTemplateConditions, templateConditionsDTO);

                return existingTemplateConditions;
            })
            .map(templateConditionsRepository::save)
            .map(templateConditionsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TemplateConditionsDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all TemplateConditions");
        return templateConditionsRepository.findAll(pageable).map(templateConditionsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TemplateConditionsDTO> findOne(Long id) {
        LOG.debug("Request to get TemplateConditions : {}", id);
        return templateConditionsRepository.findById(id).map(templateConditionsMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete TemplateConditions : {}", id);
        templateConditionsRepository.deleteById(id);
    }
}
