package com.dq.lilas.service;

import com.dq.lilas.service.dto.PromotionDetailsDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.PromotionDetails}.
 */
public interface PromotionDetailsService {
    /**
     * Save a promotionDetails.
     *
     * @param promotionDetailsDTO the entity to save.
     * @return the persisted entity.
     */
    PromotionDetailsDTO save(PromotionDetailsDTO promotionDetailsDTO);

    /**
     * Updates a promotionDetails.
     *
     * @param promotionDetailsDTO the entity to update.
     * @return the persisted entity.
     */
    PromotionDetailsDTO update(PromotionDetailsDTO promotionDetailsDTO);

    /**
     * Partially updates a promotionDetails.
     *
     * @param promotionDetailsDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<PromotionDetailsDTO> partialUpdate(PromotionDetailsDTO promotionDetailsDTO);

    /**
     * Get all the promotionDetails.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<PromotionDetailsDTO> findAll(Pageable pageable);

    /**
     * Get the "id" promotionDetails.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<PromotionDetailsDTO> findOne(Long id);

    /**
     * Delete the "id" promotionDetails.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
