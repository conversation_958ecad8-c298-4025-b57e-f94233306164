package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Deliverymode;
import com.dq.lilas.domain.Deliverymodelang;
import com.dq.lilas.service.dto.DeliverymodeDTO;
import com.dq.lilas.service.dto.DeliverymodelangDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Deliverymodelang} and its DTO {@link DeliverymodelangDTO}.
 */
@Mapper(componentModel = "spring")
public interface DeliverymodelangMapper extends EntityMapper<DeliverymodelangDTO, Deliverymodelang> {
    @Mapping(target = "deliverymode", source = "deliverymode", qualifiedByName = "deliverymodeId")
    DeliverymodelangDTO toDto(Deliverymodelang s);

    @Named("deliverymodeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    DeliverymodeDTO toDtoDeliverymodeId(Deliverymode deliverymode);
}
