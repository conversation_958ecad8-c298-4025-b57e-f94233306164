package com.dq.lilas.service;

import com.dq.lilas.service.dto.TemplateConditionsDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.TemplateConditions}.
 */
public interface TemplateConditionsService {
    /**
     * Save a templateConditions.
     *
     * @param templateConditionsDTO the entity to save.
     * @return the persisted entity.
     */
    TemplateConditionsDTO save(TemplateConditionsDTO templateConditionsDTO);

    /**
     * Updates a templateConditions.
     *
     * @param templateConditionsDTO the entity to update.
     * @return the persisted entity.
     */
    TemplateConditionsDTO update(TemplateConditionsDTO templateConditionsDTO);

    /**
     * Partially updates a templateConditions.
     *
     * @param templateConditionsDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<TemplateConditionsDTO> partialUpdate(TemplateConditionsDTO templateConditionsDTO);

    /**
     * Get all the templateConditions.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<TemplateConditionsDTO> findAll(Pageable pageable);

    /**
     * Get the "id" templateConditions.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<TemplateConditionsDTO> findOne(Long id);

    /**
     * Delete the "id" templateConditions.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
