package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Action;
import com.dq.lilas.domain.Correspondence;
import com.dq.lilas.domain.Correspondencecopy;
import com.dq.lilas.domain.Deliverymode;
import com.dq.lilas.domain.Employee;
import com.dq.lilas.domain.Transfer;
import com.dq.lilas.domain.Typecorrespondence;
import com.dq.lilas.domain.Unit;
import com.dq.lilas.service.dto.ActionDTO;
import com.dq.lilas.service.dto.CorrespondenceDTO;
import com.dq.lilas.service.dto.CorrespondencecopyDTO;
import com.dq.lilas.service.dto.DeliverymodeDTO;
import com.dq.lilas.service.dto.EmployeeDTO;
import com.dq.lilas.service.dto.TransferDTO;
import com.dq.lilas.service.dto.TypecorrespondenceDTO;
import com.dq.lilas.service.dto.UnitDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Transfer} and its DTO {@link TransferDTO}.
 */
@Mapper(componentModel = "spring")
public interface TransferMapper extends EntityMapper<TransferDTO, Transfer> {
    @Mapping(target = "transmoth", source = "transmoth", qualifiedByName = "transferId")
    @Mapping(target = "correspondencecopy", source = "correspondencecopy", qualifiedByName = "correspondencecopyId")
    @Mapping(target = "correspondence", source = "correspondence", qualifiedByName = "correspondenceId")
    @Mapping(target = "deliverymode", source = "deliverymode", qualifiedByName = "deliverymodeId")
    @Mapping(target = "employee", source = "employee", qualifiedByName = "employeeId")
    @Mapping(target = "unit", source = "unit", qualifiedByName = "unitId")
    @Mapping(target = "action", source = "action", qualifiedByName = "actionId")
    @Mapping(target = "userreceive", source = "userreceive", qualifiedByName = "employeeId")
    @Mapping(target = "usertrans", source = "usertrans", qualifiedByName = "employeeId")
    @Mapping(target = "usertransto", source = "usertransto", qualifiedByName = "employeeId")
    @Mapping(target = "unittransto", source = "unittransto", qualifiedByName = "unitId")
    @Mapping(target = "userrevoke", source = "userrevoke", qualifiedByName = "employeeId")
    @Mapping(target = "userreceiveto", source = "userreceiveto", qualifiedByName = "employeeId")
    @Mapping(target = "useraction", source = "useraction", qualifiedByName = "employeeId")
    @Mapping(target = "fromdept", source = "fromdept", qualifiedByName = "unitId")
    @Mapping(target = "transprincip", source = "transprincip", qualifiedByName = "transferId")
    @Mapping(target = "typecorrespondence", source = "typecorrespondence", qualifiedByName = "typecorrespondenceId")
    TransferDTO toDto(Transfer s);

    @Named("transferId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TransferDTO toDtoTransferId(Transfer transfer);

    @Named("correspondencecopyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    CorrespondencecopyDTO toDtoCorrespondencecopyId(Correspondencecopy correspondencecopy);

    @Named("correspondenceId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    CorrespondenceDTO toDtoCorrespondenceId(Correspondence correspondence);

    @Named("deliverymodeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    DeliverymodeDTO toDtoDeliverymodeId(Deliverymode deliverymode);

    @Named("employeeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    EmployeeDTO toDtoEmployeeId(Employee employee);

    @Named("unitId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    UnitDTO toDtoUnitId(Unit unit);

    @Named("actionId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    ActionDTO toDtoActionId(Action action);

    @Named("typecorrespondenceId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TypecorrespondenceDTO toDtoTypecorrespondenceId(Typecorrespondence typecorrespondence);
}
