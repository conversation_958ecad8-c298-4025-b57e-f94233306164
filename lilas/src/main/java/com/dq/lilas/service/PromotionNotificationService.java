package com.dq.lilas.service;

import com.dq.lilas.domain.DemandePromotion;
import com.dq.lilas.domain.User;
import com.dq.lilas.domain.enumeration.PromotionStatus;
import com.dq.lilas.repository.UserRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.thymeleaf.context.Context;
import org.thymeleaf.spring6.SpringTemplateEngine;

import java.util.List;

/**
 * Service for sending promotion request email notifications.
 * Uses the existing MailService for reliable email delivery.
 */
@Service
public class PromotionNotificationService {

    private static final Logger LOG = LoggerFactory.getLogger(PromotionNotificationService.class);

    private static final String CONTROLE_GESTION_AUTHORITY = "Controle_gestion";

    private final MailService mailService;
    private final UserRepository userRepository;
    private final SpringTemplateEngine templateEngine;

    public PromotionNotificationService(
        MailService mailService, 
        UserRepository userRepository,
        SpringTemplateEngine templateEngine
    ) {
        this.mailService = mailService;
        this.userRepository = userRepository;
        this.templateEngine = templateEngine;
    }

    /**
     * Send notification to all users with "Controle_gestion" authority when a new promotion request is submitted
     * @param demandePromotion the submitted promotion request
     */
    @Async
    public void notifyControleGestionOnNewRequest(DemandePromotion demandePromotion) {
        LOG.debug("Sending new promotion request notification to Controle_gestion users for request ID: {}", demandePromotion.getId());
        
        // Validate mail configuration first
        if (!mailService.isMailConfigurationValid()) {
            LOG.error("Cannot send email notifications - mail configuration is invalid. Check your SMTP settings.");
            LOG.info("Mail configuration status: {}", mailService.getMailConfigurationStatus());
            return;
        }
        
        List<User> controleGestionUsers = userRepository.findAllByActivatedIsTrueAndAuthorities_Name(CONTROLE_GESTION_AUTHORITY);
        
        if (controleGestionUsers.isEmpty()) {
            LOG.warn("No active Controle_gestion users found to notify about new promotion request");
            return;
        }

        String subject = "New Promotion Request Submitted - ID: " + demandePromotion.getId();
        
        for (User user : controleGestionUsers) {
            if (user.getEmail() != null && !user.getEmail().isEmpty()) {
                try {
                    Context context = createPromotionRequestContext(demandePromotion, user);
                    String content = templateEngine.process("mail/promotionRequestSubmitted", context);
                    
                    mailService.sendEmailWithValidation(user.getEmail(), subject, content, false, true);
                    LOG.debug("Notification sent to Controle_gestion user: {}", user.getEmail());
                } catch (Exception e) {
                    LOG.warn("Failed to process email template, falling back to plain text for user: {}", user.getEmail(), e);
                    try {
                        String fallbackContent = createFallbackNewRequestContent(demandePromotion, user);
                        mailService.sendEmailWithValidation(user.getEmail(), subject, fallbackContent, false, false);
                        LOG.debug("Fallback notification sent to Controle_gestion user: {}", user.getEmail());
                    } catch (Exception fallbackException) {
                        LOG.error("Failed to send fallback notification to Controle_gestion user: {}", user.getEmail(), fallbackException);
                    }
                }
            }
        }
    }

    /**
     * Send notification to the request owner when their promotion request is validated or rejected
     * @param demandePromotion the processed promotion request
     * @param status the final status (Approved or Rejected)
     * @param gmsComments the GMS manager comments
     */
    @Async
    public void notifyRequesterOnStatusChange(DemandePromotion demandePromotion, PromotionStatus status, String gmsComments) {
        LOG.info("=== PROMOTION STATUS CHANGE NOTIFICATION ===");
        LOG.info("Request ID: {}, New Status: {}", demandePromotion.getId(), status);
        
        if (demandePromotion.getEmployee() == null) {
            LOG.warn("No employee found for promotion request ID: {}", demandePromotion.getId());
            return;
        }
        
        // Log detailed employee information
        LOG.info("Employee Info - ID: {}, Name: {}, Mail: {}", 
                 demandePromotion.getEmployee().getId(),
                 demandePromotion.getEmployee().getFullname(),
                 demandePromotion.getEmployee().getMail());
        
        if (demandePromotion.getEmployee().getUser() != null) {
            LOG.info("Employee's User Info - ID: {}, Login: {}, Email: {}", 
                     demandePromotion.getEmployee().getUser().getId(),
                     demandePromotion.getEmployee().getUser().getLogin(),
                     demandePromotion.getEmployee().getUser().getEmail());
        } else {
            LOG.info("Employee's User Info: NULL");
        }
        
        // Try to get email from employee's User relationship first, then from employee's mail field
        String requesterEmail = null;
        if (demandePromotion.getEmployee().getUser() != null && 
            demandePromotion.getEmployee().getUser().getEmail() != null) {
            requesterEmail = demandePromotion.getEmployee().getUser().getEmail();
            LOG.info("Using requester email from User entity: {}", requesterEmail);
        } else if (demandePromotion.getEmployee().getMail() != null && 
                   !demandePromotion.getEmployee().getMail().isEmpty()) {
            requesterEmail = demandePromotion.getEmployee().getMail();
            LOG.info("Using requester email from Employee mail field: {}", requesterEmail);
        }
        
        if (requesterEmail == null || requesterEmail.isEmpty()) {
            LOG.warn("No email found for requester of promotion request ID: {}", demandePromotion.getId());
            return;
        }

        LOG.info("FINAL DECISION: Sending {} notification to requester: {}", status, requesterEmail);
        LOG.info("=== END PROMOTION STATUS CHANGE NOTIFICATION ===");
        
        String subject = String.format("Promotion Request %s - ID: %d", 
                                      status == PromotionStatus.Approved ? "Approved" : "Rejected", 
                                      demandePromotion.getId());
        
        try {
            Context context = createStatusChangeContext(demandePromotion, status, gmsComments);
            String templateName = status == PromotionStatus.Approved ? 
                                "mail/promotionRequestApproved" : "mail/promotionRequestRejected";
            String content = templateEngine.process(templateName, context);
            
            mailService.sendEmailWithValidation(requesterEmail, subject, content, false, true);
            LOG.debug("Status change notification sent to requester: {}", requesterEmail);
        } catch (Exception e) {
            LOG.warn("Failed to process email template, falling back to plain text for requester: {}", requesterEmail, e);
            try {
                String fallbackContent = createFallbackStatusChangeContent(demandePromotion, status, gmsComments);
                mailService.sendEmailWithValidation(requesterEmail, subject, fallbackContent, false, false);
                LOG.debug("Fallback status change notification sent to requester: {}", requesterEmail);
            } catch (Exception fallbackException) {
                LOG.error("Failed to send fallback status change notification to requester: {}", requesterEmail, fallbackException);
            }
        }
    }

    /**
     * Create Thymeleaf context for promotion request submission templates
     */
    private Context createPromotionRequestContext(DemandePromotion demandePromotion, User recipient) {
        Context context = new Context();
        context.setVariable("recipient", recipient);
        context.setVariable("request", demandePromotion);
        context.setVariable("requesterName", demandePromotion.getEmployee() != null ? 
                           demandePromotion.getEmployee().getFullname() : "Unknown");
        context.setVariable("requestId", demandePromotion.getId());
        context.setVariable("clientCode", demandePromotion.getCodeClient());
        context.setVariable("action", demandePromotion.getAction());
        context.setVariable("brand", demandePromotion.getEnseigne());
        context.setVariable("promotionStartDate", demandePromotion.getPeriodPromotionStart());
        context.setVariable("promotionEndDate", demandePromotion.getPeriodPromotionEnd());
        return context;
    }

    /**
     * Create Thymeleaf context for status change templates
     */
    private Context createStatusChangeContext(DemandePromotion demandePromotion, PromotionStatus status, String gmsComments) {
        Context context = new Context();
        context.setVariable("request", demandePromotion);
        context.setVariable("status", status);
        context.setVariable("isApproved", status == PromotionStatus.Approved);
        context.setVariable("isRejected", status == PromotionStatus.Rejected);
        context.setVariable("gmsComments", gmsComments);
        context.setVariable("requesterName", demandePromotion.getEmployee() != null ? 
                           demandePromotion.getEmployee().getFullname() : "Unknown");
        context.setVariable("requestId", demandePromotion.getId());
        context.setVariable("clientCode", demandePromotion.getCodeClient());
        context.setVariable("action", demandePromotion.getAction());
        context.setVariable("brand", demandePromotion.getEnseigne());
        context.setVariable("promotionStartDate", demandePromotion.getPeriodPromotionStart());
        context.setVariable("promotionEndDate", demandePromotion.getPeriodPromotionEnd());
        return context;
    }

    /**
     * Create fallback plain text content for new request notifications
     */
    private String createFallbackNewRequestContent(DemandePromotion demandePromotion, User recipient) {
        StringBuilder content = new StringBuilder();
        content.append("Dear ").append(recipient.getFirstName()).append(" ").append(recipient.getLastName()).append(",\n\n");
        content.append("A new promotion request has been submitted and requires your review.\n\n");
        content.append("Request Details:\n");
        content.append("- Request ID: ").append(demandePromotion.getId()).append("\n");
        content.append("- Submitted by: ").append(demandePromotion.getEmployee() != null ? 
                      demandePromotion.getEmployee().getFullname() : "Unknown").append("\n");
        content.append("- Client Code: ").append(demandePromotion.getCodeClient()).append("\n");
        content.append("- Brand: ").append(demandePromotion.getEnseigne()).append("\n");
        content.append("- Action: ").append(demandePromotion.getAction()).append("\n");
        
        if (demandePromotion.getPeriodPromotionStart() != null && demandePromotion.getPeriodPromotionEnd() != null) {
            content.append("- Promotion Period: ").append(demandePromotion.getPeriodPromotionStart())
                   .append(" to ").append(demandePromotion.getPeriodPromotionEnd()).append("\n");
        }
        
        content.append("\nPlease log in to the system to review and process this request.\n\n");
        content.append("Best regards,\n");
        content.append("Taysir Lilas System");
        
        return content.toString();
    }

    /**
     * Create fallback plain text content for status change notifications
     */
    private String createFallbackStatusChangeContent(DemandePromotion demandePromotion, PromotionStatus status, String gmsComments) {
        StringBuilder content = new StringBuilder();
        content.append("Dear ").append(demandePromotion.getEmployee() != null ? 
                      demandePromotion.getEmployee().getFullname() : "User").append(",\n\n");
        
        content.append("Your promotion request has been ");
        if (status == PromotionStatus.Approved) {
            content.append("APPROVED");
        } else {
            content.append("REJECTED");
        }
        content.append(".\n\n");
        
        content.append("Request Details:\n");
        content.append("- Request ID: ").append(demandePromotion.getId()).append("\n");
        content.append("- Client Code: ").append(demandePromotion.getCodeClient()).append("\n");
        content.append("- Brand: ").append(demandePromotion.getEnseigne()).append("\n");
        content.append("- Action: ").append(demandePromotion.getAction()).append("\n");
        
        if (gmsComments != null && !gmsComments.trim().isEmpty()) {
            content.append("\nGMS Manager Comments:\n").append(gmsComments).append("\n");
        }
        
        content.append("\nPlease log in to the system for more details.\n\n");
        content.append("Best regards,\n");
        content.append("Taysir Lilas System");
        
        return content.toString();
    }
}
