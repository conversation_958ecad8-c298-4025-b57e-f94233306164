package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Correspondencecopy} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class CorrespondencecopyDTO implements Serializable {

    private Long id;

    @Size(max = 15)
    private String numcopy;

    @Size(max = 4000)
    private String comments;

    private Instant datejccreate;

    @Size(max = 10)
    private String datehjrcreate;

    @Size(max = 1)
    private String typereceive;

    @Size(max = 15)
    private String typecopy;

    @Size(max = 15)
    private String userreceive;

    private Instant datejcreceive;

    @Size(max = 10)
    private String datehjrreceive;

    private Instant datejcaction;

    @Size(max = 10)
    private String datehjraction;

    @Size(max = 15)
    private String actiontype;

    @Size(max = 5)
    private String savecorrespcpy;

    @Size(max = 50)
    private String ordernbr;

    @Size(max = 200)
    private String pagenbr;

    @Size(max = 200)
    private String expno;

    @Size(max = 15)
    private String expyear;

    @Size(max = 15)
    private String docyear;

    @Size(max = 5)
    private String categorycorresp;

    @Size(max = 20)
    private String heureaction;

    @Size(max = 10)
    private String statusdeniedcopy;

    @Size(max = 1000)
    private String pagenbrpaper;

    @Size(max = 20)
    private String taskscan;

    @Size(max = 1000)
    private String pathtransto;

    @Size(max = 1000)
    private String pathtranscc;

    @Size(max = 1000)
    private String pathtranstolib;

    @Size(max = 1000)
    private String pathtranscclib;

    @Size(max = 20)
    private String flggroup;

    private Instant datejcdelete;

    private Instant datejcrevoke;

    @Size(max = 10)
    private String datehjrrevoke;

    private Instant dateremove;

    private CorrespondenceDTO correspondence;

    private EmployeeDTO employee;

    private UnitDTO unit;

    private ActionDTO action;

    private EmployeeDTO useraction;

    private EmployeeDTO userrevoke;

    private EmployeeDTO userremove;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getNumcopy() {
        return numcopy;
    }

    public void setNumcopy(String numcopy) {
        this.numcopy = numcopy;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Instant getDatejccreate() {
        return datejccreate;
    }

    public void setDatejccreate(Instant datejccreate) {
        this.datejccreate = datejccreate;
    }

    public String getDatehjrcreate() {
        return datehjrcreate;
    }

    public void setDatehjrcreate(String datehjrcreate) {
        this.datehjrcreate = datehjrcreate;
    }

    public String getTypereceive() {
        return typereceive;
    }

    public void setTypereceive(String typereceive) {
        this.typereceive = typereceive;
    }

    public String getTypecopy() {
        return typecopy;
    }

    public void setTypecopy(String typecopy) {
        this.typecopy = typecopy;
    }

    public String getUserreceive() {
        return userreceive;
    }

    public void setUserreceive(String userreceive) {
        this.userreceive = userreceive;
    }

    public Instant getDatejcreceive() {
        return datejcreceive;
    }

    public void setDatejcreceive(Instant datejcreceive) {
        this.datejcreceive = datejcreceive;
    }

    public String getDatehjrreceive() {
        return datehjrreceive;
    }

    public void setDatehjrreceive(String datehjrreceive) {
        this.datehjrreceive = datehjrreceive;
    }

    public Instant getDatejcaction() {
        return datejcaction;
    }

    public void setDatejcaction(Instant datejcaction) {
        this.datejcaction = datejcaction;
    }

    public String getDatehjraction() {
        return datehjraction;
    }

    public void setDatehjraction(String datehjraction) {
        this.datehjraction = datehjraction;
    }

    public String getActiontype() {
        return actiontype;
    }

    public void setActiontype(String actiontype) {
        this.actiontype = actiontype;
    }

    public String getSavecorrespcpy() {
        return savecorrespcpy;
    }

    public void setSavecorrespcpy(String savecorrespcpy) {
        this.savecorrespcpy = savecorrespcpy;
    }

    public String getOrdernbr() {
        return ordernbr;
    }

    public void setOrdernbr(String ordernbr) {
        this.ordernbr = ordernbr;
    }

    public String getPagenbr() {
        return pagenbr;
    }

    public void setPagenbr(String pagenbr) {
        this.pagenbr = pagenbr;
    }

    public String getExpno() {
        return expno;
    }

    public void setExpno(String expno) {
        this.expno = expno;
    }

    public String getExpyear() {
        return expyear;
    }

    public void setExpyear(String expyear) {
        this.expyear = expyear;
    }

    public String getDocyear() {
        return docyear;
    }

    public void setDocyear(String docyear) {
        this.docyear = docyear;
    }

    public String getCategorycorresp() {
        return categorycorresp;
    }

    public void setCategorycorresp(String categorycorresp) {
        this.categorycorresp = categorycorresp;
    }

    public String getHeureaction() {
        return heureaction;
    }

    public void setHeureaction(String heureaction) {
        this.heureaction = heureaction;
    }

    public String getStatusdeniedcopy() {
        return statusdeniedcopy;
    }

    public void setStatusdeniedcopy(String statusdeniedcopy) {
        this.statusdeniedcopy = statusdeniedcopy;
    }

    public String getPagenbrpaper() {
        return pagenbrpaper;
    }

    public void setPagenbrpaper(String pagenbrpaper) {
        this.pagenbrpaper = pagenbrpaper;
    }

    public String getTaskscan() {
        return taskscan;
    }

    public void setTaskscan(String taskscan) {
        this.taskscan = taskscan;
    }

    public String getPathtransto() {
        return pathtransto;
    }

    public void setPathtransto(String pathtransto) {
        this.pathtransto = pathtransto;
    }

    public String getPathtranscc() {
        return pathtranscc;
    }

    public void setPathtranscc(String pathtranscc) {
        this.pathtranscc = pathtranscc;
    }

    public String getPathtranstolib() {
        return pathtranstolib;
    }

    public void setPathtranstolib(String pathtranstolib) {
        this.pathtranstolib = pathtranstolib;
    }

    public String getPathtranscclib() {
        return pathtranscclib;
    }

    public void setPathtranscclib(String pathtranscclib) {
        this.pathtranscclib = pathtranscclib;
    }

    public String getFlggroup() {
        return flggroup;
    }

    public void setFlggroup(String flggroup) {
        this.flggroup = flggroup;
    }

    public Instant getDatejcdelete() {
        return datejcdelete;
    }

    public void setDatejcdelete(Instant datejcdelete) {
        this.datejcdelete = datejcdelete;
    }

    public Instant getDatejcrevoke() {
        return datejcrevoke;
    }

    public void setDatejcrevoke(Instant datejcrevoke) {
        this.datejcrevoke = datejcrevoke;
    }

    public String getDatehjrrevoke() {
        return datehjrrevoke;
    }

    public void setDatehjrrevoke(String datehjrrevoke) {
        this.datehjrrevoke = datehjrrevoke;
    }

    public Instant getDateremove() {
        return dateremove;
    }

    public void setDateremove(Instant dateremove) {
        this.dateremove = dateremove;
    }

    public CorrespondenceDTO getCorrespondence() {
        return correspondence;
    }

    public void setCorrespondence(CorrespondenceDTO correspondence) {
        this.correspondence = correspondence;
    }

    public EmployeeDTO getEmployee() {
        return employee;
    }

    public void setEmployee(EmployeeDTO employee) {
        this.employee = employee;
    }

    public UnitDTO getUnit() {
        return unit;
    }

    public void setUnit(UnitDTO unit) {
        this.unit = unit;
    }

    public ActionDTO getAction() {
        return action;
    }

    public void setAction(ActionDTO action) {
        this.action = action;
    }

    public EmployeeDTO getUseraction() {
        return useraction;
    }

    public void setUseraction(EmployeeDTO useraction) {
        this.useraction = useraction;
    }

    public EmployeeDTO getUserrevoke() {
        return userrevoke;
    }

    public void setUserrevoke(EmployeeDTO userrevoke) {
        this.userrevoke = userrevoke;
    }

    public EmployeeDTO getUserremove() {
        return userremove;
    }

    public void setUserremove(EmployeeDTO userremove) {
        this.userremove = userremove;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CorrespondencecopyDTO)) {
            return false;
        }

        CorrespondencecopyDTO correspondencecopyDTO = (CorrespondencecopyDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, correspondencecopyDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "CorrespondencecopyDTO{" +
            "id=" + getId() +
            ", numcopy='" + getNumcopy() + "'" +
            ", comments='" + getComments() + "'" +
            ", datejccreate='" + getDatejccreate() + "'" +
            ", datehjrcreate='" + getDatehjrcreate() + "'" +
            ", typereceive='" + getTypereceive() + "'" +
            ", typecopy='" + getTypecopy() + "'" +
            ", userreceive='" + getUserreceive() + "'" +
            ", datejcreceive='" + getDatejcreceive() + "'" +
            ", datehjrreceive='" + getDatehjrreceive() + "'" +
            ", datejcaction='" + getDatejcaction() + "'" +
            ", datehjraction='" + getDatehjraction() + "'" +
            ", actiontype='" + getActiontype() + "'" +
            ", savecorrespcpy='" + getSavecorrespcpy() + "'" +
            ", ordernbr='" + getOrdernbr() + "'" +
            ", pagenbr='" + getPagenbr() + "'" +
            ", expno='" + getExpno() + "'" +
            ", expyear='" + getExpyear() + "'" +
            ", docyear='" + getDocyear() + "'" +
            ", categorycorresp='" + getCategorycorresp() + "'" +
            ", heureaction='" + getHeureaction() + "'" +
            ", statusdeniedcopy='" + getStatusdeniedcopy() + "'" +
            ", pagenbrpaper='" + getPagenbrpaper() + "'" +
            ", taskscan='" + getTaskscan() + "'" +
            ", pathtransto='" + getPathtransto() + "'" +
            ", pathtranscc='" + getPathtranscc() + "'" +
            ", pathtranstolib='" + getPathtranstolib() + "'" +
            ", pathtranscclib='" + getPathtranscclib() + "'" +
            ", flggroup='" + getFlggroup() + "'" +
            ", datejcdelete='" + getDatejcdelete() + "'" +
            ", datejcrevoke='" + getDatejcrevoke() + "'" +
            ", datehjrrevoke='" + getDatehjrrevoke() + "'" +
            ", dateremove='" + getDateremove() + "'" +
            ", correspondence=" + getCorrespondence() +
            ", employee=" + getEmployee() +
            ", unit=" + getUnit() +
            ", action=" + getAction() +
            ", useraction=" + getUseraction() +
            ", userrevoke=" + getUserrevoke() +
            ", userremove=" + getUserremove() +
            "}";
    }
}
