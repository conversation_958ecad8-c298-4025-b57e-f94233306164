package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Room;
import com.dq.lilas.service.dto.RoomDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * Mapper for the entity Room and its DTO RoomDTO.
 */
@Mapper(componentModel = "spring", uses = {UserMapper.class, GroupMapper.class})
public interface RoomMapper extends EntityMapper<RoomDTO, Room> {

    @Mapping(source = "group.id", target = "groupId")
    RoomDTO toDto(Room room);

    @Mapping(source = "groupId", target = "group")
    Room toEntity(RoomDTO roomDTO);

    default Room fromId(Long id) {
        if (id == null) {
            return null;
        }
        Room room = new Room();
        room.setId(id);
        return room;
    }
}
