package com.dq.lilas.service.mapper.impl;

import com.dq.lilas.domain.DemandePromotion;
import com.dq.lilas.domain.PromotionDetails;
import com.dq.lilas.domain.enumeration.PromotionStatus;
import com.dq.lilas.service.dto.DemandePromotionDTO;
import com.dq.lilas.service.dto.PromotionDetailsDTO;
import com.dq.lilas.service.mapper.DemandePromotionMapper;
import com.dq.lilas.service.mapper.EmployeeMapper;
import com.dq.lilas.service.mapper.PromotionDetailsMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Manual implementation of DemandePromotionMapper
 */
@Component
public class DemandePromotionMapperImpl implements DemandePromotionMapper {

    @Autowired
    private EmployeeMapper employeeMapper;

    @Autowired
    @Qualifier("customPromotionDetailsMapperImpl")
    private PromotionDetailsMapper promotionDetailsMapper;

    @Override
    public DemandePromotionDTO toDto(DemandePromotion entity) {
        if (entity == null) {
            return null;
        }

        DemandePromotionDTO dto = new DemandePromotionDTO();
        dto.setId(entity.getId());
        dto.setCodeClient(entity.getCodeClient());
        dto.setEnseigne(entity.getEnseigne());
        dto.setAction(entity.getAction());
        dto.setPeriodPromotionStart(entity.getPeriodPromotionStart());
        dto.setPeriodPromotionEnd(entity.getPeriodPromotionEnd());
        dto.setPeriodFacturationStart(entity.getPeriodFacturationStart());
        dto.setPeriodFacturationEnd(entity.getPeriodFacturationEnd());
        dto.setDateOfRequest(entity.getDateOfRequest());
        dto.setStatus(entity.getStatus());
        
        if (entity.getEmployee() != null) {
            dto.setEmployee(employeeMapper.toDto(entity.getEmployee()));
        }
        
        if (entity.getPromotionDetails() != null) {
            Set<PromotionDetailsDTO> promotionDetailsDTOs = entity.getPromotionDetails().stream()
                .map(promotionDetailsMapper::toDto)
                .collect(Collectors.toSet());
            dto.setPromotionDetails(promotionDetailsDTOs);
        }

        return dto;
    }

    @Override
    public DemandePromotion toEntity(DemandePromotionDTO dto) {
        if (dto == null) {
            return null;
        }

        DemandePromotion entity = new DemandePromotion();
        entity.setId(dto.getId());
        entity.setCodeClient(dto.getCodeClient());
        entity.setEnseigne(dto.getEnseigne());
        entity.setAction(dto.getAction());
        entity.setPeriodPromotionStart(dto.getPeriodPromotionStart());
        entity.setPeriodPromotionEnd(dto.getPeriodPromotionEnd());
        entity.setPeriodFacturationStart(dto.getPeriodFacturationStart());
        entity.setPeriodFacturationEnd(dto.getPeriodFacturationEnd());
        entity.setDateOfRequest(dto.getDateOfRequest());
        
        // Set status, defaulting to Waiting if null
        if (dto.getStatus() != null) {
            entity.setStatus(dto.getStatus());
        } else {
            entity.setStatus(PromotionStatus.Waiting);
        }
        
        if (dto.getEmployee() != null) {
            entity.setEmployee(employeeMapper.toEntity(dto.getEmployee()));
        }
        
        if (dto.getPromotionDetails() != null) {
            Set<PromotionDetails> promotionDetails = dto.getPromotionDetails().stream()
                .map(promotionDetailsMapper::toEntity)
                .collect(Collectors.toSet());
            entity.setPromotionDetails(promotionDetails);
            
            // Set the back-reference
            promotionDetails.forEach(detail -> detail.setDemandePromotion(entity));
        }

        return entity;
    }

    @Override
    public List<DemandePromotionDTO> toDto(List<DemandePromotion> entityList) {
        if (entityList == null) {
            return null;
        }
        return entityList.stream()
            .map(this::toDto)
            .collect(Collectors.toList());
    }

    @Override
    public List<DemandePromotion> toEntity(List<DemandePromotionDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
            .map(this::toEntity)
            .collect(Collectors.toList());
    }

    @Override
    public void partialUpdate(DemandePromotion entity, DemandePromotionDTO dto) {
        if (dto == null) {
            return;
        }

        if (dto.getId() != null) {
            entity.setId(dto.getId());
        }
        if (dto.getCodeClient() != null) {
            entity.setCodeClient(dto.getCodeClient());
        }
        if (dto.getEnseigne() != null) {
            entity.setEnseigne(dto.getEnseigne());
        }
        if (dto.getAction() != null) {
            entity.setAction(dto.getAction());
        }
        if (dto.getPeriodPromotionStart() != null) {
            entity.setPeriodPromotionStart(dto.getPeriodPromotionStart());
        }
        if (dto.getPeriodPromotionEnd() != null) {
            entity.setPeriodPromotionEnd(dto.getPeriodPromotionEnd());
        }
        if (dto.getPeriodFacturationStart() != null) {
            entity.setPeriodFacturationStart(dto.getPeriodFacturationStart());
        }
        if (dto.getPeriodFacturationEnd() != null) {
            entity.setPeriodFacturationEnd(dto.getPeriodFacturationEnd());
        }
        if (dto.getDateOfRequest() != null) {
            entity.setDateOfRequest(dto.getDateOfRequest());
        }
        if (dto.getStatus() != null) {
            entity.setStatus(dto.getStatus());
        } else if (entity.getStatus() == null) {
            // If entity doesn't have status and dto doesn't provide one, set default
            entity.setStatus(PromotionStatus.Waiting);
        }
        if (dto.getEmployee() != null) {
            entity.setEmployee(employeeMapper.toEntity(dto.getEmployee()));
        }
        if (dto.getPromotionDetails() != null) {
            entity.getPromotionDetails().clear();
            Set<PromotionDetails> promotionDetails = dto.getPromotionDetails().stream()
                .map(promotionDetailsMapper::toEntity)
                .collect(Collectors.toSet());
            entity.setPromotionDetails(promotionDetails);
            
            // Set the back-reference
            promotionDetails.forEach(detail -> detail.setDemandePromotion(entity));
        }
    }

    public DemandePromotion fromId(Long id) {
        if (id == null) {
            return null;
        }
        DemandePromotion demandePromotion = new DemandePromotion();
        demandePromotion.setId(id);
        return demandePromotion;
    }
}
