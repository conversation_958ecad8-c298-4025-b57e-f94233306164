package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Company;
import com.dq.lilas.domain.DailyBatches;
import com.dq.lilas.repository.DailyBatchesRepository;
import com.dq.lilas.service.CompanyService;
import com.dq.lilas.service.DailyBatchesService;
import com.dq.lilas.service.dto.DailyBatchesDTO;
import com.dq.lilas.service.mapper.DailyBatchesMapper;

import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.DailyBatches}.
 */
@Service
@Transactional
public class DailyBatchesServiceImpl implements DailyBatchesService {

    private static final Logger LOG = LoggerFactory.getLogger(DailyBatchesServiceImpl.class);

    private final DailyBatchesRepository dailyBatchesRepository;

    private final DailyBatchesMapper dailyBatchesMapper;

    private final CompanyService companyService;

    public DailyBatchesServiceImpl(DailyBatchesRepository dailyBatchesRepository, DailyBatchesMapper dailyBatchesMapper, CompanyService companyService) {
        this.dailyBatchesRepository = dailyBatchesRepository;
        this.dailyBatchesMapper = dailyBatchesMapper;
        this.companyService = companyService;
    }

    @Override
    public DailyBatchesDTO save(DailyBatchesDTO dailyBatchesDTO) {
        LOG.debug("Request to save DailyBatches : {}", dailyBatchesDTO);
        DailyBatches dailyBatches = dailyBatchesMapper.toEntity(dailyBatchesDTO);
        dailyBatches = dailyBatchesRepository.save(dailyBatches);
        return dailyBatchesMapper.toDto(dailyBatches);
    }

    @Override
    public DailyBatchesDTO update(DailyBatchesDTO dailyBatchesDTO) {
        LOG.debug("Request to update DailyBatches : {}", dailyBatchesDTO);
        DailyBatches dailyBatches = dailyBatchesMapper.toEntity(dailyBatchesDTO);
        dailyBatches = dailyBatchesRepository.save(dailyBatches);
        return dailyBatchesMapper.toDto(dailyBatches);
    }

    @Override
    public Optional<DailyBatchesDTO> partialUpdate(DailyBatchesDTO dailyBatchesDTO) {
        LOG.debug("Request to partially update DailyBatches : {}", dailyBatchesDTO);

        return dailyBatchesRepository
            .findById(dailyBatchesDTO.getId())
            .map(existingDailyBatches -> {
                dailyBatchesMapper.partialUpdate(existingDailyBatches, dailyBatchesDTO);

                return existingDailyBatches;
            })
            .map(dailyBatchesRepository::save)
            .map(dailyBatchesMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<DailyBatchesDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all DailyBatches");
        return dailyBatchesRepository.findAll(pageable).map(dailyBatchesMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<DailyBatchesDTO> findOne(Long id) {
        LOG.debug("Request to get DailyBatches : {}", id);
        return dailyBatchesRepository.findById(id).map(dailyBatchesMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<DailyBatchesDTO> findByBatchDate(String batchDate) {
        LOG.debug("Request to get DailyBatches by local date : {}", batchDate);
        try {
            LocalDate localDate = LocalDate.parse(batchDate, DateTimeFormatter.ISO_LOCAL_DATE);
            Instant startOfDay = localDate.atStartOfDay(ZoneId.systemDefault()).toInstant();
            Instant endOfDay = localDate.plusDays(1).atStartOfDay(ZoneId.systemDefault()).toInstant().minusMillis(1);
            return dailyBatchesRepository.findByBatchDateBetween(startOfDay, endOfDay)
                .stream()
                .map(dailyBatchesMapper::toDto)
                .collect(Collectors.toList());
        } catch (Exception e) {
            LOG.error("Invalid batchDate format: {}", batchDate, e);
            return Collections.emptyList();
        }
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete DailyBatches : {}", id);
        dailyBatchesRepository.deleteById(id);
    }

    @Scheduled(cron = "0 1 0 * * ?")
    public void runDailyBatch() {
        LOG.info("Running daily batch process");
        companyService.findAll().forEach(c -> {
            Company company = new Company();
            company.setId(c.getId());
            DailyBatches dailyBatches = new DailyBatches();
            dailyBatches.setBatchDate(java.time.Instant.now());
            dailyBatches.setCompany(company);
            dailyBatchesRepository.save(dailyBatches);
            LOG.info("Daily batch created for company: {}", c.getComapanyName());
        });
    }

    @Override
    @Transactional(readOnly = true)
    public List<DailyBatches> findBatchesByCompanies(List<Long> companyIds) {
        LOG.debug("Request to find DailyBatches by company ID : {}", companyIds);
        return dailyBatchesRepository.findBatchesByCompanies(companyIds);
    }
}
