package com.dq.lilas.service.camunda;

import com.dq.lilas.domain.DemandePromotion;
import com.dq.lilas.repository.DemandePromotionRepository;
import com.dq.lilas.service.PromotionNotificationService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Service delegate for sending notification to Controle_gestion users when a new promotion request is submitted
 */
@Component("notificationService")
public class PromotionNotificationDelegate implements JavaDelegate {

    private static final Logger LOG = LoggerFactory.getLogger(PromotionNotificationDelegate.class);

    private final DemandePromotionRepository demandePromotionRepository;
    private final PromotionNotificationService promotionNotificationService;

    public PromotionNotificationDelegate(
        DemandePromotionRepository demandePromotionRepository,
        PromotionNotificationService promotionNotificationService
    ) {
        this.demandePromotionRepository = demandePromotionRepository;
        this.promotionNotificationService = promotionNotificationService;
    }

    @Override
    public void execute(DelegateExecution execution) throws Exception {
        LOG.info("Executing promotion notification service task");
        
        try {
            Long demandePromotionId = (Long) execution.getVariable("demandePromotionId");
            
            LOG.info("Sending notification to Controle_gestion for promotion request ID: {}", demandePromotionId);
            
            if (demandePromotionId == null) {
                LOG.error("No demandePromotionId found in process variables");
                throw new RuntimeException("Missing demandePromotionId in process variables");
            }
            
            // Retrieve the promotion request
            DemandePromotion demandePromotion = demandePromotionRepository.findById(demandePromotionId)
                .orElseThrow(() -> new RuntimeException("Promotion request not found: " + demandePromotionId));
            
            // Send notification to Controle_gestion users
            promotionNotificationService.notifyControleGestionOnNewRequest(demandePromotion);
            
            LOG.info("Successfully sent notification to Controle_gestion for promotion request ID: {}", demandePromotionId);
            
        } catch (Exception e) {
            LOG.error("Failed to send notification to Controle_gestion", e);
            // Don't fail the process if notification fails
            LOG.warn("Continuing process execution despite notification failure");
        }
    }
}
