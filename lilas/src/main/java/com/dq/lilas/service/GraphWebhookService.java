package com.dq.lilas.service;

import com.dq.lilas.config.GraphClientConfiguration.GraphProperties;
import com.microsoft.graph.models.Subscription;
import com.microsoft.graph.serviceclient.GraphServiceClient;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;

import java.time.OffsetDateTime;
import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
@EnableScheduling
public class GraphWebhookService {

    private final GraphServiceClient graphClient;
    private final GraphProperties graphProperties;

    // In-memory storage for subscription IDs
    private final List<String> activeSubscriptions = new ArrayList<>();

    public Subscription createMessageSubscription(String userId) {
        Subscription subscription = new Subscription();
        subscription.setChangeType("created");
        subscription.setNotificationUrl(graphProperties.getWebhook().getUrl());
        subscription.setResource(String.format("users/%s/mailFolders('Inbox')/messages", userId));
        subscription.setExpirationDateTime(OffsetDateTime.now().plusHours(24));
        subscription.setClientState(graphProperties.getWebhook().getNotificationSecret());

        Subscription createdSubscription = graphClient.subscriptions().post(subscription);
        if (createdSubscription != null && createdSubscription.getId() != null) {
            synchronized (activeSubscriptions) {
                activeSubscriptions.add(createdSubscription.getId());
                log.info("Subscription created: {} for user: {}", createdSubscription.getId(), userId);
            }
        }
        return createdSubscription;
    }

    public void renewSubscription(String subscriptionId) {
        try {
            Subscription subscription = new Subscription();
            subscription.setExpirationDateTime(OffsetDateTime.now().plusHours(24));

            graphClient.subscriptions()
                .bySubscriptionId(subscriptionId)
                .patch(subscription);
            log.info("Subscription renewed: {}", subscriptionId);
        } catch (Exception e) {
            log.error("Failed to renew subscription {}: {}", subscriptionId, e.getMessage());
            synchronized (activeSubscriptions) {
                activeSubscriptions.remove(subscriptionId);
            }
        }
    }

    public void deleteSubscription(String subscriptionId) {
        try {
            graphClient.subscriptions()
                .bySubscriptionId(subscriptionId)
                .delete();
            synchronized (activeSubscriptions) {
                activeSubscriptions.remove(subscriptionId);
            }
            log.info("Subscription deleted: {}", subscriptionId);
        } catch (Exception e) {
            log.error("Failed to delete subscription {}: {}", subscriptionId, e.getMessage());
        }
    }

    @Scheduled(cron = "0 0 */12 * * *") // Every 12 hours
    public void autoRenewSubscriptions() {
        if (!graphProperties.getWebhook().isAutoRenew()) {
            log.info("Auto-renewal is disabled.");
            return;
        }

        log.info("Starting subscription renewal process. Active subscriptions: {}", activeSubscriptions.size());
        List<String> subscriptionsToRenew;
        synchronized (activeSubscriptions) {
            subscriptionsToRenew = new ArrayList<>(activeSubscriptions);
        }

        for (String subscriptionId : subscriptionsToRenew) {
            renewSubscription(subscriptionId);
        }
    }
}
