package com.dq.lilas.service.dto;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.EmployeeGmsBrands} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class EmployeeGmsBrandsDTO implements Serializable {

    private Long id;

    private Instant assignmentDate;

    private Boolean status;

    private EmployeeDTO employee;

    private GmsBrandsDTO gmsBrands;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Instant getAssignmentDate() {
        return assignmentDate;
    }

    public void setAssignmentDate(Instant assignmentDate) {
        this.assignmentDate = assignmentDate;
    }

    public Boolean getStatus() {
        return status;
    }

    public void setStatus(Boolean status) {
        this.status = status;
    }

    public EmployeeDTO getEmployee() {
        return employee;
    }

    public void setEmployee(EmployeeDTO employee) {
        this.employee = employee;
    }

    public GmsBrandsDTO getGmsBrands() {
        return gmsBrands;
    }

    public void setGmsBrands(GmsBrandsDTO gmsBrands) {
        this.gmsBrands = gmsBrands;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof EmployeeGmsBrandsDTO)) {
            return false;
        }

        EmployeeGmsBrandsDTO employeeGmsBrandsDTO = (EmployeeGmsBrandsDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, employeeGmsBrandsDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "EmployeeGmsBrandsDTO{" +
            "id=" + getId() +
            ", assignmentDate='" + getAssignmentDate() + "'" +
            ", status='" + getStatus() + "'" +
            ", employee=" + getEmployee() +
            ", gmsBrands=" + getGmsBrands() +
            "}";
    }
}
