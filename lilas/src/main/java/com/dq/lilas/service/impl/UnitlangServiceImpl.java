package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Unitlang;
import com.dq.lilas.repository.UnitlangRepository;
import com.dq.lilas.service.UnitlangService;
import com.dq.lilas.service.dto.UnitlangDTO;
import com.dq.lilas.service.mapper.UnitlangMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Unitlang}.
 */
@Service
@Transactional
public class UnitlangServiceImpl implements UnitlangService {

    private static final Logger LOG = LoggerFactory.getLogger(UnitlangServiceImpl.class);

    private final UnitlangRepository unitlangRepository;

    private final UnitlangMapper unitlangMapper;

    public UnitlangServiceImpl(UnitlangRepository unitlangRepository, UnitlangMapper unitlangMapper) {
        this.unitlangRepository = unitlangRepository;
        this.unitlangMapper = unitlangMapper;
    }

    @Override
    public UnitlangDTO save(UnitlangDTO unitlangDTO) {
        LOG.debug("Request to save Unitlang : {}", unitlangDTO);
        Unitlang unitlang = unitlangMapper.toEntity(unitlangDTO);
        unitlang = unitlangRepository.save(unitlang);
        return unitlangMapper.toDto(unitlang);
    }

    @Override
    public UnitlangDTO update(UnitlangDTO unitlangDTO) {
        LOG.debug("Request to update Unitlang : {}", unitlangDTO);
        Unitlang unitlang = unitlangMapper.toEntity(unitlangDTO);
        unitlang = unitlangRepository.save(unitlang);
        return unitlangMapper.toDto(unitlang);
    }

    @Override
    public Optional<UnitlangDTO> partialUpdate(UnitlangDTO unitlangDTO) {
        LOG.debug("Request to partially update Unitlang : {}", unitlangDTO);

        return unitlangRepository
            .findById(unitlangDTO.getId())
            .map(existingUnitlang -> {
                unitlangMapper.partialUpdate(existingUnitlang, unitlangDTO);

                return existingUnitlang;
            })
            .map(unitlangRepository::save)
            .map(unitlangMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<UnitlangDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Unitlangs");
        return unitlangRepository.findAll(pageable).map(unitlangMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<UnitlangDTO> findOne(Long id) {
        LOG.debug("Request to get Unitlang : {}", id);
        return unitlangRepository.findById(id).map(unitlangMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Unitlang : {}", id);
        unitlangRepository.deleteById(id);
    }
}
