package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Unit} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class UnitDTO implements Serializable {

    private Long id;

    @Size(max = 15)
    private String parentid;

    @Size(max = 15)
    private String tel;

    @Size(max = 15)
    private String fax;

    private Integer order;

    @Size(max = 1)
    private String deanstatus;

    @Size(max = 1000)
    private String address;

    @Size(max = 15)
    private String nbr;

    @Size(max = 1000)
    private String name;

    @Size(max = 5)
    private String lang;

    @Size(max = 15)
    private String abbreviated;

    @Size(max = 100)
    private String mail;

    @Size(max = 20)
    private String checkenabled;

    @Size(max = 20)
    private String activate;

    @Size(max = 20)
    private String active;

    @Size(max = 20)
    private String level;

    @Size(max = 20)
    private String grp;

    @Size(max = 20)
    private String compid;

    @Size(max = 20)
    private String branch;

    @Size(max = 20)
    private String regoffice;

    @Size(max = 20)
    private String unitgroup;

    @Size(max = 20)
    private String grdparent;

    @Size(max = 1)
    private String status;

    private String category;

    private String functionUnit;

    private String position;

    private String section;

    private String categdir;

    private String categUnit;

    private String function;

    @Size(max = 15)
    private String responsible;

    private CompanyDTO company;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getParentid() {
        return parentid;
    }

    public void setParentid(String parentid) {
        this.parentid = parentid;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public Integer getOrder() {
        return order;
    }

    public void setOrder(Integer order) {
        this.order = order;
    }

    public String getDeanstatus() {
        return deanstatus;
    }

    public void setDeanstatus(String deanstatus) {
        this.deanstatus = deanstatus;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNbr() {
        return nbr;
    }

    public void setNbr(String nbr) {
        this.nbr = nbr;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getAbbreviated() {
        return abbreviated;
    }

    public void setAbbreviated(String abbreviated) {
        this.abbreviated = abbreviated;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getCheckenabled() {
        return checkenabled;
    }

    public void setCheckenabled(String checkenabled) {
        this.checkenabled = checkenabled;
    }

    public String getActivate() {
        return activate;
    }

    public void setActivate(String activate) {
        this.activate = activate;
    }

    public String getActive() {
        return active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getLevel() {
        return level;
    }

    public void setLevel(String level) {
        this.level = level;
    }

    public String getGrp() {
        return grp;
    }

    public void setGrp(String grp) {
        this.grp = grp;
    }

    public String getCompid() {
        return compid;
    }

    public void setCompid(String compid) {
        this.compid = compid;
    }

    public String getBranch() {
        return branch;
    }

    public void setBranch(String branch) {
        this.branch = branch;
    }

    public String getRegoffice() {
        return regoffice;
    }

    public void setRegoffice(String regoffice) {
        this.regoffice = regoffice;
    }

    public String getUnitgroup() {
        return unitgroup;
    }

    public void setUnitgroup(String unitgroup) {
        this.unitgroup = unitgroup;
    }

    public String getGrdparent() {
        return grdparent;
    }

    public void setGrdparent(String grdparent) {
        this.grdparent = grdparent;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getFunctionUnit() {
        return functionUnit;
    }

    public void setFunctionUnit(String functionUnit) {
        this.functionUnit = functionUnit;
    }

    public String getPosition() {
        return position;
    }

    public void setPosition(String position) {
        this.position = position;
    }

    public String getSection() {
        return section;
    }

    public void setSection(String section) {
        this.section = section;
    }

    public String getCategdir() {
        return categdir;
    }

    public void setCategdir(String categdir) {
        this.categdir = categdir;
    }

    public String getCategUnit() {
        return categUnit;
    }

    public void setCategUnit(String categUnit) {
        this.categUnit = categUnit;
    }

    public String getFunction() {
        return function;
    }

    public void setFunction(String function) {
        this.function = function;
    }

    public String getResponsible() {
        return responsible;
    }

    public void setResponsible(String responsible) {
        this.responsible = responsible;
    }

    public CompanyDTO getCompany() {
        return company;
    }

    public void setCompany(CompanyDTO company) {
        this.company = company;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof UnitDTO)) {
            return false;
        }

        UnitDTO unitDTO = (UnitDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, unitDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "UnitDTO{" +
            "id=" + getId() +
            ", parentid='" + getParentid() + "'" +
            ", tel='" + getTel() + "'" +
            ", fax='" + getFax() + "'" +
            ", order=" + getOrder() +
            ", deanstatus='" + getDeanstatus() + "'" +
            ", address='" + getAddress() + "'" +
            ", nbr='" + getNbr() + "'" +
            ", name='" + getName() + "'" +
            ", lang='" + getLang() + "'" +
            ", abbreviated='" + getAbbreviated() + "'" +
            ", mail='" + getMail() + "'" +
            ", checkenabled='" + getCheckenabled() + "'" +
            ", activate='" + getActivate() + "'" +
            ", active='" + getActive() + "'" +
            ", level='" + getLevel() + "'" +
            ", grp='" + getGrp() + "'" +
            ", compid='" + getCompid() + "'" +
            ", branch='" + getBranch() + "'" +
            ", regoffice='" + getRegoffice() + "'" +
            ", unitgroup='" + getUnitgroup() + "'" +
            ", grdparent='" + getGrdparent() + "'" +
            ", status='" + getStatus() + "'" +
            ", category='" + getCategory() + "'" +
            ", functionUnit='" + getFunctionUnit() + "'" +
            ", position='" + getPosition() + "'" +
            ", section='" + getSection() + "'" +
            ", categdir='" + getCategdir() + "'" +
            ", categUnit='" + getCategUnit() + "'" +
            ", function='" + getFunction() + "'" +
            ", responsible='" + getResponsible() + "'" +
            ", company=" + getCompany() +
            "}";
    }
}
