package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Cadencier;
import com.dq.lilas.repository.CadencierRepository;
import com.dq.lilas.service.CadencierService;
import com.dq.lilas.service.dto.CadencierDTO;
import com.dq.lilas.service.mapper.CadencierMapper;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Cadencier}.
 */
@Service
@Transactional
public class CadencierServiceImpl implements CadencierService {

    private static final Logger LOG = LoggerFactory.getLogger(CadencierServiceImpl.class);

    private final CadencierRepository cadencierRepository;

    private final CadencierMapper cadencierMapper;

    public CadencierServiceImpl(CadencierRepository cadencierRepository, CadencierMapper cadencierMapper) {
        this.cadencierRepository = cadencierRepository;
        this.cadencierMapper = cadencierMapper;
    }

    @Override
    public CadencierDTO save(CadencierDTO cadencierDTO) {
        LOG.debug("Request to save Cadencier : {}", cadencierDTO);
        Cadencier cadencier = cadencierMapper.toEntity(cadencierDTO);
        cadencier = cadencierRepository.save(cadencier);
        return cadencierMapper.toDto(cadencier);
    }

    @Override
    public CadencierDTO update(CadencierDTO cadencierDTO) {
        LOG.debug("Request to update Cadencier : {}", cadencierDTO);
        Cadencier cadencier = cadencierMapper.toEntity(cadencierDTO);
        cadencier = cadencierRepository.save(cadencier);
        return cadencierMapper.toDto(cadencier);
    }

    @Override
    public Optional<CadencierDTO> partialUpdate(CadencierDTO cadencierDTO) {
        LOG.debug("Request to partially update Cadencier : {}", cadencierDTO);

        return cadencierRepository
            .findById(cadencierDTO.getId())
            .map(existingCadencier -> {
                cadencierMapper.partialUpdate(existingCadencier, cadencierDTO);

                return existingCadencier;
            })
            .map(cadencierRepository::save)
            .map(cadencierMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CadencierDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Cadenciers");
        return cadencierRepository.findAll(pageable).map(cadencierMapper::toDto);
    }

    /**
     *  Get all the cadenciers where GmsClients is {@code null}.
     *  @return the list of entities.
     */
    @Transactional(readOnly = true)
    public List<CadencierDTO> findAllWhereGmsClientsIsNull() {
        LOG.debug("Request to get all cadenciers where GmsClients is null");
        return StreamSupport.stream(cadencierRepository.findAll().spliterator(), false)
            .filter(cadencier -> cadencier.getGmsClients() == null)
            .map(cadencierMapper::toDto)
            .collect(Collectors.toCollection(LinkedList::new));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CadencierDTO> findOne(Long id) {
        LOG.debug("Request to get Cadencier : {}", id);
        return cadencierRepository.findById(id).map(cadencierMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Cadencier : {}", id);
        cadencierRepository.deleteById(id);
    }
}
