package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Transfer} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class TransferDTO implements Serializable {

    private Long id;

    @Size(max = 15)
    private String docyear;

    @Size(max = 4000)
    private String texttransfer;

    private Instant datejctransfer;

    @Size(max = 30)
    private String datehjrtransfer;

    @Size(max = 5)
    private String statustransfer;

    private Instant datesendjctransfer;

    @Size(max = 30)
    private String datesendhjrtransfer;

    @Size(max = 5)
    private String savetransfer;

    @Size(max = 25)
    private String numcopy;

    @Size(max = 1)
    private String highlevel;

    @Size(max = 10)
    private String confidentiel;

    @Size(max = 1)
    private String priority;

    @Size(max = 10)
    private String timeaction;

    private Instant deadline;

    @Size(max = 15)
    private String rappelnum;

    @Size(max = 15)
    private String rappeltype;

    @Size(max = 1)
    private String readrequest;

    @Size(max = 10)
    private String typereceive;

    private Instant datejcreceive;

    @Size(max = 10)
    private String datehjrreceive;

    @Size(max = 20)
    private String heurearch;

    @Size(max = 20)
    private String actiontype;

    @Size(max = 1000)
    private String comments;

    private Instant datearch;

    @Size(max = 30)
    private String datearchhj;

    @Size(max = 20)
    private String lasttransserial;

    @Size(max = 100)
    private String adrsbooktransto;

    @Size(max = 20)
    private String statusreceiveto;

    @Size(max = 1000)
    private String commentsreceiveto;

    private Instant receivedatejcuserto;

    @Size(max = 30)
    private String receivedatehjruserto;

    @Size(max = 15)
    private String typetransfer;

    @Size(max = 20)
    private String transrecserial;

    @Size(max = 300)
    private String attach;

    @Size(max = 10)
    private String transtype;

    @Size(max = 50)
    private String ordernbr;

    @Size(max = 20)
    private String heureaction;

    private Instant datejcaction;

    @Size(max = 20)
    private String datehjraction;

    @Size(max = 10)
    private String statusdenied;

    @Size(max = 1000)
    private String subjectcorresp;

    private Instant datejccorresp;

    @Size(max = 20)
    private String datehjrcorresp;

    @Size(max = 10)
    private String oldstatus;

    @Size(max = 20)
    private String step;

    @Size(max = 20)
    private String typeprocess;

    @Size(max = 50)
    private String codetask;

    @Size(max = 100)
    private String refusetext;

    @Size(max = 10)
    private String statusrefused;

    @Size(max = 20)
    private String bidadrsbook;

    @Size(max = 1000)
    private String pagenbrpaper;

    @Size(max = 20)
    private String flagprint;

    private Instant dateprint;

    @Size(max = 20)
    private String datehjrprint;

    private Instant datejcdelete;

    private Instant datejcrevoke;

    @Size(max = 20)
    private String datehjrrevoke;

    @Size(max = 4000)
    private String gabaritcontext;

    @Size(max = 20)
    private String approvedspeech;

    private Instant datejcapprovedspeech;

    @Size(max = 20)
    private String datehjrapprovedspeech;

    @Size(max = 50)
    private String conformitytask;

    @Size(max = 500)
    private String useradrsbook;

    @Size(max = 20)
    private String stepmaxwf;

    @Size(max = 5)
    private String incidenttransfer;

    @Size(max = 20)
    private String qualificationincident;

    @Size(max = 20)
    private String categorieincident;

    @Size(max = 20)
    private String statutincident;

    @Size(max = 20)
    private String criticiteincident;

    @Size(max = 255)
    private String voiceId;

    @Size(max = 1)
    private String favoris;

    @Size(max = 1)
    private String checkinboxfavorite;

    @Size(max = 1)
    private String checkclosefavorite;

    @Size(max = 1)
    private String checkfavorite;

    @Size(max = 20)
    private String taskcategId;

    private Boolean pinrequired;

    @Size(max = 6)
    private String codePin;

    private Boolean sendwithmail;

    private TransferDTO transmoth;

    private CorrespondencecopyDTO correspondencecopy;

    private CorrespondenceDTO correspondence;

    private DeliverymodeDTO deliverymode;

    private EmployeeDTO employee;

    private UnitDTO unit;

    private ActionDTO action;

    private EmployeeDTO userreceive;

    private EmployeeDTO usertrans;

    private EmployeeDTO usertransto;

    private UnitDTO unittransto;

    private EmployeeDTO userrevoke;

    private EmployeeDTO userreceiveto;

    private EmployeeDTO useraction;

    private UnitDTO fromdept;

    private TransferDTO transprincip;

    private TypecorrespondenceDTO typecorrespondence;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getDocyear() {
        return docyear;
    }

    public void setDocyear(String docyear) {
        this.docyear = docyear;
    }

    public String getTexttransfer() {
        return texttransfer;
    }

    public void setTexttransfer(String texttransfer) {
        this.texttransfer = texttransfer;
    }

    public Instant getDatejctransfer() {
        return datejctransfer;
    }

    public void setDatejctransfer(Instant datejctransfer) {
        this.datejctransfer = datejctransfer;
    }

    public String getDatehjrtransfer() {
        return datehjrtransfer;
    }

    public void setDatehjrtransfer(String datehjrtransfer) {
        this.datehjrtransfer = datehjrtransfer;
    }

    public String getStatustransfer() {
        return statustransfer;
    }

    public void setStatustransfer(String statustransfer) {
        this.statustransfer = statustransfer;
    }

    public Instant getDatesendjctransfer() {
        return datesendjctransfer;
    }

    public void setDatesendjctransfer(Instant datesendjctransfer) {
        this.datesendjctransfer = datesendjctransfer;
    }

    public String getDatesendhjrtransfer() {
        return datesendhjrtransfer;
    }

    public void setDatesendhjrtransfer(String datesendhjrtransfer) {
        this.datesendhjrtransfer = datesendhjrtransfer;
    }

    public String getSavetransfer() {
        return savetransfer;
    }

    public void setSavetransfer(String savetransfer) {
        this.savetransfer = savetransfer;
    }

    public String getNumcopy() {
        return numcopy;
    }

    public void setNumcopy(String numcopy) {
        this.numcopy = numcopy;
    }

    public String getHighlevel() {
        return highlevel;
    }

    public void setHighlevel(String highlevel) {
        this.highlevel = highlevel;
    }

    public String getConfidentiel() {
        return confidentiel;
    }

    public void setConfidentiel(String confidentiel) {
        this.confidentiel = confidentiel;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getTimeaction() {
        return timeaction;
    }

    public void setTimeaction(String timeaction) {
        this.timeaction = timeaction;
    }

    public Instant getDeadline() {
        return deadline;
    }

    public void setDeadline(Instant deadline) {
        this.deadline = deadline;
    }

    public String getRappelnum() {
        return rappelnum;
    }

    public void setRappelnum(String rappelnum) {
        this.rappelnum = rappelnum;
    }

    public String getRappeltype() {
        return rappeltype;
    }

    public void setRappeltype(String rappeltype) {
        this.rappeltype = rappeltype;
    }

    public String getReadrequest() {
        return readrequest;
    }

    public void setReadrequest(String readrequest) {
        this.readrequest = readrequest;
    }

    public String getTypereceive() {
        return typereceive;
    }

    public void setTypereceive(String typereceive) {
        this.typereceive = typereceive;
    }

    public Instant getDatejcreceive() {
        return datejcreceive;
    }

    public void setDatejcreceive(Instant datejcreceive) {
        this.datejcreceive = datejcreceive;
    }

    public String getDatehjrreceive() {
        return datehjrreceive;
    }

    public void setDatehjrreceive(String datehjrreceive) {
        this.datehjrreceive = datehjrreceive;
    }

    public String getHeurearch() {
        return heurearch;
    }

    public void setHeurearch(String heurearch) {
        this.heurearch = heurearch;
    }

    public String getActiontype() {
        return actiontype;
    }

    public void setActiontype(String actiontype) {
        this.actiontype = actiontype;
    }

    public String getComments() {
        return comments;
    }

    public void setComments(String comments) {
        this.comments = comments;
    }

    public Instant getDatearch() {
        return datearch;
    }

    public void setDatearch(Instant datearch) {
        this.datearch = datearch;
    }

    public String getDatearchhj() {
        return datearchhj;
    }

    public void setDatearchhj(String datearchhj) {
        this.datearchhj = datearchhj;
    }

    public String getLasttransserial() {
        return lasttransserial;
    }

    public void setLasttransserial(String lasttransserial) {
        this.lasttransserial = lasttransserial;
    }

    public String getAdrsbooktransto() {
        return adrsbooktransto;
    }

    public void setAdrsbooktransto(String adrsbooktransto) {
        this.adrsbooktransto = adrsbooktransto;
    }

    public String getStatusreceiveto() {
        return statusreceiveto;
    }

    public void setStatusreceiveto(String statusreceiveto) {
        this.statusreceiveto = statusreceiveto;
    }

    public String getCommentsreceiveto() {
        return commentsreceiveto;
    }

    public void setCommentsreceiveto(String commentsreceiveto) {
        this.commentsreceiveto = commentsreceiveto;
    }

    public Instant getReceivedatejcuserto() {
        return receivedatejcuserto;
    }

    public void setReceivedatejcuserto(Instant receivedatejcuserto) {
        this.receivedatejcuserto = receivedatejcuserto;
    }

    public String getReceivedatehjruserto() {
        return receivedatehjruserto;
    }

    public void setReceivedatehjruserto(String receivedatehjruserto) {
        this.receivedatehjruserto = receivedatehjruserto;
    }

    public String getTypetransfer() {
        return typetransfer;
    }

    public void setTypetransfer(String typetransfer) {
        this.typetransfer = typetransfer;
    }

    public String getTransrecserial() {
        return transrecserial;
    }

    public void setTransrecserial(String transrecserial) {
        this.transrecserial = transrecserial;
    }

    public String getAttach() {
        return attach;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }

    public String getTranstype() {
        return transtype;
    }

    public void setTranstype(String transtype) {
        this.transtype = transtype;
    }

    public String getOrdernbr() {
        return ordernbr;
    }

    public void setOrdernbr(String ordernbr) {
        this.ordernbr = ordernbr;
    }

    public String getHeureaction() {
        return heureaction;
    }

    public void setHeureaction(String heureaction) {
        this.heureaction = heureaction;
    }

    public Instant getDatejcaction() {
        return datejcaction;
    }

    public void setDatejcaction(Instant datejcaction) {
        this.datejcaction = datejcaction;
    }

    public String getDatehjraction() {
        return datehjraction;
    }

    public void setDatehjraction(String datehjraction) {
        this.datehjraction = datehjraction;
    }

    public String getStatusdenied() {
        return statusdenied;
    }

    public void setStatusdenied(String statusdenied) {
        this.statusdenied = statusdenied;
    }

    public String getSubjectcorresp() {
        return subjectcorresp;
    }

    public void setSubjectcorresp(String subjectcorresp) {
        this.subjectcorresp = subjectcorresp;
    }

    public Instant getDatejccorresp() {
        return datejccorresp;
    }

    public void setDatejccorresp(Instant datejccorresp) {
        this.datejccorresp = datejccorresp;
    }

    public String getDatehjrcorresp() {
        return datehjrcorresp;
    }

    public void setDatehjrcorresp(String datehjrcorresp) {
        this.datehjrcorresp = datehjrcorresp;
    }

    public String getOldstatus() {
        return oldstatus;
    }

    public void setOldstatus(String oldstatus) {
        this.oldstatus = oldstatus;
    }

    public String getStep() {
        return step;
    }

    public void setStep(String step) {
        this.step = step;
    }

    public String getTypeprocess() {
        return typeprocess;
    }

    public void setTypeprocess(String typeprocess) {
        this.typeprocess = typeprocess;
    }

    public String getCodetask() {
        return codetask;
    }

    public void setCodetask(String codetask) {
        this.codetask = codetask;
    }

    public String getRefusetext() {
        return refusetext;
    }

    public void setRefusetext(String refusetext) {
        this.refusetext = refusetext;
    }

    public String getStatusrefused() {
        return statusrefused;
    }

    public void setStatusrefused(String statusrefused) {
        this.statusrefused = statusrefused;
    }

    public String getBidadrsbook() {
        return bidadrsbook;
    }

    public void setBidadrsbook(String bidadrsbook) {
        this.bidadrsbook = bidadrsbook;
    }

    public String getPagenbrpaper() {
        return pagenbrpaper;
    }

    public void setPagenbrpaper(String pagenbrpaper) {
        this.pagenbrpaper = pagenbrpaper;
    }

    public String getFlagprint() {
        return flagprint;
    }

    public void setFlagprint(String flagprint) {
        this.flagprint = flagprint;
    }

    public Instant getDateprint() {
        return dateprint;
    }

    public void setDateprint(Instant dateprint) {
        this.dateprint = dateprint;
    }

    public String getDatehjrprint() {
        return datehjrprint;
    }

    public void setDatehjrprint(String datehjrprint) {
        this.datehjrprint = datehjrprint;
    }

    public Instant getDatejcdelete() {
        return datejcdelete;
    }

    public void setDatejcdelete(Instant datejcdelete) {
        this.datejcdelete = datejcdelete;
    }

    public Instant getDatejcrevoke() {
        return datejcrevoke;
    }

    public void setDatejcrevoke(Instant datejcrevoke) {
        this.datejcrevoke = datejcrevoke;
    }

    public String getDatehjrrevoke() {
        return datehjrrevoke;
    }

    public void setDatehjrrevoke(String datehjrrevoke) {
        this.datehjrrevoke = datehjrrevoke;
    }

    public String getGabaritcontext() {
        return gabaritcontext;
    }

    public void setGabaritcontext(String gabaritcontext) {
        this.gabaritcontext = gabaritcontext;
    }

    public String getApprovedspeech() {
        return approvedspeech;
    }

    public void setApprovedspeech(String approvedspeech) {
        this.approvedspeech = approvedspeech;
    }

    public Instant getDatejcapprovedspeech() {
        return datejcapprovedspeech;
    }

    public void setDatejcapprovedspeech(Instant datejcapprovedspeech) {
        this.datejcapprovedspeech = datejcapprovedspeech;
    }

    public String getDatehjrapprovedspeech() {
        return datehjrapprovedspeech;
    }

    public void setDatehjrapprovedspeech(String datehjrapprovedspeech) {
        this.datehjrapprovedspeech = datehjrapprovedspeech;
    }

    public String getConformitytask() {
        return conformitytask;
    }

    public void setConformitytask(String conformitytask) {
        this.conformitytask = conformitytask;
    }

    public String getUseradrsbook() {
        return useradrsbook;
    }

    public void setUseradrsbook(String useradrsbook) {
        this.useradrsbook = useradrsbook;
    }

    public String getStepmaxwf() {
        return stepmaxwf;
    }

    public void setStepmaxwf(String stepmaxwf) {
        this.stepmaxwf = stepmaxwf;
    }

    public String getIncidenttransfer() {
        return incidenttransfer;
    }

    public void setIncidenttransfer(String incidenttransfer) {
        this.incidenttransfer = incidenttransfer;
    }

    public String getQualificationincident() {
        return qualificationincident;
    }

    public void setQualificationincident(String qualificationincident) {
        this.qualificationincident = qualificationincident;
    }

    public String getCategorieincident() {
        return categorieincident;
    }

    public void setCategorieincident(String categorieincident) {
        this.categorieincident = categorieincident;
    }

    public String getStatutincident() {
        return statutincident;
    }

    public void setStatutincident(String statutincident) {
        this.statutincident = statutincident;
    }

    public String getCriticiteincident() {
        return criticiteincident;
    }

    public void setCriticiteincident(String criticiteincident) {
        this.criticiteincident = criticiteincident;
    }

    public String getVoiceId() {
        return voiceId;
    }

    public void setVoiceId(String voiceId) {
        this.voiceId = voiceId;
    }

    public String getFavoris() {
        return favoris;
    }

    public void setFavoris(String favoris) {
        this.favoris = favoris;
    }

    public String getCheckinboxfavorite() {
        return checkinboxfavorite;
    }

    public void setCheckinboxfavorite(String checkinboxfavorite) {
        this.checkinboxfavorite = checkinboxfavorite;
    }

    public String getCheckclosefavorite() {
        return checkclosefavorite;
    }

    public void setCheckclosefavorite(String checkclosefavorite) {
        this.checkclosefavorite = checkclosefavorite;
    }

    public String getCheckfavorite() {
        return checkfavorite;
    }

    public void setCheckfavorite(String checkfavorite) {
        this.checkfavorite = checkfavorite;
    }

    public String getTaskcategId() {
        return taskcategId;
    }

    public void setTaskcategId(String taskcategId) {
        this.taskcategId = taskcategId;
    }

    public Boolean getPinrequired() {
        return pinrequired;
    }

    public void setPinrequired(Boolean pinrequired) {
        this.pinrequired = pinrequired;
    }

    public String getCodePin() {
        return codePin;
    }

    public void setCodePin(String codePin) {
        this.codePin = codePin;
    }

    public Boolean getSendwithmail() {
        return sendwithmail;
    }

    public void setSendwithmail(Boolean sendwithmail) {
        this.sendwithmail = sendwithmail;
    }

    public TransferDTO getTransmoth() {
        return transmoth;
    }

    public void setTransmoth(TransferDTO transmoth) {
        this.transmoth = transmoth;
    }

    public CorrespondencecopyDTO getCorrespondencecopy() {
        return correspondencecopy;
    }

    public void setCorrespondencecopy(CorrespondencecopyDTO correspondencecopy) {
        this.correspondencecopy = correspondencecopy;
    }

    public CorrespondenceDTO getCorrespondence() {
        return correspondence;
    }

    public void setCorrespondence(CorrespondenceDTO correspondence) {
        this.correspondence = correspondence;
    }

    public DeliverymodeDTO getDeliverymode() {
        return deliverymode;
    }

    public void setDeliverymode(DeliverymodeDTO deliverymode) {
        this.deliverymode = deliverymode;
    }

    public EmployeeDTO getEmployee() {
        return employee;
    }

    public void setEmployee(EmployeeDTO employee) {
        this.employee = employee;
    }

    public UnitDTO getUnit() {
        return unit;
    }

    public void setUnit(UnitDTO unit) {
        this.unit = unit;
    }

    public ActionDTO getAction() {
        return action;
    }

    public void setAction(ActionDTO action) {
        this.action = action;
    }

    public EmployeeDTO getUserreceive() {
        return userreceive;
    }

    public void setUserreceive(EmployeeDTO userreceive) {
        this.userreceive = userreceive;
    }

    public EmployeeDTO getUsertrans() {
        return usertrans;
    }

    public void setUsertrans(EmployeeDTO usertrans) {
        this.usertrans = usertrans;
    }

    public EmployeeDTO getUsertransto() {
        return usertransto;
    }

    public void setUsertransto(EmployeeDTO usertransto) {
        this.usertransto = usertransto;
    }

    public UnitDTO getUnittransto() {
        return unittransto;
    }

    public void setUnittransto(UnitDTO unittransto) {
        this.unittransto = unittransto;
    }

    public EmployeeDTO getUserrevoke() {
        return userrevoke;
    }

    public void setUserrevoke(EmployeeDTO userrevoke) {
        this.userrevoke = userrevoke;
    }

    public EmployeeDTO getUserreceiveto() {
        return userreceiveto;
    }

    public void setUserreceiveto(EmployeeDTO userreceiveto) {
        this.userreceiveto = userreceiveto;
    }

    public EmployeeDTO getUseraction() {
        return useraction;
    }

    public void setUseraction(EmployeeDTO useraction) {
        this.useraction = useraction;
    }

    public UnitDTO getFromdept() {
        return fromdept;
    }

    public void setFromdept(UnitDTO fromdept) {
        this.fromdept = fromdept;
    }

    public TransferDTO getTransprincip() {
        return transprincip;
    }

    public void setTransprincip(TransferDTO transprincip) {
        this.transprincip = transprincip;
    }

    public TypecorrespondenceDTO getTypecorrespondence() {
        return typecorrespondence;
    }

    public void setTypecorrespondence(TypecorrespondenceDTO typecorrespondence) {
        this.typecorrespondence = typecorrespondence;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TransferDTO)) {
            return false;
        }

        TransferDTO transferDTO = (TransferDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, transferDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "TransferDTO{" +
            "id=" + getId() +
            ", docyear='" + getDocyear() + "'" +
            ", texttransfer='" + getTexttransfer() + "'" +
            ", datejctransfer='" + getDatejctransfer() + "'" +
            ", datehjrtransfer='" + getDatehjrtransfer() + "'" +
            ", statustransfer='" + getStatustransfer() + "'" +
            ", datesendjctransfer='" + getDatesendjctransfer() + "'" +
            ", datesendhjrtransfer='" + getDatesendhjrtransfer() + "'" +
            ", savetransfer='" + getSavetransfer() + "'" +
            ", numcopy='" + getNumcopy() + "'" +
            ", highlevel='" + getHighlevel() + "'" +
            ", confidentiel='" + getConfidentiel() + "'" +
            ", priority='" + getPriority() + "'" +
            ", timeaction='" + getTimeaction() + "'" +
            ", deadline='" + getDeadline() + "'" +
            ", rappelnum='" + getRappelnum() + "'" +
            ", rappeltype='" + getRappeltype() + "'" +
            ", readrequest='" + getReadrequest() + "'" +
            ", typereceive='" + getTypereceive() + "'" +
            ", datejcreceive='" + getDatejcreceive() + "'" +
            ", datehjrreceive='" + getDatehjrreceive() + "'" +
            ", heurearch='" + getHeurearch() + "'" +
            ", actiontype='" + getActiontype() + "'" +
            ", comments='" + getComments() + "'" +
            ", datearch='" + getDatearch() + "'" +
            ", datearchhj='" + getDatearchhj() + "'" +
            ", lasttransserial='" + getLasttransserial() + "'" +
            ", adrsbooktransto='" + getAdrsbooktransto() + "'" +
            ", statusreceiveto='" + getStatusreceiveto() + "'" +
            ", commentsreceiveto='" + getCommentsreceiveto() + "'" +
            ", receivedatejcuserto='" + getReceivedatejcuserto() + "'" +
            ", receivedatehjruserto='" + getReceivedatehjruserto() + "'" +
            ", typetransfer='" + getTypetransfer() + "'" +
            ", transrecserial='" + getTransrecserial() + "'" +
            ", attach='" + getAttach() + "'" +
            ", transtype='" + getTranstype() + "'" +
            ", ordernbr='" + getOrdernbr() + "'" +
            ", heureaction='" + getHeureaction() + "'" +
            ", datejcaction='" + getDatejcaction() + "'" +
            ", datehjraction='" + getDatehjraction() + "'" +
            ", statusdenied='" + getStatusdenied() + "'" +
            ", subjectcorresp='" + getSubjectcorresp() + "'" +
            ", datejccorresp='" + getDatejccorresp() + "'" +
            ", datehjrcorresp='" + getDatehjrcorresp() + "'" +
            ", oldstatus='" + getOldstatus() + "'" +
            ", step='" + getStep() + "'" +
            ", typeprocess='" + getTypeprocess() + "'" +
            ", codetask='" + getCodetask() + "'" +
            ", refusetext='" + getRefusetext() + "'" +
            ", statusrefused='" + getStatusrefused() + "'" +
            ", bidadrsbook='" + getBidadrsbook() + "'" +
            ", pagenbrpaper='" + getPagenbrpaper() + "'" +
            ", flagprint='" + getFlagprint() + "'" +
            ", dateprint='" + getDateprint() + "'" +
            ", datehjrprint='" + getDatehjrprint() + "'" +
            ", datejcdelete='" + getDatejcdelete() + "'" +
            ", datejcrevoke='" + getDatejcrevoke() + "'" +
            ", datehjrrevoke='" + getDatehjrrevoke() + "'" +
            ", gabaritcontext='" + getGabaritcontext() + "'" +
            ", approvedspeech='" + getApprovedspeech() + "'" +
            ", datejcapprovedspeech='" + getDatejcapprovedspeech() + "'" +
            ", datehjrapprovedspeech='" + getDatehjrapprovedspeech() + "'" +
            ", conformitytask='" + getConformitytask() + "'" +
            ", useradrsbook='" + getUseradrsbook() + "'" +
            ", stepmaxwf='" + getStepmaxwf() + "'" +
            ", incidenttransfer='" + getIncidenttransfer() + "'" +
            ", qualificationincident='" + getQualificationincident() + "'" +
            ", categorieincident='" + getCategorieincident() + "'" +
            ", statutincident='" + getStatutincident() + "'" +
            ", criticiteincident='" + getCriticiteincident() + "'" +
            ", voiceId='" + getVoiceId() + "'" +
            ", favoris='" + getFavoris() + "'" +
            ", checkinboxfavorite='" + getCheckinboxfavorite() + "'" +
            ", checkclosefavorite='" + getCheckclosefavorite() + "'" +
            ", checkfavorite='" + getCheckfavorite() + "'" +
            ", taskcategId='" + getTaskcategId() + "'" +
            ", pinrequired='" + getPinrequired() + "'" +
            ", codePin='" + getCodePin() + "'" +
            ", sendwithmail='" + getSendwithmail() + "'" +
            ", transmoth=" + getTransmoth() +
            ", correspondencecopy=" + getCorrespondencecopy() +
            ", correspondence=" + getCorrespondence() +
            ", deliverymode=" + getDeliverymode() +
            ", employee=" + getEmployee() +
            ", unit=" + getUnit() +
            ", action=" + getAction() +
            ", userreceive=" + getUserreceive() +
            ", usertrans=" + getUsertrans() +
            ", usertransto=" + getUsertransto() +
            ", unittransto=" + getUnittransto() +
            ", userrevoke=" + getUserrevoke() +
            ", userreceiveto=" + getUserreceiveto() +
            ", useraction=" + getUseraction() +
            ", fromdept=" + getFromdept() +
            ", transprincip=" + getTransprincip() +
            ", typecorrespondence=" + getTypecorrespondence() +
            "}";
    }
}
