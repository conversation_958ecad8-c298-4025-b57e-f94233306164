package com.dq.lilas.service.dto;

import jakarta.persistence.Lob;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.EmailsNotifications} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class EmailsNotificationsDTO implements Serializable {

    private Long id;

    private String subject;

    private Instant notificationDate;

    @Lob
    private byte[] body;

    private String bodyContentType;

    private Long recipient;

    private String sender;

    private OrderDetailsDTO orderDetails;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public Instant getNotificationDate() {
        return notificationDate;
    }

    public void setNotificationDate(Instant notificationDate) {
        this.notificationDate = notificationDate;
    }

    public byte[] getBody() {
        return body;
    }

    public void setBody(byte[] body) {
        this.body = body;
    }

    public String getBodyContentType() {
        return bodyContentType;
    }

    public void setBodyContentType(String bodyContentType) {
        this.bodyContentType = bodyContentType;
    }

    public Long getRecipient() {
        return recipient;
    }

    public void setRecipient(Long recipient) {
        this.recipient = recipient;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public OrderDetailsDTO getOrderDetails() {
        return orderDetails;
    }

    public void setOrderDetails(OrderDetailsDTO orderDetails) {
        this.orderDetails = orderDetails;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof EmailsNotificationsDTO)) {
            return false;
        }

        EmailsNotificationsDTO emailsNotificationsDTO = (EmailsNotificationsDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, emailsNotificationsDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "EmailsNotificationsDTO{" +
            "id=" + getId() +
            ", subject='" + getSubject() + "'" +
            ", notificationDate='" + getNotificationDate() + "'" +
            ", body='" + getBody() + "'" +
            ", recipient=" + getRecipient() +
            ", sender='" + getSender() + "'" +
            ", orderDetails=" + getOrderDetails() +
            "}";
    }
}
