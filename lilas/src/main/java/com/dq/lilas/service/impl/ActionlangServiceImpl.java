package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Actionlang;
import com.dq.lilas.repository.ActionlangRepository;
import com.dq.lilas.service.ActionlangService;
import com.dq.lilas.service.dto.ActionlangDTO;
import com.dq.lilas.service.mapper.ActionlangMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Actionlang}.
 */
@Service
@Transactional
public class ActionlangServiceImpl implements ActionlangService {

    private static final Logger LOG = LoggerFactory.getLogger(ActionlangServiceImpl.class);

    private final ActionlangRepository actionlangRepository;

    private final ActionlangMapper actionlangMapper;

    public ActionlangServiceImpl(ActionlangRepository actionlangRepository, ActionlangMapper actionlangMapper) {
        this.actionlangRepository = actionlangRepository;
        this.actionlangMapper = actionlangMapper;
    }

    @Override
    public ActionlangDTO save(ActionlangDTO actionlangDTO) {
        LOG.debug("Request to save Actionlang : {}", actionlangDTO);
        Actionlang actionlang = actionlangMapper.toEntity(actionlangDTO);
        actionlang = actionlangRepository.save(actionlang);
        return actionlangMapper.toDto(actionlang);
    }

    @Override
    public ActionlangDTO update(ActionlangDTO actionlangDTO) {
        LOG.debug("Request to update Actionlang : {}", actionlangDTO);
        Actionlang actionlang = actionlangMapper.toEntity(actionlangDTO);
        actionlang = actionlangRepository.save(actionlang);
        return actionlangMapper.toDto(actionlang);
    }

    @Override
    public Optional<ActionlangDTO> partialUpdate(ActionlangDTO actionlangDTO) {
        LOG.debug("Request to partially update Actionlang : {}", actionlangDTO);

        return actionlangRepository
            .findById(actionlangDTO.getId())
            .map(existingActionlang -> {
                actionlangMapper.partialUpdate(existingActionlang, actionlangDTO);

                return existingActionlang;
            })
            .map(actionlangRepository::save)
            .map(actionlangMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ActionlangDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Actionlangs");
        return actionlangRepository.findAll(pageable).map(actionlangMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ActionlangDTO> findOne(Long id) {
        LOG.debug("Request to get Actionlang : {}", id);
        return actionlangRepository.findById(id).map(actionlangMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Actionlang : {}", id);
        actionlangRepository.deleteById(id);
    }
}
