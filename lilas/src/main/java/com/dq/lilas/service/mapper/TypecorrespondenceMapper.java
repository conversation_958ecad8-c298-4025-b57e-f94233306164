package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Typecorrespondence;
import com.dq.lilas.service.dto.TypecorrespondenceDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Typecorrespondence} and its DTO {@link TypecorrespondenceDTO}.
 */
@Mapper(componentModel = "spring")
public interface TypecorrespondenceMapper extends EntityMapper<TypecorrespondenceDTO, Typecorrespondence> {}
