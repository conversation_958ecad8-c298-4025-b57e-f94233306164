package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Conversation;
import com.dq.lilas.service.dto.ConversationDTO;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

/**
 * Mapper for the entity Conversation and its DTO ConversationDTO.
 */
@Mapper(componentModel = "spring", uses = {RoomMapper.class, UserMapper.class})
public interface ConversationMapper extends EntityMapper<ConversationDTO, Conversation> {

    @Mapping(source = "room.id", target = "roomId")
    @Mapping(source = "sender.id", target = "senderId")
    @Mapping(source = "sender.firstName", target = "senderFirstName")
    @Mapping(source = "sender.lastName", target = "senderLastName")
    @Mapping(source = "sender.login", target = "senderLogin")
    ConversationDTO toDto(Conversation conversation);

    @Mapping(source = "roomId", target = "room")
    @Mapping(source = "senderId", target = "sender")
    Conversation toEntity(ConversationDTO conversationDTO);

    default Conversation fromId(Long id) {
        if (id == null) {
            return null;
        }
        Conversation conversation = new Conversation();
        conversation.setId(id);
        return conversation;
    }
}
