package com.dq.lilas.service;

import com.dq.lilas.service.dto.DeliverymodelangDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Deliverymodelang}.
 */
public interface DeliverymodelangService {
    /**
     * Save a deliverymodelang.
     *
     * @param deliverymodelangDTO the entity to save.
     * @return the persisted entity.
     */
    DeliverymodelangDTO save(DeliverymodelangDTO deliverymodelangDTO);

    /**
     * Updates a deliverymodelang.
     *
     * @param deliverymodelangDTO the entity to update.
     * @return the persisted entity.
     */
    DeliverymodelangDTO update(DeliverymodelangDTO deliverymodelangDTO);

    /**
     * Partially updates a deliverymodelang.
     *
     * @param deliverymodelangDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<DeliverymodelangDTO> partialUpdate(DeliverymodelangDTO deliverymodelangDTO);

    /**
     * Get all the deliverymodelangs.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<DeliverymodelangDTO> findAll(Pageable pageable);

    /**
     * Get the "id" deliverymodelang.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<DeliverymodelangDTO> findOne(Long id);

    /**
     * Delete the "id" deliverymodelang.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
