package com.dq.lilas.service;

import com.dq.lilas.service.dto.ProductsListDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.ProductsList}.
 */
public interface ProductsListService {
    /**
     * Save a productsList.
     *
     * @param productsListDTO the entity to save.
     * @return the persisted entity.
     */
    ProductsListDTO save(ProductsListDTO productsListDTO);

    /**
     * Updates a productsList.
     *
     * @param productsListDTO the entity to update.
     * @return the persisted entity.
     */
    ProductsListDTO update(ProductsListDTO productsListDTO);

    /**
     * Partially updates a productsList.
     *
     * @param productsListDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<ProductsListDTO> partialUpdate(ProductsListDTO productsListDTO);

    /**
     * Get all the productsLists.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<ProductsListDTO> findAll(Pageable pageable);

    /**
     * Get the "id" productsList.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<ProductsListDTO> findOne(Long id);

    /**
     * Delete the "id" productsList.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
