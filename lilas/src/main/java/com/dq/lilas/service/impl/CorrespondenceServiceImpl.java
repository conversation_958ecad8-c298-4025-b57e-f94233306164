package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Correspondence;
import com.dq.lilas.repository.CorrespondenceRepository;
import com.dq.lilas.service.CorrespondenceService;
import com.dq.lilas.service.dto.CorrespondenceDTO;
import com.dq.lilas.service.mapper.CorrespondenceMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Correspondence}.
 */
@Service
@Transactional
public class CorrespondenceServiceImpl implements CorrespondenceService {

    private static final Logger LOG = LoggerFactory.getLogger(CorrespondenceServiceImpl.class);

    private final CorrespondenceRepository correspondenceRepository;

    private final CorrespondenceMapper correspondenceMapper;

    public CorrespondenceServiceImpl(CorrespondenceRepository correspondenceRepository, CorrespondenceMapper correspondenceMapper) {
        this.correspondenceRepository = correspondenceRepository;
        this.correspondenceMapper = correspondenceMapper;
    }

    @Override
    public CorrespondenceDTO save(CorrespondenceDTO correspondenceDTO) {
        LOG.debug("Request to save Correspondence : {}", correspondenceDTO);
        Correspondence correspondence = correspondenceMapper.toEntity(correspondenceDTO);
        correspondence = correspondenceRepository.save(correspondence);
        return correspondenceMapper.toDto(correspondence);
    }

    @Override
    public CorrespondenceDTO update(CorrespondenceDTO correspondenceDTO) {
        LOG.debug("Request to update Correspondence : {}", correspondenceDTO);
        Correspondence correspondence = correspondenceMapper.toEntity(correspondenceDTO);
        correspondence = correspondenceRepository.save(correspondence);
        return correspondenceMapper.toDto(correspondence);
    }

    @Override
    public Optional<CorrespondenceDTO> partialUpdate(CorrespondenceDTO correspondenceDTO) {
        LOG.debug("Request to partially update Correspondence : {}", correspondenceDTO);

        return correspondenceRepository
            .findById(correspondenceDTO.getId())
            .map(existingCorrespondence -> {
                correspondenceMapper.partialUpdate(existingCorrespondence, correspondenceDTO);

                return existingCorrespondence;
            })
            .map(correspondenceRepository::save)
            .map(correspondenceMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CorrespondenceDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Correspondences");
        return correspondenceRepository.findAll(pageable).map(correspondenceMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CorrespondenceDTO> findOne(Long id) {
        LOG.debug("Request to get Correspondence : {}", id);
        return correspondenceRepository.findById(id).map(correspondenceMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Correspondence : {}", id);
        correspondenceRepository.deleteById(id);
    }
}
