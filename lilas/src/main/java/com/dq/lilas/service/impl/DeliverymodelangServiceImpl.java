package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Deliverymodelang;
import com.dq.lilas.repository.DeliverymodelangRepository;
import com.dq.lilas.service.DeliverymodelangService;
import com.dq.lilas.service.dto.DeliverymodelangDTO;
import com.dq.lilas.service.mapper.DeliverymodelangMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Deliverymodelang}.
 */
@Service
@Transactional
public class DeliverymodelangServiceImpl implements DeliverymodelangService {

    private static final Logger LOG = LoggerFactory.getLogger(DeliverymodelangServiceImpl.class);

    private final DeliverymodelangRepository deliverymodelangRepository;

    private final DeliverymodelangMapper deliverymodelangMapper;

    public DeliverymodelangServiceImpl(
        DeliverymodelangRepository deliverymodelangRepository,
        DeliverymodelangMapper deliverymodelangMapper
    ) {
        this.deliverymodelangRepository = deliverymodelangRepository;
        this.deliverymodelangMapper = deliverymodelangMapper;
    }

    @Override
    public DeliverymodelangDTO save(DeliverymodelangDTO deliverymodelangDTO) {
        LOG.debug("Request to save Deliverymodelang : {}", deliverymodelangDTO);
        Deliverymodelang deliverymodelang = deliverymodelangMapper.toEntity(deliverymodelangDTO);
        deliverymodelang = deliverymodelangRepository.save(deliverymodelang);
        return deliverymodelangMapper.toDto(deliverymodelang);
    }

    @Override
    public DeliverymodelangDTO update(DeliverymodelangDTO deliverymodelangDTO) {
        LOG.debug("Request to update Deliverymodelang : {}", deliverymodelangDTO);
        Deliverymodelang deliverymodelang = deliverymodelangMapper.toEntity(deliverymodelangDTO);
        deliverymodelang = deliverymodelangRepository.save(deliverymodelang);
        return deliverymodelangMapper.toDto(deliverymodelang);
    }

    @Override
    public Optional<DeliverymodelangDTO> partialUpdate(DeliverymodelangDTO deliverymodelangDTO) {
        LOG.debug("Request to partially update Deliverymodelang : {}", deliverymodelangDTO);

        return deliverymodelangRepository
            .findById(deliverymodelangDTO.getId())
            .map(existingDeliverymodelang -> {
                deliverymodelangMapper.partialUpdate(existingDeliverymodelang, deliverymodelangDTO);

                return existingDeliverymodelang;
            })
            .map(deliverymodelangRepository::save)
            .map(deliverymodelangMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<DeliverymodelangDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Deliverymodelangs");
        return deliverymodelangRepository.findAll(pageable).map(deliverymodelangMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<DeliverymodelangDTO> findOne(Long id) {
        LOG.debug("Request to get Deliverymodelang : {}", id);
        return deliverymodelangRepository.findById(id).map(deliverymodelangMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Deliverymodelang : {}", id);
        deliverymodelangRepository.deleteById(id);
    }
}
