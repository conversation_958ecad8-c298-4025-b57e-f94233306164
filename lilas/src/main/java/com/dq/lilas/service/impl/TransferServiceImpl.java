package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Transfer;
import com.dq.lilas.repository.TransferRepository;
import com.dq.lilas.service.TransferService;
import com.dq.lilas.service.dto.TransferDTO;
import com.dq.lilas.service.mapper.TransferMapper;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Transfer}.
 */
@Service
@Transactional
public class TransferServiceImpl implements TransferService {

    private static final Logger LOG = LoggerFactory.getLogger(TransferServiceImpl.class);

    private final TransferRepository transferRepository;

    private final TransferMapper transferMapper;

    public TransferServiceImpl(TransferRepository transferRepository, TransferMapper transferMapper) {
        this.transferRepository = transferRepository;
        this.transferMapper = transferMapper;
    }

    @Override
    public TransferDTO save(TransferDTO transferDTO) {
        LOG.debug("Request to save Transfer : {}", transferDTO);
        Transfer transfer = transferMapper.toEntity(transferDTO);
        transfer = transferRepository.save(transfer);
        return transferMapper.toDto(transfer);
    }

    @Override
    public TransferDTO update(TransferDTO transferDTO) {
        LOG.debug("Request to update Transfer : {}", transferDTO);
        Transfer transfer = transferMapper.toEntity(transferDTO);
        transfer = transferRepository.save(transfer);
        return transferMapper.toDto(transfer);
    }

    @Override
    public Optional<TransferDTO> partialUpdate(TransferDTO transferDTO) {
        LOG.debug("Request to partially update Transfer : {}", transferDTO);

        return transferRepository
            .findById(transferDTO.getId())
            .map(existingTransfer -> {
                transferMapper.partialUpdate(existingTransfer, transferDTO);

                return existingTransfer;
            })
            .map(transferRepository::save)
            .map(transferMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TransferDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Transfers");
        return transferRepository.findAll(pageable).map(transferMapper::toDto);
    }

    /**
     *  Get all the transfers where Transfer is {@code null}.
     *  @return the list of entities.
     */
    @Transactional(readOnly = true)
    public List<TransferDTO> findAllWhereTransferIsNull() {
        LOG.debug("Request to get all transfers where Transfer is null");
        return StreamSupport.stream(transferRepository.findAll().spliterator(), false)
            .filter(transfer -> transfer.getTransfer() == null)
            .map(transferMapper::toDto)
            .collect(Collectors.toCollection(LinkedList::new));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TransferDTO> findOne(Long id) {
        LOG.debug("Request to get Transfer : {}", id);
        return transferRepository.findById(id).map(transferMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Transfer : {}", id);
        transferRepository.deleteById(id);
    }
}
