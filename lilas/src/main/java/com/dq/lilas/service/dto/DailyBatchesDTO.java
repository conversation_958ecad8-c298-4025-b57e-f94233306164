package com.dq.lilas.service.dto;

import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.DailyBatches} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class DailyBatchesDTO implements Serializable {

    private Long id;

    private Instant batchDate;

    private int orderNb;


    private CompanyDTO company;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Instant getBatchDate() {
        return batchDate;
    }

    public void setBatchDate(Instant batchDate) {
        this.batchDate = batchDate;
    }

    public int getOrderNb() {
        return orderNb;
    }

    public void setOrderNb(int orderNb) {
        this.orderNb = orderNb;
    }

    public CompanyDTO getCompany() {
        return company;
    }

    public void setCompany(CompanyDTO company) {
        this.company = company;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DailyBatchesDTO)) {
            return false;
        }

        DailyBatchesDTO dailyBatchesDTO = (DailyBatchesDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, dailyBatchesDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "DailyBatchesDTO{" +
            "id=" + getId() +
            ", batchDate='" + getBatchDate() + "'" +
            ", orderNb='" + getOrderNb() + "'" +
            ", company=" + getCompany() +
            "}";
    }
}
