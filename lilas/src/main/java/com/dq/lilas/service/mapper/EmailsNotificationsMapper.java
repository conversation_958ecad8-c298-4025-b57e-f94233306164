package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.EmailsNotifications;
import com.dq.lilas.domain.OrderDetails;
import com.dq.lilas.service.dto.EmailsNotificationsDTO;
import com.dq.lilas.service.dto.OrderDetailsDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link EmailsNotifications} and its DTO {@link EmailsNotificationsDTO}.
 */
@Mapper(componentModel = "spring")
public interface EmailsNotificationsMapper extends EntityMapper<EmailsNotificationsDTO, EmailsNotifications> {
    @Mapping(target = "orderDetails", source = "orderDetails", qualifiedByName = "orderDetailsId")
    EmailsNotificationsDTO toDto(EmailsNotifications s);

    @Named("orderDetailsId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    OrderDetailsDTO toDtoOrderDetailsId(OrderDetails orderDetails);
}
