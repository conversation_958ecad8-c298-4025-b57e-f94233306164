package com.dq.lilas.service.impl;

import com.dq.lilas.domain.DailyStock;
import com.dq.lilas.domain.OrderDetails;
import com.dq.lilas.repository.DailyStockRepository;
import com.dq.lilas.repository.OrderDetailsRepository;
import com.dq.lilas.service.DailyStockService;
import com.dq.lilas.service.dto.DailyStockDTO;
import com.dq.lilas.service.mapper.DailyStockMapper;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Optional;

import com.dq.lilas.service.mapper.OrderDetailsMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.DailyStock}.
 */
@Service
@Transactional
public class DailyStockServiceImpl implements DailyStockService {

    private static final Logger LOG = LoggerFactory.getLogger(DailyStockServiceImpl.class);

    private final DailyStockRepository dailyStockRepository;

    private final DailyStockMapper dailyStockMapper;

    private final OrderDetailsMapper orderDetailsMapper;

    private final OrderDetailsRepository orderDetailsRepository;

    public DailyStockServiceImpl(DailyStockRepository dailyStockRepository, DailyStockMapper dailyStockMapper, OrderDetailsMapper orderDetailsMapper, OrderDetailsRepository orderDetailsRepository) {
        this.dailyStockRepository = dailyStockRepository;
        this.dailyStockMapper = dailyStockMapper;
        this.orderDetailsMapper = orderDetailsMapper;
        this.orderDetailsRepository = orderDetailsRepository;
    }


    @Override
    public DailyStockDTO save(DailyStockDTO dailyStockDTO) {
        LOG.debug("Request to save DailyStock : {}", dailyStockDTO);
        DailyStock dailyStock = dailyStockMapper.toEntity(dailyStockDTO);
        dailyStock.setRemainingQuantity(dailyStockDTO.getStockQty());
        dailyStock = dailyStockRepository.save(dailyStock);
        return dailyStockMapper.toDto(dailyStock);
    }

    @Override
    public DailyStockDTO update(DailyStockDTO dailyStockDTO) {
        LOG.debug("Request to update DailyStock : {}", dailyStockDTO);
        DailyStock dailyStock = dailyStockMapper.toEntity(dailyStockDTO);
        dailyStock = dailyStockRepository.save(dailyStock);
        return dailyStockMapper.toDto(dailyStock);
    }

    @Override
    public Optional<DailyStockDTO> partialUpdate(DailyStockDTO dailyStockDTO) {
        LOG.debug("Request to partially update DailyStock : {}", dailyStockDTO);

        return dailyStockRepository
            .findById(dailyStockDTO.getId())
            .map(existingDailyStock -> {
                dailyStockMapper.partialUpdate(existingDailyStock, dailyStockDTO);

                return existingDailyStock;
            })
            .map(dailyStockRepository::save)
            .map(dailyStockMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<DailyStockDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all DailyStocks");
        return dailyStockRepository.findAll(pageable).map(dailyStockMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<DailyStockDTO> findOne(Long id) {
        LOG.debug("Request to get DailyStock : {}", id);
        return dailyStockRepository.findById(id).map(dailyStockMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete DailyStock : {}", id);
        dailyStockRepository.deleteById(id);
    }

    @Override
    public DailyStockDTO updateByInternalCodeAndDate(String internalCode, LocalDate stockDate, long quantityOrdered) {
        DailyStock dailyStock = dailyStockRepository.findByInternalCodeAndStockDate(internalCode, stockDate);
        LOG.debug("find DailyStock with internal code: {}", dailyStock);

        if (dailyStock == null) {
            throw new IllegalArgumentException("DailyStock not found for internalCode: " + internalCode + " and stockDate: " + stockDate);
        }

        Long remaining = dailyStock.getRemainingQuantity();
        if (remaining == null) {
            remaining = 0L;
        }

        if (remaining > quantityOrdered) {
            dailyStock.setRemainingQuantity(remaining - quantityOrdered);
            dailyStock = dailyStockRepository.save(dailyStock);
            return dailyStockMapper.toDto(dailyStock);
        } else {
            OrderDetails orderDetails = orderDetailsRepository.findOrderDetailsByInternalCode(internalCode);
            LOG.debug("OrderDetails with internal code: {}", orderDetails);

            if (orderDetails != null) {
                orderDetails.setUpdatedQty(remaining);
                orderDetailsRepository.save(orderDetails);
            }
            dailyStock.setRemainingQuantity(0L);
            dailyStock = dailyStockRepository.save(dailyStock);
            return dailyStockMapper.toDto(dailyStock);
        }
    }

    @Override
    public DailyStockDTO restoreStockByInternalCodeAndDate(String internalCode, LocalDate stockDate, long quantityOrdered, long quantityUpdated) {
        DailyStock dailyStock = dailyStockRepository.findByInternalCodeAndStockDate(internalCode, stockDate);
        LOG.debug("find DailyStock with internal code: {}", dailyStock);

        if (dailyStock == null) {
            throw new IllegalArgumentException("DailyStock not found for internalCode: " + internalCode + " and stockDate: " + stockDate);
        }

        Long remaining = dailyStock.getRemainingQuantity();
        if (remaining == null) {
            remaining = 0L;
        }

        long quantity = quantityOrdered - quantityUpdated;
        long newRemaining = remaining + quantity;


        dailyStock.setRemainingQuantity(newRemaining);
        dailyStock = dailyStockRepository.save(dailyStock);
        return dailyStockMapper.toDto(dailyStock);
    }

    @Override
    public long getRemainingQuantityByInternalCodeAndDate(String internalCode, LocalDate stockDate) {
        DailyStock dailyStock = dailyStockRepository.findByInternalCodeAndStockDate(internalCode, stockDate);
        LOG.debug("find DailyStock with internal code: {}", dailyStock);

        if (dailyStock == null) {
            throw new IllegalArgumentException("DailyStock not found for internalCode: " + internalCode + " and stockDate: " + stockDate);
        }

        return dailyStock.getRemainingQuantity();
    }

}
