package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Typecorrespondencelang;
import com.dq.lilas.repository.TypecorrespondencelangRepository;
import com.dq.lilas.service.TypecorrespondencelangService;
import com.dq.lilas.service.dto.TypecorrespondencelangDTO;
import com.dq.lilas.service.mapper.TypecorrespondencelangMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Typecorrespondencelang}.
 */
@Service
@Transactional
public class TypecorrespondencelangServiceImpl implements TypecorrespondencelangService {

    private static final Logger LOG = LoggerFactory.getLogger(TypecorrespondencelangServiceImpl.class);

    private final TypecorrespondencelangRepository typecorrespondencelangRepository;

    private final TypecorrespondencelangMapper typecorrespondencelangMapper;

    public TypecorrespondencelangServiceImpl(
        TypecorrespondencelangRepository typecorrespondencelangRepository,
        TypecorrespondencelangMapper typecorrespondencelangMapper
    ) {
        this.typecorrespondencelangRepository = typecorrespondencelangRepository;
        this.typecorrespondencelangMapper = typecorrespondencelangMapper;
    }

    @Override
    public TypecorrespondencelangDTO save(TypecorrespondencelangDTO typecorrespondencelangDTO) {
        LOG.debug("Request to save Typecorrespondencelang : {}", typecorrespondencelangDTO);
        Typecorrespondencelang typecorrespondencelang = typecorrespondencelangMapper.toEntity(typecorrespondencelangDTO);
        typecorrespondencelang = typecorrespondencelangRepository.save(typecorrespondencelang);
        return typecorrespondencelangMapper.toDto(typecorrespondencelang);
    }

    @Override
    public TypecorrespondencelangDTO update(TypecorrespondencelangDTO typecorrespondencelangDTO) {
        LOG.debug("Request to update Typecorrespondencelang : {}", typecorrespondencelangDTO);
        Typecorrespondencelang typecorrespondencelang = typecorrespondencelangMapper.toEntity(typecorrespondencelangDTO);
        typecorrespondencelang = typecorrespondencelangRepository.save(typecorrespondencelang);
        return typecorrespondencelangMapper.toDto(typecorrespondencelang);
    }

    @Override
    public Optional<TypecorrespondencelangDTO> partialUpdate(TypecorrespondencelangDTO typecorrespondencelangDTO) {
        LOG.debug("Request to partially update Typecorrespondencelang : {}", typecorrespondencelangDTO);

        return typecorrespondencelangRepository
            .findById(typecorrespondencelangDTO.getId())
            .map(existingTypecorrespondencelang -> {
                typecorrespondencelangMapper.partialUpdate(existingTypecorrespondencelang, typecorrespondencelangDTO);

                return existingTypecorrespondencelang;
            })
            .map(typecorrespondencelangRepository::save)
            .map(typecorrespondencelangMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TypecorrespondencelangDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Typecorrespondencelangs");
        return typecorrespondencelangRepository.findAll(pageable).map(typecorrespondencelangMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TypecorrespondencelangDTO> findOne(Long id) {
        LOG.debug("Request to get Typecorrespondencelang : {}", id);
        return typecorrespondencelangRepository.findById(id).map(typecorrespondencelangMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Typecorrespondencelang : {}", id);
        typecorrespondencelangRepository.deleteById(id);
    }
}
