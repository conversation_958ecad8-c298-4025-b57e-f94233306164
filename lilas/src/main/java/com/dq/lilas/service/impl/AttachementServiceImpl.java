package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Attachement;
import com.dq.lilas.repository.AttachementRepository;
import com.dq.lilas.service.AttachementService;
import com.dq.lilas.service.dto.AttachementDTO;
import com.dq.lilas.service.mapper.AttachementMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Attachement}.
 */
@Service
@Transactional
public class AttachementServiceImpl implements AttachementService {

    private static final Logger LOG = LoggerFactory.getLogger(AttachementServiceImpl.class);

    private final AttachementRepository attachementRepository;

    private final AttachementMapper attachementMapper;

    public AttachementServiceImpl(AttachementRepository attachementRepository, AttachementMapper attachementMapper) {
        this.attachementRepository = attachementRepository;
        this.attachementMapper = attachementMapper;
    }

    @Override
    public AttachementDTO save(AttachementDTO attachementDTO) {
        LOG.debug("Request to save Attachement : {}", attachementDTO);
        Attachement attachement = attachementMapper.toEntity(attachementDTO);
        attachement = attachementRepository.save(attachement);
        return attachementMapper.toDto(attachement);
    }

    @Override
    public AttachementDTO update(AttachementDTO attachementDTO) {
        LOG.debug("Request to update Attachement : {}", attachementDTO);
        Attachement attachement = attachementMapper.toEntity(attachementDTO);
        attachement = attachementRepository.save(attachement);
        return attachementMapper.toDto(attachement);
    }

    @Override
    public Optional<AttachementDTO> partialUpdate(AttachementDTO attachementDTO) {
        LOG.debug("Request to partially update Attachement : {}", attachementDTO);

        return attachementRepository
            .findById(attachementDTO.getId())
            .map(existingAttachement -> {
                attachementMapper.partialUpdate(existingAttachement, attachementDTO);

                return existingAttachement;
            })
            .map(attachementRepository::save)
            .map(attachementMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<AttachementDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Attachements");
        return attachementRepository.findAll(pageable).map(attachementMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<AttachementDTO> findOne(Long id) {
        LOG.debug("Request to get Attachement : {}", id);
        return attachementRepository.findById(id).map(attachementMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Attachement : {}", id);
        attachementRepository.deleteById(id);
    }
}
