package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Action;
import com.dq.lilas.domain.Actionlang;
import com.dq.lilas.service.dto.ActionDTO;
import com.dq.lilas.service.dto.ActionlangDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Actionlang} and its DTO {@link ActionlangDTO}.
 */
@Mapper(componentModel = "spring")
public interface ActionlangMapper extends EntityMapper<ActionlangDTO, Actionlang> {
    @Mapping(target = "action", source = "action", qualifiedByName = "actionId")
    ActionlangDTO toDto(Actionlang s);

    @Named("actionId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    ActionDTO toDtoActionId(Action action);
}
