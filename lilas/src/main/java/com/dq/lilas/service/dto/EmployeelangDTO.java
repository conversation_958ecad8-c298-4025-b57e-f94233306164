package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Employeelang} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class EmployeelangDTO implements Serializable {

    private Long id;

    @Size(max = 500)
    private String fullname;

    @Size(max = 500)
    private String address;

    @Size(max = 5)
    private String lang;

    @Size(max = 10)
    private String matricule;

    private EmployeeDTO employee;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getMatricule() {
        return matricule;
    }

    public void setMatricule(String matricule) {
        this.matricule = matricule;
    }

    public EmployeeDTO getEmployee() {
        return employee;
    }

    public void setEmployee(EmployeeDTO employee) {
        this.employee = employee;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof EmployeelangDTO)) {
            return false;
        }

        EmployeelangDTO employeelangDTO = (EmployeelangDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, employeelangDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "EmployeelangDTO{" +
            "id=" + getId() +
            ", fullname='" + getFullname() + "'" +
            ", address='" + getAddress() + "'" +
            ", lang='" + getLang() + "'" +
            ", matricule='" + getMatricule() + "'" +
            ", employee=" + getEmployee() +
            "}";
    }
}
