package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Order;
import com.dq.lilas.domain.OrderDetails;
import com.dq.lilas.domain.Company;
import com.dq.lilas.domain.GmsClients;
import com.dq.lilas.domain.GmsBrands;
import com.dq.lilas.domain.DailyBatches;
import com.dq.lilas.domain.enumeration.OrderStatus;
import com.dq.lilas.repository.OrderDetailsRepository;
import com.dq.lilas.repository.OrderRepository;
import com.dq.lilas.repository.CompanyRepository;
import com.dq.lilas.repository.GmsClientsRepository;
import com.dq.lilas.repository.GmsBrandsRepository;
import com.dq.lilas.repository.DailyBatchesRepository;
import com.dq.lilas.service.OrderService;
import com.dq.lilas.service.dto.DailyBatchesDTO;
import com.dq.lilas.service.dto.EmployeeDTO;
import com.dq.lilas.service.dto.GmsClientsDTO;
import com.dq.lilas.service.dto.MailsDTO;
import com.dq.lilas.service.dto.OrderDTO;
import com.dq.lilas.service.dto.OrderDetailsDTO;
import com.dq.lilas.service.dto.TemplateConditionsDTO;
import com.dq.lilas.service.dto.ClientBrandInfoDTO;
import com.dq.lilas.service.dto.BatchOrderInfoDTO;
import com.dq.lilas.service.mapper.OrderMapper;
import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Order}.
 */
@Service
@Transactional
public class OrderServiceImpl implements OrderService {

    private static final Logger LOG = LoggerFactory.getLogger(OrderServiceImpl.class);

    private final OrderRepository orderRepository;

    private final OrderDetailsRepository orderDetailsRepository;

    private final OrderMapper orderMapper;

    private final CompanyRepository companyRepository;

    private final GmsClientsRepository gmsClientsRepository;

    private final GmsBrandsRepository gmsBrandsRepository;

    private final DailyBatchesRepository dailyBatchesRepository;

    public OrderServiceImpl(
            OrderRepository orderRepository,
            OrderDetailsRepository orderDetailsRepository,
            OrderMapper orderMapper,
            CompanyRepository companyRepository,
            GmsClientsRepository gmsClientsRepository,
            GmsBrandsRepository gmsBrandsRepository,
            DailyBatchesRepository dailyBatchesRepository) {
        this.orderRepository = orderRepository;
        this.orderDetailsRepository = orderDetailsRepository;
        this.orderMapper = orderMapper;
        this.companyRepository = companyRepository;
        this.gmsClientsRepository = gmsClientsRepository;
        this.gmsBrandsRepository = gmsBrandsRepository;
        this.dailyBatchesRepository = dailyBatchesRepository;
    }

    @Override
    public OrderDTO save(OrderDTO orderDTO) {
        LOG.debug("Request to save Order : {}", orderDTO);
        Order order = orderMapper.toEntity(orderDTO);
        order = orderRepository.save(order);
        return orderMapper.toDto(order);
    }

    @Override
    public OrderDTO update(OrderDTO orderDTO) {
        LOG.debug("Request to update Order : {}", orderDTO);
        Order order = orderMapper.toEntity(orderDTO);
        order = orderRepository.save(order);
        return orderMapper.toDto(order);
    }

    @Override
    public Optional<OrderDTO> partialUpdate(OrderDTO orderDTO) {
        LOG.debug("Request to partially update Order : {}", orderDTO);

        return orderRepository
                .findById(orderDTO.getId())
                .map(existingOrder -> {
                    orderMapper.partialUpdate(existingOrder, orderDTO);

                    return existingOrder;
                })
                .map(orderRepository::save)
                .map(orderMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OrderDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Orders");
        return orderRepository.findAll(pageable).map(orderMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<OrderDTO> findOne(Long id) {
        LOG.debug("Request to get Order : {}", id);
        return orderRepository.findById(id)
                .map(order -> {
                    // Use custom mapping to ensure all fields are populated
                    OrderDTO orderDTO = orderMapper.toDto(order);

                    // Ensure that order details have proper references back to the order
                    if (orderDTO.getOrderDetails() != null) {
                        // Create a single order reference to be shared by all details
                        OrderDTO orderRef = new OrderDTO();
                        orderRef.setId(orderDTO.getId());
                        // Include only essential fields to avoid data duplication
                        orderRef.setOrderNumber(orderDTO.getOrderNumber());
                        orderRef.setStatus(orderDTO.getStatus());

                        // Set this shared order reference on each detail
                        for (OrderDetailsDTO detailDTO : orderDTO.getOrderDetails()) {
                            detailDTO.setOrder(orderRef);
                        }
                    }

                    return orderDTO;
                });
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Order : {}", id);
        orderRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<OrderDTO> getOrdersByDeliveryDates(String operator, Instant comparisonDate, Integer status) {
        LOG.debug("Request to get Orders by delivery dates: operator={}, comparisonDate={}, status={}", operator,
                comparisonDate, status);

        List<Order> orders;
        if (">".equals(operator)) {
            orders = orderRepository.findOrdersByDeliveryDateAfterAndStatus(comparisonDate, status);
        } else if ("<=".equals(operator)) {
            orders = orderRepository.findOrdersByDeliveryDateBeforeOrEqualAndStatus(comparisonDate, status);
        } else {
            throw new IllegalArgumentException(
                    "Unsupported operator: " + operator + ". Supported operators are '>' and '<='");
        }

        return orders.stream()
                .map(orderMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<OrderDTO> getOrdersByStatus(String statusCode) {
        LOG.debug("Request to get Orders by status: statusCode={}", statusCode);

        // Map status codes to integer values
        Integer status;
        switch (statusCode.toUpperCase()) {
            case "D": // livré (delivered)
                status = 1; // Assuming 1 represents delivered status
                break;
            case "P": // payé (paid)
                status = 2; // Assuming 2 represents paid status
                break;
            default:
                throw new IllegalArgumentException(
                        "Unsupported status code: " + statusCode + ". Supported codes are 'D' (livré) and 'P' (payé)");
        }

        List<Order> orders = orderRepository.findOrdersByStatus(status);

        return orders.stream()
                .map(orderMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    public OrderDetailsDTO saveOrderDetail(OrderDetailsDTO orderDetailsDTO) {
        LOG.debug("Request to save OrderDetails : {}", orderDetailsDTO);

        try {
            // Convert DTO to entity manually to avoid using mapper
            OrderDetails orderDetails = new OrderDetails();

            // Set only the basic properties that exist in the database
            orderDetails.setQuantity(
                    orderDetailsDTO.getQuantity() != null ? orderDetailsDTO.getQuantity().longValue() : null);
            orderDetails.setUpdatedQty(
                    orderDetailsDTO.getUpdatedQty() != null ? orderDetailsDTO.getUpdatedQty().longValue() : null);
            orderDetails.setDiscount(orderDetailsDTO.getDiscount());
            orderDetails.setUpdatedDiscount(orderDetailsDTO.getUpdatedDiscount());
            orderDetails.setUnitPrice(orderDetailsDTO.getUnitPrice());
            orderDetails.setUpdatedUnitPrice(orderDetailsDTO.getUpdatedUnitPrice());
            orderDetails.setOrderLineJson(orderDetailsDTO.getOrderLineJson());
            orderDetails.setAvailability(orderDetailsDTO.getAvailability());
            // Note: validConditions and injected fields have been removed from the entity
            orderDetails.setBarcode(orderDetailsDTO.getBarcode());
            orderDetails.setUpdatedBarcode(orderDetailsDTO.getUpdatedBarcode());
            orderDetails.setInternalCode(orderDetailsDTO.getInternalCode());
            orderDetails.setUpdatedInternalCode(orderDetailsDTO.getUpdatedInternalCode());

            // IMPORTANT: Do not set these fields as they don't exist in the database
            // orderDetails.setDiscountStatus(orderDetailsDTO.getDiscountStatus());
            // orderDetails.setPriceStatus(orderDetailsDTO.getPriceStatus());
            // orderDetails.setQuantityStatus(orderDetailsDTO.getQuantityStatus());
            // orderDetails.setProductStatus(orderDetailsDTO.getProductStatus());

            // Set parent order if present
            if (orderDetailsDTO.getOrder() != null && orderDetailsDTO.getOrder().getId() != null) {
                Order order = orderRepository.findById(orderDetailsDTO.getOrder().getId()).orElse(null);
                orderDetails.setOrder(order);
            }

            // Save the entity
            orderDetails = orderDetailsRepository.save(orderDetails);

            // Convert back to DTO manually to avoid mapper
            OrderDetailsDTO result = new OrderDetailsDTO();
            result.setId(orderDetails.getId());
            result.setQuantity(orderDetails.getQuantity() != null ? new BigDecimal(orderDetails.getQuantity()) : null);
            result.setUpdatedQty(
                    orderDetails.getUpdatedQty() != null ? new BigDecimal(orderDetails.getUpdatedQty()) : null);
            result.setDiscount(orderDetails.getDiscount());
            result.setUpdatedDiscount(orderDetails.getUpdatedDiscount());
            result.setUnitPrice(orderDetails.getUnitPrice());
            result.setUpdatedUnitPrice(orderDetails.getUpdatedUnitPrice());
            result.setOrderLineJson(orderDetails.getOrderLineJson());
            result.setAvailability(orderDetails.getAvailability());
            // Note: validConditions and injected fields have been removed from the entity
            result.setBarcode(orderDetails.getBarcode());
            result.setUpdatedBarcode(orderDetails.getUpdatedBarcode());
            result.setInternalCode(orderDetails.getInternalCode());
            result.setUpdatedInternalCode(orderDetails.getUpdatedInternalCode());

            // Create minimal order reference with only essential properties
            if (orderDetails.getOrder() != null) {
                OrderDTO orderDTO = new OrderDTO();
                Order order = orderDetails.getOrder();
                orderDTO.setId(order.getId());
                orderDTO.setOrderNumber(order.getOrderNumber());
                orderDTO.setStatus(order.getStatus());
                result.setOrder(orderDTO);
            }

            return result;
        } catch (Exception e) {
            LOG.error("Error saving OrderDetails: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public OrderDTO createOrderFromBC(MailsDTO savedMail, com.dq.lilas.service.dto.BCCheckResponse bcCheckResponse) {
        LOG.debug("Request to create Order from BC response for mail: {}", savedMail.getId());

        try {
            // Create OrderDTO with BC data
            OrderDTO orderDTO = new OrderDTO();
            orderDTO.setMails(savedMail);
            orderDTO.setStatus(OrderStatus.WAITING);
            orderDTO.setLocked(false);
            orderDTO.setRank(1);
            orderDTO.setOrderDate(java.time.LocalDate.now());

            // Generate order number
            orderDTO.setOrderNumber(generateOrderNumber(savedMail));

            // Extract BC data directly - NO FALLBACKS
            if (bcCheckResponse != null) {
                orderDTO.setCompany(bcCheckResponse.getCompany());
                orderDTO.setClientName(bcCheckResponse.getCompany());
                orderDTO.setClientCode(bcCheckResponse.getTaxNumber() != null
                        ? "CLI-" + bcCheckResponse.getTaxNumber().replaceAll("[^A-Za-z0-9]", "")
                        : null);
                orderDTO.setDeliveryLocation(bcCheckResponse.getDeliveryAddress());
            }

            // Extract order details directly from BC table data
            Set<OrderDetailsDTO> orderDetails = extractOrderDetailsFromBC(bcCheckResponse);
            orderDTO.setOrderDetails(orderDetails);

            // Calculate totals from actual data
            orderDTO.setLineCount(orderDetails.size());
            orderDTO.setPacksNb(orderDetails.stream().mapToInt(d -> d.getPackNb() != null ? d.getPackNb() : 0).sum());
            orderDTO.setTotalQuantity(orderDetails.stream()
                    .mapToDouble(d -> d.getQuantity() != null ? d.getQuantity().doubleValue() : 0.0).sum());

            // Save using existing saveWithDetails method
            OrderDTO savedOrder = saveWithDetails(orderDTO);
            LOG.info("Created order ID: {} with {} details from BC for mail: {}",
                    savedOrder.getId(), orderDetails.size(), savedMail.getId());

            return savedOrder;

        } catch (Exception e) {
            LOG.error("Error creating order from BC for mail {}: {}", savedMail.getId(), e.getMessage(), e);
            return null;
        }
    }

    /**
     * Generate order number from mail ID
     */
    private String generateOrderNumber(MailsDTO savedMail) {
        return "BC-" + savedMail.getId() + "-" + System.currentTimeMillis();
    }

    /**
     * Extract order details directly from BC table data - OPTIMIZED & NO FALLBACKS
     */
    private Set<OrderDetailsDTO> extractOrderDetailsFromBC(com.dq.lilas.service.dto.BCCheckResponse bcCheckResponse) {
        Set<OrderDetailsDTO> orderDetails = new HashSet<>();

        if (bcCheckResponse == null || bcCheckResponse.getExtractedData() == null ||
                bcCheckResponse.getExtractedData().getTableInfo() == null) {
            return orderDetails;
        }

        Map<String, List<Map<String, String>>> tableInfo = bcCheckResponse.getExtractedData().getTableInfo();

        // Find table data using actual BC API keys
        List<Map<String, String>> items = null;
        for (String key : new String[] { "Page 1", "Page 2", "Page 3", "Page 4", "Page 5" }) {
            items = tableInfo.get(key);
            if (items != null && !items.isEmpty())
                break;
        }

        if (items != null) {
            for (Map<String, String> item : items) {
                OrderDetailsDTO detail = new OrderDetailsDTO();

                // Extract using exact BC API field names - NO FALLBACKS
                detail.setProductName(getValue(item, "EAN principal", "Libellé"));
                detail.setInternalCode(getValue(item, "N° article"));
                detail.setRef(getValue(item, "N° article"));

                // Parse numeric fields safely
                detail.setQuantity(parseDecimal(getValue(item, "Qté")));
                detail.setUnitPrice(parseDouble(getValue(item, "Prix achat en DT")));
                detail.setPcb(parseDouble(getValue(item, "PCB")));
                detail.setTva(parseDouble(getValue(item, "TVA")));
                detail.setPackNb(parseInt(getValue(item, "Nb colis")));

                // Set required fields
                detail.setAvailability(true);
                detail.setProductUnit("UNIT");

                orderDetails.add(detail);
            }
        }

        return orderDetails;
    }

    /**
     * Optimized helper methods for BC data extraction
     */
    private String getValue(Map<String, String> item, String... keys) {
        if (item == null)
            return null;
        for (String key : keys) {
            String value = item.get(key);
            if (value != null && !value.trim().isEmpty()) {
                return value.trim();
            }
        }
        return null;
    }

    private BigDecimal parseDecimal(String value) {
        if (value == null || value.trim().isEmpty())
            return null;
        try {
            return new BigDecimal(value.replaceAll("[^0-9.,]", "").replace(",", "."));
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Double parseDouble(String value) {
        if (value == null || value.trim().isEmpty())
            return null;
        try {
            return Double.parseDouble(value.replaceAll("[^0-9.,]", "").replace(",", "."));
        } catch (NumberFormatException e) {
            return null;
        }
    }

    private Integer parseInt(String value) {
        if (value == null || value.trim().isEmpty())
            return null;
        try {
            return Integer.parseInt(value.trim().split("\\.")[0]); // Handle decimal values
        } catch (NumberFormatException e) {
            return null;
        }
    }

    @Override
    public OrderDTO saveWithDetails(OrderDTO orderDTO) {
        LOG.debug("Request to save Order with details : {}", orderDTO);

        try {
            // Store a copy of the order details
            Set<OrderDetailsDTO> detailsDTOs = orderDTO.getOrderDetails() != null
                    ? new HashSet<>(orderDTO.getOrderDetails())
                    : new HashSet<>();

            // Clear order details to avoid circular reference during mapping
            orderDTO.setOrderDetails(new HashSet<>());

            // Ensure default values are set
            if (orderDTO.getStatus() == null) {
                orderDTO.setStatus(OrderStatus.WAITING); // Default status
            }

            if (orderDTO.getLocked() == null) {
                orderDTO.setLocked(false); // Default unlocked
            }

            if (orderDTO.getRank() == null) {
                orderDTO.setRank(1); // Default rank
            }

            // Save the order first
            Order order = orderMapper.toEntity(orderDTO);
            order = orderRepository.save(order);

            // Save order details with reference to the parent order
            if (!detailsDTOs.isEmpty()) {
                LOG.debug("Processing {} order details for order ID: {}", detailsDTOs.size(), order.getId());
                Set<OrderDetails> savedDetails = new HashSet<>();
                int detailIndex = 0;
                for (OrderDetailsDTO detailDTO : detailsDTOs) {
                    LOG.debug("Processing order detail {} of {}", ++detailIndex, detailsDTOs.size());
                    // Create new OrderDetails entity manually
                    OrderDetails detail = new OrderDetails();

                    // Set basic properties manually - only fields that exist in the database
                    detail.setQuantity(detailDTO.getQuantity() != null ? detailDTO.getQuantity().longValue() : null);
                    detail.setUpdatedQty(
                            detailDTO.getUpdatedQty() != null ? detailDTO.getUpdatedQty().longValue() : null);
                    detail.setDiscount(detailDTO.getDiscount());
                    detail.setUpdatedDiscount(detailDTO.getUpdatedDiscount());
                    detail.setUnitPrice(detailDTO.getUnitPrice());
                    detail.setUpdatedUnitPrice(detailDTO.getUpdatedUnitPrice());
                    detail.setOrderLineJson(detailDTO.getOrderLineJson());
                    detail.setAvailability(detailDTO.getAvailability());
                    // Note: validConditions and injected fields have been removed from the entity
                    detail.setBarcode(detailDTO.getBarcode());
                    detail.setUpdatedBarcode(detailDTO.getUpdatedBarcode());
                    detail.setInternalCode(detailDTO.getInternalCode());
                    detail.setUpdatedInternalCode(detailDTO.getUpdatedInternalCode());

                    // Set parent order manually - ensure each detail has the same parent
                    detail.setOrder(order);

                    // Save the detail
                    OrderDetails savedDetail = orderDetailsRepository.save(detail);
                    savedDetails.add(savedDetail);
                }

                // Add the saved details to the order entity
                order.setOrderDetails(savedDetails);
            }

            // Return the DTO with all data - use our own DTO construction to avoid mapper
            // circular references
            OrderDTO result = new OrderDTO();
            result.setId(order.getId());
            result.setHearderJson(order.getHearderJson());
            result.setRank(order.getRank());
            result.setStatus(order.getStatus());
            result.setLocked(order.getLocked());
            result.setOrderNumber(order.getOrderNumber());
            result.setFooterJson(order.getFooterJson());

            // Set basic references
            if (order.getDailyBatches() != null) {
                DailyBatchesDTO dailyBatchesDTO = new DailyBatchesDTO();
                dailyBatchesDTO.setId(order.getDailyBatches().getId());
                result.setDailyBatches(dailyBatchesDTO);
            }

            if (order.getGmsClients() != null) {
                GmsClientsDTO gmsClientsDTO = new GmsClientsDTO();
                gmsClientsDTO.setId(order.getGmsClients().getId());
                result.setGmsClients(gmsClientsDTO);
            }

            if (order.getTemplateConditions() != null) {
                TemplateConditionsDTO templateConditionsDTO = new TemplateConditionsDTO();
                templateConditionsDTO.setId(order.getTemplateConditions().getId());
                result.setTemplateConditions(templateConditionsDTO);
            }

            if (order.getMails() != null) {
                MailsDTO mailsDTO = new MailsDTO();
                mailsDTO.setId(order.getMails().getId());
                result.setMails(mailsDTO);
            }

            if (order.getEmployee() != null) {
                EmployeeDTO employeeDTO = new EmployeeDTO();
                employeeDTO.setId(order.getEmployee().getId());
                result.setEmployee(employeeDTO);
            }

            // Create a single order reference to be shared by all details
            OrderDTO orderRef = new OrderDTO();
            orderRef.setId(order.getId());
            orderRef.setOrderNumber(order.getOrderNumber());
            orderRef.setStatus(order.getStatus());

            // Add order details manually with the shared parent reference
            Set<OrderDetailsDTO> resultDetails = new HashSet<>();
            for (OrderDetails detail : order.getOrderDetails()) {
                OrderDetailsDTO detailDTO = new OrderDetailsDTO();
                detailDTO.setId(detail.getId());
                detailDTO.setQuantity(detail.getQuantity() != null ? BigDecimal.valueOf(detail.getQuantity()) : null);
                detailDTO.setUpdatedQty(
                        detail.getUpdatedQty() != null ? BigDecimal.valueOf(detail.getUpdatedQty()) : null);
                detailDTO.setDiscount(detail.getDiscount());
                detailDTO.setUpdatedDiscount(detail.getUpdatedDiscount());
                detailDTO.setUnitPrice(detail.getUnitPrice());
                detailDTO.setUpdatedUnitPrice(detail.getUpdatedUnitPrice());
                detailDTO.setOrderLineJson(detail.getOrderLineJson());
                detailDTO.setAvailability(detail.getAvailability());
                // Note: validConditions and injected fields have been removed from the entity
                detailDTO.setBarcode(detail.getBarcode());
                detailDTO.setUpdatedBarcode(detail.getUpdatedBarcode());
                detailDTO.setInternalCode(detail.getInternalCode());
                detailDTO.setUpdatedInternalCode(detail.getUpdatedInternalCode());

                // Set the shared order reference
                detailDTO.setOrder(orderRef);

                resultDetails.add(detailDTO);
            }

            result.setOrderDetails(resultDetails);

            return result;
        } catch (Exception e) {
            LOG.error("Error in saveWithDetails: {}", e.getMessage(), e);
            throw e;
        }
    }

    @Override
    public Optional<OrderDTO> findByBrandAndBatche(Long batcheId, Long brandId) {
        LOG.debug("Request to get Order by batchId: {}, brandId: {}", batcheId, brandId);
        return orderRepository.findByBrandAndBatche(batcheId, brandId)
                .map(orderMapper::toDto);
    }

    @Override
    public List<OrderDTO> findByBatcheAndBrands(Long batcheId, List<Long> brandIds) {
        LOG.debug("Request to get Order by batchId: {}, brandIds: {}", batcheId, brandIds);
        List<OrderDTO> result = new ArrayList<>();
        for (Long brandId : brandIds) {
            Optional<Order> orderOpt = orderRepository.findByBrandAndBatche(batcheId, brandId);
            orderOpt.map(orderMapper::toDto).ifPresent(result::add);
        }
        return result;
    }

    @Override
    @Transactional(readOnly = true)
    public ClientBrandInfoDTO getClientAndBrandByFiscalId(String fiscalId) {
        LOG.debug("Request to get client and brand info by fiscal ID: {}", fiscalId);

        try {
            Optional<GmsClients> clientOpt = gmsClientsRepository.findByFiscaleId(fiscalId);
            if (clientOpt.isPresent()) {
                GmsClients client = clientOpt.get();
                // For now, we'll return the client ID and a default brand ID (1)
                // This can be enhanced later if there's a direct relationship between clients
                // and brands
                Long defaultBrandId = 1L; // You may want to make this configurable or fetch from database

                // Try to get the first available brand if default doesn't exist
                Optional<GmsBrands> firstBrand = gmsBrandsRepository.findAll().stream().findFirst();
                if (firstBrand.isPresent()) {
                    defaultBrandId = firstBrand.get().getId();
                }

                return new ClientBrandInfoDTO(client.getId(), defaultBrandId);
            }

            // Return null if client not found
            return null;
        } catch (Exception e) {
            LOG.error("Error finding client by fiscal ID '{}': {}", fiscalId, e.getMessage());
            throw new RuntimeException(
                    "Failed to find unique client with fiscal ID: " + fiscalId + ". " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public BatchOrderInfoDTO getNextOrderNumber(Long companyId) {
        LOG.debug("Request to get next order number for company ID: {}", companyId);

        try {
            LocalDate today = LocalDate.now();
            Optional<DailyBatches> batchOpt = dailyBatchesRepository.findByCompanyIdAndBatchDate(companyId, today);

            if (batchOpt.isPresent()) {
                DailyBatches batch = batchOpt.get();
                return new BatchOrderInfoDTO(batch.getId(), batch.getOrderNb() + 1);
            } else {
                // Create a new batch for today if it doesn't exist
                DailyBatches newBatch = new DailyBatches();
                newBatch.setBatchDate(today.atStartOfDay(ZoneId.systemDefault()).toInstant());
                newBatch.setOrderNb(0);

                // Set the company relationship
                Company company = new Company();
                company.setId(companyId);
                newBatch.setCompany(company);

                DailyBatches savedBatch = dailyBatchesRepository.save(newBatch);
                return new BatchOrderInfoDTO(savedBatch.getId(), 1);
            }
        } catch (Exception e) {
            LOG.error("Error getting next order number for company ID '{}': {}", companyId, e.getMessage());
            throw new RuntimeException(
                    "Failed to get unique batch for company ID: " + companyId + ". " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional
    public void incrementOrderNumber(Long batchId) {
        LOG.debug("Request to increment order number for batch ID: {}", batchId);

        Optional<DailyBatches> batchOpt = dailyBatchesRepository.findById(batchId);
        if (batchOpt.isPresent()) {
            DailyBatches batch = batchOpt.get();
            int newOrderNb = batch.getOrderNb() + 1;
            dailyBatchesRepository.updateOrderNumber(batchId, newOrderNb);
        } else {
            throw new RuntimeException("Daily batch not found with ID: " + batchId);
        }
    }

    @Override
    @Transactional
    public OrderDTO updateWithDetails(OrderDTO orderDTO) {
        LOG.debug("Request to update Order with details : {}", orderDTO);

        if (orderDTO.getId() == null) {
            throw new IllegalArgumentException("Order ID cannot be null for update operation");
        }

        try {
            // Verify the order exists
            Optional<Order> existingOrderOpt = orderRepository.findById(orderDTO.getId());
            if (existingOrderOpt.isEmpty()) {
                throw new RuntimeException("Order not found with ID: " + orderDTO.getId());
            }

            Order existingOrder = existingOrderOpt.get();

            // Store a copy of the new order details
            Set<OrderDetailsDTO> newDetailsDTOs = orderDTO.getOrderDetails() != null
                    ? new HashSet<>(orderDTO.getOrderDetails())
                    : new HashSet<>();

            // Clear order details to avoid circular reference during mapping
            orderDTO.setOrderDetails(new HashSet<>());

            // Update the order first using partial update to preserve existing data
            orderMapper.partialUpdate(existingOrder, orderDTO);
            Order updatedOrder = orderRepository.save(existingOrder);

            // Handle order details updates
            updateOrderDetailsForOrder(updatedOrder, newDetailsDTOs);

            // Reload the order with updated details
            updatedOrder = orderRepository.findById(updatedOrder.getId()).orElse(updatedOrder);

            // Convert to DTO with proper circular reference handling
            OrderDTO result = orderMapper.toDto(updatedOrder);

            // Manually add order details to avoid circular reference issues
            if (updatedOrder.getOrderDetails() != null && !updatedOrder.getOrderDetails().isEmpty()) {
                Set<OrderDetailsDTO> resultDetails = new HashSet<>();

                // Create a shared order reference for all details
                OrderDTO orderRef = new OrderDTO();
                orderRef.setId(result.getId());
                orderRef.setOrderNumber(result.getOrderNumber());
                orderRef.setStatus(result.getStatus());

                for (OrderDetails detail : updatedOrder.getOrderDetails()) {
                    OrderDetailsDTO detailDTO = createOrderDetailDTO(detail);
                    detailDTO.setOrder(orderRef);
                    resultDetails.add(detailDTO);
                }

                result.setOrderDetails(resultDetails);
            }

            LOG.info("Successfully updated order with ID: {} and {} details",
                    result.getId(), result.getOrderDetails() != null ? result.getOrderDetails().size() : 0);

            return result;

        } catch (Exception e) {
            LOG.error("Error in updateWithDetails for order {}: {}", orderDTO.getId(), e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional
    public OrderDTO bulkUpdateOrderDetails(Long orderId, Set<OrderDetailsDTO> orderDetailsSet) {
        LOG.debug("Request to bulk update order details for order ID: {}", orderId);

        if (orderId == null) {
            throw new IllegalArgumentException("Order ID cannot be null");
        }

        try {
            // Verify the order exists
            Optional<Order> existingOrderOpt = orderRepository.findById(orderId);
            if (existingOrderOpt.isEmpty()) {
                throw new RuntimeException("Order not found with ID: " + orderId);
            }

            Order existingOrder = existingOrderOpt.get();

            // Update order details
            updateOrderDetailsForOrder(existingOrder, orderDetailsSet != null ? orderDetailsSet : new HashSet<>());

            // Reload the order with updated details
            Order updatedOrder = orderRepository.findById(orderId).orElse(existingOrder);

            // Convert to DTO with proper circular reference handling
            OrderDTO result = orderMapper.toDto(updatedOrder);

            // Manually add order details to avoid circular reference issues
            if (updatedOrder.getOrderDetails() != null && !updatedOrder.getOrderDetails().isEmpty()) {
                Set<OrderDetailsDTO> resultDetails = new HashSet<>();

                // Create a shared order reference for all details
                OrderDTO orderRef = new OrderDTO();
                orderRef.setId(result.getId());
                orderRef.setOrderNumber(result.getOrderNumber());
                orderRef.setStatus(result.getStatus());

                for (OrderDetails detail : updatedOrder.getOrderDetails()) {
                    OrderDetailsDTO detailDTO = createOrderDetailDTO(detail);
                    detailDTO.setOrder(orderRef);
                    resultDetails.add(detailDTO);
                }

                result.setOrderDetails(resultDetails);
            }

            LOG.info("Successfully bulk updated {} order details for order ID: {}",
                    result.getOrderDetails() != null ? result.getOrderDetails().size() : 0, orderId);

            return result;

        } catch (Exception e) {
            LOG.error("Error in bulkUpdateOrderDetails for order {}: {}", orderId, e.getMessage(), e);
            throw e;
        }
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<OrderDTO> findOneWithDetails(Long id) {
        LOG.debug("Request to get Order with details : {}", id);

        if (id == null) {
            LOG.warn("Cannot find order: ID is null");
            return Optional.empty();
        }

        try {
            // Find the order with its details
            Optional<Order> orderOpt = orderRepository.findById(id);

            if (orderOpt.isEmpty()) {
                LOG.debug("Order not found with ID: {}", id);
                return Optional.empty();
            }

            Order order = orderOpt.get();

            // Convert to DTO with proper circular reference handling
            OrderDTO orderDTO = orderMapper.toDto(order);

            // Manually handle order details to avoid circular reference issues
            if (order.getOrderDetails() != null && !order.getOrderDetails().isEmpty()) {
                Set<OrderDetailsDTO> orderDetailsDTOs = new HashSet<>();

                // Create a shared order reference for all details
                OrderDTO orderRef = new OrderDTO();
                orderRef.setId(orderDTO.getId());
                orderRef.setOrderNumber(orderDTO.getOrderNumber());
                orderRef.setStatus(orderDTO.getStatus());
                orderRef.setCompany(orderDTO.getCompany());
                orderRef.setOrderDate(orderDTO.getOrderDate());

                for (OrderDetails detail : order.getOrderDetails()) {
                    OrderDetailsDTO detailDTO = createOrderDetailDTO(detail);
                    detailDTO.setOrder(orderRef);
                    orderDetailsDTOs.add(detailDTO);
                }

                orderDTO.setOrderDetails(orderDetailsDTOs);

                LOG.debug("Retrieved order {} with {} details", id, orderDetailsDTOs.size());
            } else {
                LOG.debug("Retrieved order {} with no details", id);
                orderDTO.setOrderDetails(new HashSet<>());
            }

            return Optional.of(orderDTO);

        } catch (Exception e) {
            LOG.error("Error retrieving order with details for ID {}: {}", id, e.getMessage(), e);
            return Optional.empty();
        }
    }

    /**
     * Helper method to update order details for a given order.
     * Handles adding new details, updating existing ones, and removing details not
     * in the new set.
     */
    private void updateOrderDetailsForOrder(Order order, Set<OrderDetailsDTO> newDetailsDTOs) {
        LOG.debug("Updating order details for order ID: {}", order.getId());

        // Get existing order details
        Set<OrderDetails> existingDetails = order.getOrderDetails() != null
                ? new HashSet<>(order.getOrderDetails())
                : new HashSet<>();

        // Create maps for efficient lookup
        Map<Long, OrderDetails> existingDetailsMap = existingDetails.stream()
                .filter(detail -> detail.getId() != null)
                .collect(Collectors.toMap(OrderDetails::getId, detail -> detail));

        Map<Long, OrderDetailsDTO> newDetailsMap = newDetailsDTOs.stream()
                .filter(detail -> detail.getId() != null)
                .collect(Collectors.toMap(OrderDetailsDTO::getId, detail -> detail));

        // Process updates and additions
        Set<OrderDetails> updatedDetails = new HashSet<>();

        for (OrderDetailsDTO newDetailDTO : newDetailsDTOs) {
            if (newDetailDTO.getId() != null && existingDetailsMap.containsKey(newDetailDTO.getId())) {
                // Update existing detail
                OrderDetails existingDetail = existingDetailsMap.get(newDetailDTO.getId());
                updateOrderDetailEntity(existingDetail, newDetailDTO);
                existingDetail.setOrder(order);
                updatedDetails.add(orderDetailsRepository.save(existingDetail));
                LOG.debug("Updated existing order detail with ID: {}", existingDetail.getId());
            } else {
                // Add new detail
                OrderDetails newDetail = createOrderDetailEntity(newDetailDTO);
                newDetail.setOrder(order);
                updatedDetails.add(orderDetailsRepository.save(newDetail));
                LOG.debug("Added new order detail for order ID: {}", order.getId());
            }
        }

        // Remove details that are not in the new set
        for (OrderDetails existingDetail : existingDetails) {
            if (existingDetail.getId() != null && !newDetailsMap.containsKey(existingDetail.getId())) {
                orderDetailsRepository.deleteById(existingDetail.getId());
                LOG.debug("Removed order detail with ID: {}", existingDetail.getId());
            }
        }

        // Update the order's details collection
        order.setOrderDetails(updatedDetails);
    }

    /**
     * Helper method to create OrderDetails entity from DTO
     */
    private OrderDetails createOrderDetailEntity(OrderDetailsDTO detailDTO) {
        OrderDetails detail = new OrderDetails();
        updateOrderDetailEntity(detail, detailDTO);
        return detail;
    }

    /**
     * Helper method to update OrderDetails entity from DTO
     */
    private void updateOrderDetailEntity(OrderDetails detail, OrderDetailsDTO detailDTO) {
        LOG.debug("=== Mapping OrderDetailsDTO to OrderDetails Entity ===");
        LOG.debug("DTO Input - ID: {}, ProductName: {}, InternalCode: {}, Ref: {}",
                detailDTO.getId(), detailDTO.getProductName(), detailDTO.getInternalCode(), detailDTO.getRef());
        LOG.debug("DTO Input - Quantity: {}, UnitPrice: {}, PackNb: {}, ProductUnit: {}",
                detailDTO.getQuantity(), detailDTO.getUnitPrice(), detailDTO.getPackNb(), detailDTO.getProductUnit());
        LOG.debug("DTO Input - Availability: {}, Tva: {}", detailDTO.getAvailability(), detailDTO.getTva());

        // Set basic properties manually - only fields that exist in the database
        // CRITICAL: Handle BigDecimal to Long conversion properly
        if (detailDTO.getQuantity() != null) {
            try {
                detail.setQuantity(detailDTO.getQuantity().longValue());
                LOG.debug("Mapped quantity: {} -> {}", detailDTO.getQuantity(), detail.getQuantity());
            } catch (Exception e) {
                LOG.error("Error converting quantity {} to Long: {}", detailDTO.getQuantity(), e.getMessage());
                detail.setQuantity(1L); // Safe fallback
            }
        } else {
            LOG.warn("DTO quantity is null, setting entity quantity to null");
            detail.setQuantity(null);
        }

        if (detailDTO.getUpdatedQty() != null) {
            try {
                detail.setUpdatedQty(detailDTO.getUpdatedQty().longValue());
            } catch (Exception e) {
                LOG.error("Error converting updatedQty {} to Long: {}", detailDTO.getUpdatedQty(), e.getMessage());
                detail.setUpdatedQty(null);
            }
        } else {
            detail.setUpdatedQty(null);
        }

        detail.setDiscount(detailDTO.getDiscount());
        detail.setUpdatedDiscount(detailDTO.getUpdatedDiscount());
        detail.setUnitPrice(detailDTO.getUnitPrice());
        detail.setUpdatedUnitPrice(detailDTO.getUpdatedUnitPrice());
        detail.setOrderLineJson(detailDTO.getOrderLineJson());
        detail.setAvailability(detailDTO.getAvailability());
        detail.setBarcode(detailDTO.getBarcode());
        detail.setUpdatedBarcode(detailDTO.getUpdatedBarcode());
        detail.setInternalCode(detailDTO.getInternalCode());
        detail.setUpdatedInternalCode(detailDTO.getUpdatedInternalCode());

        // Set additional fields with logging
        detail.setRef(detailDTO.getRef());
        detail.setProductName(detailDTO.getProductName());
        detail.setPackNb(detailDTO.getPackNb());
        detail.setPcb(detailDTO.getPcb());
        detail.setProductUnit(detailDTO.getProductUnit());
        detail.setTva(detailDTO.getTva());
        detail.setTypeUc(detailDTO.getTypeUc());
        detail.setUvcUc(detailDTO.getUvcUc());
        detail.setUnit(detailDTO.getUnit());

        // Set status if provided
        if (detailDTO.getStatus() != null) {
            detail.setStatus(detailDTO.getStatus());
        }

        LOG.debug("Entity Output - ID: {}, ProductName: {}, InternalCode: {}, Ref: {}",
                detail.getId(), detail.getProductName(), detail.getInternalCode(), detail.getRef());
        LOG.debug("Entity Output - Quantity: {}, UnitPrice: {}, PackNb: {}, ProductUnit: {}",
                detail.getQuantity(), detail.getUnitPrice(), detail.getPackNb(), detail.getProductUnit());
        LOG.debug("Entity Output - Availability: {}, Tva: {}", detail.getAvailability(), detail.getTva());
        LOG.debug("=== End Mapping OrderDetailsDTO to Entity ===");
    }

    /**
     * Helper method to create OrderDetailsDTO from entity
     */
    private OrderDetailsDTO createOrderDetailDTO(OrderDetails detail) {
        OrderDetailsDTO detailDTO = new OrderDetailsDTO();
        detailDTO.setId(detail.getId());
        detailDTO.setQuantity(detail.getQuantity() != null ? BigDecimal.valueOf(detail.getQuantity()) : null);
        detailDTO.setUpdatedQty(detail.getUpdatedQty() != null ? BigDecimal.valueOf(detail.getUpdatedQty()) : null);
        detailDTO.setDiscount(detail.getDiscount());
        detailDTO.setUpdatedDiscount(detail.getUpdatedDiscount());
        detailDTO.setUnitPrice(detail.getUnitPrice());
        detailDTO.setUpdatedUnitPrice(detail.getUpdatedUnitPrice());
        detailDTO.setOrderLineJson(detail.getOrderLineJson());
        detailDTO.setAvailability(detail.getAvailability());
        detailDTO.setBarcode(detail.getBarcode());
        detailDTO.setUpdatedBarcode(detail.getUpdatedBarcode());
        detailDTO.setInternalCode(detail.getInternalCode());
        detailDTO.setUpdatedInternalCode(detail.getUpdatedInternalCode());
        detailDTO.setRef(detail.getRef());
        detailDTO.setProductName(detail.getProductName());
        detailDTO.setPackNb(detail.getPackNb());
        detailDTO.setPcb(detail.getPcb());
        detailDTO.setProductUnit(detail.getProductUnit());
        detailDTO.setTva(detail.getTva());
        detailDTO.setTypeUc(detail.getTypeUc());
        detailDTO.setUvcUc(detail.getUvcUc());
        detailDTO.setUnit(detail.getUnit());
        detailDTO.setStatus(detail.getStatus());

        // Note: Order reference will be set by the calling method to avoid circular
        // references
        return detailDTO;
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<Long> getCompanyIdByName(String companyName) {
        LOG.debug("Request to get company ID by name: {}", companyName);

        try {
            Optional<Company> companyOpt = companyRepository.findByComapanyNameIgnoreCase(companyName);
            return companyOpt.map(Company::getId);
        } catch (Exception e) {
            LOG.error("Error finding company by name '{}': {}", companyName, e.getMessage());
            throw new RuntimeException(
                    "Failed to find unique company with name: " + companyName + ". " + e.getMessage(), e);
        }
    }

    @Override
    @Transactional(readOnly = true)
    public List<OrderDTO> getOrdersByBrandId(Long brandId) {
        LOG.debug("Request to get Orders by brand ID: {}", brandId);

        List<Order> orders = orderRepository.findOrdersByBrandId(brandId);
        return orders.stream()
                .map(orderMapper::toDto)
                .collect(Collectors.toList());
    }

    @Override
    @Transactional(readOnly = true)
    public List<OrderDTO> getOrdersByCompany(String company) {
        LOG.debug("Request to get Orders by company: {}", company);

        List<Order> orders = orderRepository.findOrdersByCompany(company);
        return orders.stream()
                .map(orderMapper::toDto)
                .collect(Collectors.toList());
    }
}
