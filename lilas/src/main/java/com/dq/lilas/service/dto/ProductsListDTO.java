package com.dq.lilas.service.dto;

import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.ProductsList} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ProductsListDTO implements Serializable {

    private Long id;

    private String internalCode;

    private String barcode;

    private String qadName;

    private String category;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getInternalCode() {
        return internalCode;
    }

    public void setInternalCode(String internalCode) {
        this.internalCode = internalCode;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getQadName() {
        return qadName;
    }

    public void setQadName(String qadName) {
        this.qadName = qadName;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ProductsListDTO)) {
            return false;
        }

        ProductsListDTO productsListDTO = (ProductsListDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, productsListDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ProductsListDTO{" +
            "id=" + getId() +
            ", internalCode='" + getInternalCode() + "'" +
            ", barcode='" + getBarcode() + "'" +
            ", qadName='" + getQadName() + "'" +
            ", category='" + getCategory() + "'" +
            "}";
    }
}
