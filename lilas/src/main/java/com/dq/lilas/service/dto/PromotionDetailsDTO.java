package com.dq.lilas.service.dto;

import com.dq.lilas.domain.enumeration.PromotionStatus;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.PromotionDetails} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class PromotionDetailsDTO implements Serializable {

    private Long id;

    private String codeProduit;

    private String description;

    private Double priceHT;

    private Double remiseFixe;

    private Double remiseDePro;

    private Double gratuitEnNat;

    private String approManagerDuMagasin;

    private String approManagerGMS;

    private PromotionStatus status;

    private DemandePromotionDTO demandePromotion;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCodeProduit() {
        return codeProduit;
    }

    public void setCodeProduit(String codeProduit) {
        this.codeProduit = codeProduit;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public Double getPriceHT() {
        return priceHT;
    }

    public void setPriceHT(Double priceHT) {
        this.priceHT = priceHT;
    }

    public Double getRemiseFixe() {
        return remiseFixe;
    }

    public void setRemiseFixe(Double remiseFixe) {
        this.remiseFixe = remiseFixe;
    }

    public Double getRemiseDePro() {
        return remiseDePro;
    }

    public void setRemiseDePro(Double remiseDePro) {
        this.remiseDePro = remiseDePro;
    }

    public Double getGratuitEnNat() {
        return gratuitEnNat;
    }

    public void setGratuitEnNat(Double gratuitEnNat) {
        this.gratuitEnNat = gratuitEnNat;
    }

    public String getApproManagerDuMagasin() {
        return approManagerDuMagasin;
    }

    public void setApproManagerDuMagasin(String approManagerDuMagasin) {
        this.approManagerDuMagasin = approManagerDuMagasin;
    }

    public String getApproManagerGMS() {
        return approManagerGMS;
    }

    public void setApproManagerGMS(String approManagerGMS) {
        this.approManagerGMS = approManagerGMS;
    }

    public PromotionStatus getStatus() {
        return status;
    }

    public void setStatus(PromotionStatus status) {
        this.status = status;
    }

    public DemandePromotionDTO getDemandePromotion() {
        return demandePromotion;
    }

    public void setDemandePromotion(DemandePromotionDTO demandePromotion) {
        this.demandePromotion = demandePromotion;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof PromotionDetailsDTO)) {
            return false;
        }

        PromotionDetailsDTO promotionDetailsDTO = (PromotionDetailsDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, promotionDetailsDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "PromotionDetailsDTO{" +
            "id=" + getId() +
            ", codeProduit='" + getCodeProduit() + "'" +
            ", description='" + getDescription() + "'" +
            ", priceHT=" + getPriceHT() +
            ", remiseFixe=" + getRemiseFixe() +
            ", remiseDePro=" + getRemiseDePro() +
            ", gratuitEnNat=" + getGratuitEnNat() +
            ", approManagerDuMagasin='" + getApproManagerDuMagasin() + "'" +
            ", approManagerGMS='" + getApproManagerGMS() + "'" +
            "}";
    }
}
