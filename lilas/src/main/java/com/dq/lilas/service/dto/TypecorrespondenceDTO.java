package com.dq.lilas.service.dto;

import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Typecorrespondence} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class TypecorrespondenceDTO implements Serializable {

    private Long id;

    private String typecorresp;

    private String typedecision;

    private String hosp;

    private String lbl;

    private String statut;

    private String abbreviated;

    private String speech;

    private Long nbrnotifbeforerec;

    private Long nbrnotifafterrec;

    private String archiveSubjectsId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTypecorresp() {
        return typecorresp;
    }

    public void setTypecorresp(String typecorresp) {
        this.typecorresp = typecorresp;
    }

    public String getTypedecision() {
        return typedecision;
    }

    public void setTypedecision(String typedecision) {
        this.typedecision = typedecision;
    }

    public String getHosp() {
        return hosp;
    }

    public void setHosp(String hosp) {
        this.hosp = hosp;
    }

    public String getLbl() {
        return lbl;
    }

    public void setLbl(String lbl) {
        this.lbl = lbl;
    }

    public String getStatut() {
        return statut;
    }

    public void setStatut(String statut) {
        this.statut = statut;
    }

    public String getAbbreviated() {
        return abbreviated;
    }

    public void setAbbreviated(String abbreviated) {
        this.abbreviated = abbreviated;
    }

    public String getSpeech() {
        return speech;
    }

    public void setSpeech(String speech) {
        this.speech = speech;
    }

    public Long getNbrnotifbeforerec() {
        return nbrnotifbeforerec;
    }

    public void setNbrnotifbeforerec(Long nbrnotifbeforerec) {
        this.nbrnotifbeforerec = nbrnotifbeforerec;
    }

    public Long getNbrnotifafterrec() {
        return nbrnotifafterrec;
    }

    public void setNbrnotifafterrec(Long nbrnotifafterrec) {
        this.nbrnotifafterrec = nbrnotifafterrec;
    }

    public String getArchiveSubjectsId() {
        return archiveSubjectsId;
    }

    public void setArchiveSubjectsId(String archiveSubjectsId) {
        this.archiveSubjectsId = archiveSubjectsId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TypecorrespondenceDTO)) {
            return false;
        }

        TypecorrespondenceDTO typecorrespondenceDTO = (TypecorrespondenceDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, typecorrespondenceDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "TypecorrespondenceDTO{" +
            "id=" + getId() +
            ", typecorresp='" + getTypecorresp() + "'" +
            ", typedecision='" + getTypedecision() + "'" +
            ", hosp='" + getHosp() + "'" +
            ", lbl='" + getLbl() + "'" +
            ", statut='" + getStatut() + "'" +
            ", abbreviated='" + getAbbreviated() + "'" +
            ", speech='" + getSpeech() + "'" +
            ", nbrnotifbeforerec=" + getNbrnotifbeforerec() +
            ", nbrnotifafterrec=" + getNbrnotifafterrec() +
            ", archiveSubjectsId='" + getArchiveSubjectsId() + "'" +
            "}";
    }
}
