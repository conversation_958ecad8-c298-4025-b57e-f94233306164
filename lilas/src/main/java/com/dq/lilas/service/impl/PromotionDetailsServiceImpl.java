package com.dq.lilas.service.impl;

import com.dq.lilas.domain.PromotionDetails;
import com.dq.lilas.repository.PromotionDetailsRepository;
import com.dq.lilas.service.PromotionDetailsService;
import com.dq.lilas.service.dto.PromotionDetailsDTO;
import com.dq.lilas.service.mapper.PromotionDetailsMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.PromotionDetails}.
 */
@Service
@Transactional
public class PromotionDetailsServiceImpl implements PromotionDetailsService {

    private static final Logger LOG = LoggerFactory.getLogger(PromotionDetailsServiceImpl.class);

    private final PromotionDetailsRepository promotionDetailsRepository;

    private final PromotionDetailsMapper promotionDetailsMapper;

    public PromotionDetailsServiceImpl(
        PromotionDetailsRepository promotionDetailsRepository,
        PromotionDetailsMapper promotionDetailsMapper
    ) {
        this.promotionDetailsRepository = promotionDetailsRepository;
        this.promotionDetailsMapper = promotionDetailsMapper;
    }

    @Override
    public PromotionDetailsDTO save(PromotionDetailsDTO promotionDetailsDTO) {
        LOG.debug("Request to save PromotionDetails : {}", promotionDetailsDTO);
        PromotionDetails promotionDetails = promotionDetailsMapper.toEntity(promotionDetailsDTO);
        promotionDetails = promotionDetailsRepository.save(promotionDetails);
        return promotionDetailsMapper.toDto(promotionDetails);
    }

    @Override
    public PromotionDetailsDTO update(PromotionDetailsDTO promotionDetailsDTO) {
        LOG.debug("Request to update PromotionDetails : {}", promotionDetailsDTO);
        PromotionDetails promotionDetails = promotionDetailsMapper.toEntity(promotionDetailsDTO);
        promotionDetails = promotionDetailsRepository.save(promotionDetails);
        return promotionDetailsMapper.toDto(promotionDetails);
    }

    @Override
    public Optional<PromotionDetailsDTO> partialUpdate(PromotionDetailsDTO promotionDetailsDTO) {
        LOG.debug("Request to partially update PromotionDetails : {}", promotionDetailsDTO);

        return promotionDetailsRepository
            .findById(promotionDetailsDTO.getId())
            .map(existingPromotionDetails -> {
                promotionDetailsMapper.partialUpdate(existingPromotionDetails, promotionDetailsDTO);

                return existingPromotionDetails;
            })
            .map(promotionDetailsRepository::save)
            .map(promotionDetailsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<PromotionDetailsDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all PromotionDetails");
        return promotionDetailsRepository.findAll(pageable).map(promotionDetailsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<PromotionDetailsDTO> findOne(Long id) {
        LOG.debug("Request to get PromotionDetails : {}", id);
        return promotionDetailsRepository.findById(id).map(promotionDetailsMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete PromotionDetails : {}", id);
        
        try {
            // Find the entity first to properly handle the bidirectional relationship
            Optional<PromotionDetails> promotionDetailsOptional = promotionDetailsRepository.findById(id);
            
            if (promotionDetailsOptional.isPresent()) {
                PromotionDetails promotionDetails = promotionDetailsOptional.get();
                
                // Remove the relationship from the parent entity to avoid orphaned references
                if (promotionDetails.getDemandePromotion() != null) {
                    promotionDetails.getDemandePromotion().getPromotionDetails().remove(promotionDetails);
                    promotionDetails.setDemandePromotion(null);
                }
                
                // Now safely delete the entity
                promotionDetailsRepository.delete(promotionDetails);
                LOG.debug("Successfully deleted PromotionDetails with id: {}", id);
            } else {
                LOG.warn("PromotionDetails with id {} not found for deletion", id);
            }
        } catch (Exception e) {
            LOG.error("Error deleting PromotionDetails with id {}: {}", id, e.getMessage());
            throw e;
        }
    }
}
