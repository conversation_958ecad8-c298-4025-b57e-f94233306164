package com.dq.lilas.service;

import com.dq.lilas.service.dto.DemandePromotionDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.DemandePromotion}.
 */
public interface DemandePromotionService {
    /**
     * Save a demandePromotion.
     *
     * @param demandePromotionDTO the entity to save.
     * @return the persisted entity.
     */
    DemandePromotionDTO save(DemandePromotionDTO demandePromotionDTO);

    /**
     * Updates a demandePromotion.
     *
     * @param demandePromotionDTO the entity to update.
     * @return the persisted entity.
     */
    DemandePromotionDTO update(DemandePromotionDTO demandePromotionDTO);

    /**
     * Partially updates a demandePromotion.
     *
     * @param demandePromotionDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<DemandePromotionDTO> partialUpdate(DemandePromotionDTO demandePromotionDTO);

    /**
     * Get all the demandePromotions.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<DemandePromotionDTO> findAll(Pageable pageable);

    /**
     * Get all the demandePromotions for the current user.
     *
     * @param pageable the pagination information.
     * @return the list of entities for the current user.
     */
    Page<DemandePromotionDTO> findAllForCurrentUser(Pageable pageable);

    /**
     * Get the "id" demandePromotion.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<DemandePromotionDTO> findOne(Long id);

    /**
     * Delete the "id" demandePromotion.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
