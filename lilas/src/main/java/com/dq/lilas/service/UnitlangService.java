package com.dq.lilas.service;

import com.dq.lilas.service.dto.UnitlangDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Unitlang}.
 */
public interface UnitlangService {
    /**
     * Save a unitlang.
     *
     * @param unitlangDTO the entity to save.
     * @return the persisted entity.
     */
    UnitlangDTO save(UnitlangDTO unitlangDTO);

    /**
     * Updates a unitlang.
     *
     * @param unitlangDTO the entity to update.
     * @return the persisted entity.
     */
    UnitlangDTO update(UnitlangDTO unitlangDTO);

    /**
     * Partially updates a unitlang.
     *
     * @param unitlangDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<UnitlangDTO> partialUpdate(UnitlangDTO unitlangDTO);

    /**
     * Get all the unitlangs.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<UnitlangDTO> findAll(Pageable pageable);

    /**
     * Get the "id" unitlang.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<UnitlangDTO> findOne(Long id);

    /**
     * Delete the "id" unitlang.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
