package com.dq.lilas.service;

import com.dq.lilas.domain.DailyBatches;
import com.dq.lilas.service.dto.DailyBatchesDTO;

import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.DailyBatches}.
 */
public interface DailyBatchesService {
    /**
     * Save a dailyBatches.
     *
     * @param dailyBatchesDTO the entity to save.
     * @return the persisted entity.
     */
    DailyBatchesDTO save(DailyBatchesDTO dailyBatchesDTO);

    /**
     * Updates a dailyBatches.
     *
     * @param dailyBatchesDTO the entity to update.
     * @return the persisted entity.
     */
    DailyBatchesDTO update(DailyBatchesDTO dailyBatchesDTO);

    /**
     * Partially updates a dailyBatches.
     *
     * @param dailyBatchesDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<DailyBatchesDTO> partialUpdate(DailyBatchesDTO dailyBatchesDTO);

    /**
     * Get all the dailyBatches.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<DailyBatchesDTO> findAll(Pageable pageable);

    /**
     * Get the "id" dailyBatches.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<DailyBatchesDTO> findOne(Long id);

    /**
     * Delete the "id" dailyBatches.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);

    /**
     * Get the dailyBatches by batchDate.
     *
     * @param batchDate the batchDate of the entities.
     * @return the list of entities.
     */
    List<DailyBatchesDTO> findByBatchDate(String batchDate);

    /**
     * Find all daily batches by company ID.
     *
     * @param companyIds the ID of the company to filter batches.
     * @return the entity.
     */
    List<DailyBatches> findBatchesByCompanies(List<Long> companyIds);

}
