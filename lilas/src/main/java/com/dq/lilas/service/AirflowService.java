package com.dq.lilas.service;

import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.Base64;

@Service
@RequiredArgsConstructor
public class AirflowService {

    private final RestTemplate restTemplate;

    @Value("${airflow.url}")
    String airflowUrl;

    @Value("${airflow.username}")
    String airflowUsername;

    @Value("${airflow.password}")
    String airflowPassword;

    public String triggerAirflowDag(String dag_id) {
        // Set up Basic Authentication
        String auth = String.format("%s:%s", airflowUsername, airflowPassword);
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
        HttpHeaders headers = new HttpHeaders();
        headers.set("Authorization", "Basic " + encodedAuth);
        headers.set("Content-Type", "application/json");

        // Request body for triggering DAG run
        String requestBody = "{}";

        HttpEntity<String> request = new HttpEntity<>(requestBody, headers);

        try {
            // Make POST request to trigger DAG
            restTemplate.exchange(
                String.format("%s/dags/%s/dagRuns", airflowUrl, dag_id),
                HttpMethod.POST,
                request,
                String.class
            );
            return "SUCCESS";
        } catch (Exception e) {
            return "FAILED";
        }
    }
}
