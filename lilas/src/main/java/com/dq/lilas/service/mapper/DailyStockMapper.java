package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.DailyStock;
import com.dq.lilas.domain.ProductsList;
import com.dq.lilas.service.dto.DailyStockDTO;
import com.dq.lilas.service.dto.ProductsListDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link DailyStock} and its DTO {@link DailyStockDTO}.
 */
@Mapper(componentModel = "spring")
public interface DailyStockMapper extends EntityMapper<DailyStockDTO, DailyStock> {
    @Mapping(target = "productsList", source = "productsList", qualifiedByName = "productsListId")
    DailyStockDTO toDto(DailyStock s);

    @Named("productsListId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    ProductsListDTO toDtoProductsListId(ProductsList productsList);
}
