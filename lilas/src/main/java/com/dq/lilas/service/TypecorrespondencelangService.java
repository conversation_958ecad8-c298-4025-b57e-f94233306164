package com.dq.lilas.service;

import com.dq.lilas.service.dto.TypecorrespondencelangDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Typecorrespondencelang}.
 */
public interface TypecorrespondencelangService {
    /**
     * Save a typecorrespondencelang.
     *
     * @param typecorrespondencelangDTO the entity to save.
     * @return the persisted entity.
     */
    TypecorrespondencelangDTO save(TypecorrespondencelangDTO typecorrespondencelangDTO);

    /**
     * Updates a typecorrespondencelang.
     *
     * @param typecorrespondencelangDTO the entity to update.
     * @return the persisted entity.
     */
    TypecorrespondencelangDTO update(TypecorrespondencelangDTO typecorrespondencelangDTO);

    /**
     * Partially updates a typecorrespondencelang.
     *
     * @param typecorrespondencelangDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<TypecorrespondencelangDTO> partialUpdate(TypecorrespondencelangDTO typecorrespondencelangDTO);

    /**
     * Get all the typecorrespondencelangs.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<TypecorrespondencelangDTO> findAll(Pageable pageable);

    /**
     * Get the "id" typecorrespondencelang.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<TypecorrespondencelangDTO> findOne(Long id);

    /**
     * Delete the "id" typecorrespondencelang.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
