package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.GmsClients} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class GmsClientsDTO implements Serializable {

    private Long id;

    private String clientName;

    private String code;

    private String classe;

    private String ville;

    private Instant creationDate;

    private Instant updateDate;

    private String fiscaleId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getClasse() {
        return classe;
    }

    public void setClasse(String classe) {
        this.classe = classe;
    }

    public String getVille() {
        return ville;
    }

    public void setVille(String ville) {
        this.ville = ville;
    }

    public Instant getCreationDate() {
        return creationDate;
    }

    public void setCreationDate(Instant creationDate) {
        this.creationDate = creationDate;
    }

    public Instant getUpdateDate() {
        return updateDate;
    }

    public void setUpdateDate(Instant updateDate) {
        this.updateDate = updateDate;
    }

    public String getFiscaleId() {
        return fiscaleId;
    }

    public void setFiscaleId(String fiscaleId) {
        this.fiscaleId = fiscaleId;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof GmsClientsDTO)) {
            return false;
        }

        GmsClientsDTO gmsClientsDTO = (GmsClientsDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, gmsClientsDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "GmsClientsDTO{" +
            "id=" + getId() +
            ", clientName='" + getClientName() + "'" +
            ", code='" + getCode() + "'" +
            ", classe='" + getClasse() + "'" +
            ", ville='" + getVille() + "'" +
            ", creationDate='" + getCreationDate() + "'" +
            ", updateDate='" + getUpdateDate() + "'" +
            ", fiscaleId=" + getFiscaleId() +
            "}";
    }
}
