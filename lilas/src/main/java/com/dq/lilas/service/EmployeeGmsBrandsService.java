package com.dq.lilas.service;

import com.dq.lilas.domain.EmployeeGmsBrands;
import com.dq.lilas.service.dto.EmployeeGmsBrandsDTO;

import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.EmployeeGmsBrands}.
 */
public interface EmployeeGmsBrandsService {
    /**
     * Save a employeeGmsBrands.
     *
     * @param employeeGmsBrandsDTO the entity to save.
     * @return the persisted entity.
     */
    EmployeeGmsBrandsDTO save(EmployeeGmsBrandsDTO employeeGmsBrandsDTO);

    /**
     * Updates a employeeGmsBrands.
     *
     * @param employeeGmsBrandsDTO the entity to update.
     * @return the persisted entity.
     */
    EmployeeGmsBrandsDTO update(EmployeeGmsBrandsDTO employeeGmsBrandsDTO);

    /**
     * Partially updates a employeeGmsBrands.
     *
     * @param employeeGmsBrandsDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<EmployeeGmsBrandsDTO> partialUpdate(EmployeeGmsBrandsDTO employeeGmsBrandsDTO);

    /**
     * Get all the employeeGmsBrands.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<EmployeeGmsBrandsDTO> findAll(Pageable pageable);

    /**
     * Get the "id" employeeGmsBrands.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<EmployeeGmsBrandsDTO> findOne(Long id);

    /**
     * Delete the "id" employeeGmsBrands.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);

    /**
     * Get all the employeeGmsBrands for a specific employee.
     *
     * @param employeeId the id of the employee.
     * @return the list of entities.
     */
    List<EmployeeGmsBrands> findByEmployee(Long employeeId);

    /**
     * Get all the brands associated with a specific employee.
     *
     * @param employeeId the id of the employee.
     * @return the list of brands.
     */
    List<Long> findBrandsByEmployee(Long employeeId);

    /**
     * Set brands for a specific employee.
     *
     * @param employeeId the id of the employee.
     * @param brandIds the list of brand ids to associate with the employee.
     * @return the list of created employeeGmsBrands.
     */
    List<EmployeeGmsBrandsDTO> assignEmployeeBrands(Long employeeId, List<Long> brandIds);

}
