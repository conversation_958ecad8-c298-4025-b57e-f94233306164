package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Company;
import com.dq.lilas.domain.DailyBatches;
import com.dq.lilas.service.dto.CompanyDTO;
import com.dq.lilas.service.dto.DailyBatchesDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link DailyBatches} and its DTO {@link DailyBatchesDTO}.
 */
@Mapper(componentModel = "spring")
public interface DailyBatchesMapper extends EntityMapper<DailyBatchesDTO, DailyBatches> {
    @Mapping(target = "company", source = "company", qualifiedByName = "companyId")
    DailyBatchesDTO toDto(DailyBatches s);

    @Named("companyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    CompanyDTO toDtoCompanyId(Company company);
}
