package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Typecorrespondence;
import com.dq.lilas.repository.TypecorrespondenceRepository;
import com.dq.lilas.service.TypecorrespondenceService;
import com.dq.lilas.service.dto.TypecorrespondenceDTO;
import com.dq.lilas.service.mapper.TypecorrespondenceMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Typecorrespondence}.
 */
@Service
@Transactional
public class TypecorrespondenceServiceImpl implements TypecorrespondenceService {

    private static final Logger LOG = LoggerFactory.getLogger(TypecorrespondenceServiceImpl.class);

    private final TypecorrespondenceRepository typecorrespondenceRepository;

    private final TypecorrespondenceMapper typecorrespondenceMapper;

    public TypecorrespondenceServiceImpl(
        TypecorrespondenceRepository typecorrespondenceRepository,
        TypecorrespondenceMapper typecorrespondenceMapper
    ) {
        this.typecorrespondenceRepository = typecorrespondenceRepository;
        this.typecorrespondenceMapper = typecorrespondenceMapper;
    }

    @Override
    public TypecorrespondenceDTO save(TypecorrespondenceDTO typecorrespondenceDTO) {
        LOG.debug("Request to save Typecorrespondence : {}", typecorrespondenceDTO);
        Typecorrespondence typecorrespondence = typecorrespondenceMapper.toEntity(typecorrespondenceDTO);
        typecorrespondence = typecorrespondenceRepository.save(typecorrespondence);
        return typecorrespondenceMapper.toDto(typecorrespondence);
    }

    @Override
    public TypecorrespondenceDTO update(TypecorrespondenceDTO typecorrespondenceDTO) {
        LOG.debug("Request to update Typecorrespondence : {}", typecorrespondenceDTO);
        Typecorrespondence typecorrespondence = typecorrespondenceMapper.toEntity(typecorrespondenceDTO);
        typecorrespondence = typecorrespondenceRepository.save(typecorrespondence);
        return typecorrespondenceMapper.toDto(typecorrespondence);
    }

    @Override
    public Optional<TypecorrespondenceDTO> partialUpdate(TypecorrespondenceDTO typecorrespondenceDTO) {
        LOG.debug("Request to partially update Typecorrespondence : {}", typecorrespondenceDTO);

        return typecorrespondenceRepository
            .findById(typecorrespondenceDTO.getId())
            .map(existingTypecorrespondence -> {
                typecorrespondenceMapper.partialUpdate(existingTypecorrespondence, typecorrespondenceDTO);

                return existingTypecorrespondence;
            })
            .map(typecorrespondenceRepository::save)
            .map(typecorrespondenceMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<TypecorrespondenceDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Typecorrespondences");
        return typecorrespondenceRepository.findAll(pageable).map(typecorrespondenceMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<TypecorrespondenceDTO> findOne(Long id) {
        LOG.debug("Request to get Typecorrespondence : {}", id);
        return typecorrespondenceRepository.findById(id).map(typecorrespondenceMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Typecorrespondence : {}", id);
        typecorrespondenceRepository.deleteById(id);
    }
}
