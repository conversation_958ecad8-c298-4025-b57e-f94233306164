package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Company;
import com.dq.lilas.domain.Employee;
import com.dq.lilas.domain.EmployeeCompanyPermission;
import com.dq.lilas.service.dto.CompanyDTO;
import com.dq.lilas.service.dto.EmployeeCompanyPermissionDTO;
import com.dq.lilas.service.dto.EmployeeDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link EmployeeCompanyPermission} and its DTO {@link EmployeeCompanyPermissionDTO}.
 */
@Mapper(componentModel = "spring")
public interface EmployeeCompanyPermissionMapper extends EntityMapper<EmployeeCompanyPermissionDTO, EmployeeCompanyPermission> {
    @Mapping(target = "employee", source = "employee", qualifiedByName = "employeeId")
    @Mapping(target = "company", source = "company", qualifiedByName = "companyId")
    EmployeeCompanyPermissionDTO toDto(EmployeeCompanyPermission s);

    @Named("employeeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    EmployeeDTO toDtoEmployeeId(Employee employee);

    @Named("companyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    CompanyDTO toDtoCompanyId(Company company);
}
