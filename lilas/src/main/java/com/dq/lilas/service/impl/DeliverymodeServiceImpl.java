package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Deliverymode;
import com.dq.lilas.repository.DeliverymodeRepository;
import com.dq.lilas.service.DeliverymodeService;
import com.dq.lilas.service.dto.DeliverymodeDTO;
import com.dq.lilas.service.mapper.DeliverymodeMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Deliverymode}.
 */
@Service
@Transactional
public class DeliverymodeServiceImpl implements DeliverymodeService {

    private static final Logger LOG = LoggerFactory.getLogger(DeliverymodeServiceImpl.class);

    private final DeliverymodeRepository deliverymodeRepository;

    private final DeliverymodeMapper deliverymodeMapper;

    public DeliverymodeServiceImpl(DeliverymodeRepository deliverymodeRepository, DeliverymodeMapper deliverymodeMapper) {
        this.deliverymodeRepository = deliverymodeRepository;
        this.deliverymodeMapper = deliverymodeMapper;
    }

    @Override
    public DeliverymodeDTO save(DeliverymodeDTO deliverymodeDTO) {
        LOG.debug("Request to save Deliverymode : {}", deliverymodeDTO);
        Deliverymode deliverymode = deliverymodeMapper.toEntity(deliverymodeDTO);
        deliverymode = deliverymodeRepository.save(deliverymode);
        return deliverymodeMapper.toDto(deliverymode);
    }

    @Override
    public DeliverymodeDTO update(DeliverymodeDTO deliverymodeDTO) {
        LOG.debug("Request to update Deliverymode : {}", deliverymodeDTO);
        Deliverymode deliverymode = deliverymodeMapper.toEntity(deliverymodeDTO);
        deliverymode = deliverymodeRepository.save(deliverymode);
        return deliverymodeMapper.toDto(deliverymode);
    }

    @Override
    public Optional<DeliverymodeDTO> partialUpdate(DeliverymodeDTO deliverymodeDTO) {
        LOG.debug("Request to partially update Deliverymode : {}", deliverymodeDTO);

        return deliverymodeRepository
            .findById(deliverymodeDTO.getId())
            .map(existingDeliverymode -> {
                deliverymodeMapper.partialUpdate(existingDeliverymode, deliverymodeDTO);

                return existingDeliverymode;
            })
            .map(deliverymodeRepository::save)
            .map(deliverymodeMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<DeliverymodeDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Deliverymodes");
        return deliverymodeRepository.findAll(pageable).map(deliverymodeMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<DeliverymodeDTO> findOne(Long id) {
        LOG.debug("Request to get Deliverymode : {}", id);
        return deliverymodeRepository.findById(id).map(deliverymodeMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Deliverymode : {}", id);
        deliverymodeRepository.deleteById(id);
    }
}
