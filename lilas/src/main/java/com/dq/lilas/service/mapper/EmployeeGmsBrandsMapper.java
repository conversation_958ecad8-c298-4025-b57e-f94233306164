package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Employee;
import com.dq.lilas.domain.EmployeeGmsBrands;
import com.dq.lilas.domain.GmsBrands;
import com.dq.lilas.service.dto.EmployeeDTO;
import com.dq.lilas.service.dto.EmployeeGmsBrandsDTO;
import com.dq.lilas.service.dto.GmsBrandsDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link EmployeeGmsBrands} and its DTO {@link EmployeeGmsBrandsDTO}.
 */
@Mapper(componentModel = "spring")
public interface EmployeeGmsBrandsMapper extends EntityMapper<EmployeeGmsBrandsDTO, EmployeeGmsBrands> {
    @Mapping(target = "employee", source = "employee", qualifiedByName = "employeeId")
    @Mapping(target = "gmsBrands", source = "gmsBrands", qualifiedByName = "gmsBrandsId")
    EmployeeGmsBrandsDTO toDto(EmployeeGmsBrands s);

    @Named("employeeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    EmployeeDTO toDtoEmployeeId(Employee employee);

    @Named("gmsBrandsId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    GmsBrandsDTO toDtoGmsBrandsId(GmsBrands gmsBrands);
}
