package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Employee} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class EmployeeDTO implements Serializable {

    private Long id;

    @Size(max = 15)
    private String tel;

    @Size(max = 15)
    private String fax;

    @Size(max = 100)
    private String mail;

    @Size(max = 10)
    private String confidentiel;

    @Size(max = 15)
    private String numidentity;

    @Size(max = 15)
    private String empnumber;

    @Size(max = 1000)
    private String fullname;

    @Size(max = 1000)
    private String address;

    @Size(max = 10)
    private String matricule;

    @Size(max = 5)
    private String upscale;

    @Size(max = 10)
    private String active;

    @Size(max = 1)
    private String statut;

    @Size(max = 15)
    private String gender;

    @Size(max = 100)
    private String avatar;

    @Size(max = 255)
    private String filenameparaf;

    @Size(max = 255)
    private String filenamesign;

    private Boolean coursier;

    private UserDTO user;

    private JobDTO job;

    private UnitDTO unit;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getTel() {
        return tel;
    }

    public void setTel(String tel) {
        this.tel = tel;
    }

    public String getFax() {
        return fax;
    }

    public void setFax(String fax) {
        this.fax = fax;
    }

    public String getMail() {
        return mail;
    }

    public void setMail(String mail) {
        this.mail = mail;
    }

    public String getConfidentiel() {
        return confidentiel;
    }

    public void setConfidentiel(String confidentiel) {
        this.confidentiel = confidentiel;
    }

    public String getNumidentity() {
        return numidentity;
    }

    public void setNumidentity(String numidentity) {
        this.numidentity = numidentity;
    }

    public String getEmpnumber() {
        return empnumber;
    }

    public void setEmpnumber(String empnumber) {
        this.empnumber = empnumber;
    }

    public String getFullname() {
        return fullname;
    }

    public void setFullname(String fullname) {
        this.fullname = fullname;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getMatricule() {
        return matricule;
    }

    public void setMatricule(String matricule) {
        this.matricule = matricule;
    }

    public String getUpscale() {
        return upscale;
    }

    public void setUpscale(String upscale) {
        this.upscale = upscale;
    }

    public String getActive() {
        return active;
    }

    public void setActive(String active) {
        this.active = active;
    }

    public String getStatut() {
        return statut;
    }

    public void setStatut(String statut) {
        this.statut = statut;
    }

    public String getGender() {
        return gender;
    }

    public void setGender(String gender) {
        this.gender = gender;
    }

    public String getAvatar() {
        return avatar;
    }

    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }

    public String getFilenameparaf() {
        return filenameparaf;
    }

    public void setFilenameparaf(String filenameparaf) {
        this.filenameparaf = filenameparaf;
    }

    public String getFilenamesign() {
        return filenamesign;
    }

    public void setFilenamesign(String filenamesign) {
        this.filenamesign = filenamesign;
    }

    public Boolean getCoursier() {
        return coursier;
    }

    public void setCoursier(Boolean coursier) {
        this.coursier = coursier;
    }

    public UserDTO getUser() {
        return user;
    }

    public void setUser(UserDTO user) {
        this.user = user;
    }

    public JobDTO getJob() {
        return job;
    }

    public void setJob(JobDTO job) {
        this.job = job;
    }

    public UnitDTO getUnit() {
        return unit;
    }

    public void setUnit(UnitDTO unit) {
        this.unit = unit;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof EmployeeDTO)) {
            return false;
        }

        EmployeeDTO employeeDTO = (EmployeeDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, employeeDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "EmployeeDTO{" +
            "id=" + getId() +
            ", tel='" + getTel() + "'" +
            ", fax='" + getFax() + "'" +
            ", mail='" + getMail() + "'" +
            ", confidentiel='" + getConfidentiel() + "'" +
            ", numidentity='" + getNumidentity() + "'" +
            ", empnumber='" + getEmpnumber() + "'" +
            ", fullname='" + getFullname() + "'" +
            ", address='" + getAddress() + "'" +
            ", matricule='" + getMatricule() + "'" +
            ", upscale='" + getUpscale() + "'" +
            ", active='" + getActive() + "'" +
            ", statut='" + getStatut() + "'" +
            ", gender='" + getGender() + "'" +
            ", avatar='" + getAvatar() + "'" +
            ", filenameparaf='" + getFilenameparaf() + "'" +
            ", filenamesign='" + getFilenamesign() + "'" +
            ", coursier='" + getCoursier() + "'" +
            ", user=" + getUser() +
            ", job=" + getJob() +
            ", unit=" + getUnit() +
            "}";
    }
}
