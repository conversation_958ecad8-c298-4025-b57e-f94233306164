package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.DemandePromotion;
import com.dq.lilas.service.dto.DemandePromotionDTO;

/**
 * Mapper for the entity {@link DemandePromotion} and its DTO {@link DemandePromotionDTO}.
 */
public interface DemandePromotionMapper extends EntityMapper<DemandePromotionDTO, DemandePromotion> {
    
    DemandePromotionDTO toDto(DemandePromotion demandePromotion);
    DemandePromotion toEntity(DemandePromotionDTO demandePromotionDTO);
    
    default DemandePromotion fromId(Long id) {
        if (id == null) {
            return null;
        }
        DemandePromotion demandePromotion = new DemandePromotion();
        demandePromotion.setId(id);
        return demandePromotion;
    }
}
