package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Correspondencecopy;
import com.dq.lilas.repository.CorrespondencecopyRepository;
import com.dq.lilas.service.CorrespondencecopyService;
import com.dq.lilas.service.dto.CorrespondencecopyDTO;
import com.dq.lilas.service.mapper.CorrespondencecopyMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Correspondencecopy}.
 */
@Service
@Transactional
public class CorrespondencecopyServiceImpl implements CorrespondencecopyService {

    private static final Logger LOG = LoggerFactory.getLogger(CorrespondencecopyServiceImpl.class);

    private final CorrespondencecopyRepository correspondencecopyRepository;

    private final CorrespondencecopyMapper correspondencecopyMapper;

    public CorrespondencecopyServiceImpl(
        CorrespondencecopyRepository correspondencecopyRepository,
        CorrespondencecopyMapper correspondencecopyMapper
    ) {
        this.correspondencecopyRepository = correspondencecopyRepository;
        this.correspondencecopyMapper = correspondencecopyMapper;
    }

    @Override
    public CorrespondencecopyDTO save(CorrespondencecopyDTO correspondencecopyDTO) {
        LOG.debug("Request to save Correspondencecopy : {}", correspondencecopyDTO);
        Correspondencecopy correspondencecopy = correspondencecopyMapper.toEntity(correspondencecopyDTO);
        correspondencecopy = correspondencecopyRepository.save(correspondencecopy);
        return correspondencecopyMapper.toDto(correspondencecopy);
    }

    @Override
    public CorrespondencecopyDTO update(CorrespondencecopyDTO correspondencecopyDTO) {
        LOG.debug("Request to update Correspondencecopy : {}", correspondencecopyDTO);
        Correspondencecopy correspondencecopy = correspondencecopyMapper.toEntity(correspondencecopyDTO);
        correspondencecopy = correspondencecopyRepository.save(correspondencecopy);
        return correspondencecopyMapper.toDto(correspondencecopy);
    }

    @Override
    public Optional<CorrespondencecopyDTO> partialUpdate(CorrespondencecopyDTO correspondencecopyDTO) {
        LOG.debug("Request to partially update Correspondencecopy : {}", correspondencecopyDTO);

        return correspondencecopyRepository
            .findById(correspondencecopyDTO.getId())
            .map(existingCorrespondencecopy -> {
                correspondencecopyMapper.partialUpdate(existingCorrespondencecopy, correspondencecopyDTO);

                return existingCorrespondencecopy;
            })
            .map(correspondencecopyRepository::save)
            .map(correspondencecopyMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<CorrespondencecopyDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Correspondencecopies");
        return correspondencecopyRepository.findAll(pageable).map(correspondencecopyMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<CorrespondencecopyDTO> findOne(Long id) {
        LOG.debug("Request to get Correspondencecopy : {}", id);
        return correspondencecopyRepository.findById(id).map(correspondencecopyMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Correspondencecopy : {}", id);
        correspondencecopyRepository.deleteById(id);
    }
}
