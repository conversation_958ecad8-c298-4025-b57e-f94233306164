package com.dq.lilas.service;

import com.dq.lilas.service.dto.CorrespondencecopyDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Correspondencecopy}.
 */
public interface CorrespondencecopyService {
    /**
     * Save a correspondencecopy.
     *
     * @param correspondencecopyDTO the entity to save.
     * @return the persisted entity.
     */
    CorrespondencecopyDTO save(CorrespondencecopyDTO correspondencecopyDTO);

    /**
     * Updates a correspondencecopy.
     *
     * @param correspondencecopyDTO the entity to update.
     * @return the persisted entity.
     */
    CorrespondencecopyDTO update(CorrespondencecopyDTO correspondencecopyDTO);

    /**
     * Partially updates a correspondencecopy.
     *
     * @param correspondencecopyDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<CorrespondencecopyDTO> partialUpdate(CorrespondencecopyDTO correspondencecopyDTO);

    /**
     * Get all the correspondencecopies.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<CorrespondencecopyDTO> findAll(Pageable pageable);

    /**
     * Get the "id" correspondencecopy.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<CorrespondencecopyDTO> findOne(Long id);

    /**
     * Delete the "id" correspondencecopy.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
