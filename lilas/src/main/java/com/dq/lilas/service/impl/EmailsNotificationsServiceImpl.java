package com.dq.lilas.service.impl;

import com.dq.lilas.domain.EmailsNotifications;
import com.dq.lilas.repository.EmailsNotificationsRepository;
import com.dq.lilas.service.EmailsNotificationsService;
import com.dq.lilas.service.dto.EmailsNotificationsDTO;
import com.dq.lilas.service.mapper.EmailsNotificationsMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.EmailsNotifications}.
 */
@Service
@Transactional
public class EmailsNotificationsServiceImpl implements EmailsNotificationsService {

    private static final Logger LOG = LoggerFactory.getLogger(EmailsNotificationsServiceImpl.class);

    private final EmailsNotificationsRepository emailsNotificationsRepository;

    private final EmailsNotificationsMapper emailsNotificationsMapper;

    public EmailsNotificationsServiceImpl(
        EmailsNotificationsRepository emailsNotificationsRepository,
        EmailsNotificationsMapper emailsNotificationsMapper
    ) {
        this.emailsNotificationsRepository = emailsNotificationsRepository;
        this.emailsNotificationsMapper = emailsNotificationsMapper;
    }

    @Override
    public EmailsNotificationsDTO save(EmailsNotificationsDTO emailsNotificationsDTO) {
        LOG.debug("Request to save EmailsNotifications : {}", emailsNotificationsDTO);
        EmailsNotifications emailsNotifications = emailsNotificationsMapper.toEntity(emailsNotificationsDTO);
        emailsNotifications = emailsNotificationsRepository.save(emailsNotifications);
        return emailsNotificationsMapper.toDto(emailsNotifications);
    }

    @Override
    public EmailsNotificationsDTO update(EmailsNotificationsDTO emailsNotificationsDTO) {
        LOG.debug("Request to update EmailsNotifications : {}", emailsNotificationsDTO);
        EmailsNotifications emailsNotifications = emailsNotificationsMapper.toEntity(emailsNotificationsDTO);
        emailsNotifications = emailsNotificationsRepository.save(emailsNotifications);
        return emailsNotificationsMapper.toDto(emailsNotifications);
    }

    @Override
    public Optional<EmailsNotificationsDTO> partialUpdate(EmailsNotificationsDTO emailsNotificationsDTO) {
        LOG.debug("Request to partially update EmailsNotifications : {}", emailsNotificationsDTO);

        return emailsNotificationsRepository
            .findById(emailsNotificationsDTO.getId())
            .map(existingEmailsNotifications -> {
                emailsNotificationsMapper.partialUpdate(existingEmailsNotifications, emailsNotificationsDTO);

                return existingEmailsNotifications;
            })
            .map(emailsNotificationsRepository::save)
            .map(emailsNotificationsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EmailsNotificationsDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all EmailsNotifications");
        return emailsNotificationsRepository.findAll(pageable).map(emailsNotificationsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EmailsNotificationsDTO> findOne(Long id) {
        LOG.debug("Request to get EmailsNotifications : {}", id);
        return emailsNotificationsRepository.findById(id).map(emailsNotificationsMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete EmailsNotifications : {}", id);
        emailsNotificationsRepository.deleteById(id);
    }
}
