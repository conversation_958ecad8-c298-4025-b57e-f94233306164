package com.dq.lilas.service.dto;

import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.GmsBrands} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class GmsBrandsDTO implements Serializable {

    private Long id;

    private String name;

    private String classe;

    private String iconPath;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getClasse() {
        return classe;
    }

    public void setClasse(String classe) {
        this.classe = classe;
    }

    public String getIconPath() {
        return iconPath;
    }

    public void setIconPath(String iconPath) {
        this.iconPath = iconPath;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof GmsBrandsDTO)) {
            return false;
        }

        GmsBrandsDTO gmsBrandsDTO = (GmsBrandsDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, gmsBrandsDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "GmsBrandsDTO{" +
            "id=" + getId() +
            ", name='" + getName() + "'" +
            ", classe='" + getClasse() + "'" +
            ", iconPath='" + getIconPath() + "'" +
            "}";
    }
}
