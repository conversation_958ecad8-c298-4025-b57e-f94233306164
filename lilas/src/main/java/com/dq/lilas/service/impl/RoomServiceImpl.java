package com.dq.lilas.service.impl;

import com.dq.lilas.domain.*;
import com.dq.lilas.repository.ConversationRepository;
import com.dq.lilas.repository.RoomRepository;
import com.dq.lilas.service.GroupService;
import com.dq.lilas.service.RoomService;
import com.dq.lilas.service.UserService;
import com.dq.lilas.service.dto.GroupDTO;
import com.dq.lilas.service.dto.RoomDTO;
import com.dq.lilas.service.dto.UserDTO;
import com.dq.lilas.service.mapper.RoomMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing Room.
 */
@Service
@Transactional
public class RoomServiceImpl implements RoomService {

    private final Logger log = LoggerFactory.getLogger(RoomServiceImpl.class);

    private final RoomRepository roomRepository;

    private final RoomMapper roomMapper;

    private final UserService userService;

    private final GroupService groupService;

    @Autowired
    ConversationRepository conversationRepository;

    public RoomServiceImpl(RoomRepository roomRepository, RoomMapper roomMapper, UserService userService, GroupService groupService, ConversationRepository conversationRepository) {
        this.roomRepository = roomRepository;
        this.roomMapper = roomMapper;
        this.userService = userService;
        this.groupService = groupService;
        this.conversationRepository = conversationRepository;
    }

    /**
     * Save a room.
     *
     * @param roomDTO the entity to save
     * @return the persisted entity
     */
    @Override
    @Transactional
    public RoomDTO save(RoomDTO roomDTO) {
        log.debug("Request to save Room : {}", roomDTO);
        Optional<User> optionalUser = userService.getUserWithAuthorities();
        Room room = roomMapper.toEntity(roomDTO);
        room = roomRepository.save(room);
        try {
            // update users and add logged user if not exist
            Set<UserDTO> users = roomDTO.getUsers();
            if (optionalUser.isPresent()) {
                User loggedUser = optionalUser.get();
                boolean isExist = users.stream().anyMatch(userDTO -> userDTO.getId().equals(loggedUser.getId()));
                if(!isExist) {
                    UserDTO userDTO = new UserDTO();
                    userDTO.setId(loggedUser.getId());
                    users.add(userDTO);
                }
            }
            Room finalRoom = room;
            Set<RoomUser> roomUsers = new HashSet<>();
            users.forEach(userDTO -> {
                User user = new User();
                user.setId(userDTO.getId());
                RoomUser roomUser = createRoomUserRelationship(finalRoom, user);
                roomUsers.add(roomUser);
            });
            room.setRoomUsers(roomUsers);
            room = roomRepository.save(room);
        } catch (Exception e) {
            // problem occurred when crate room user relationship
            throw e;
        }
        return roomMapper.toDto(room);
    }

    private RoomUser createRoomUserRelationship(Room room, User user) {
        RoomUser roomUser = new RoomUser();
        roomUser.setId(new RoomUserPK(room.getId(), user.getId()));
        roomUser.setRoom(room);
        roomUser.setUser(user);
        return roomUser;
    }

    /**
     * Get all the rooms.
     *
     * @param pageable the pagination information
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public Page<RoomDTO> findAllByPage(Pageable pageable) {
        log.debug("Request to get all Rooms");
        return roomRepository.findAll(pageable)
                .map(roomMapper::toDto);
    }

    /**
     * Get all the rooms.
     *
     * @return the list of entities
     */
    @Override
    public List<RoomDTO> findAll() {
        return roomRepository.findAll().stream().map(roomMapper::toDto).collect(Collectors.toList());
    }

    /**
     * Get all the Room with eager load of many-to-many relationships.
     *
     * @return the list of entities
     */
    public Page<RoomDTO> findAllWithEagerRelationships(Pageable pageable) {
        return roomRepository.findAllWithEagerRelationships(pageable).map(roomMapper::toDto);
    }

    /**
     * Get all rooms of group.
     *
     * @return the list of entities.
     */
    public List<RoomDTO> findAllByGroup(Long idGroup) {
        Optional<GroupDTO> optionalGroup = groupService.findOne(idGroup);
        if (optionalGroup.isPresent()) {
            GroupDTO groupDTO = optionalGroup.get();
            return roomRepository.findAllByGroup_Id(groupDTO.getId())
                    .stream()
                    .map(roomMapper::toDto)
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * Get all rooms of a group joined by the logged user.
     *
     * @return the list of entities.
     */
    public List<RoomDTO> findAllOfGroupJoinedByLoggedUser(Long idGroup) {
        Optional<User> optionalUser = userService.getUserWithAuthorities();
        if (optionalUser.isPresent()) {
            User user = optionalUser.get();
            return roomRepository.findAllByUsersIsContainingAndGroup(user.getId(), idGroup)
                    .stream()
                    .map(Room::toDTO)
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * Get all Rooms joined by Logged User.
     *
     * @return the list of entities.
     */
    public List<RoomDTO> findAllByLoggedUser() {
        Optional<User> optionalUser = userService.getUserWithAuthorities();
        if (optionalUser.isPresent()) {
            User user = optionalUser.get();
            return roomRepository.findAllByUsersIsContaining(user.getId())
                    .stream()
                    .map(roomMapper::toDto)
                    .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * Get Private Room by Logged User And Selected User.
     *
     * @param id the id of the selected user.
     * @return the entity.
     */
    @Transactional
    public RoomDTO getPrivateRoomByLoggedUserAndSelectedUser(Long id) {
        Set<User> users = new HashSet<>();
        Optional<User> optionalLoggedUser = userService.getUserWithAuthorities();
        Optional<User> optionalSelectedUser = userService.findUserById(id);
        if (optionalLoggedUser.isPresent() && optionalSelectedUser.isPresent()) {
            User loggedUser = optionalLoggedUser.get();
            User selectedUser = optionalSelectedUser.get();
            boolean isSameUser = loggedUser.equals(selectedUser);
            if (isSameUser) {
                return new RoomDTO();
            }
            users.addAll(Arrays.asList(loggedUser, selectedUser));
            Optional<Room> optionalRoom = this.roomRepository.findPrivateRoomBySenderAndReceiver(loggedUser.getId(), selectedUser.getId());
            Room room;
            if (optionalRoom.isPresent()) {
                room = optionalRoom.get();
            } else {
                // create a private room for 2 users
                room = new Room();
                room.setIsActivated(true);
                room.setAllowCall(true);
                String name = "PRIVATE-" + UUID.randomUUID();
                room.setName(name);
                room = roomRepository.save(room);
                Room finalRoom = room;
                try {
                    users.forEach(user -> {
                        RoomUser roomUser = createRoomUserRelationship(finalRoom, user);
                        finalRoom.addRoomUser(roomUser);
                    });
                    room = roomRepository.save(finalRoom);
                } catch (Exception e) {
                    throw e;
                }
            }
            return roomMapper.toDto(room);
        }
        return new RoomDTO();
    }


    /**
     * Create First Room in Group.
     *
     * @param roomDTO the entity.
     */
    @Transactional
    public void createFirstRoomInGroup(RoomDTO roomDTO) {
        Long groupId = roomDTO.getGroupId();
        log.debug("Request to create first Room in Group");
        Optional<GroupDTO> optionalGroup = groupService.findOne(groupId);
        if (optionalGroup.isPresent()) {
            GroupDTO groupDTO = optionalGroup.get();
            String name = groupDTO.getName();
            roomDTO.setName(name.toLowerCase());
            roomDTO.setIsActivated(true);
            roomDTO.setGroupId(groupId);
            roomDTO.setUsers(groupDTO.getUsers());
            Room room = roomMapper.toEntity(roomDTO);
            room = roomRepository.save(room);
            // add users (create room-user relationship)
            try {
                Room finalRoom = room;
                groupDTO.getUsers()
                    .forEach(userDTO -> {
                        User user = new User();
                        user.setId(userDTO.getId());
                        RoomUser roomUser = createRoomUserRelationship(finalRoom, user);
                        finalRoom.addRoomUser(roomUser);
                    });
                roomRepository.save(finalRoom);
            } catch (Exception e) {
                throw e;
            }
        }
    }

    /**
     * Update (add or remove) logged user from room by name and group.
     *
     * @param groupId the group entity id.
     * @param name the room entity name.
     * @param isAdd boolean to indicate to add new user to room or remove.
     */
    @Override
    public void updateLoggedUserFromRoomByNameAndGroup(Long groupId, String name, Boolean isAdd) {
        Optional<User> optionalLoggedUser = userService.getUserWithAuthorities();
        if (optionalLoggedUser.isPresent()) {
            User loggedUser = optionalLoggedUser.get();
            Optional<Room> optionalRoom = roomRepository.findFirstByNameAndGroupId(name, groupId);
            if(optionalRoom.isPresent()) {
                Room room = optionalRoom.get();
                RoomUser roomUser = createRoomUserRelationship(room, loggedUser);
                if(isAdd) {
                    room.addRoomUser(roomUser);
                } else {
                    room.removeRoomUser(roomUser);
                }
                roomRepository.save(room);
            }
        }
    }

    /**
     * Get one room by id.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<RoomDTO> findOne(Long id) {
        log.debug("Request to get Room : {}", id);
        return roomRepository.findOneWithEagerRelationships(id)
            .map(Room::toDTO);
    }

    /**
     * Get one room by id.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    public Optional<Room> findById(Long id) {
        return roomRepository.findById(id);
    }

    /**
     * Delete the room by id.
     *
     * @param id the id of the entity
     */
    @Override
    public void delete(Long id) {
        log.debug("Request to delete Room : {}", id);
        conversationRepository.deleteAllByRoomId(id);
        roomRepository.deleteById(id);
    }
}
