package com.dq.lilas.service.mapper.impl;

import com.dq.lilas.domain.DemandePromotion;
import com.dq.lilas.domain.PromotionDetails;
import com.dq.lilas.domain.enumeration.PromotionStatus;
import com.dq.lilas.service.dto.DemandePromotionDTO;
import com.dq.lilas.service.dto.PromotionDetailsDTO;
import com.dq.lilas.service.mapper.PromotionDetailsMapper;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * Manual implementation of PromotionDetailsMapper to avoid circular reference
 */
@Primary
@Component("customPromotionDetailsMapperImpl")
public class PromotionDetailsMapperImpl implements PromotionDetailsMapper {

    @Override
    public PromotionDetailsDTO toDto(PromotionDetails entity) {
        if (entity == null) {
            return null;
        }

        PromotionDetailsDTO dto = new PromotionDetailsDTO();
        dto.setId(entity.getId());
        dto.setCodeProduit(entity.getCodeProduit());
        dto.setDescription(entity.getDescription());
        dto.setPriceHT(entity.getPriceHT());
        dto.setRemiseFixe(entity.getRemiseFixe());
        dto.setRemiseDePro(entity.getRemiseDePro());
        dto.setGratuitEnNat(entity.getGratuitEnNat());
        dto.setApproManagerDuMagasin(entity.getApproManagerDuMagasin());
        dto.setApproManagerGMS(entity.getApproManagerGMS());
        dto.setStatus(entity.getStatus());
        
        // Map DemandePromotion without its promotionDetails to avoid infinite recursion
        if (entity.getDemandePromotion() != null) {
            DemandePromotionDTO demandePromotionDTO = new DemandePromotionDTO();
            demandePromotionDTO.setId(entity.getDemandePromotion().getId());
            demandePromotionDTO.setCodeClient(entity.getDemandePromotion().getCodeClient());
            demandePromotionDTO.setEnseigne(entity.getDemandePromotion().getEnseigne());
            demandePromotionDTO.setAction(entity.getDemandePromotion().getAction());
            demandePromotionDTO.setPeriodPromotionStart(entity.getDemandePromotion().getPeriodPromotionStart());
            demandePromotionDTO.setPeriodPromotionEnd(entity.getDemandePromotion().getPeriodPromotionEnd());
            demandePromotionDTO.setPeriodFacturationStart(entity.getDemandePromotion().getPeriodFacturationStart());
            demandePromotionDTO.setPeriodFacturationEnd(entity.getDemandePromotion().getPeriodFacturationEnd());
            // DO NOT map promotionDetails here to avoid circular references
            
            dto.setDemandePromotion(demandePromotionDTO);
        }

        return dto;
    }

    @Override
    public PromotionDetails toEntity(PromotionDetailsDTO dto) {
        if (dto == null) {
            return null;
        }

        PromotionDetails entity = new PromotionDetails();
        entity.setId(dto.getId());
        entity.setCodeProduit(dto.getCodeProduit());
        entity.setDescription(dto.getDescription());
        entity.setPriceHT(dto.getPriceHT());
        entity.setRemiseFixe(dto.getRemiseFixe());
        entity.setRemiseDePro(dto.getRemiseDePro());
        entity.setGratuitEnNat(dto.getGratuitEnNat());
        entity.setApproManagerDuMagasin(dto.getApproManagerDuMagasin());
        entity.setApproManagerGMS(dto.getApproManagerGMS());
        
        // Set status, defaulting to Waiting if null
        if (dto.getStatus() != null) {
            entity.setStatus(dto.getStatus());
        } else {
            entity.setStatus(PromotionStatus.Waiting);
        }
        
        // Map DemandePromotion without its promotionDetails to avoid infinite recursion
        if (dto.getDemandePromotion() != null) {
            DemandePromotion demandePromotion = new DemandePromotion();
            demandePromotion.setId(dto.getDemandePromotion().getId());
            demandePromotion.setCodeClient(dto.getDemandePromotion().getCodeClient());
            demandePromotion.setEnseigne(dto.getDemandePromotion().getEnseigne());
            demandePromotion.setAction(dto.getDemandePromotion().getAction());
            demandePromotion.setPeriodPromotionStart(dto.getDemandePromotion().getPeriodPromotionStart());
            demandePromotion.setPeriodPromotionEnd(dto.getDemandePromotion().getPeriodPromotionEnd());
            demandePromotion.setPeriodFacturationStart(dto.getDemandePromotion().getPeriodFacturationStart());
            demandePromotion.setPeriodFacturationEnd(dto.getDemandePromotion().getPeriodFacturationEnd());
            // DO NOT map promotionDetails here to avoid circular references
            
            entity.setDemandePromotion(demandePromotion);
        }

        return entity;
    }

    @Override
    public List<PromotionDetailsDTO> toDto(List<PromotionDetails> entityList) {
        if (entityList == null) {
            return null;
        }
        return entityList.stream()
            .map(this::toDto)
            .collect(Collectors.toList());
    }

    @Override
    public List<PromotionDetails> toEntity(List<PromotionDetailsDTO> dtoList) {
        if (dtoList == null) {
            return null;
        }
        return dtoList.stream()
            .map(this::toEntity)
            .collect(Collectors.toList());
    }

    @Override
    public void partialUpdate(PromotionDetails entity, PromotionDetailsDTO dto) {
        if (dto == null) {
            return;
        }

        if (dto.getId() != null) {
            entity.setId(dto.getId());
        }
        if (dto.getCodeProduit() != null) {
            entity.setCodeProduit(dto.getCodeProduit());
        }
        if (dto.getDescription() != null) {
            entity.setDescription(dto.getDescription());
        }
        if (dto.getPriceHT() != null) {
            entity.setPriceHT(dto.getPriceHT());
        }
        if (dto.getRemiseFixe() != null) {
            entity.setRemiseFixe(dto.getRemiseFixe());
        }
        if (dto.getRemiseDePro() != null) {
            entity.setRemiseDePro(dto.getRemiseDePro());
        }
        if (dto.getGratuitEnNat() != null) {
            entity.setGratuitEnNat(dto.getGratuitEnNat());
        }
        if (dto.getApproManagerDuMagasin() != null) {
            entity.setApproManagerDuMagasin(dto.getApproManagerDuMagasin());
        }
        if (dto.getApproManagerGMS() != null) {
            entity.setApproManagerGMS(dto.getApproManagerGMS());
        }
        if (dto.getStatus() != null) {
            entity.setStatus(dto.getStatus());
        } else if (entity.getStatus() == null) {
            // If entity doesn't have status and dto doesn't provide one, set default
            entity.setStatus(PromotionStatus.Waiting);
        }
    }
}
