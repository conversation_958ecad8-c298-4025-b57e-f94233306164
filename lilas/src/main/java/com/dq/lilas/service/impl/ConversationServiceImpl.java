package com.dq.lilas.service.impl;

import com.dq.lilas.domain.*;
import com.dq.lilas.domain.enumeration.ConversationState;
import com.dq.lilas.repository.ConversationRepository;
import com.dq.lilas.service.ConversationService;
import com.dq.lilas.service.RoomService;
import com.dq.lilas.service.UserService;
import com.dq.lilas.service.dto.ConversationDTO;
import com.dq.lilas.service.dto.UserDTO;
import com.dq.lilas.service.mapper.ConversationMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.ArrayList;
import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service Implementation for managing Conversation.
 */
@Service
@Transactional
public class ConversationServiceImpl implements ConversationService {

    private final Logger log = LoggerFactory.getLogger(ConversationServiceImpl.class);

    private final ConversationRepository conversationRepository;

    private final ConversationMapper conversationMapper;

    private final UserService userService;

    private final RoomService roomService;

    public ConversationServiceImpl(ConversationRepository conversationRepository, ConversationMapper conversationMapper, UserService userService, RoomService roomService) {
        this.conversationRepository = conversationRepository;
        this.conversationMapper = conversationMapper;
        this.userService = userService;
        this.roomService = roomService;
    }

    /**
     * Save a conversation.
     *
     * @param conversationDTO the entity to save
     * @param receivers the receivers
     * @return the persisted entity
     */
    @Override
    public ConversationDTO save(ConversationDTO conversationDTO, Set<UserDTO> receivers) {
        log.debug("Request to save Conversation : {}", conversationDTO);
        Conversation conversation = new Conversation();
        Long senderId = conversationDTO.getSenderId();
        Long roomId = conversationDTO.getRoomId();
        Optional<Room> optionalRoom = roomService.findById(roomId);
        if (optionalRoom.isPresent()) {
            Room room = optionalRoom.get();
            conversation.setRoom(room);
            conversation.setContent(conversationDTO.getContent());
            userService.findUserById(senderId).ifPresent(conversation::sender);
            addConversationReceivers(conversation, receivers);
            conversationRepository.save(conversation);
        }
        return conversationMapper.toDto(conversation);
    }

    private void addConversationReceivers(Conversation conversation, Set<UserDTO> receivers) {
        if(receivers != null) {
            receivers.forEach(receiver -> {
                ConversationReceiver conversationReceiver = new ConversationReceiver();
                conversationReceiver.setId(new ConversationReceiverPK(conversation.getId(), receiver.getId()));
                userService.findUserById(receiver.getId()).ifPresent(conversationReceiver::setReceiver);
                conversationReceiver.setConversation(conversation);
                conversationReceiver.setConversationState(ConversationState.DELIVERED);
                conversation.addConversationReceiver(conversationReceiver);
            });
        }
    }

    /**
     * Get all the conversations.
     *
     * @param pageable the pagination information
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public Page<ConversationDTO> findAll(Pageable pageable) {
        log.debug("Request to get all Conversations");
        return conversationRepository.findAll(pageable)
            .map(conversationMapper::toDto);
    }

    /**
     * Get all the conversations Of Room.
     *
     * @return the list of entities.
     */
    @Override
    public Page<ConversationDTO> findAllByRoom(Pageable pageable, long roomId) {
        return conversationRepository.findAllByRoomId(pageable, roomId).map(conversationMapper::toDto);
    }

    /**
     * Get all the conversations by logged user of Room after join date.
     *
     * @return the list of entities.
     */
    @Override
    public Page<ConversationDTO> findAllByLoggedUserOfRoomAfterJoinDate(Pageable pageable, long roomId) {
        Optional<User> optionalUser = userService.getUserWithAuthorities();
        if (optionalUser.isPresent()) {
            User user = optionalUser.get();
            return conversationRepository.findAllByLoggedUserOfRoomAfterJoinDate(pageable, roomId, user.getId()).map(conversationMapper::toDto);
        }
        return null;
    }

    /**
     * Get Current User Conversations Count By State grouped by Room
     **/
    @Transactional(readOnly = true)
    public List<?> getCurrentUserConversationsCountByStateGroupedByRoom(ConversationState conversationState) {
        log.debug("Request to get current user conversations count by state grouped by room");
        Optional<User> optionalUser = userService.getUserWithAuthorities();
        if (optionalUser.isPresent()) {
            User user = optionalUser.get();
            return conversationRepository.getConversationsCountByReceiverAndStateGroupedByRoom(conversationState, user.getId());
        }
        return new ArrayList<>();
    }

    /**
     * Get Current User Conversations Count By State grouped by Sender
     **/
    @Transactional(readOnly = true)
    public List<?> getCurrentUserConversationsCountByStateGroupedBySender(ConversationState conversationState) {
        log.debug("Request to get current user conversations count by state grouped by sender");
        Optional<User> optionalUser = userService.getUserWithAuthorities();
        if (optionalUser.isPresent()) {
            User user = optionalUser.get();
            return conversationRepository.getConversationsCountByReceiverAndStateGroupedBySender(conversationState, user.getId());
        }
        return new ArrayList<>();
    }

    /**
     * Get Current User Conversations Count By State and Room
     **/
    @Transactional(readOnly = true)
    public int getCurrentUserConversationCountByRoomAndReceiverAndState(ConversationState conversationState, Long roomId) {
        log.debug("Request to get current user conversations count by state and Room");
        Optional<User> optionalUser = userService.getUserWithAuthorities();
        if (optionalUser.isPresent()) {
            User user = optionalUser.get();
            return conversationRepository.getConversationCountByRoomAndReceiverAndState(conversationState, user.getId(), roomId);
        }
        return 0;
    }

    /**
     * Update Current User Conversation State By Room
     **/
    public void updateConversationStateByRoom(ConversationState conversationState, Long roomId) {
        Optional<User> optionalUser = userService.getUserWithAuthorities();
        if (optionalUser.isPresent()) {
            User user = optionalUser.get();
            this.conversationRepository.updateConversationStateByRoom(conversationState, roomId, user.getId());
        }
    }

    /**
     * Get one conversation by id.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<ConversationDTO> findOne(Long id) {
        log.debug("Request to get Conversation : {}", id);
        return conversationRepository.findById(id)
            .map(conversationMapper::toDto);
    }

    /**
     * Delete the conversation by id.
     *
     * @param id the id of the entity
     */
    @Override
    public void delete(Long id) {
        log.debug("Request to delete Conversation : {}", id);
        conversationRepository.deleteById(id);
    }
}
