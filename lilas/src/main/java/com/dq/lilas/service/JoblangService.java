package com.dq.lilas.service;

import com.dq.lilas.service.dto.JoblangDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Joblang}.
 */
public interface JoblangService {
    /**
     * Save a joblang.
     *
     * @param joblangDTO the entity to save.
     * @return the persisted entity.
     */
    JoblangDTO save(JoblangDTO joblangDTO);

    /**
     * Updates a joblang.
     *
     * @param joblangDTO the entity to update.
     * @return the persisted entity.
     */
    JoblangDTO update(JoblangDTO joblangDTO);

    /**
     * Partially updates a joblang.
     *
     * @param joblangDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<JoblangDTO> partialUpdate(JoblangDTO joblangDTO);

    /**
     * Get all the joblangs.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<JoblangDTO> findAll(Pageable pageable);

    /**
     * Get the "id" joblang.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<JoblangDTO> findOne(Long id);

    /**
     * Delete the "id" joblang.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
