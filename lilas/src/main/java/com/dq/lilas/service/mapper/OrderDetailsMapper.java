package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Order;
import com.dq.lilas.domain.OrderDetails;
import com.dq.lilas.service.dto.OrderDTO;
import com.dq.lilas.service.dto.OrderDetailsDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link OrderDetails} and its DTO {@link OrderDetailsDTO}.
 */
@Mapper(componentModel = "spring")
public interface OrderDetailsMapper extends EntityMapper<OrderDetailsDTO, OrderDetails> {
    @Mapping(target = "order", source = "order", qualifiedByName = "orderId")
    OrderDetailsDTO toDto(OrderDetails s);

    @Named("orderId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "orderDetails", ignore = true)
    OrderDTO toDtoOrderId(Order order);
    
    @Override
    @Mapping(target = "emailsNotifications", ignore = true)
    @Mapping(target = "removeEmailsNotifications", ignore = true)
    OrderDetails toEntity(OrderDetailsDTO dto);
}
