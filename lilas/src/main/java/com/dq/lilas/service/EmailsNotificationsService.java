package com.dq.lilas.service;

import com.dq.lilas.service.dto.EmailsNotificationsDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.EmailsNotifications}.
 */
public interface EmailsNotificationsService {
    /**
     * Save a emailsNotifications.
     *
     * @param emailsNotificationsDTO the entity to save.
     * @return the persisted entity.
     */
    EmailsNotificationsDTO save(EmailsNotificationsDTO emailsNotificationsDTO);

    /**
     * Updates a emailsNotifications.
     *
     * @param emailsNotificationsDTO the entity to update.
     * @return the persisted entity.
     */
    EmailsNotificationsDTO update(EmailsNotificationsDTO emailsNotificationsDTO);

    /**
     * Partially updates a emailsNotifications.
     *
     * @param emailsNotificationsDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<EmailsNotificationsDTO> partialUpdate(EmailsNotificationsDTO emailsNotificationsDTO);

    /**
     * Get all the emailsNotifications.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<EmailsNotificationsDTO> findAll(Pageable pageable);

    /**
     * Get the "id" emailsNotifications.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<EmailsNotificationsDTO> findOne(Long id);

    /**
     * Delete the "id" emailsNotifications.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
