package com.dq.lilas.service.dto;

import java.io.Serializable;
import java.time.LocalDate;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.DailyStock} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class DailyStockDTO implements Serializable {

    private Long id;

    private String internalCode;

    private String barcode;

    private String productName;

    private Long stockQty;

    private LocalDate stockDate;

    private Long remainingQuantity;

    private ProductsListDTO productsList;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getInternalCode() {
        return internalCode;
    }

    public void setInternalCode(String internalCode) {
        this.internalCode = internalCode;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Long getStockQty() {
        return stockQty;
    }

    public void setStockQty(Long stockQty) {
        this.stockQty = stockQty;
    }

    public LocalDate getStockDate() {
        return stockDate;
    }

    public void setStockDate(LocalDate stockDate) {
        this.stockDate = stockDate;
    }

    public Long getRemainingQuantity() {
        return remainingQuantity;
    }

    public void setRemainingQuantity(Long remainingQuantity) {
        this.remainingQuantity = remainingQuantity;
    }

    public ProductsListDTO getProductsList() {
        return productsList;
    }

    public void setProductsList(ProductsListDTO productsList) {
        this.productsList = productsList;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DailyStockDTO)) {
            return false;
        }

        DailyStockDTO dailyStockDTO = (DailyStockDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, dailyStockDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "DailyStockDTO{" +
            "id=" + getId() +
            ", internalCode='" + getInternalCode() + "'" +
            ", barcode='" + getBarcode() + "'" +
            ", productName='" + getProductName() + "'" +
            ", stockQty=" + getStockQty() +
            ", stockDate='" + getStockDate() + "'" +
            ", remainingQuantity=" + getRemainingQuantity() +
            ", productsList=" + getProductsList() +
            "}";
    }
}
