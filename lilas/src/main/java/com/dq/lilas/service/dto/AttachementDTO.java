package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Attachement} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class AttachementDTO implements Serializable {

    private Long id;

    @Size(max = 30)
    private String copyId;

    @Size(max = 150)
    private String lblAttachment;

    @Size(max = 50)
    private String idDocAttachment;

    @Size(max = 15)
    private String sizeAttachement;

    @Size(max = 500)
    private String filenameattachment;

    @Size(max = 20)
    private String userattachment;

    @Size(max = 20)
    private String iddecision;

    @Size(max = 20)
    private String idtemplate;

    private Instant datejcattachment;

    private Instant datehjrattachment;

    @Size(max = 20)
    private String idtransfer;

    @Size(max = 20)
    private String idcorresp;

    private Double ordering;

    @Size(max = 10)
    private String levelattachement;

    @Size(max = 20)
    private String idreq;

    private Double orderingscan;

    @Size(max = 20)
    private String iddocext;

    @Size(max = 20)
    private String idleave;

    @Size(max = 15)
    private String configLevel;

    @Size(max = 15)
    private String typeAtach;

    @Size(max = 15)
    private String idDirectOrder;

    @Size(max = 100)
    private String pathFile;

    private Integer version;

    private OrderDTO order;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCopyId() {
        return copyId;
    }

    public void setCopyId(String copyId) {
        this.copyId = copyId;
    }

    public String getLblAttachment() {
        return lblAttachment;
    }

    public void setLblAttachment(String lblAttachment) {
        this.lblAttachment = lblAttachment;
    }

    public String getIdDocAttachment() {
        return idDocAttachment;
    }

    public void setIdDocAttachment(String idDocAttachment) {
        this.idDocAttachment = idDocAttachment;
    }

    public String getSizeAttachement() {
        return sizeAttachement;
    }

    public void setSizeAttachement(String sizeAttachement) {
        this.sizeAttachement = sizeAttachement;
    }

    public String getFilenameattachment() {
        return filenameattachment;
    }

    public void setFilenameattachment(String filenameattachment) {
        this.filenameattachment = filenameattachment;
    }

    public String getUserattachment() {
        return userattachment;
    }

    public void setUserattachment(String userattachment) {
        this.userattachment = userattachment;
    }

    public String getIddecision() {
        return iddecision;
    }

    public void setIddecision(String iddecision) {
        this.iddecision = iddecision;
    }

    public String getIdtemplate() {
        return idtemplate;
    }

    public void setIdtemplate(String idtemplate) {
        this.idtemplate = idtemplate;
    }

    public Instant getDatejcattachment() {
        return datejcattachment;
    }

    public void setDatejcattachment(Instant datejcattachment) {
        this.datejcattachment = datejcattachment;
    }

    public Instant getDatehjrattachment() {
        return datehjrattachment;
    }

    public void setDatehjrattachment(Instant datehjrattachment) {
        this.datehjrattachment = datehjrattachment;
    }

    public String getIdtransfer() {
        return idtransfer;
    }

    public void setIdtransfer(String idtransfer) {
        this.idtransfer = idtransfer;
    }

    public String getIdcorresp() {
        return idcorresp;
    }

    public void setIdcorresp(String idcorresp) {
        this.idcorresp = idcorresp;
    }

    public Double getOrdering() {
        return ordering;
    }

    public void setOrdering(Double ordering) {
        this.ordering = ordering;
    }

    public String getLevelattachement() {
        return levelattachement;
    }

    public void setLevelattachement(String levelattachement) {
        this.levelattachement = levelattachement;
    }

    public String getIdreq() {
        return idreq;
    }

    public void setIdreq(String idreq) {
        this.idreq = idreq;
    }

    public Double getOrderingscan() {
        return orderingscan;
    }

    public void setOrderingscan(Double orderingscan) {
        this.orderingscan = orderingscan;
    }

    public String getIddocext() {
        return iddocext;
    }

    public void setIddocext(String iddocext) {
        this.iddocext = iddocext;
    }

    public String getIdleave() {
        return idleave;
    }

    public void setIdleave(String idleave) {
        this.idleave = idleave;
    }

    public String getConfigLevel() {
        return configLevel;
    }

    public void setConfigLevel(String configLevel) {
        this.configLevel = configLevel;
    }

    public String getTypeAtach() {
        return typeAtach;
    }

    public void setTypeAtach(String typeAtach) {
        this.typeAtach = typeAtach;
    }

    public String getIdDirectOrder() {
        return idDirectOrder;
    }

    public void setIdDirectOrder(String idDirectOrder) {
        this.idDirectOrder = idDirectOrder;
    }

    public String getPathFile() {
        return pathFile;
    }

    public void setPathFile(String pathFile) {
        this.pathFile = pathFile;
    }

    public Integer getVersion() {
        return version;
    }

    public void setVersion(Integer version) {
        this.version = version;
    }

    public OrderDTO getOrder() {
        return order;
    }

    public void setOrder(OrderDTO order) {
        this.order = order;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof AttachementDTO)) {
            return false;
        }

        AttachementDTO attachementDTO = (AttachementDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, attachementDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "AttachementDTO{" +
            "id=" + getId() +
            ", copyId='" + getCopyId() + "'" +
            ", lblAttachment='" + getLblAttachment() + "'" +
            ", idDocAttachment='" + getIdDocAttachment() + "'" +
            ", sizeAttachement='" + getSizeAttachement() + "'" +
            ", filenameattachment='" + getFilenameattachment() + "'" +
            ", userattachment='" + getUserattachment() + "'" +
            ", iddecision='" + getIddecision() + "'" +
            ", idtemplate='" + getIdtemplate() + "'" +
            ", datejcattachment='" + getDatejcattachment() + "'" +
            ", datehjrattachment='" + getDatehjrattachment() + "'" +
            ", idtransfer='" + getIdtransfer() + "'" +
            ", idcorresp='" + getIdcorresp() + "'" +
            ", ordering=" + getOrdering() +
            ", levelattachement='" + getLevelattachement() + "'" +
            ", idreq='" + getIdreq() + "'" +
            ", orderingscan=" + getOrderingscan() +
            ", iddocext='" + getIddocext() + "'" +
            ", idleave='" + getIdleave() + "'" +
            ", configLevel='" + getConfigLevel() + "'" +
            ", typeAtach='" + getTypeAtach() + "'" +
            ", idDirectOrder='" + getIdDirectOrder() + "'" +
            ", pathFile='" + getPathFile() + "'" +
            ", version=" + getVersion() +
            ", order=" + getOrder() +
            "}";
    }
}
