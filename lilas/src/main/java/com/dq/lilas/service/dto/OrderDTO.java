package com.dq.lilas.service.dto;

import com.dq.lilas.domain.enumeration.OrderStatus;
import jakarta.persistence.Column;
import jakarta.persistence.Lob;
import java.io.Serializable;
import java.util.HashSet;
import java.time.LocalDate;
import java.util.Objects;
import java.util.Set;

/**
 * A DTO for the {@link com.dq.lilas.domain.Order} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class OrderDTO implements Serializable {

    private Long id;

    private String hearderJson;

    private Integer rank;

    private OrderStatus status;

    private Boolean locked;

    private String orderNumber;

    private String footerJson;

    private Long brandId;
    private String company;
    private String fiscaleId;
    private String deliveryLocation;
    private LocalDate orderDate;
    private LocalDate deliveryDate;
    private String rayon;
    private String grVariantes;
    private String clientName;
    private String distinataire;
    private Integer lineCount;
    private Integer packsNb;
    private Double purchaseAmount;
    private Double volume;
    private Double grossWeight;
    private String clientCode;
    private String contract;
    private String sector;
    private String commissionar;
    @Lob
    private String observation;
    @Lob
    private String billingInfo;
    private Double totalTtc;
    private Double totalHt;
    private Double totalQuantity;
    private Double taxBase;
    private Double taxAmount;


    public String getCompany() {
        return company;
    }

    public void setCompany(String company) {
        this.company = company;
    }

    public String getFiscaleId() {
        return fiscaleId;
    }

    public void setFiscaleId(String fiscaleId) {
        this.fiscaleId = fiscaleId;
    }

    public String getDeliveryLocation() {
        return deliveryLocation;
    }

    public void setDeliveryLocation(String deliveryLocation) {
        this.deliveryLocation = deliveryLocation;
    }

    public LocalDate getOrderDate() {
        return orderDate;
    }

    public void setOrderDate(LocalDate orderDate) {
        this.orderDate = orderDate;
    }

    public LocalDate getDeliveryDate() {
        return deliveryDate;
    }

    public void setDeliveryDate(LocalDate deliveryDate) {
        this.deliveryDate = deliveryDate;
    }

    public String getRayon() {
        return rayon;
    }

    public void setRayon(String rayon) {
        this.rayon = rayon;
    }

    public String getGrVariantes() {
        return grVariantes;
    }

    public void setGrVariantes(String grVariantes) {
        this.grVariantes = grVariantes;
    }

    public String getClientName() {
        return clientName;
    }

    public void setClientName(String clientName) {
        this.clientName = clientName;
    }

    public String getDistinataire() {
        return distinataire;
    }

    public void setDistinataire(String distinataire) {
        this.distinataire = distinataire;
    }

    public Integer getLineCount() {
        return lineCount;
    }

    public void setLineCount(Integer lineCount) {
        this.lineCount = lineCount;
    }

    public Integer getPacksNb() {
        return packsNb;
    }

    public void setPacksNb(Integer packsNb) {
        this.packsNb = packsNb;
    }

    public Double getPurchaseAmount() {
        return purchaseAmount;
    }

    public void setPurchaseAmount(Double purchaseAmount) {
        this.purchaseAmount = purchaseAmount;
    }

    public Double getVolume() {
        return volume;
    }

    public void setVolume(Double volume) {
        this.volume = volume;
    }

    public Double getGrossWeight() {
        return grossWeight;
    }

    public void setGrossWeight(Double grossWeight) {
        this.grossWeight = grossWeight;
    }

    public String getClientCode() {
        return clientCode;
    }

    public void setClientCode(String clientCode) {
        this.clientCode = clientCode;
    }

    public String getContract() {
        return contract;
    }

    public void setContract(String contract) {
        this.contract = contract;
    }

    public String getSector() {
        return sector;
    }

    public void setSector(String sector) {
        this.sector = sector;
    }

    public String getCommissionar() {
        return commissionar;
    }

    public void setCommissionar(String commissionar) {
        this.commissionar = commissionar;
    }

    public String getObservation() {
        return observation;
    }

    public void setObservation(String observation) {
        this.observation = observation;
    }

    public String getBillingInfo() {
        return billingInfo;
    }

    public void setBillingInfo(String billingInfo) {
        this.billingInfo = billingInfo;
    }

    public Double getTotalTtc() {
        return totalTtc;
    }

    public void setTotalTtc(Double totalTtc) {
        this.totalTtc = totalTtc;
    }

    public Double getTotalHt() {
        return totalHt;
    }

    public void setTotalHt(Double totalHt) {
        this.totalHt = totalHt;
    }

    public Double getTotalQuantity() {
        return totalQuantity;
    }

    public void setTotalQuantity(Double totalQuantity) {
        this.totalQuantity = totalQuantity;
    }

    public Double getTaxBase() {
        return taxBase;
    }

    public void setTaxBase(Double taxBase) {
        this.taxBase = taxBase;
    }

    public Double getTaxAmount() {
        return taxAmount;
    }

    public void setTaxAmount(Double taxAmount) {
        this.taxAmount = taxAmount;
    }

    private DailyBatchesDTO dailyBatches;

    private GmsClientsDTO gmsClients;

    private TemplateConditionsDTO templateConditions;

    private MailsDTO mails;

    private EmployeeDTO employee;
    
    private Set<OrderDetailsDTO> orderDetails = new HashSet<>();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getHearderJson() {
        return hearderJson;
    }

    public void setHearderJson(String hearderJson) {
        this.hearderJson = hearderJson;
    }

    public Integer getRank() {
        return rank;
    }

    public void setRank(Integer rank) {
        this.rank = rank;
    }

    public OrderStatus getStatus() {
        return status;
    }

    public void setStatus(OrderStatus status) {
        this.status = status;
    }

    public String getFooterJson() {
        return footerJson;
    }

    public void setFooterJson(String footerJson) {
        this.footerJson = footerJson;
    }

    public Long getBrandId() {
        return brandId;
    }

    public void setBrandId(Long brandId) {
        this.brandId = brandId;
    }

    public Boolean getLocked() {
        return locked;
    }

    public void setLocked(Boolean locked) {
        this.locked = locked;
    }

    public String getOrderNumber() {
        return orderNumber;
    }

    public void setOrderNumber(String orderNumber) {
        this.orderNumber = orderNumber;
    }

    public DailyBatchesDTO getDailyBatches() {
        return dailyBatches;
    }

    public void setDailyBatches(DailyBatchesDTO dailyBatches) {
        this.dailyBatches = dailyBatches;
    }

    public GmsClientsDTO getGmsClients() {
        return gmsClients;
    }

    public void setGmsClients(GmsClientsDTO gmsClients) {
        this.gmsClients = gmsClients;
    }

    public TemplateConditionsDTO getTemplateConditions() {
        return templateConditions;
    }

    public void setTemplateConditions(TemplateConditionsDTO templateConditions) {
        this.templateConditions = templateConditions;
    }

    public MailsDTO getMails() {
        return mails;
    }

    public void setMails(MailsDTO mails) {
        this.mails = mails;
    }

    public EmployeeDTO getEmployee() {
        return employee;
    }

    public void setEmployee(EmployeeDTO employee) {
        this.employee = employee;
    }
    
    public Set<OrderDetailsDTO> getOrderDetails() {
        return orderDetails;
    }

    public void setOrderDetails(Set<OrderDetailsDTO> orderDetails) {
        this.orderDetails = orderDetails;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof OrderDTO)) {
            return false;
        }

        OrderDTO orderDTO = (OrderDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, orderDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "OrderDTO{" +
            "id=" + getId() +
            ", hearderJson='" + getHearderJson() + "'" +
            ", rank=" + getRank() +
            ", status='" + getStatus() + "'" +
            ", locked='" + getLocked() + "'" +
            ", orderNumber='" + getOrderNumber() + "'" +
            ", footerJson='" + getFooterJson() + "'" +
            ", footerJson='" + getFooterJson() + "'" +
            ", company='" + getCompany() + "'" +
            ", brandId='" + getBrandId() + "'" +
            ", fiscaleId='" + getFiscaleId() + "'" +
            ", deliveryLocation='" + getDeliveryLocation() + "'" +
            ", orderDate=" + getOrderDate() +
            ", deliveryDate=" + getDeliveryDate() +
            ", rayon='" + getRayon() + "'" +
            ", grVariantes='" + getGrVariantes() + "'" +
            ", clientName='" + getClientName() + "'" +
            ", distinataire='" + getDistinataire() + "'" +
            ", lineCount=" + getLineCount() +
            ", packsNb=" + getPacksNb() +
            ", purchaseAmount=" + getPurchaseAmount() +
            ", volume=" + getVolume() +
            ", grossWeight=" + getGrossWeight() +
            ", clientCode='" + getClientCode() + "'" +
            ", contract='" + getContract() + "'" +
            ", sector='" + getSector() + "'" +
            ", commissionar='" + getCommissionar() + "'" +
            ", observation='" + getObservation() + "'" +
            ", billingInfo='" + getBillingInfo() + "'" +
            ", totalTtc=" + getTotalTtc() +
            ", totalHt=" + getTotalHt() +
            ", totalQuantity=" + getTotalQuantity() +
            ", taxBase=" + getTaxBase() +
            ", taxAmount=" + getTaxAmount() +
            ", dailyBatches=" + getDailyBatches() +
            ", gmsClients=" + getGmsClients() +
            ", templateConditions=" + getTemplateConditions() +
            ", mails=" + getMails() +
            ", employee=" + getEmployee() +
            ", orderDetails=" + getOrderDetails() +
            "}";
    }
}
