package com.dq.lilas.service.impl;

import com.dq.lilas.domain.EmployeeCompanyPermission;
import com.dq.lilas.repository.EmployeeCompanyPermissionRepository;
import com.dq.lilas.service.EmployeeCompanyPermissionService;
import com.dq.lilas.service.dto.EmployeeCompanyPermissionDTO;
import com.dq.lilas.service.mapper.EmployeeCompanyPermissionMapper;
import java.util.LinkedList;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.EmployeeCompanyPermission}.
 */
@Service
@Transactional
public class EmployeeCompanyPermissionServiceImpl implements EmployeeCompanyPermissionService {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeeCompanyPermissionServiceImpl.class);

    private final EmployeeCompanyPermissionRepository employeeCompanyPermissionRepository;

    private final EmployeeCompanyPermissionMapper employeeCompanyPermissionMapper;

    public EmployeeCompanyPermissionServiceImpl(
        EmployeeCompanyPermissionRepository employeeCompanyPermissionRepository,
        EmployeeCompanyPermissionMapper employeeCompanyPermissionMapper
    ) {
        this.employeeCompanyPermissionRepository = employeeCompanyPermissionRepository;
        this.employeeCompanyPermissionMapper = employeeCompanyPermissionMapper;
    }

    @Override
    public EmployeeCompanyPermissionDTO save(EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO) {
        LOG.debug("Request to save EmployeeCompanyPermission : {}", employeeCompanyPermissionDTO);
        EmployeeCompanyPermission employeeCompanyPermission = employeeCompanyPermissionMapper.toEntity(employeeCompanyPermissionDTO);
        employeeCompanyPermission = employeeCompanyPermissionRepository.save(employeeCompanyPermission);
        return employeeCompanyPermissionMapper.toDto(employeeCompanyPermission);
    }

    @Override
    public EmployeeCompanyPermissionDTO update(EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO) {
        LOG.debug("Request to update EmployeeCompanyPermission : {}", employeeCompanyPermissionDTO);
        EmployeeCompanyPermission employeeCompanyPermission = employeeCompanyPermissionMapper.toEntity(employeeCompanyPermissionDTO);
        employeeCompanyPermission = employeeCompanyPermissionRepository.save(employeeCompanyPermission);
        return employeeCompanyPermissionMapper.toDto(employeeCompanyPermission);
    }

    @Override
    public Optional<EmployeeCompanyPermissionDTO> partialUpdate(EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO) {
        LOG.debug("Request to partially update EmployeeCompanyPermission : {}", employeeCompanyPermissionDTO);

        return employeeCompanyPermissionRepository
            .findById(employeeCompanyPermissionDTO.getId())
            .map(existingUserCompanyPermission -> {
                employeeCompanyPermissionMapper.partialUpdate(existingUserCompanyPermission, employeeCompanyPermissionDTO);

                return existingUserCompanyPermission;
            })
            .map(employeeCompanyPermissionRepository::save)
            .map(employeeCompanyPermissionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<EmployeeCompanyPermissionDTO> findAll() {
        LOG.debug("Request to get all UserCompanyPermissions");
        return employeeCompanyPermissionRepository
            .findAll()
            .stream()
            .map(employeeCompanyPermissionMapper::toDto)
            .collect(Collectors.toCollection(LinkedList::new));
    }

    public Page<EmployeeCompanyPermissionDTO> findAllWithEagerRelationships(Pageable pageable) {
        return employeeCompanyPermissionRepository.findAllWithEagerRelationships(pageable).map(employeeCompanyPermissionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EmployeeCompanyPermissionDTO> findOne(Long id) {
        LOG.debug("Request to get EmployeeCompanyPermission : {}", id);
        return employeeCompanyPermissionRepository.findOneWithEagerRelationships(id).map(employeeCompanyPermissionMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete EmployeeCompanyPermission : {}", id);
        employeeCompanyPermissionRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public List<Long> findCompanyIdsByEmployeeId(Long employeeId) {
        return employeeCompanyPermissionRepository.findCompanyIdsByEmployeeId(employeeId);
    }}
