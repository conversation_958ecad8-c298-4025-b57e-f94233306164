package com.dq.lilas.service.dto;

import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.TemplateConditions} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class TemplateConditionsDTO implements Serializable {

    private Long id;

    private String name;

    private EmployeeDTO employee;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public EmployeeDTO getEmployee() {
        return employee;
    }

    public void setEmployee(EmployeeDTO employee) {
        this.employee = employee;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TemplateConditionsDTO)) {
            return false;
        }

        TemplateConditionsDTO templateConditionsDTO = (TemplateConditionsDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, templateConditionsDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "TemplateConditionsDTO{" +
            "id=" + getId() +
            ", name='" + getName() + "'" +
            ", employee=" + getEmployee() +
            "}";
    }
}
