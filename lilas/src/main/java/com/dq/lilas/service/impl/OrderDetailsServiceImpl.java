package com.dq.lilas.service.impl;

import com.dq.lilas.domain.OrderDetails;
import com.dq.lilas.domain.enumeration.OrderDetailsStatus;
import com.dq.lilas.repository.OrderDetailsRepository;
import com.dq.lilas.service.OrderDetailsService;
import com.dq.lilas.service.dto.OrderDetailsDTO;
import com.dq.lilas.service.mapper.OrderDetailsMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.OrderDetails}.
 */
@Service
@Transactional
public class OrderDetailsServiceImpl implements OrderDetailsService {

    private static final Logger LOG = LoggerFactory.getLogger(OrderDetailsServiceImpl.class);

    private final OrderDetailsRepository orderDetailsRepository;

    private final OrderDetailsMapper orderDetailsMapper;

    public OrderDetailsServiceImpl(OrderDetailsRepository orderDetailsRepository, OrderDetailsMapper orderDetailsMapper) {
        this.orderDetailsRepository = orderDetailsRepository;
        this.orderDetailsMapper = orderDetailsMapper;
    }

    @Override
    public OrderDetailsDTO save(OrderDetailsDTO orderDetailsDTO) {
        LOG.debug("Request to save OrderDetails : {}", orderDetailsDTO);
        OrderDetails orderDetails = orderDetailsMapper.toEntity(orderDetailsDTO);
        orderDetails = orderDetailsRepository.save(orderDetails);
        return orderDetailsMapper.toDto(orderDetails);
    }

    @Override
    public OrderDetailsDTO update(OrderDetailsDTO orderDetailsDTO) {
        LOG.debug("Request to update OrderDetails : {}", orderDetailsDTO);
        OrderDetails orderDetails = orderDetailsMapper.toEntity(orderDetailsDTO);
        orderDetails = orderDetailsRepository.save(orderDetails);
        return orderDetailsMapper.toDto(orderDetails);
    }

    @Override
    public Optional<OrderDetailsDTO> partialUpdate(OrderDetailsDTO orderDetailsDTO) {
        LOG.debug("Request to partially update OrderDetails : {}", orderDetailsDTO);

        return orderDetailsRepository
            .findById(orderDetailsDTO.getId())
            .map(existingOrderDetails -> {
                orderDetailsMapper.partialUpdate(existingOrderDetails, orderDetailsDTO);

                return existingOrderDetails;
            })
            .map(orderDetailsRepository::save)
            .map(orderDetailsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<OrderDetailsDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all OrderDetails");
        return orderDetailsRepository.findAll(pageable).map(orderDetailsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<OrderDetailsDTO> findOne(Long id) {
        LOG.debug("Request to get OrderDetails : {}", id);
        return orderDetailsRepository.findById(id).map(orderDetailsMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete OrderDetails : {}", id);
        orderDetailsRepository.deleteById(id);
    }

    public OrderDetailsDTO deleteOrderLine(Long idOrderDetails) {
        LOG.debug("Request to delete Order Line : {}", idOrderDetails);
        Optional<OrderDetails> orderDetails = orderDetailsRepository.findById(idOrderDetails);
        if (orderDetails.isEmpty()) {
            LOG.warn("OrderDetails with id {} not found", idOrderDetails);
            return null;
        }
        orderDetails.ifPresent(od -> {;
            od.setStatus(OrderDetailsStatus.CANCELLED);
            orderDetailsRepository.save(od);
        });
        return orderDetailsMapper.toDto(orderDetails.orElse(null));
    }


    public OrderDetailsDTO validateOrderLine(Long idOrderDetails) {
        LOG.debug("Request to validate orderLine : {}", idOrderDetails);
        Optional<OrderDetails> orderDetails = orderDetailsRepository.findById(idOrderDetails);
        if (orderDetails.isEmpty()) {
            LOG.warn("OrderDetails with id {} not found", idOrderDetails);
            return null;
        }
        orderDetails.ifPresent(od -> {;
            od.setStatus(OrderDetailsStatus.VALIDATED);
            orderDetailsRepository.save(od);
        });
        return orderDetailsMapper.toDto(orderDetails.orElse(null));
    }

}
