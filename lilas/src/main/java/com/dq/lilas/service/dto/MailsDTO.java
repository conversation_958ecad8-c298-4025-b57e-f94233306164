package com.dq.lilas.service.dto;

import com.dq.lilas.domain.enumeration.MailType;
import jakarta.persistence.Lob;
import java.io.Serializable;
import java.time.Instant;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Mails} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class MailsDTO implements Serializable {

    private Long id;

    private Instant maildate;

    private String subject;

    @Lob
    private String mailbody;

    private String recipient;

    private String sender;

    private MailType mailType;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Instant getMaildate() {
        return maildate;
    }

    public void setMaildate(Instant maildate) {
        this.maildate = maildate;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getMailbody() {
        return mailbody;
    }

    public void setMailbody(String mailbody) {
        this.mailbody = mailbody;
    }

    public String getRecipient() {
        return recipient;
    }

    public void setRecipient(String recipient) {
        this.recipient = recipient;
    }

    public String getSender() {
        return sender;
    }

    public void setSender(String sender) {
        this.sender = sender;
    }

    public MailType getMailType() {
        return mailType;
    }

    public void setMailType(MailType mailType) {
        this.mailType = mailType;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof MailsDTO)) {
            return false;
        }

        MailsDTO mailsDTO = (MailsDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, mailsDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "MailsDTO{" +
            "id=" + getId() +
            ", maildate='" + getMaildate() + "'" +
            ", subject='" + getSubject() + "'" +
            ", mailbody='" + getMailbody() + "'" +
            ", recipient='" + getRecipient() + "'" +
            ", sender='" + getSender() + "'" +
            ", mailType='" + getMailType() + "'" +
            "}";
    }
}
