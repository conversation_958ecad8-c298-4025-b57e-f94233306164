package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Mails;
import com.dq.lilas.repository.MailsRepository;
import com.dq.lilas.service.MailsService;
import com.dq.lilas.service.dto.MailsDTO;
import com.dq.lilas.service.mapper.MailsMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Mails}.
 */
@Service
@Transactional
public class MailsServiceImpl implements MailsService {

    private static final Logger LOG = LoggerFactory.getLogger(MailsServiceImpl.class);

    private final MailsRepository mailsRepository;

    private final MailsMapper mailsMapper;

    public MailsServiceImpl(MailsRepository mailsRepository, MailsMapper mailsMapper) {
        this.mailsRepository = mailsRepository;
        this.mailsMapper = mailsMapper;
    }

    @Override
    public MailsDTO save(MailsDTO mailsDTO) {
        LOG.debug("Request to save Mails : {}", mailsDTO);
        Mails mails = mailsMapper.toEntity(mailsDTO);
        mails = mailsRepository.save(mails);
        return mailsMapper.toDto(mails);
    }

    @Override
    public MailsDTO update(MailsDTO mailsDTO) {
        LOG.debug("Request to update Mails : {}", mailsDTO);
        Mails mails = mailsMapper.toEntity(mailsDTO);
        mails = mailsRepository.save(mails);
        return mailsMapper.toDto(mails);
    }

    @Override
    public Optional<MailsDTO> partialUpdate(MailsDTO mailsDTO) {
        LOG.debug("Request to partially update Mails : {}", mailsDTO);

        return mailsRepository
            .findById(mailsDTO.getId())
            .map(existingMails -> {
                mailsMapper.partialUpdate(existingMails, mailsDTO);

                return existingMails;
            })
            .map(mailsRepository::save)
            .map(mailsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<MailsDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Mails");
        return mailsRepository.findAll(pageable).map(mailsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<MailsDTO> findOne(Long id) {
        LOG.debug("Request to get Mails : {}", id);
        return mailsRepository.findById(id).map(mailsMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Mails : {}", id);
        mailsRepository.deleteById(id);
    }
}
