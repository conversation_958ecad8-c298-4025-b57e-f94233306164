package com.dq.lilas.service;

import com.dq.lilas.domain.DailyStock;
import com.dq.lilas.service.dto.DailyStockDTO;

import java.time.LocalDate;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link DailyStock}.
 */
public interface DailyStockService {
    /**
     * Save a dailyStock.
     *
     * @param dailyStockDTO the entity to save.
     * @return the persisted entity.
     */
    DailyStockDTO save(DailyStockDTO dailyStockDTO);

    /**
     * Updates a dailyStock.
     *
     * @param dailyStockDTO the entity to update.
     * @return the persisted entity.
     */
    DailyStockDTO update(DailyStockDTO dailyStockDTO);

    /**
     * Partially updates a dailyStock.
     *
     * @param dailyStockDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<DailyStockDTO> partialUpdate(DailyStockDTO dailyStockDTO);

    /**
     * Get all the dailyStocks.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<DailyStockDTO> findAll(Pageable pageable);

    /**
     * Get the "id" dailyStock.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<DailyStockDTO> findOne(Long id);

    /**
     * Delete the "id" dailyStock.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);


    /**
     * Updates a dailyStock by internal code and date.
     *
     * @param internalCode the internal code of the stock.
     * @param stockDate the date of the stock.
     * @param quantityOrdered the quantity ordered to update stock.
     *
     * @return the persisted entity.
     */
    DailyStockDTO updateByInternalCodeAndDate(String internalCode, LocalDate stockDate, long quantityOrdered);

    /**
     * Updates a dailyStock by internal code and date.
     *
     * @param internalCode the internal code of the stock.
     * @param stockDate the date of the stock.
     * @param quantityOrdered the quantity ordered to update stock.
     * @param quantityUpdated the quantity updated to restore stock.
     *
     *
     * @return the persisted entity.
     */
    DailyStockDTO restoreStockByInternalCodeAndDate(String internalCode, LocalDate stockDate, long quantityOrdered, long quantityUpdated);


    /**
     * Get the remaining quantity of a dailyStock by internal code and date.
     *
     * @param internalCode the internal code of the stock.
     * @param stockDate the date of the stock.
     * @return the remaining quantity.
     */
    long getRemainingQuantityByInternalCodeAndDate(String internalCode, LocalDate stockDate);

}
