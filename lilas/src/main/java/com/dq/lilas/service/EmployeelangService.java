package com.dq.lilas.service;

import com.dq.lilas.service.dto.EmployeelangDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Employeelang}.
 */
public interface EmployeelangService {
    /**
     * Save a employeelang.
     *
     * @param employeelangDTO the entity to save.
     * @return the persisted entity.
     */
    EmployeelangDTO save(EmployeelangDTO employeelangDTO);

    /**
     * Updates a employeelang.
     *
     * @param employeelangDTO the entity to update.
     * @return the persisted entity.
     */
    EmployeelangDTO update(EmployeelangDTO employeelangDTO);

    /**
     * Partially updates a employeelang.
     *
     * @param employeelangDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<EmployeelangDTO> partialUpdate(EmployeelangDTO employeelangDTO);

    /**
     * Get all the employeelangs.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<EmployeelangDTO> findAll(Pageable pageable);

    /**
     * Get the "id" employeelang.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<EmployeelangDTO> findOne(Long id);

    /**
     * Delete the "id" employeelang.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
