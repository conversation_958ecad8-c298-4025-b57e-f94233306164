package com.dq.lilas.service.dto;

import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Typecorrespondencelang} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class TypecorrespondencelangDTO implements Serializable {

    private Long id;

    private String lbl;

    private String lang;

    private String abbreviated;

    private TypecorrespondenceDTO typecorrespondence;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLbl() {
        return lbl;
    }

    public void setLbl(String lbl) {
        this.lbl = lbl;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getAbbreviated() {
        return abbreviated;
    }

    public void setAbbreviated(String abbreviated) {
        this.abbreviated = abbreviated;
    }

    public TypecorrespondenceDTO getTypecorrespondence() {
        return typecorrespondence;
    }

    public void setTypecorrespondence(TypecorrespondenceDTO typecorrespondence) {
        this.typecorrespondence = typecorrespondence;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof TypecorrespondencelangDTO)) {
            return false;
        }

        TypecorrespondencelangDTO typecorrespondencelangDTO = (TypecorrespondencelangDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, typecorrespondencelangDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "TypecorrespondencelangDTO{" +
            "id=" + getId() +
            ", lbl='" + getLbl() + "'" +
            ", lang='" + getLang() + "'" +
            ", abbreviated='" + getAbbreviated() + "'" +
            ", typecorrespondence=" + getTypecorrespondence() +
            "}";
    }
}
