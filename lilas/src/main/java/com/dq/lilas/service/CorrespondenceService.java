package com.dq.lilas.service;

import com.dq.lilas.service.dto.CorrespondenceDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Correspondence}.
 */
public interface CorrespondenceService {
    /**
     * Save a correspondence.
     *
     * @param correspondenceDTO the entity to save.
     * @return the persisted entity.
     */
    CorrespondenceDTO save(CorrespondenceDTO correspondenceDTO);

    /**
     * Updates a correspondence.
     *
     * @param correspondenceDTO the entity to update.
     * @return the persisted entity.
     */
    CorrespondenceDTO update(CorrespondenceDTO correspondenceDTO);

    /**
     * Partially updates a correspondence.
     *
     * @param correspondenceDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<CorrespondenceDTO> partialUpdate(CorrespondenceDTO correspondenceDTO);

    /**
     * Get all the correspondences.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<CorrespondenceDTO> findAll(Pageable pageable);

    /**
     * Get the "id" correspondence.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<CorrespondenceDTO> findOne(Long id);

    /**
     * Delete the "id" correspondence.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
