package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Employeelang;
import com.dq.lilas.repository.EmployeelangRepository;
import com.dq.lilas.service.EmployeelangService;
import com.dq.lilas.service.dto.EmployeelangDTO;
import com.dq.lilas.service.mapper.EmployeelangMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Employeelang}.
 */
@Service
@Transactional
public class EmployeelangServiceImpl implements EmployeelangService {

    private static final Logger LOG = LoggerFactory.getLogger(EmployeelangServiceImpl.class);

    private final EmployeelangRepository employeelangRepository;

    private final EmployeelangMapper employeelangMapper;

    public EmployeelangServiceImpl(EmployeelangRepository employeelangRepository, EmployeelangMapper employeelangMapper) {
        this.employeelangRepository = employeelangRepository;
        this.employeelangMapper = employeelangMapper;
    }

    @Override
    public EmployeelangDTO save(EmployeelangDTO employeelangDTO) {
        LOG.debug("Request to save Employeelang : {}", employeelangDTO);
        Employeelang employeelang = employeelangMapper.toEntity(employeelangDTO);
        employeelang = employeelangRepository.save(employeelang);
        return employeelangMapper.toDto(employeelang);
    }

    @Override
    public EmployeelangDTO update(EmployeelangDTO employeelangDTO) {
        LOG.debug("Request to update Employeelang : {}", employeelangDTO);
        Employeelang employeelang = employeelangMapper.toEntity(employeelangDTO);
        employeelang = employeelangRepository.save(employeelang);
        return employeelangMapper.toDto(employeelang);
    }

    @Override
    public Optional<EmployeelangDTO> partialUpdate(EmployeelangDTO employeelangDTO) {
        LOG.debug("Request to partially update Employeelang : {}", employeelangDTO);

        return employeelangRepository
            .findById(employeelangDTO.getId())
            .map(existingEmployeelang -> {
                employeelangMapper.partialUpdate(existingEmployeelang, employeelangDTO);

                return existingEmployeelang;
            })
            .map(employeelangRepository::save)
            .map(employeelangMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<EmployeelangDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Employeelangs");
        return employeelangRepository.findAll(pageable).map(employeelangMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<EmployeelangDTO> findOne(Long id) {
        LOG.debug("Request to get Employeelang : {}", id);
        return employeelangRepository.findById(id).map(employeelangMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Employeelang : {}", id);
        employeelangRepository.deleteById(id);
    }
}
