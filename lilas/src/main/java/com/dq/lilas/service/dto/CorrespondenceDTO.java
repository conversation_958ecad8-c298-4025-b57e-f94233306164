package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.time.ZonedDateTime;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Correspondence} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class CorrespondenceDTO implements Serializable {

    private Long id;

    @Size(max = 50)
    private String ordernbr;

    @Size(max = 15)
    private String numcopy;

    @Size(max = 500)
    private String doclink;

    @Size(max = 200)
    private String pagenbr;

    @Size(max = 5)
    private String highlevel;

    @Size(max = 10)
    private String confidentiel;

    @Size(max = 5)
    private String priority;

    private String obs;

    @NotNull
    @Size(max = 5)
    private String category;

    @Size(max = 5)
    private String status;

    private ZonedDateTime datejc;

    private ZonedDateTime datejcsend;

    @Size(max = 1)
    private String typereceive;

    @Size(max = 15)
    private String typecopy;

    @Size(max = 500)
    private String refsnd;

    @Size(max = 4000)
    private String text;

    @Size(max = 4)
    private String docyear;

    @Size(max = 10)
    private String datehjrsave;

    private ZonedDateTime datejcsave;

    private String oldOrderNumber;

    @NotNull
    @Size(max = 1000)
    private String subject;

    @Size(max = 200)
    private String attach;

    @Size(max = 50)
    private String ordernbradrsbook;

    private Integer seqkeyadrsbook;

    @Size(max = 500)
    private String useradrsbook;

    @Size(max = 15)
    private String cityzenncard;

    @Size(max = 15)
    private String cityzenphone;

    @Size(max = 1)
    private String sms;

    @Size(max = 5)
    private String incident;

    @Size(max = 1)
    private String checkFavorite;

    private String companyId;

    private EmployeeDTO employee;

    private UnitDTO unit;

    private DeliverymodeDTO deliverymode;

    private TypecorrespondenceDTO typecorrespondence;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getOrdernbr() {
        return ordernbr;
    }

    public void setOrdernbr(String ordernbr) {
        this.ordernbr = ordernbr;
    }

    public String getNumcopy() {
        return numcopy;
    }

    public void setNumcopy(String numcopy) {
        this.numcopy = numcopy;
    }

    public String getDoclink() {
        return doclink;
    }

    public void setDoclink(String doclink) {
        this.doclink = doclink;
    }

    public String getPagenbr() {
        return pagenbr;
    }

    public void setPagenbr(String pagenbr) {
        this.pagenbr = pagenbr;
    }

    public String getHighlevel() {
        return highlevel;
    }

    public void setHighlevel(String highlevel) {
        this.highlevel = highlevel;
    }

    public String getConfidentiel() {
        return confidentiel;
    }

    public void setConfidentiel(String confidentiel) {
        this.confidentiel = confidentiel;
    }

    public String getPriority() {
        return priority;
    }

    public void setPriority(String priority) {
        this.priority = priority;
    }

    public String getObs() {
        return obs;
    }

    public void setObs(String obs) {
        this.obs = obs;
    }

    public String getCategory() {
        return category;
    }

    public void setCategory(String category) {
        this.category = category;
    }

    public String getStatus() {
        return status;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public ZonedDateTime getDatejc() {
        return datejc;
    }

    public void setDatejc(ZonedDateTime datejc) {
        this.datejc = datejc;
    }

    public ZonedDateTime getDatejcsend() {
        return datejcsend;
    }

    public void setDatejcsend(ZonedDateTime datejcsend) {
        this.datejcsend = datejcsend;
    }

    public String getTypereceive() {
        return typereceive;
    }

    public void setTypereceive(String typereceive) {
        this.typereceive = typereceive;
    }

    public String getTypecopy() {
        return typecopy;
    }

    public void setTypecopy(String typecopy) {
        this.typecopy = typecopy;
    }

    public String getRefsnd() {
        return refsnd;
    }

    public void setRefsnd(String refsnd) {
        this.refsnd = refsnd;
    }

    public String getText() {
        return text;
    }

    public void setText(String text) {
        this.text = text;
    }

    public String getDocyear() {
        return docyear;
    }

    public void setDocyear(String docyear) {
        this.docyear = docyear;
    }

    public String getDatehjrsave() {
        return datehjrsave;
    }

    public void setDatehjrsave(String datehjrsave) {
        this.datehjrsave = datehjrsave;
    }

    public ZonedDateTime getDatejcsave() {
        return datejcsave;
    }

    public void setDatejcsave(ZonedDateTime datejcsave) {
        this.datejcsave = datejcsave;
    }

    public String getOldOrderNumber() {
        return oldOrderNumber;
    }

    public void setOldOrderNumber(String oldOrderNumber) {
        this.oldOrderNumber = oldOrderNumber;
    }

    public String getSubject() {
        return subject;
    }

    public void setSubject(String subject) {
        this.subject = subject;
    }

    public String getAttach() {
        return attach;
    }

    public void setAttach(String attach) {
        this.attach = attach;
    }

    public String getOrdernbradrsbook() {
        return ordernbradrsbook;
    }

    public void setOrdernbradrsbook(String ordernbradrsbook) {
        this.ordernbradrsbook = ordernbradrsbook;
    }

    public Integer getSeqkeyadrsbook() {
        return seqkeyadrsbook;
    }

    public void setSeqkeyadrsbook(Integer seqkeyadrsbook) {
        this.seqkeyadrsbook = seqkeyadrsbook;
    }

    public String getUseradrsbook() {
        return useradrsbook;
    }

    public void setUseradrsbook(String useradrsbook) {
        this.useradrsbook = useradrsbook;
    }

    public String getCityzenncard() {
        return cityzenncard;
    }

    public void setCityzenncard(String cityzenncard) {
        this.cityzenncard = cityzenncard;
    }

    public String getCityzenphone() {
        return cityzenphone;
    }

    public void setCityzenphone(String cityzenphone) {
        this.cityzenphone = cityzenphone;
    }

    public String getSms() {
        return sms;
    }

    public void setSms(String sms) {
        this.sms = sms;
    }

    public String getIncident() {
        return incident;
    }

    public void setIncident(String incident) {
        this.incident = incident;
    }

    public String getCheckFavorite() {
        return checkFavorite;
    }

    public void setCheckFavorite(String checkFavorite) {
        this.checkFavorite = checkFavorite;
    }

    public String getCompanyId() {
        return companyId;
    }

    public void setCompanyId(String companyId) {
        this.companyId = companyId;
    }

    public EmployeeDTO getEmployee() {
        return employee;
    }

    public void setEmployee(EmployeeDTO employee) {
        this.employee = employee;
    }

    public UnitDTO getUnit() {
        return unit;
    }

    public void setUnit(UnitDTO unit) {
        this.unit = unit;
    }

    public DeliverymodeDTO getDeliverymode() {
        return deliverymode;
    }

    public void setDeliverymode(DeliverymodeDTO deliverymode) {
        this.deliverymode = deliverymode;
    }

    public TypecorrespondenceDTO getTypecorrespondence() {
        return typecorrespondence;
    }

    public void setTypecorrespondence(TypecorrespondenceDTO typecorrespondence) {
        this.typecorrespondence = typecorrespondence;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CorrespondenceDTO)) {
            return false;
        }

        CorrespondenceDTO correspondenceDTO = (CorrespondenceDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, correspondenceDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "CorrespondenceDTO{" +
            "id=" + getId() +
            ", ordernbr='" + getOrdernbr() + "'" +
            ", numcopy='" + getNumcopy() + "'" +
            ", doclink='" + getDoclink() + "'" +
            ", pagenbr='" + getPagenbr() + "'" +
            ", highlevel='" + getHighlevel() + "'" +
            ", confidentiel='" + getConfidentiel() + "'" +
            ", priority='" + getPriority() + "'" +
            ", obs='" + getObs() + "'" +
            ", category='" + getCategory() + "'" +
            ", status='" + getStatus() + "'" +
            ", datejc='" + getDatejc() + "'" +
            ", datejcsend='" + getDatejcsend() + "'" +
            ", typereceive='" + getTypereceive() + "'" +
            ", typecopy='" + getTypecopy() + "'" +
            ", refsnd='" + getRefsnd() + "'" +
            ", text='" + getText() + "'" +
            ", docyear='" + getDocyear() + "'" +
            ", datehjrsave='" + getDatehjrsave() + "'" +
            ", datejcsave='" + getDatejcsave() + "'" +
            ", oldOrderNumber='" + getOldOrderNumber() + "'" +
            ", subject='" + getSubject() + "'" +
            ", attach='" + getAttach() + "'" +
            ", ordernbradrsbook='" + getOrdernbradrsbook() + "'" +
            ", seqkeyadrsbook=" + getSeqkeyadrsbook() +
            ", useradrsbook='" + getUseradrsbook() + "'" +
            ", cityzenncard='" + getCityzenncard() + "'" +
            ", cityzenphone='" + getCityzenphone() + "'" +
            ", sms='" + getSms() + "'" +
            ", incident='" + getIncident() + "'" +
            ", checkFavorite='" + getCheckFavorite() + "'" +
            ", companyId='" + getCompanyId() + "'" +
            ", employee=" + getEmployee() +
            ", unit=" + getUnit() +
            ", deliverymode=" + getDeliverymode() +
            ", typecorrespondence=" + getTypecorrespondence() +
            "}";
    }
}
