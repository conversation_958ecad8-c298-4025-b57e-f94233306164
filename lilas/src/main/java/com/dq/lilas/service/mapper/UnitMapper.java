package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Company;
import com.dq.lilas.domain.Unit;
import com.dq.lilas.service.dto.CompanyDTO;
import com.dq.lilas.service.dto.UnitDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Unit} and its DTO {@link UnitDTO}.
 */
@Mapper(componentModel = "spring")
public interface UnitMapper extends EntityMapper<UnitDTO, Unit> {
    @Mapping(target = "company", source = "company", qualifiedByName = "companyId")
    UnitDTO toDto(Unit s);

    @Named("companyId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    CompanyDTO toDtoCompanyId(Company company);
}
