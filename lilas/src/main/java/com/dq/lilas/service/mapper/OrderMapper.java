package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.DailyBatches;
import com.dq.lilas.domain.Employee;
import com.dq.lilas.domain.GmsClients;
import com.dq.lilas.domain.Mails;
import com.dq.lilas.domain.Order;
import com.dq.lilas.domain.OrderDetails;
import com.dq.lilas.domain.TemplateConditions;
import com.dq.lilas.service.dto.DailyBatchesDTO;
import com.dq.lilas.service.dto.EmployeeDTO;
import com.dq.lilas.service.dto.GmsClientsDTO;
import com.dq.lilas.service.dto.MailsDTO;
import com.dq.lilas.service.dto.OrderDTO;
import com.dq.lilas.service.dto.OrderDetailsDTO;
import com.dq.lilas.service.dto.TemplateConditionsDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Order} and its DTO {@link OrderDTO}.
 */
@Mapper(componentModel = "spring", uses = {OrderDetailsMapper.class})
public interface OrderMapper extends EntityMapper<OrderDTO, Order> {
    @Mapping(target = "dailyBatches", source = "dailyBatches", qualifiedByName = "dailyBatchesId")
    @Mapping(target = "gmsClients", source = "gmsClients", qualifiedByName = "gmsClientsId")
    @Mapping(target = "templateConditions", source = "templateConditions", qualifiedByName = "templateConditionsId")
    @Mapping(target = "mails", source = "mails", qualifiedByName = "mailsId")
    @Mapping(target = "employee", source = "employee", qualifiedByName = "employeeId")
    @Mapping(target = "orderDetails", source = "orderDetails")
    OrderDTO toDto(Order s);

    @Override
    @Mapping(target = "removeOrderDetails", ignore = true)
    Order toEntity(OrderDTO dto);

    @Named("dailyBatchesId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    DailyBatchesDTO toDtoDailyBatchesId(DailyBatches dailyBatches);

    @Named("gmsClientsId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    GmsClientsDTO toDtoGmsClientsId(GmsClients gmsClients);

    @Named("templateConditionsId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TemplateConditionsDTO toDtoTemplateConditionsId(TemplateConditions templateConditions);

    @Named("mailsId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    MailsDTO toDtoMailsId(Mails mails);

    @Named("employeeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    EmployeeDTO toDtoEmployeeId(Employee employee);
}
