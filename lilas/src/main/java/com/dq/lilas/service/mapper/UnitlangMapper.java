package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Unit;
import com.dq.lilas.domain.Unitlang;
import com.dq.lilas.service.dto.UnitDTO;
import com.dq.lilas.service.dto.UnitlangDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Unitlang} and its DTO {@link UnitlangDTO}.
 */
@Mapper(componentModel = "spring")
public interface UnitlangMapper extends EntityMapper<UnitlangDTO, Unitlang> {
    @Mapping(target = "unit", source = "unit", qualifiedByName = "unitId")
    UnitlangDTO toDto(Unitlang s);

    @Named("unitId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    UnitDTO toDtoUnitId(Unit unit);
}
