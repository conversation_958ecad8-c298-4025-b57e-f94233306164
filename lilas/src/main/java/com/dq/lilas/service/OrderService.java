package com.dq.lilas.service;

import com.dq.lilas.service.dto.OrderDTO;
import com.dq.lilas.service.dto.OrderDetailsDTO;
import com.dq.lilas.service.dto.ClientBrandInfoDTO;
import com.dq.lilas.service.dto.BatchOrderInfoDTO;
import java.time.Instant;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Order}.
 */
public interface OrderService {
    /**
     * Save a order.
     *
     * @param orderDTO the entity to save.
     * @return the persisted entity.
     */
    OrderDTO save(OrderDTO orderDTO);

    /**
     * Save an order with its associated order details.
     * This method handles the circular reference issue.
     *
     * @param orderDTO the entity to save with its details.
     * @return the persisted entity with its details.
     */
    OrderDTO saveWithDetails(OrderDTO orderDTO);

    /**
     * Create an order from BC (Bon de Commande) response data.
     * This method extracts order and order details from BCCheckResponse and saves
     * them atomically.
     *
     * @param savedMail       the mail entity associated with the order.
     * @param bcCheckResponse the BC response containing order data.
     * @return the created order with its details, or null if creation failed.
     */
    OrderDTO createOrderFromBC(com.dq.lilas.service.dto.MailsDTO savedMail,
            com.dq.lilas.service.dto.BCCheckResponse bcCheckResponse);

    /**
     * Save an order detail for an existing order.
     *
     * @param orderDetailsDTO the entity to save.
     * @return the persisted entity.
     */
    OrderDetailsDTO saveOrderDetail(OrderDetailsDTO orderDetailsDTO);

    /**
     * Update an order with its associated order details atomically.
     * This method handles the circular reference issue and ensures data
     * consistency.
     * It can handle adding new details, updating existing ones, and removing
     * details.
     *
     * @param orderDTO the entity to update with its details.
     * @return the updated entity with its details.
     */
    OrderDTO updateWithDetails(OrderDTO orderDTO);

    /**
     * Bulk update multiple order details for an existing order.
     * This method is optimized for updating many order details at once.
     *
     * @param orderId         the ID of the parent order.
     * @param orderDetailsSet the set of order details to update.
     * @return the updated order with its details.
     */
    OrderDTO bulkUpdateOrderDetails(Long orderId, Set<OrderDetailsDTO> orderDetailsSet);

    /**
     * Get an order with its associated order details.
     * This method handles the circular reference issue and provides complete order
     * information.
     *
     * @param id the ID of the order to retrieve.
     * @return the order with its details, or empty if not found.
     */
    Optional<OrderDTO> findOneWithDetails(Long id);

    /**
     * Updates a order.
     *
     * @param orderDTO the entity to update.
     * @return the persisted entity.
     */
    OrderDTO update(OrderDTO orderDTO);

    /**
     * Partially updates a order.
     *
     * @param orderDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<OrderDTO> partialUpdate(OrderDTO orderDTO);

    /**
     * Get all the orders.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<OrderDTO> findAll(Pageable pageable);

    /**
     * Get the "id" order.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<OrderDTO> findOne(Long id);

    /**
     * Delete the "id" order.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);

    /**
     * Get orders by delivery date comparison and status
     *
     * @param operator       the comparison operator (">" or "<=")
     * @param comparisonDate the date to compare against (typically systemdate + 1)
     * @param status         the order status (typically 'V')
     * @return list of orders matching the criteria
     */
    List<OrderDTO> getOrdersByDeliveryDates(String operator, Instant comparisonDate, Integer status);

    /**
     * Get orders by status
     *
     * @param statusCode the status code ('D' for livré/delivered, 'P' for
     *                   payé/paid)
     * @return list of orders matching the status
     */
    List<OrderDTO> getOrdersByStatus(String statusCode);

    /**
     * Get the order by batche and brand id.
     *
     * @param batchId the id of the batche.
     * @param brandId the id of the brand.
     *
     * @return the entity.
     */
    Optional<OrderDTO> findByBrandAndBatche(Long batchId, Long brandId);

    /**
     * Get the order by batche and brand id.
     *
     * @param batchId  the id of the batche.
     * @param brandIds list of the id of the brand.
     *
     * @return the entity.
     */
    List<OrderDTO> findByBatcheAndBrands(Long batchId, List<Long> brandIds);

    /**
     * Get client and brand information by fiscal ID.
     *
     * @param fiscalId the fiscal ID to search for.
     * @return the client and brand information.
     */
    ClientBrandInfoDTO getClientAndBrandByFiscalId(String fiscalId);

    /**
     * Get the next order number for a company on the current date.
     *
     * @param companyId the company ID.
     * @return the batch ID and next order number.
     */
    BatchOrderInfoDTO getNextOrderNumber(Long companyId);

    /**
     * Increment the order number for a batch.
     *
     * @param batchId the batch ID.
     */
    void incrementOrderNumber(Long batchId);

    /**
     * Get company ID by company name.
     *
     * @param companyName the company name.
     * @return the company ID.
     */
    Optional<Long> getCompanyIdByName(String companyName);

    /**
     * Get orders by brand ID.
     *
     * @param brandId the brand ID.
     * @return list of orders matching the brand ID.
     */
    List<OrderDTO> getOrdersByBrandId(Long brandId);

    /**
     * Get orders by company name.
     *
     * @param company the company name.
     * @return list of orders matching the company name.
     */
    List<OrderDTO> getOrdersByCompany(String company);
}
