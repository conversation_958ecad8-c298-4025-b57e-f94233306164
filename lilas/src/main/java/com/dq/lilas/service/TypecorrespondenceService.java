package com.dq.lilas.service;

import com.dq.lilas.service.dto.TypecorrespondenceDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Typecorrespondence}.
 */
public interface TypecorrespondenceService {
    /**
     * Save a typecorrespondence.
     *
     * @param typecorrespondenceDTO the entity to save.
     * @return the persisted entity.
     */
    TypecorrespondenceDTO save(TypecorrespondenceDTO typecorrespondenceDTO);

    /**
     * Updates a typecorrespondence.
     *
     * @param typecorrespondenceDTO the entity to update.
     * @return the persisted entity.
     */
    TypecorrespondenceDTO update(TypecorrespondenceDTO typecorrespondenceDTO);

    /**
     * Partially updates a typecorrespondence.
     *
     * @param typecorrespondenceDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<TypecorrespondenceDTO> partialUpdate(TypecorrespondenceDTO typecorrespondenceDTO);

    /**
     * Get all the typecorrespondences.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<TypecorrespondenceDTO> findAll(Pageable pageable);

    /**
     * Get the "id" typecorrespondence.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<TypecorrespondenceDTO> findOne(Long id);

    /**
     * Delete the "id" typecorrespondence.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
