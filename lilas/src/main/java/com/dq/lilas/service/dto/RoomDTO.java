package com.dq.lilas.service.dto;

import java.io.Serializable;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * A DTO for the Room entity.
 */
public class RoomDTO implements Serializable {

    private Long id;

    private String name;

    private Boolean isActivated;

    private Boolean allowCall;

    private Boolean allowImageMessage;

    private Boolean allowVoiceMessage;

    private Boolean allowStickerMessage;

    private String createdBy;

    private Set<UserDTO> users = new HashSet<>();

    private Long groupId;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public Boolean getAllowCall() {
        return allowCall;
    }

    public void setAllowCall(Boolean allowCall) {
        this.allowCall = allowCall;
    }

    public Boolean isIsActivated() {
        return isActivated;
    }

    public void setIsActivated(Boolean isActivated) {
        this.isActivated = isActivated;
    }

    public Boolean isAllowImageMessage() {
        return allowImageMessage;
    }

    public void setAllowImageMessage(Boolean allowImageMessage) {
        this.allowImageMessage = allowImageMessage;
    }

    public Boolean isAllowVoiceMessage() {
        return allowVoiceMessage;
    }

    public void setAllowVoiceMessage(Boolean allowVoiceMessage) {
        this.allowVoiceMessage = allowVoiceMessage;
    }

    public Boolean isAllowStickerMessage() {
        return allowStickerMessage;
    }

    public void setAllowStickerMessage(Boolean allowStickerMessage) {
        this.allowStickerMessage = allowStickerMessage;
    }

    public Set<UserDTO> getUsers() {
        return users;
    }

    public void setUsers(Set<UserDTO> users) {
        this.users.clear();
        if(users != null) {
            this.users.addAll(users);
        }
    }

    public Long getGroupId() {
        return groupId;
    }

    public void setGroupId(Long groupId) {
        this.groupId = groupId;
    }

    public String getCreatedBy() {
        return createdBy;
    }

    public void setCreatedBy(String createdBy) {
        this.createdBy = createdBy;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }

        RoomDTO roomDTO = (RoomDTO) o;
        if (roomDTO.getId() == null || getId() == null) {
            return false;
        }
        return Objects.equals(getId(), roomDTO.getId());
    }

    @Override
    public int hashCode() {
        return Objects.hashCode(getId());
    }

    @Override
    public String toString() {
        return "RoomDTO{" +
                "id=" + getId() +
                ", name='" + getName() + "'" +
                ", isActivated='" + isIsActivated() + "'" +
                ", allowCall='" + getAllowCall() + "'" +
                ", allowImageMessage='" + isAllowImageMessage() + "'" +
                ", allowVoiceMessage='" + isAllowVoiceMessage() + "'" +
                ", allowStickerMessage='" + isAllowStickerMessage() + "'" +
                ", group=" + getGroupId() +
                "}";
    }
}
