package com.dq.lilas.service.dto;

import com.dq.lilas.domain.enumeration.PromotionStatus;
import java.io.Serializable;
import java.time.Instant;
import java.util.HashSet;
import java.util.Objects;
import java.util.Set;

/**
 * A DTO for the {@link com.dq.lilas.domain.DemandePromotion} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class DemandePromotionDTO implements Serializable {

    private Long id;

    private String codeClient;

    private String enseigne;

    private String action;

    private Instant periodPromotionStart;

    private Instant periodPromotionEnd;

    private Instant periodFacturationStart;

    private Instant periodFacturationEnd;

    private Instant dateOfRequest;

    private PromotionStatus status;

    private EmployeeDTO employee;

    private Set<PromotionDetailsDTO> promotionDetails = new HashSet<>();

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCodeClient() {
        return codeClient;
    }

    public void setCodeClient(String codeClient) {
        this.codeClient = codeClient;
    }

    public String getEnseigne() {
        return enseigne;
    }

    public void setEnseigne(String enseigne) {
        this.enseigne = enseigne;
    }

    public String getAction() {
        return action;
    }

    public void setAction(String action) {
        this.action = action;
    }

    public Instant getPeriodPromotionStart() {
        return periodPromotionStart;
    }

    public void setPeriodPromotionStart(Instant periodPromotionStart) {
        this.periodPromotionStart = periodPromotionStart;
    }

    public Instant getPeriodPromotionEnd() {
        return periodPromotionEnd;
    }

    public void setPeriodPromotionEnd(Instant periodPromotionEnd) {
        this.periodPromotionEnd = periodPromotionEnd;
    }

    public Instant getPeriodFacturationStart() {
        return periodFacturationStart;
    }

    public void setPeriodFacturationStart(Instant periodFacturationStart) {
        this.periodFacturationStart = periodFacturationStart;
    }

    public Instant getPeriodFacturationEnd() {
        return periodFacturationEnd;
    }

    public void setPeriodFacturationEnd(Instant periodFacturationEnd) {
        this.periodFacturationEnd = periodFacturationEnd;
    }

    public Instant getDateOfRequest() {
        return dateOfRequest;
    }

    public void setDateOfRequest(Instant dateOfRequest) {
        this.dateOfRequest = dateOfRequest;
    }

    public PromotionStatus getStatus() {
        return status;
    }

    public void setStatus(PromotionStatus status) {
        this.status = status;
    }

    public EmployeeDTO getEmployee() {
        return employee;
    }

    public void setEmployee(EmployeeDTO employee) {
        this.employee = employee;
    }

    public Set<PromotionDetailsDTO> getPromotionDetails() {
        return promotionDetails;
    }

    public void setPromotionDetails(Set<PromotionDetailsDTO> promotionDetails) {
        this.promotionDetails = promotionDetails;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DemandePromotionDTO)) {
            return false;
        }

        DemandePromotionDTO demandePromotionDTO = (DemandePromotionDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, demandePromotionDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "DemandePromotionDTO{" +
            "id=" + getId() +
            ", codeClient='" + getCodeClient() + "'" +
            ", enseigne='" + getEnseigne() + "'" +
            ", action='" + getAction() + "'" +
            ", periodPromotionStart='" + getPeriodPromotionStart() + "'" +
            ", periodPromotionEnd='" + getPeriodPromotionEnd() + "'" +
            ", periodFacturationStart='" + getPeriodFacturationStart() + "'" +
            ", periodFacturationEnd='" + getPeriodFacturationEnd() + "'" +
            ", dateOfRequest='" + getDateOfRequest() + "'" +
            ", status='" + getStatus() + "'" +
            ", employee=" + getEmployee() +
            ", promotionDetails=" + getPromotionDetails() +
            "}";
    }
}
