package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Actionlang} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class ActionlangDTO implements Serializable {

    private Long id;

    @Size(max = 100)
    private String appType;

    @Size(max = 5)
    private String lang;

    @Size(max = 10)
    private String abrv;

    private ActionDTO action;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getAbrv() {
        return abrv;
    }

    public void setAbrv(String abrv) {
        this.abrv = abrv;
    }

    public ActionDTO getAction() {
        return action;
    }

    public void setAction(ActionDTO action) {
        this.action = action;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof ActionlangDTO)) {
            return false;
        }

        ActionlangDTO actionlangDTO = (ActionlangDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, actionlangDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "ActionlangDTO{" +
            "id=" + getId() +
            ", appType='" + getAppType() + "'" +
            ", lang='" + getLang() + "'" +
            ", abrv='" + getAbrv() + "'" +
            ", action=" + getAction() +
            "}";
    }
}
