package com.dq.lilas.service;

import com.dq.lilas.service.dto.ActionlangDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Actionlang}.
 */
public interface ActionlangService {
    /**
     * Save a actionlang.
     *
     * @param actionlangDTO the entity to save.
     * @return the persisted entity.
     */
    ActionlangDTO save(ActionlangDTO actionlangDTO);

    /**
     * Updates a actionlang.
     *
     * @param actionlangDTO the entity to update.
     * @return the persisted entity.
     */
    ActionlangDTO update(ActionlangDTO actionlangDTO);

    /**
     * Partially updates a actionlang.
     *
     * @param actionlangDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<ActionlangDTO> partialUpdate(ActionlangDTO actionlangDTO);

    /**
     * Get all the actionlangs.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<ActionlangDTO> findAll(Pageable pageable);

    /**
     * Get the "id" actionlang.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<ActionlangDTO> findOne(Long id);

    /**
     * Delete the "id" actionlang.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
