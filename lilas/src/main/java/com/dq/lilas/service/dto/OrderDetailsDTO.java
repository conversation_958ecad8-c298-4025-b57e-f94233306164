package com.dq.lilas.service.dto;

import com.dq.lilas.domain.enumeration.OrderDetailsStatus;
import jakarta.persistence.Lob;
import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.OrderDetails} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class OrderDetailsDTO implements Serializable {

    private Long id;

    private BigDecimal quantity;

    private BigDecimal updatedQty;

    private Integer discount;

    private Integer updatedDiscount;

    private Double unitPrice;

    private Double updatedUnitPrice;

    @Lob
    private String orderLineJson;

    private Boolean availability;

    private OrderDetailsStatus status;

    private String barcode;

    private String updatedBarcode;

    private String internalCode;

    private String updatedInternalCode;

    private Boolean discountStatus;

    private Boolean priceStatus;

    private Boolean quantityStatus;

    private Boolean productStatus;

    private String ref;

    private String productName;

    private Integer packNb;

    private Double pcb;

    private String productUnit;

    private Double tva;

    private String typeUc;

    private String uvcUc;

    private String unit;

    private OrderDTO order;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public BigDecimal getQuantity() {
        return quantity;
    }

    public void setQuantity(BigDecimal quantity) {
        this.quantity = quantity;
    }

    public BigDecimal getUpdatedQty() {
        return updatedQty;
    }

    public void setUpdatedQty(BigDecimal updatedQty) {
        this.updatedQty = updatedQty;
    }

    public Integer getDiscount() {
        return discount;
    }

    public void setDiscount(Integer discount) {
        this.discount = discount;
    }

    public Integer getUpdatedDiscount() {
        return updatedDiscount;
    }

    public void setUpdatedDiscount(Integer updatedDiscount) {
        this.updatedDiscount = updatedDiscount;
    }

    public Double getUnitPrice() {
        return unitPrice;
    }

    public void setUnitPrice(Double unitPrice) {
        this.unitPrice = unitPrice;
    }

    public Double getUpdatedUnitPrice() {
        return updatedUnitPrice;
    }

    public void setUpdatedUnitPrice(Double updatedUnitPrice) {
        this.updatedUnitPrice = updatedUnitPrice;
    }

    public String getOrderLineJson() {
        return orderLineJson;
    }

    public void setOrderLineJson(String orderLineJson) {
        this.orderLineJson = orderLineJson;
    }

    public Boolean getAvailability() {
        return availability;
    }

    public void setAvailability(Boolean availability) {
        this.availability = availability;
    }

    public OrderDetailsStatus getStatus() {
        return status;
    }

    public void setStatus(OrderDetailsStatus status) {
        this.status = status;
    }

    public String getBarcode() {
        return barcode;
    }

    public void setBarcode(String barcode) {
        this.barcode = barcode;
    }

    public String getUpdatedBarcode() {
        return updatedBarcode;
    }

    public void setUpdatedBarcode(String updatedBarcode) {
        this.updatedBarcode = updatedBarcode;
    }

    public String getInternalCode() {
        return internalCode;
    }

    public void setInternalCode(String internalCode) {
        this.internalCode = internalCode;
    }

    public String getUpdatedInternalCode() {
        return updatedInternalCode;
    }

    public void setUpdatedInternalCode(String updatedInternalCode) {
        this.updatedInternalCode = updatedInternalCode;
    }

    public Boolean getDiscountStatus() {
        return discountStatus;
    }

    public void setDiscountStatus(Boolean discountStatus) {
        this.discountStatus = discountStatus;
    }

    public Boolean getPriceStatus() {
        return priceStatus;
    }

    public void setPriceStatus(Boolean priceStatus) {
        this.priceStatus = priceStatus;
    }

    public Boolean getQuantityStatus() {
        return quantityStatus;
    }

    public void setQuantityStatus(Boolean quantityStatus) {
        this.quantityStatus = quantityStatus;
    }

    public Boolean getProductStatus() {
        return productStatus;
    }

    public void setProductStatus(Boolean productStatus) {
        this.productStatus = productStatus;
    }

    public String getRef() {
        return ref;
    }

    public void setRef(String ref) {
        this.ref = ref;
    }

    public String getProductName() {
        return productName;
    }

    public void setProductName(String productName) {
        this.productName = productName;
    }

    public Integer getPackNb() {
        return packNb;
    }

    public void setPackNb(Integer packNb) {
        this.packNb = packNb;
    }

    public Double getPcb() {
        return pcb;
    }

    public void setPcb(Double pcb) {
        this.pcb = pcb;
    }

    public String getProductUnit() {
        return productUnit;
    }

    public void setProductUnit(String productUnit) {
        this.productUnit = productUnit;
    }

    public Double getTva() {
        return tva;
    }

    public void setTva(Double tva) {
        this.tva = tva;
    }

    public String getTypeUc() {
        return typeUc;
    }

    public void setTypeUc(String typeUc) {
        this.typeUc = typeUc;
    }

    public String getUvcUc() {
        return uvcUc;
    }

    public void setUvcUc(String uvcUc) {
        this.uvcUc = uvcUc;
    }

    public String getUnit() {
        return unit;
    }

    public void setUnit(String unit) {
        this.unit = unit;
    }

    public OrderDTO getOrder() {
        return order;
    }

    public void setOrder(OrderDTO order) {
        this.order = order;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof OrderDetailsDTO)) {
            return false;
        }

        OrderDetailsDTO orderDetailsDTO = (OrderDetailsDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, orderDetailsDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "OrderDetailsDTO{" +
            "id=" + getId() +
            ", quantity=" + getQuantity() +
            ", updatedQty=" + getUpdatedQty() +
            ", discount=" + getDiscount() +
            ", updatedDiscount=" + getUpdatedDiscount() +
            ", unitPrice=" + getUnitPrice() +
            ", updatedUnitPrice=" + getUpdatedUnitPrice() +
            ", orderLineJson='" + getOrderLineJson() + "'" +
            ", availability='" + getAvailability() + "'" +
            ", status='" + getStatus() + "'" +
            ", barcode='" + getBarcode() + "'" +
            ", updatedBarcode='" + getUpdatedBarcode() + "'" +
            ", internalCode='" + getInternalCode() + "'" +
            ", updatedInternalCode='" + getUpdatedInternalCode() + "'" +
            ", ref='" + getRef() + "'" +
            ", productName='" + getProductName() + "'" +
            ", packNb=" + getPackNb() +
            ", pcb=" + getPcb() +
            ", productUnit='" + getProductUnit() + "'" +
            ", tva=" + getTva() +
            ", typeUc='" + getTypeUc() + "'" +
            ", uvcUc='" + getUvcUc() + "'" +
            ", unit='" + getUnit() + "'" +
            ", order=" + getOrder() +
            "}";
    }
}
