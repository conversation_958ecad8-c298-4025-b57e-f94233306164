package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Employee;
import com.dq.lilas.domain.Employeelang;
import com.dq.lilas.service.dto.EmployeeDTO;
import com.dq.lilas.service.dto.EmployeelangDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Employeelang} and its DTO {@link EmployeelangDTO}.
 */
@Mapper(componentModel = "spring")
public interface EmployeelangMapper extends EntityMapper<EmployeelangDTO, Employeelang> {
    @Mapping(target = "employee", source = "employee", qualifiedByName = "employeeId")
    EmployeelangDTO toDto(Employeelang s);

    @Named("employeeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    EmployeeDTO toDtoEmployeeId(Employee employee);
}
