package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Group;
import com.dq.lilas.domain.User;
import com.dq.lilas.repository.GroupRepository;
import com.dq.lilas.service.GroupService;
import com.dq.lilas.service.UserService;
import com.dq.lilas.service.dto.GroupDTO;
import com.dq.lilas.service.dto.UserDTO;
import com.dq.lilas.service.mapper.GroupMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.stream.Collectors;

/**
 * Service Implementation for managing Group.
 */
@Service
@Transactional
public class GroupServiceImpl implements GroupService {

    private final String PUBLIC_GROUP_NAME = "public";

    private final Logger log = LoggerFactory.getLogger(GroupServiceImpl.class);

    private final GroupRepository groupRepository;

    private final UserService userService;

    private final GroupMapper groupMapper;

    public GroupServiceImpl(GroupRepository groupRepository, UserService userService, GroupMapper groupMapper) {
        this.groupRepository = groupRepository;
        this.userService = userService;
        this.groupMapper = groupMapper;
    }

    /**
     * Save a group.
     *
     * @param groupDTO the entity to save
     * @return the persisted entity
     */
    @Override
    public GroupDTO save(GroupDTO groupDTO) {
        log.debug("Request to save Group : {}", groupDTO);
        String name = groupDTO.getName();
        if (name != null) {
            name = name.toLowerCase();
            groupDTO.setName(name);
        }
        Group group = groupMapper.toEntity(groupDTO);
        group = groupRepository.save(group);
        return groupMapper.toDto(group);
    }

    /**
     * Add or Remove Logged User from group By Name.
     *
     * @param name      the group entity name
     * @param isAddUser boolean to indicate add or remove user from group
     * @return GroupDTO
     */
    @Override
    public GroupDTO addOrRemoveLoggedUserFromGroup(String name, boolean isAddUser) {
        Optional<User> optionalUser = userService.getUserWithAuthorities();
        if (optionalUser.isPresent()) {
            User user = optionalUser.get();
            Group group = findGroupByName(name);
            if (group != null) {
                if (isAddUser) {
                    // add user
                    group.addUser(user);
                } else {
                    // remove user from group
                    group.removeUser(user.getId());
                }
                group = groupRepository.save(group);
                return groupMapper.toDto(group);
            }
        }
        return null;
    }

    public Group findGroupByName(String name) {
        if (name != null) {
            name = name.toLowerCase();
            Optional<Group> optionalGroup = groupRepository.findFirstByName(name);
            return optionalGroup.orElse(null);
        }
        return null;
    }

    /**
     * Check If User is in group.
     *
     * @param groupName the name of Group
     * @return boolean
     */
    @Override
    public boolean checkIfUserIsInGroupByName(String groupName) {
        Optional<User> optionalUser = userService.getUserWithAuthorities();
        if (optionalUser.isPresent()) {
            User user = optionalUser.get();
            return this.groupRepository.existsGroupByUsersIsContainingAndName(user, groupName.toLowerCase());
        }
        return false;
    }

    /**
     * Get all the groups.
     *
     * @param pageable the pagination information
     * @return the list of entities
     */
    @Override
    @Transactional(readOnly = true)
    public Page<GroupDTO> findAll(Pageable pageable) {
        log.debug("Request to get all Groups");
        return groupRepository.findAllWithEagerRelationships(pageable)
            .map(groupMapper::toDto);
    }

    /**
     * Get all the groups.
     *
     * @return the list of entities
     */
    @Override
    public List<GroupDTO> findAll() {
        return groupRepository.findAll().stream().map(groupMapper::toDto).collect(Collectors.toList());
    }

    /**
     * Get all Groups joined by user.
     *
     * @return the list of entities.
     */
    public List<GroupDTO> findGroupsByLoggedUser() {
        Optional<User> optionalUser = userService.getUserWithAuthorities();
        if (optionalUser.isPresent()) {
            User user = optionalUser.get();
            return groupRepository.findAllByUsersIsContaining(user)
                .stream()
                .map(groupMapper::toDto)
                .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }


    /**
     * Get all Groups joined by user.
     *
     * @return the list of entities.
     */
    public List<UserDTO> getUsersJoinedToSameGroupsAsLoggedUser() {
        Optional<User> optionalUser = userService.getUserWithAuthorities();
        if (optionalUser.isPresent()) {
            User loggedUser = optionalUser.get();
            return groupRepository.findAllByUsersIsContaining(loggedUser)
                .stream()
                .filter(group -> !group.getName().equalsIgnoreCase(PUBLIC_GROUP_NAME))
                .map(Group::getUsers)
                .flatMap(Collection::stream)
                .filter(users -> !users.getLogin().equals(loggedUser.getLogin()))
                .distinct()
                .map(user -> {
                    UserDTO userDTO = new UserDTO();
                    userDTO.setId(user.getId());
                    userDTO.setLogin(user.getLogin());
                    userDTO.setFirstName(user.getFirstName());
                    userDTO.setLastName(user.getLastName());
                    return userDTO;
                })
                .collect(Collectors.toList());
        }
        return new ArrayList<>();
    }

    /**
     * Get users by group.
     *
     * @return the list of entities.
     */
    public Set<UserDTO> getUsersJoinedToGroup(Long id) {
        Optional<GroupDTO> optionalGroupDTO = findOne(id);
        if (optionalGroupDTO.isPresent()) {
            GroupDTO groupDTO = optionalGroupDTO.get();
            return groupDTO.getUsers();
        }
        return new HashSet<>();
    }

    /**
     * Get users by group and by username like.
     *
     * @return the list of entities.
     */
    public Set<UserDTO> getUsersJoinedToGroupByNameIsLike(Long id, String name) {
        if (name != null) {
            String nameToLowerCase = name.toLowerCase();
            return getUsersJoinedToGroup(id).stream()
                .filter(user -> {
                    String fullName = user.getFullName().toLowerCase();
                    return fullName.contains(nameToLowerCase);
                })
                .collect(Collectors.toSet());
        }
        return new HashSet<>();
    }

    /**
     * Get all the Group with eager load of many-to-many relationships.
     *
     * @return the list of entities
     */
    public Page<GroupDTO> findAllWithEagerRelationships(Pageable pageable) {
        return groupRepository.findAllWithEagerRelationships(pageable).map(groupMapper::toDto);
    }

    /**
     * Get one group by id.
     *
     * @param id the id of the entity
     * @return the entity
     */
    @Override
    @Transactional(readOnly = true)
    public Optional<GroupDTO> findOne(Long id) {
        log.debug("Request to get Group : {}", id);
        return groupRepository.findOneWithEagerRelationships(id)
            .map(groupMapper::toDto);
    }

    /**
     * Delete the group by id.
     *
     * @param id the id of the entity
     */
    @Override
    public void delete(Long id) {
        log.debug("Request to delete Group : {}", id);
        groupRepository.deleteById(id);
    }
}
