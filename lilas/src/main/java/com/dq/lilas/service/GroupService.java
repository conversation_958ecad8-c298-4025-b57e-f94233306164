package com.dq.lilas.service;

import com.dq.lilas.service.dto.GroupDTO;
import com.dq.lilas.service.dto.UserDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;
import java.util.Set;

/**
 * Service Interface for managing Group.
 */
public interface GroupService {

    /**
     * Save a group.
     *
     * @param groupDTO the entity to save
     * @return the persisted entity
     */
    GroupDTO save(GroupDTO groupDTO);

    /**
     * Add or Remove Logged User from group By Name.
     *
     * @param name      the group entity name
     * @param isAddUser boolean to indicate add or remove user from group
     * @return GroupDTO
     */
    GroupDTO addOrRemoveLoggedUserFromGroup(String name, boolean isAddUser);

    /**
     * Check If User is in group.
     *
     * @param groupName the name of Group
     * @return boolean
     */
    boolean checkIfUserIsInGroupByName(String groupName);

    /**
     * Get all the groups.
     *
     * @param pageable the pagination information
     * @return the list of entities
     */
    Page<GroupDTO> findAll(Pageable pageable);

    /**
     * Get all the groups.
     *
     * @return the list of entities
     */
    List<GroupDTO> findAll();

    /**
     * Get all the Group with eager load of many-to-many relationships.
     *
     * @return the list of entities
     */
    Page<GroupDTO> findAllWithEagerRelationships(Pageable pageable);

    /**
     * Get all distinct users joined by same groups as logged user.
     *
     * @return the list of entities.
     */
    List<UserDTO> getUsersJoinedToSameGroupsAsLoggedUser();

    /**
     * Get all users in Groups.
     *
     * @param id the id of the entity
     * @return the list of entities.
     */
    Set<UserDTO> getUsersJoinedToGroup(Long id);

    /**
     * Get users by group and by username like.
     *
     * @return the list of entities.
     */
    Set<UserDTO> getUsersJoinedToGroupByNameIsLike(Long id, String name);

    /**
     * Get all Groups joined by user.
     *
     * @return the list of entities.
     */
    List<GroupDTO> findGroupsByLoggedUser();

    /**
     * Get the "id" group.
     *
     * @param id the id of the entity
     * @return the entity
     */
    Optional<GroupDTO> findOne(Long id);

    /**
     * Delete the "id" group.
     *
     * @param id the id of the entity
     */
    void delete(Long id);

}
