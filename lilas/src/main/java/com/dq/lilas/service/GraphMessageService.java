package com.dq.lilas.service;

import com.microsoft.graph.models.Attachment;
import com.microsoft.graph.models.AttachmentCollectionResponse;
import com.microsoft.graph.models.Message;
import com.microsoft.graph.models.MessageCollectionResponse;
import com.microsoft.graph.serviceclient.GraphServiceClient;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class GraphMessageService {

    private final GraphServiceClient graphClient;

    public List<Message> getUserMessages(String userId, int top) {
        MessageCollectionResponse response = graphClient.users()
            .byUserId(userId)
            .messages()
            .get(requestConfiguration -> {
                requestConfiguration.queryParameters.top = top;
                requestConfiguration.queryParameters.select = new String[]{"subject", "sender", "sentDateTime"};
                requestConfiguration.queryParameters.orderby = new String[]{"sentDateTime desc"};
            });
        return response.getValue();
    }

    public Message getMessageById(String userId, String messageId) {
        return graphClient.users()
            .byUserId(userId)
            .messages()
            .byMessageId(messageId)
            .get(requestConfiguration -> {
                requestConfiguration.queryParameters.select = new String[]{"subject", "sender", "sentDateTime", "body", "hasAttachments", "toRecipients"};
            });
    }

    public List<Attachment> getMessageAttachments(String userId, String messageId) {
        AttachmentCollectionResponse response = graphClient.users()
            .byUserId(userId)
            .messages()
            .byMessageId(messageId)
            .attachments()
            .get();

        return response != null ? response.getValue() : List.of();
    }
}
