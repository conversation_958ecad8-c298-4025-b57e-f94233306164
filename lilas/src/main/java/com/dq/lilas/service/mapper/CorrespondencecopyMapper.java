package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Action;
import com.dq.lilas.domain.Correspondence;
import com.dq.lilas.domain.Correspondencecopy;
import com.dq.lilas.domain.Employee;
import com.dq.lilas.domain.Unit;
import com.dq.lilas.service.dto.ActionDTO;
import com.dq.lilas.service.dto.CorrespondenceDTO;
import com.dq.lilas.service.dto.CorrespondencecopyDTO;
import com.dq.lilas.service.dto.EmployeeDTO;
import com.dq.lilas.service.dto.UnitDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Correspondencecopy} and its DTO {@link CorrespondencecopyDTO}.
 */
@Mapper(componentModel = "spring")
public interface CorrespondencecopyMapper extends EntityMapper<CorrespondencecopyDTO, Correspondencecopy> {
    @Mapping(target = "correspondence", source = "correspondence", qualifiedByName = "correspondenceId")
    @Mapping(target = "employee", source = "employee", qualifiedByName = "employeeId")
    @Mapping(target = "unit", source = "unit", qualifiedByName = "unitId")
    @Mapping(target = "action", source = "action", qualifiedByName = "actionId")
    @Mapping(target = "useraction", source = "useraction", qualifiedByName = "employeeId")
    @Mapping(target = "userrevoke", source = "userrevoke", qualifiedByName = "employeeId")
    @Mapping(target = "userremove", source = "userremove", qualifiedByName = "employeeId")
    CorrespondencecopyDTO toDto(Correspondencecopy s);

    @Named("correspondenceId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    CorrespondenceDTO toDtoCorrespondenceId(Correspondence correspondence);

    @Named("employeeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    EmployeeDTO toDtoEmployeeId(Employee employee);

    @Named("unitId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    UnitDTO toDtoUnitId(Unit unit);

    @Named("actionId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    ActionDTO toDtoActionId(Action action);
}
