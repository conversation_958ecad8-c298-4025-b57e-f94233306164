package com.dq.lilas.service.dto;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;
import java.util.Map;

@Data
@NoArgsConstructor
@AllArgsConstructor
public class BCCheckResponse {

    @JsonProperty("status")
    private String status;

    @JsonProperty("Société")
    private String company;

    @JsonProperty("Matricule fiscal")
    private String taxNumber;

    @JsonProperty("Lieu de livraison")
    private String deliveryAddress;

    @JsonProperty("extracted_data")
    private ExtractedData extractedData;

    @Data
    @NoArgsConstructor
    @AllArgsConstructor
    public static class ExtractedData {
        @JsonProperty("header_info")
        private Map<String, String> headerInfo;

        @JsonProperty("delivery_info")
        private Map<String, String> deliveryInfo;

        @JsonProperty("table_info")
        private Map<String, List<Map<String, String>>> tableInfo;

        @JsonProperty("footer_info")
        private Map<String, String> footerInfo;
    }
}
