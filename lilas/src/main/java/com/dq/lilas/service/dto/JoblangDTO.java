package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Joblang} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class JoblangDTO implements Serializable {

    private Long id;

    @Size(max = 255)
    private String lbl;

    @Size(max = 5)
    private String lang;

    @Size(max = 255)
    private String abr;

    private JobDTO job;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLbl() {
        return lbl;
    }

    public void setLbl(String lbl) {
        this.lbl = lbl;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getAbr() {
        return abr;
    }

    public void setAbr(String abr) {
        this.abr = abr;
    }

    public JobDTO getJob() {
        return job;
    }

    public void setJob(JobDTO job) {
        this.job = job;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof JoblangDTO)) {
            return false;
        }

        JoblangDTO joblangDTO = (JoblangDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, joblangDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "JoblangDTO{" +
            "id=" + getId() +
            ", lbl='" + getLbl() + "'" +
            ", lang='" + getLang() + "'" +
            ", abr='" + getAbr() + "'" +
            ", job=" + getJob() +
            "}";
    }
}
