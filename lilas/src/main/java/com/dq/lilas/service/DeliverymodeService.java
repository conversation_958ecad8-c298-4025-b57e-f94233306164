package com.dq.lilas.service;

import com.dq.lilas.service.dto.DeliverymodeDTO;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.Deliverymode}.
 */
public interface DeliverymodeService {
    /**
     * Save a deliverymode.
     *
     * @param deliverymodeDTO the entity to save.
     * @return the persisted entity.
     */
    DeliverymodeDTO save(DeliverymodeDTO deliverymodeDTO);

    /**
     * Updates a deliverymode.
     *
     * @param deliverymodeDTO the entity to update.
     * @return the persisted entity.
     */
    DeliverymodeDTO update(DeliverymodeDTO deliverymodeDTO);

    /**
     * Partially updates a deliverymode.
     *
     * @param deliverymodeDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<DeliverymodeDTO> partialUpdate(DeliverymodeDTO deliverymodeDTO);

    /**
     * Get all the deliverymodes.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<DeliverymodeDTO> findAll(Pageable pageable);

    /**
     * Get the "id" deliverymode.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<DeliverymodeDTO> findOne(Long id);

    /**
     * Delete the "id" deliverymode.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
}
