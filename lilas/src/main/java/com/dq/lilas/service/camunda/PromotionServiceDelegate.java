package com.dq.lilas.service.camunda;

import com.dq.lilas.domain.DemandePromotion;
import com.dq.lilas.domain.enumeration.PromotionStatus;
import com.dq.lilas.repository.DemandePromotionRepository;
import com.dq.lilas.service.PromotionNotificationService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Service delegate for applying promotion in Camunda workflow
 */
@Component("promotionService")
public class PromotionServiceDelegate implements JavaDelegate {

    private static final Logger LOG = LoggerFactory.getLogger(PromotionServiceDelegate.class);
    
    @Autowired
    private DemandePromotionRepository demandePromotionRepository;
    
    @Autowired
    private PromotionNotificationService promotionNotificationService;
    
    @Override
    public void execute(DelegateExecution execution) throws Exception {
        LOG.info("Executing promotion application service task");
        
        try {
            Long demandePromotionId = (Long) execution.getVariable("demandePromotionId");
            Boolean approved = (Boolean) execution.getVariable("approved");
            String reviewComments = (String) execution.getVariable("reviewComments");
            
            LOG.info("Processing promotion request ID: {} with approval status: {}", demandePromotionId, approved);
            
            if (demandePromotionId == null) {
                LOG.error("No demandePromotionId found in process variables");
                throw new RuntimeException("Missing demandePromotionId in process variables");
            }
            
            // Retrieve the promotion request
            DemandePromotion demandePromotion = demandePromotionRepository.findById(demandePromotionId)
                .orElseThrow(() -> new RuntimeException("Promotion request not found: " + demandePromotionId));
            
            // Apply the promotion (this is where business logic would go)
            if (Boolean.TRUE.equals(approved)) {
                LOG.info("Applying approved promotion for request ID: {}", demandePromotionId);
                
                // Update status to Approved
                demandePromotion.setStatus(PromotionStatus.Approved);
                
                // Update promotion details status
                if (demandePromotion.getPromotionDetails() != null && !demandePromotion.getPromotionDetails().isEmpty()) {
                    demandePromotion.getPromotionDetails().forEach(detail -> {
                        detail.setStatus(PromotionStatus.Approved);
                        if (reviewComments != null && !reviewComments.trim().isEmpty()) {
                            detail.setApproManagerGMS(reviewComments);
                        }
                    });
                }
                
                // Save the changes
                demandePromotionRepository.save(demandePromotion);
                
                // Here you would typically:
                // 1. Apply the promotion to the pricing system
                // 2. Update inventory systems
                // 3. Notify relevant systems
                // 4. Generate reports
                
                LOG.info("Successfully applied promotion for request ID: {}", demandePromotionId);
                
                // Send approval notification to the requester
                try {
                    promotionNotificationService.notifyRequesterOnStatusChange(
                        demandePromotion, 
                        PromotionStatus.Approved, 
                        reviewComments
                    );
                    LOG.info("Approval notification sent for promotion request ID: {}", demandePromotionId);
                } catch (Exception e) {
                    LOG.warn("Failed to send approval notification for promotion request ID: {}", demandePromotionId, e);
                    // Don't fail the process if notification fails
                }
                
            } else {
                LOG.info("Processing rejection for promotion request ID: {}", demandePromotionId);
                
                // Update status to Rejected
                demandePromotion.setStatus(PromotionStatus.Rejected);
                
                // Update promotion details status
                if (demandePromotion.getPromotionDetails() != null && !demandePromotion.getPromotionDetails().isEmpty()) {
                    demandePromotion.getPromotionDetails().forEach(detail -> {
                        detail.setStatus(PromotionStatus.Rejected);
                        if (reviewComments != null && !reviewComments.trim().isEmpty()) {
                            detail.setApproManagerGMS("REJECTED: " + reviewComments);
                        }
                    });
                }
                
                // Save the changes
                demandePromotionRepository.save(demandePromotion);
                
                // Send rejection notification to the requester
                try {
                    promotionNotificationService.notifyRequesterOnStatusChange(
                        demandePromotion, 
                        PromotionStatus.Rejected, 
                        reviewComments
                    );
                    LOG.info("Rejection notification sent for promotion request ID: {}", demandePromotionId);
                } catch (Exception e) {
                    LOG.warn("Failed to send rejection notification for promotion request ID: {}", demandePromotionId, e);
                    // Don't fail the process if notification fails
                }
            }
            
        } catch (Exception e) {
            LOG.error("Error executing promotion application service task", e);
            throw e; // Re-throw to make Camunda handle the error
        }
    }
    
    /**
     * Method called by BPMN expression ${promotionService.apply()}
     * This is an alternative approach for simple cases
     */
    public void apply() {
        LOG.info("Apply method called via BPMN expression");
        // This method would be called if using expression instead of delegate
        // For now, we'll use the JavaDelegate approach which is more robust
    }
}
