package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Correspondence;
import com.dq.lilas.domain.Deliverymode;
import com.dq.lilas.domain.Employee;
import com.dq.lilas.domain.Typecorrespondence;
import com.dq.lilas.domain.Unit;
import com.dq.lilas.service.dto.CorrespondenceDTO;
import com.dq.lilas.service.dto.DeliverymodeDTO;
import com.dq.lilas.service.dto.EmployeeDTO;
import com.dq.lilas.service.dto.TypecorrespondenceDTO;
import com.dq.lilas.service.dto.UnitDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Correspondence} and its DTO {@link CorrespondenceDTO}.
 */
@Mapper(componentModel = "spring")
public interface CorrespondenceMapper extends EntityMapper<CorrespondenceDTO, Correspondence> {
    @Mapping(target = "employee", source = "employee", qualifiedByName = "employeeId")
    @Mapping(target = "unit", source = "unit", qualifiedByName = "unitId")
    @Mapping(target = "deliverymode", source = "deliverymode", qualifiedByName = "deliverymodeId")
    @Mapping(target = "typecorrespondence", source = "typecorrespondence", qualifiedByName = "typecorrespondenceId")
    CorrespondenceDTO toDto(Correspondence s);

    @Named("employeeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    EmployeeDTO toDtoEmployeeId(Employee employee);

    @Named("unitId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    UnitDTO toDtoUnitId(Unit unit);

    @Named("deliverymodeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    DeliverymodeDTO toDtoDeliverymodeId(Deliverymode deliverymode);

    @Named("typecorrespondenceId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TypecorrespondenceDTO toDtoTypecorrespondenceId(Typecorrespondence typecorrespondence);
}
