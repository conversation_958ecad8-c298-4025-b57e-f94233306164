package com.dq.lilas.service;

import com.dq.lilas.service.dto.GmsBrandsDTO;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.GmsBrands}.
 */
public interface GmsBrandsService {
    /**
     * Save a gmsBrands.
     *
     * @param gmsBrandsDTO the entity to save.
     * @return the persisted entity.
     */
    GmsBrandsDTO save(GmsBrandsDTO gmsBrandsDTO);

    /**
     * Updates a gmsBrands.
     *
     * @param gmsBrandsDTO the entity to update.
     * @return the persisted entity.
     */
    GmsBrandsDTO update(GmsBrandsDTO gmsBrandsDTO);

    /**
     * Partially updates a gmsBrands.
     *
     * @param gmsBrandsDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<GmsBrandsDTO> partialUpdate(GmsBrandsDTO gmsBrandsDTO);

    /**
     * Get all the gmsBrands.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<GmsBrandsDTO> findAll(Pageable pageable);

    /**
     * Get the "id" gmsBrands.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<GmsBrandsDTO> findOne(Long id);

    /**
     * Delete the "id" gmsBrands.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);
    
    /**
     * Get all brands without pagination
     * 
     * @return list of all brands
     */
    List<GmsBrandsDTO> findAllBrands();
}
