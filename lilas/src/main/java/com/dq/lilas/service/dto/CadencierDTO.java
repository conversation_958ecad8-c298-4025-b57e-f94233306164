package com.dq.lilas.service.dto;

import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Cadencier} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class CadencierDTO implements Serializable {

    private Long id;

    private Integer orderDay;

    private Integer deliveryDay;

    private String region;

    private GmsClientsDTO gmsClients;


    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Integer getOrderDay() {
        return orderDay;
    }

    public void setOrderDay(Integer orderDay) {
        this.orderDay = orderDay;
    }

    public Integer getDeliveryDay() {
        return deliveryDay;
    }

    public void setDeliveryDay(Integer deliveryDay) {
        this.deliveryDay = deliveryDay;
    }

    public String getRegion() {
        return region;
    }

    public void setRegion(String region) {
        this.region = region;
    }

    public GmsClientsDTO getGmsClients() {
        return gmsClients;
    }

    public void setGmsClients(GmsClientsDTO gmsClients) {
        this.gmsClients = gmsClients;
    }


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof CadencierDTO)) {
            return false;
        }

        CadencierDTO cadencierDTO = (CadencierDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, cadencierDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "CadencierDTO{" +
            "id=" + getId() +
            ", orderDay=" + getOrderDay() +
            ", deliveryDay=" + getDeliveryDay() +
            ", region='" + getRegion() + "'" +
            "}";
    }
}
