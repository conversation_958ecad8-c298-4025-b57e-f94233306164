package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Employee;
import com.dq.lilas.domain.TemplateConditions;
import com.dq.lilas.service.dto.EmployeeDTO;
import com.dq.lilas.service.dto.TemplateConditionsDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link TemplateConditions} and its DTO {@link TemplateConditionsDTO}.
 */
@Mapper(componentModel = "spring")
public interface TemplateConditionsMapper extends EntityMapper<TemplateConditionsDTO, TemplateConditions> {
    @Mapping(target = "employee", source = "employee", qualifiedByName = "employeeId")
    TemplateConditionsDTO toDto(TemplateConditions s);

    @Named("employeeId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    EmployeeDTO toDtoEmployeeId(Employee employee);
}
