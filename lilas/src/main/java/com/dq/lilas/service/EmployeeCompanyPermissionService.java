package com.dq.lilas.service;

import com.dq.lilas.service.dto.EmployeeCompanyPermissionDTO;
import java.util.List;
import java.util.Optional;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

/**
 * Service Interface for managing {@link com.dq.lilas.domain.EmployeeCompanyPermission}.
 */
public interface EmployeeCompanyPermissionService {
    /**
     * Save a employeeCompanyPermission.
     *
     * @param employeeCompanyPermissionDTO the entity to save.
     * @return the persisted entity.
     */
    EmployeeCompanyPermissionDTO save(EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO);

    /**
     * Updates a employeeCompanyPermission.
     *
     * @param employeeCompanyPermissionDTO the entity to update.
     * @return the persisted entity.
     */
    EmployeeCompanyPermissionDTO update(EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO);

    /**
     * Partially updates a employeeCompanyPermission.
     *
     * @param employeeCompanyPermissionDTO the entity to update partially.
     * @return the persisted entity.
     */
    Optional<EmployeeCompanyPermissionDTO> partialUpdate(EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO);

    /**
     * Get all the employeeCompanyPermissions.
     *
     * @return the list of entities.
     */
    List<EmployeeCompanyPermissionDTO> findAll();

    /**
     * Get all the employeeCompanyPermissions with eager load of many-to-many relationships.
     *
     * @param pageable the pagination information.
     * @return the list of entities.
     */
    Page<EmployeeCompanyPermissionDTO> findAllWithEagerRelationships(Pageable pageable);

    /**
     * Get the "id" employeeCompanyPermission.
     *
     * @param id the id of the entity.
     * @return the entity.
     */
    Optional<EmployeeCompanyPermissionDTO> findOne(Long id);

    /**
     * Delete the "id" employeeCompanyPermission.
     *
     * @param id the id of the entity.
     */
    void delete(Long id);


    /**
     * Get the company IDs by a specific employee.
     *
     * @param employeeId the ID of the employee.
     * @return the list of company IDs.
     */
    List<Long> findCompanyIdsByEmployeeId(Long employeeId);
}
