package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Typecorrespondence;
import com.dq.lilas.domain.Typecorrespondencelang;
import com.dq.lilas.service.dto.TypecorrespondenceDTO;
import com.dq.lilas.service.dto.TypecorrespondencelangDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Typecorrespondencelang} and its DTO {@link TypecorrespondencelangDTO}.
 */
@Mapper(componentModel = "spring")
public interface TypecorrespondencelangMapper extends EntityMapper<TypecorrespondencelangDTO, Typecorrespondencelang> {
    @Mapping(target = "typecorrespondence", source = "typecorrespondence", qualifiedByName = "typecorrespondenceId")
    TypecorrespondencelangDTO toDto(Typecorrespondencelang s);

    @Named("typecorrespondenceId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    TypecorrespondenceDTO toDtoTypecorrespondenceId(Typecorrespondence typecorrespondence);
}
