package com.dq.lilas.service.camunda;

import com.dq.lilas.domain.DemandePromotion;
import com.dq.lilas.domain.enumeration.PromotionStatus;
import com.dq.lilas.repository.DemandePromotionRepository;
import com.dq.lilas.service.PromotionNotificationService;
import org.camunda.bpm.engine.delegate.DelegateExecution;
import org.camunda.bpm.engine.delegate.JavaDelegate;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

/**
 * Service delegate for handling promotion request rejection and sending rejection notification
 */
@Component("rejectionService")
public class PromotionRejectionDelegate implements JavaDelegate {

    private static final Logger LOG = LoggerFactory.getLogger(PromotionRejectionDelegate.class);

    private final DemandePromotionRepository demandePromotionRepository;
    private final PromotionNotificationService promotionNotificationService;

    public PromotionRejectionDelegate(
        DemandePromotionRepository demandePromotionRepository,
        PromotionNotificationService promotionNotificationService
    ) {
        this.demandePromotionRepository = demandePromotionRepository;
        this.promotionNotificationService = promotionNotificationService;
    }

    @Override
    public void execute(DelegateExecution execution) throws Exception {
        LOG.info("Executing promotion rejection service task");
        
        try {
            Long demandePromotionId = (Long) execution.getVariable("demandePromotionId");
            String gmsComments = (String) execution.getVariable("gmsComments");
            
            LOG.info("Processing rejection for promotion request ID: {}", demandePromotionId);
            
            if (demandePromotionId == null) {
                LOG.error("No demandePromotionId found in process variables");
                throw new RuntimeException("Missing demandePromotionId in process variables");
            }
            
            // Retrieve the promotion request
            DemandePromotion demandePromotion = demandePromotionRepository.findById(demandePromotionId)
                .orElseThrow(() -> new RuntimeException("Promotion request not found: " + demandePromotionId));
            
            // Update status to Rejected (if not already done)
            if (demandePromotion.getStatus() != PromotionStatus.Rejected) {
                demandePromotion.setStatus(PromotionStatus.Rejected);
                
                // Update promotion details status if they exist
                if (demandePromotion.getPromotionDetails() != null && !demandePromotion.getPromotionDetails().isEmpty()) {
                    demandePromotion.getPromotionDetails().forEach(detail -> {
                        detail.setStatus(PromotionStatus.Rejected);
                        if (gmsComments != null && !gmsComments.trim().isEmpty()) {
                            detail.setApproManagerGMS("REJECTED: " + gmsComments);
                        }
                    });
                }
                
                demandePromotion = demandePromotionRepository.save(demandePromotion);
                LOG.info("Updated promotion request ID: {} status to Rejected", demandePromotionId);
            }
            
            // Send rejection notification to the requester
            promotionNotificationService.notifyRequesterOnStatusChange(demandePromotion, PromotionStatus.Rejected, gmsComments);
            
            LOG.info("Successfully processed rejection and sent notification for promotion request ID: {}", demandePromotionId);
            
        } catch (Exception e) {
            LOG.error("Failed to process promotion rejection", e);
            throw e; // Re-throw to fail the process if rejection handling fails
        }
    }
}
