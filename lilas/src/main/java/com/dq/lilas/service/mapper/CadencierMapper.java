package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Cadencier;
import com.dq.lilas.domain.Company;
import com.dq.lilas.domain.DailyBatches;
import com.dq.lilas.domain.GmsClients;
import com.dq.lilas.service.dto.CadencierDTO;
import com.dq.lilas.service.dto.CompanyDTO;
import com.dq.lilas.service.dto.DailyBatchesDTO;
import com.dq.lilas.service.dto.GmsClientsDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Cadencier} and its DTO {@link CadencierDTO}.
 */
@Mapper(componentModel = "spring")
public interface CadencierMapper extends EntityMapper<CadencierDTO, Cadencier> {

    @Mapping(target = "gmsClients", source = "gmsClients", qualifiedByName = "gmsClientsId")
    CadencierDTO toDto(Cadencier c);

    @Named("gmsClientsId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    GmsClientsDTO toDtoGmsClientsId(GmsClients gmsClients);

}
