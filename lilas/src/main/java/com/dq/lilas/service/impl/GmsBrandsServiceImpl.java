package com.dq.lilas.service.impl;

import com.dq.lilas.domain.GmsBrands;
import com.dq.lilas.repository.GmsBrandsRepository;
import com.dq.lilas.service.GmsBrandsService;
import com.dq.lilas.service.dto.GmsBrandsDTO;
import com.dq.lilas.service.mapper.GmsBrandsMapper;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.GmsBrands}.
 */
@Service
@Transactional
public class GmsBrandsServiceImpl implements GmsBrandsService {

    private static final Logger LOG = LoggerFactory.getLogger(GmsBrandsServiceImpl.class);

    private final GmsBrandsRepository gmsBrandsRepository;

    private final GmsBrandsMapper gmsBrandsMapper;

    public GmsBrandsServiceImpl(GmsBrandsRepository gmsBrandsRepository, GmsBrandsMapper gmsBrandsMapper) {
        this.gmsBrandsRepository = gmsBrandsRepository;
        this.gmsBrandsMapper = gmsBrandsMapper;
    }

    @Override
    public GmsBrandsDTO save(GmsBrandsDTO gmsBrandsDTO) {
        LOG.debug("Request to save GmsBrands : {}", gmsBrandsDTO);
        GmsBrands gmsBrands = gmsBrandsMapper.toEntity(gmsBrandsDTO);
        gmsBrands = gmsBrandsRepository.save(gmsBrands);
        return gmsBrandsMapper.toDto(gmsBrands);
    }

    @Override
    public GmsBrandsDTO update(GmsBrandsDTO gmsBrandsDTO) {
        LOG.debug("Request to update GmsBrands : {}", gmsBrandsDTO);
        GmsBrands gmsBrands = gmsBrandsMapper.toEntity(gmsBrandsDTO);
        gmsBrands = gmsBrandsRepository.save(gmsBrands);
        return gmsBrandsMapper.toDto(gmsBrands);
    }

    @Override
    public Optional<GmsBrandsDTO> partialUpdate(GmsBrandsDTO gmsBrandsDTO) {
        LOG.debug("Request to partially update GmsBrands : {}", gmsBrandsDTO);

        return gmsBrandsRepository
            .findById(gmsBrandsDTO.getId())
            .map(existingGmsBrands -> {
                gmsBrandsMapper.partialUpdate(existingGmsBrands, gmsBrandsDTO);

                return existingGmsBrands;
            })
            .map(gmsBrandsRepository::save)
            .map(gmsBrandsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GmsBrandsDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all GmsBrands");
        return gmsBrandsRepository.findAll(pageable).map(gmsBrandsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<GmsBrandsDTO> findOne(Long id) {
        LOG.debug("Request to get GmsBrands : {}", id);
        return gmsBrandsRepository.findById(id).map(gmsBrandsMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete GmsBrands : {}", id);
        gmsBrandsRepository.deleteById(id);
    }
    
    @Override
    @Transactional(readOnly = true)
    public List<GmsBrandsDTO> findAllBrands() {
        LOG.debug("Request to get all GmsBrands without pagination");
        return gmsBrandsRepository.findAll().stream()
            .map(gmsBrandsMapper::toDto)
            .collect(Collectors.toList());
    }
}
