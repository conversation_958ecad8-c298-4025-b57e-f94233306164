package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Unitlang} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class UnitlangDTO implements Serializable {

    private Long id;

    @Size(max = 500)
    private String address;

    @Size(max = 15)
    private String nbr;

    @NotNull
    @Size(max = 500)
    private String name;

    @NotNull
    @Size(max = 5)
    private String lang;

    @Size(max = 15)
    private String abrv;

    private UnitDTO unit;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getAddress() {
        return address;
    }

    public void setAddress(String address) {
        this.address = address;
    }

    public String getNbr() {
        return nbr;
    }

    public void setNbr(String nbr) {
        this.nbr = nbr;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getAbrv() {
        return abrv;
    }

    public void setAbrv(String abrv) {
        this.abrv = abrv;
    }

    public UnitDTO getUnit() {
        return unit;
    }

    public void setUnit(UnitDTO unit) {
        this.unit = unit;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof UnitlangDTO)) {
            return false;
        }

        UnitlangDTO unitlangDTO = (UnitlangDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, unitlangDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "UnitlangDTO{" +
            "id=" + getId() +
            ", address='" + getAddress() + "'" +
            ", nbr='" + getNbr() + "'" +
            ", name='" + getName() + "'" +
            ", lang='" + getLang() + "'" +
            ", abrv='" + getAbrv() + "'" +
            ", unit=" + getUnit() +
            "}";
    }
}
