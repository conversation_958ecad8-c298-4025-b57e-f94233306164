package com.dq.lilas.service.impl;

import com.dq.lilas.domain.Joblang;
import com.dq.lilas.repository.JoblangRepository;
import com.dq.lilas.service.JoblangService;
import com.dq.lilas.service.dto.JoblangDTO;
import com.dq.lilas.service.mapper.JoblangMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.Joblang}.
 */
@Service
@Transactional
public class JoblangServiceImpl implements JoblangService {

    private static final Logger LOG = LoggerFactory.getLogger(JoblangServiceImpl.class);

    private final JoblangRepository joblangRepository;

    private final JoblangMapper joblangMapper;

    public JoblangServiceImpl(JoblangRepository joblangRepository, JoblangMapper joblangMapper) {
        this.joblangRepository = joblangRepository;
        this.joblangMapper = joblangMapper;
    }

    @Override
    public JoblangDTO save(JoblangDTO joblangDTO) {
        LOG.debug("Request to save Joblang : {}", joblangDTO);
        Joblang joblang = joblangMapper.toEntity(joblangDTO);
        joblang = joblangRepository.save(joblang);
        return joblangMapper.toDto(joblang);
    }

    @Override
    public JoblangDTO update(JoblangDTO joblangDTO) {
        LOG.debug("Request to update Joblang : {}", joblangDTO);
        Joblang joblang = joblangMapper.toEntity(joblangDTO);
        joblang = joblangRepository.save(joblang);
        return joblangMapper.toDto(joblang);
    }

    @Override
    public Optional<JoblangDTO> partialUpdate(JoblangDTO joblangDTO) {
        LOG.debug("Request to partially update Joblang : {}", joblangDTO);

        return joblangRepository
            .findById(joblangDTO.getId())
            .map(existingJoblang -> {
                joblangMapper.partialUpdate(existingJoblang, joblangDTO);

                return existingJoblang;
            })
            .map(joblangRepository::save)
            .map(joblangMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<JoblangDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all Joblangs");
        return joblangRepository.findAll(pageable).map(joblangMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<JoblangDTO> findOne(Long id) {
        LOG.debug("Request to get Joblang : {}", id);
        return joblangRepository.findById(id).map(joblangMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete Joblang : {}", id);
        joblangRepository.deleteById(id);
    }
}
