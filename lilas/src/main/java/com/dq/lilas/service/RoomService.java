package com.dq.lilas.service;

import com.dq.lilas.domain.Room;
import com.dq.lilas.service.dto.RoomDTO;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;

import java.util.List;
import java.util.Optional;

/**
 * Service Interface for managing Room.
 */
public interface RoomService {

    /**
     * Save a room.
     *
     * @param roomDTO the entity to save
     * @return the persisted entity
     */
    RoomDTO save(RoomDTO roomDTO);

    /**
     * Get all the rooms.
     *
     * @param pageable the pagination information
     * @return the list of entities
     */
    Page<RoomDTO> findAllByPage(Pageable pageable);

    /**
     * Get all the rooms.
     *
     * @return the list of entities
     */
    List<RoomDTO> findAll();


    /**
     * Get all the Room with eager load of many-to-many relationships.
     *
     * @return the list of entities
     */
    Page<RoomDTO> findAllWithEagerRelationships(Pageable pageable);

    /**
     * Create First Room in Group.
     *
     * @param roomDTO the entity.
     */
    void createFirstRoomInGroup(RoomDTO roomDTO);

    /**
     * Get Private Room by Logged User And Selected User.
     *
     * @param id the id of the selected user.
     * @return the entity.
     */
    RoomDTO getPrivateRoomByLoggedUserAndSelectedUser(Long id);

    /**
     * Get all Rooms joined by Logged User.
     *
     * @return the list of entities.
     */
    List<RoomDTO> findAllByLoggedUser();

    /**
     * Get all rooms of a group joined by the logged user.
     *
     * @return the list of entities.
     */
    List<RoomDTO> findAllOfGroupJoinedByLoggedUser(Long idGroup);

    /**
     * Get all rooms of group.
     *
     * @return the list of entities.
     */
    List<RoomDTO> findAllByGroup(Long idGroup);

    /**
     * Update (add or remove) logged user from room by name and group.
     *
     * @param groupId the group entity id.
     * @param name the room entity name.
     * @param isAdd boolean to indicate to add new user to room or remove.
     */
    void updateLoggedUserFromRoomByNameAndGroup(Long groupId, String name, Boolean isAdd);

    /**
     * Get the "id" room.
     *
     * @param id the id of the entity
     * @return the entity
     */
    Optional<RoomDTO> findOne(Long id);

    /**
     * Get the "id" room.
     *
     * @param id the id of the entity
     * @return the entity
     */
    Optional<Room> findById(Long id);


    /**
     * Delete the "id" room.
     *
     * @param id the id of the entity
     */
    void delete(Long id);
}
