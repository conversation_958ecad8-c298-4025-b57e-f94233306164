package com.dq.lilas.service.dto;

import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.EmployeeCompanyPermission} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class EmployeeCompanyPermissionDTO implements Serializable {

    private Long id;

    private EmployeeDTO employee;

    private CompanyDTO company;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public EmployeeDTO getEmployee() {
        return employee;
    }

    public void setEmployee(EmployeeDTO employee) {
        this.employee = employee;
    }

    public CompanyDTO getCompany() {
        return company;
    }

    public void setCompany(CompanyDTO company) {
        this.company = company;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof EmployeeCompanyPermissionDTO)) {
            return false;
        }

        EmployeeCompanyPermissionDTO employeeCompanyPermissionDTO = (EmployeeCompanyPermissionDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, employeeCompanyPermissionDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "EmployeeCompanyPermissionDTO{" +
            "id=" + getId() +
            ", employee=" + getEmployee() +
            ", company=" + getCompany() +
            "}";
    }
}
