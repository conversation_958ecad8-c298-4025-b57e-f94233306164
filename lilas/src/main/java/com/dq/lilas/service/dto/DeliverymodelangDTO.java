package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Deliverymodelang} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class DeliverymodelangDTO implements Serializable {

    private Long id;

    @Size(max = 250)
    private String lbl;

    @Size(max = 5)
    private String lang;

    @Size(max = 10)
    private String abrv;

    private DeliverymodeDTO deliverymode;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getLbl() {
        return lbl;
    }

    public void setLbl(String lbl) {
        this.lbl = lbl;
    }

    public String getLang() {
        return lang;
    }

    public void setLang(String lang) {
        this.lang = lang;
    }

    public String getAbrv() {
        return abrv;
    }

    public void setAbrv(String abrv) {
        this.abrv = abrv;
    }

    public DeliverymodeDTO getDeliverymode() {
        return deliverymode;
    }

    public void setDeliverymode(DeliverymodeDTO deliverymode) {
        this.deliverymode = deliverymode;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DeliverymodelangDTO)) {
            return false;
        }

        DeliverymodelangDTO deliverymodelangDTO = (DeliverymodelangDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, deliverymodelangDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "DeliverymodelangDTO{" +
            "id=" + getId() +
            ", lbl='" + getLbl() + "'" +
            ", lang='" + getLang() + "'" +
            ", abrv='" + getAbrv() + "'" +
            ", deliverymode=" + getDeliverymode() +
            "}";
    }
}
