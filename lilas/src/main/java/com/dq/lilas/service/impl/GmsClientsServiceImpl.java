package com.dq.lilas.service.impl;

import com.dq.lilas.domain.GmsClients;
import com.dq.lilas.repository.GmsClientsRepository;
import com.dq.lilas.service.GmsClientsService;
import com.dq.lilas.service.dto.GmsClientsDTO;
import com.dq.lilas.service.mapper.GmsClientsMapper;
import java.util.List;
import java.util.Optional;
import java.util.stream.Collectors;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.GmsClients}.
 */
@Service
@Transactional
public class GmsClientsServiceImpl implements GmsClientsService {

    private static final Logger LOG = LoggerFactory.getLogger(GmsClientsServiceImpl.class);

    private final GmsClientsRepository gmsClientsRepository;

    private final GmsClientsMapper gmsClientsMapper;

    public GmsClientsServiceImpl(GmsClientsRepository gmsClientsRepository, GmsClientsMapper gmsClientsMapper) {
        this.gmsClientsRepository = gmsClientsRepository;
        this.gmsClientsMapper = gmsClientsMapper;
    }

    @Override
    public GmsClientsDTO save(GmsClientsDTO gmsClientsDTO) {
        LOG.debug("Request to save GmsClients : {}", gmsClientsDTO);
        GmsClients gmsClients = gmsClientsMapper.toEntity(gmsClientsDTO);
        gmsClients = gmsClientsRepository.save(gmsClients);
        return gmsClientsMapper.toDto(gmsClients);
    }

    @Override
    public GmsClientsDTO update(GmsClientsDTO gmsClientsDTO) {
        LOG.debug("Request to update GmsClients : {}", gmsClientsDTO);
        GmsClients gmsClients = gmsClientsMapper.toEntity(gmsClientsDTO);
        gmsClients = gmsClientsRepository.save(gmsClients);
        return gmsClientsMapper.toDto(gmsClients);
    }

    @Override
    public Optional<GmsClientsDTO> partialUpdate(GmsClientsDTO gmsClientsDTO) {
        LOG.debug("Request to partially update GmsClients : {}", gmsClientsDTO);

        return gmsClientsRepository
            .findById(gmsClientsDTO.getId())
            .map(existingGmsClients -> {
                gmsClientsMapper.partialUpdate(existingGmsClients, gmsClientsDTO);

                return existingGmsClients;
            })
            .map(gmsClientsRepository::save)
            .map(gmsClientsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<GmsClientsDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all GmsClients");
        return gmsClientsRepository.findAll(pageable).map(gmsClientsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<GmsClientsDTO> findOne(Long id) {
        LOG.debug("Request to get GmsClients : {}", id);
        return gmsClientsRepository.findById(id).map(gmsClientsMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete GmsClients : {}", id);
        gmsClientsRepository.deleteById(id);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<GmsClientsDTO> findByFiscaleId(String fiscaleId) {
        LOG.debug("Request to get GmsClients by fiscal ID : {}", fiscaleId);
        return gmsClientsRepository.findByFiscaleId(fiscaleId).map(gmsClientsMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public List<GmsClientsDTO> findAllByFiscaleId(String fiscaleId) {
        LOG.debug("Request to get all GmsClients by fiscal ID : {}", fiscaleId);
        return gmsClientsRepository.findAllByFiscaleId(fiscaleId).stream()
            .map(gmsClientsMapper::toDto)
            .collect(Collectors.toList());
    }
}
