package com.dq.lilas.service.dto;

import jakarta.validation.constraints.*;
import java.io.Serializable;
import java.util.Objects;

/**
 * A DTO for the {@link com.dq.lilas.domain.Deliverymode} entity.
 */
@SuppressWarnings("common-java:DuplicatedBlocks")
public class DeliverymodeDTO implements Serializable {

    private Long id;

    @Size(max = 1)
    private String statut;

    @Size(max = 5)
    private String orderpos;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getStatut() {
        return statut;
    }

    public void setStatut(String statut) {
        this.statut = statut;
    }

    public String getOrderpos() {
        return orderpos;
    }

    public void setOrderpos(String orderpos) {
        this.orderpos = orderpos;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof DeliverymodeDTO)) {
            return false;
        }

        DeliverymodeDTO deliverymodeDTO = (DeliverymodeDTO) o;
        if (this.id == null) {
            return false;
        }
        return Objects.equals(this.id, deliverymodeDTO.id);
    }

    @Override
    public int hashCode() {
        return Objects.hash(this.id);
    }

    // prettier-ignore
    @Override
    public String toString() {
        return "DeliverymodeDTO{" +
            "id=" + getId() +
            ", statut='" + getStatut() + "'" +
            ", orderpos='" + getOrderpos() + "'" +
            "}";
    }
}
