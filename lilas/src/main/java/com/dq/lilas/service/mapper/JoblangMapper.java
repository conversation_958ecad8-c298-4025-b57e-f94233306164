package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Job;
import com.dq.lilas.domain.Joblang;
import com.dq.lilas.service.dto.JobDTO;
import com.dq.lilas.service.dto.JoblangDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Joblang} and its DTO {@link JoblangDTO}.
 */
@Mapper(componentModel = "spring")
public interface JoblangMapper extends EntityMapper<JoblangDTO, Joblang> {
    @Mapping(target = "job", source = "job", qualifiedByName = "jobId")
    JoblangDTO toDto(Joblang s);

    @Named("jobId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    JobDTO toDtoJobId(Job job);
}
