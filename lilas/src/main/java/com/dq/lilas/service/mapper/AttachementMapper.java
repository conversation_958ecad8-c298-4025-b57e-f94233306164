package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Attachement;
import com.dq.lilas.domain.Order;
import com.dq.lilas.service.dto.AttachementDTO;
import com.dq.lilas.service.dto.OrderDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Attachement} and its DTO {@link AttachementDTO}.
 */
@Mapper(componentModel = "spring")
public interface AttachementMapper extends EntityMapper<AttachementDTO, Attachement> {
    @Mapping(target = "order", source = "order", qualifiedByName = "orderId")
    AttachementDTO toDto(Attachement s);

    @Named("orderId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    OrderDTO toDtoOrderId(Order order);
}
