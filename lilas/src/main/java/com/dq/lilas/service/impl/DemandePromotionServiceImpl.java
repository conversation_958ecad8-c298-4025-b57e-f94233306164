package com.dq.lilas.service.impl;

import com.dq.lilas.domain.DemandePromotion;
import com.dq.lilas.domain.Employee;
import com.dq.lilas.domain.enumeration.PromotionStatus;
import com.dq.lilas.repository.DemandePromotionRepository;
import com.dq.lilas.repository.EmployeeRepository;
import com.dq.lilas.security.SecurityUtils;
import com.dq.lilas.service.DemandePromotionService;
import com.dq.lilas.service.PromotionNotificationService;
import com.dq.lilas.service.UserService;
import com.dq.lilas.service.dto.DemandePromotionDTO;
import com.dq.lilas.service.mapper.DemandePromotionMapper;
import java.util.HashMap;
import java.util.Map;
import java.util.Optional;
import java.util.stream.Collectors;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.DemandePromotion}.
 */
@Service
@Transactional
public class DemandePromotionServiceImpl implements DemandePromotionService {

    private static final Logger LOG = LoggerFactory.getLogger(DemandePromotionServiceImpl.class);

    private final DemandePromotionRepository demandePromotionRepository;

    private final DemandePromotionMapper demandePromotionMapper;

    private final UserService userService;

    private final EmployeeRepository employeeRepository;

    private final PromotionNotificationService promotionNotificationService;

    // Camunda workflow integration
    private final RuntimeService runtimeService;
    private final TaskService taskService;
    
    private static final String PROCESS_KEY = "Process_1";
    private static final String MESSAGE_NAME = "Message_0gu6vpn";

    public DemandePromotionServiceImpl(
        DemandePromotionRepository demandePromotionRepository,
        DemandePromotionMapper demandePromotionMapper,
        UserService userService,
        EmployeeRepository employeeRepository,
        PromotionNotificationService promotionNotificationService,
        RuntimeService runtimeService,
        TaskService taskService
    ) {
        this.demandePromotionRepository = demandePromotionRepository;
        this.demandePromotionMapper = demandePromotionMapper;
        this.userService = userService;
        this.employeeRepository = employeeRepository;
        this.promotionNotificationService = promotionNotificationService;
        this.runtimeService = runtimeService;
        this.taskService = taskService;
    }

    @Override
    public DemandePromotionDTO save(DemandePromotionDTO demandePromotionDTO) {
        LOG.debug("Request to save DemandePromotion : {}", demandePromotionDTO);
        
        // Ensure status is always set
        if (demandePromotionDTO.getStatus() == null) {
            demandePromotionDTO.setStatus(PromotionStatus.Waiting);
            LOG.debug("Setting default status to Waiting for new DemandePromotion");
        }
        
        // Get current user and associate with employee
        String currentUserLogin = SecurityUtils.getCurrentUserLogin()
            .orElseThrow(() -> new RuntimeException("Current user login not found"));
        
        LOG.debug("Saving DemandePromotion for user: {}", currentUserLogin);
        
        return userService.getUserWithAuthorities()
            .map(user -> {
                LOG.debug("Current user ID: {}", user.getId());
                
                // Find or create employee for current user
                Employee employee = employeeRepository.findByUserId(user.getId())
                    .orElseGet(() -> {
                        LOG.info("No employee found for user: {}. Creating a basic employee record.", currentUserLogin);
                        // Create a minimal employee record
                        Employee newEmployee = new Employee();
                        newEmployee.setUser(user);
                        newEmployee.setFullname(user.getFirstName() + " " + user.getLastName());
                        newEmployee.setMail(user.getEmail());
                        newEmployee.setActive("Y");
                        newEmployee = employeeRepository.save(newEmployee);
                        LOG.info("Created employee ID: {} for user: {}", newEmployee.getId(), user.getLogin());
                        return newEmployee;
                    });
                
                DemandePromotion demandePromotion = demandePromotionMapper.toEntity(demandePromotionDTO);
                
                // Set the employee for this request
                demandePromotion.setEmployee(employee);
                LOG.debug("Associated DemandePromotion with employee: {}", employee.getId());
                
                demandePromotion = demandePromotionRepository.save(demandePromotion);
                LOG.debug("DemandePromotion saved with ID: {}", demandePromotion.getId());
                
                // Start Camunda workflow process for the new promotion request
                try {
                    startPromotionWorkflow(demandePromotion);
                    LOG.debug("Camunda workflow started for promotion request ID: {}", demandePromotion.getId());
                } catch (Exception e) {
                    LOG.warn("Failed to start Camunda workflow for promotion request ID: {}", demandePromotion.getId(), e);
                    // Send direct notification as fallback only if Camunda is not available
                    try {
                        promotionNotificationService.notifyControleGestionOnNewRequest(demandePromotion);
                        LOG.debug("Fallback notification sent for new promotion request ID: {}", demandePromotion.getId());
                    } catch (Exception notificationException) {
                        LOG.warn("Failed to send fallback notification for new promotion request ID: {}", demandePromotion.getId(), notificationException);
                    }
                }
                
                return demandePromotionMapper.toDto(demandePromotion);
            })
            .orElseThrow(() -> new RuntimeException("Current user not found: " + currentUserLogin));
    }

    /**
     * Start the Camunda workflow process for a new promotion request
     * @param demandePromotion the promotion request to start workflow for
     */
    private void startPromotionWorkflow(DemandePromotion demandePromotion) {
        LOG.info("Starting Camunda workflow for promotion request ID: {}", demandePromotion.getId());
        
        try {
            // Prepare process variables
            Map<String, Object> variables = new HashMap<>();
            variables.put("demandePromotionId", demandePromotion.getId());
            variables.put("requesterId", demandePromotion.getEmployee() != null ? demandePromotion.getEmployee().getId() : null);
            variables.put("requesterName", demandePromotion.getEmployee() != null ? demandePromotion.getEmployee().getFullname() : "Unknown");
            variables.put("requesterEmail", demandePromotion.getEmployee() != null ? demandePromotion.getEmployee().getMail() : null);
            variables.put("clientCode", demandePromotion.getCodeClient());
            variables.put("brand", demandePromotion.getEnseigne());
            variables.put("action", demandePromotion.getAction());
            variables.put("status", demandePromotion.getStatus().name());
            variables.put("approved", false); // Default value for gateway decision
            
            // Start process instance with message start event
            var processInstance = runtimeService.createMessageCorrelation(MESSAGE_NAME)
                .setVariables(variables)
                .correlateStartMessage();
            
            String processInstanceId = processInstance.getProcessInstanceId();
            LOG.info("Started Camunda process instance: {} for promotion request: {}", processInstanceId, demandePromotion.getId());
            
            // Send notification to Controle_gestion users about the new request
            // This happens at process start since there's no service task for it in the simplified BPMN
            try {
                promotionNotificationService.notifyControleGestionOnNewRequest(demandePromotion);
                LOG.info("Initial notification sent to Controle_gestion for promotion request ID: {}", demandePromotion.getId());
            } catch (Exception e) {
                LOG.warn("Failed to send initial notification to Controle_gestion for promotion request ID: {}", demandePromotion.getId(), e);
                // Don't fail the process if notification fails
            }
            
        } catch (Exception e) {
            LOG.error("Failed to start Camunda workflow for promotion request ID: {}", demandePromotion.getId(), e);
            throw e; // Re-throw to trigger fallback logic
        }
    }

    /**
     * Complete the Camunda user task for promotion review
     * @param demandePromotion the promotion request
     * @param status the decision (Approved or Rejected)
     */
    private void completePromotionReviewTask(DemandePromotion demandePromotion, PromotionStatus status) {
        LOG.info("Completing Camunda user task for promotion request ID: {} with status: {}", demandePromotion.getId(), status);
        
        try {
            // Find the user task for this promotion request
            Task task = taskService.createTaskQuery()
                .processVariableValueEquals("demandePromotionId", demandePromotion.getId())
                .taskDefinitionKey("UserTask_1") // This should match your BPMN user task ID
                .singleResult();
            
            if (task != null) {
                // Set the process variables based on the decision
                Map<String, Object> variables = new HashMap<>();
                variables.put("approved", status == PromotionStatus.Approved);
                variables.put("status", status.name());
                
                // Add GMS comments if available
                if (demandePromotion.getPromotionDetails() != null && !demandePromotion.getPromotionDetails().isEmpty()) {
                    String gmsComments = demandePromotion.getPromotionDetails().iterator().next().getApproManagerGMS();
                    variables.put("gmsComments", gmsComments);
                }
                
                // Complete the task with the decision
                taskService.complete(task.getId(), variables);
                
                LOG.info("Completed Camunda user task ID: {} for promotion request: {} with decision: {}", 
                        task.getId(), demandePromotion.getId(), status);
            } else {
                LOG.warn("No active user task found for promotion request ID: {}", demandePromotion.getId());
            }
            
        } catch (Exception e) {
            LOG.error("Failed to complete Camunda user task for promotion request ID: {}", demandePromotion.getId(), e);
            throw e; // Re-throw to trigger warning in calling method
        }
    }



    @Override
    public DemandePromotionDTO update(DemandePromotionDTO demandePromotionDTO) {
        LOG.debug("Request to update DemandePromotion : {}", demandePromotionDTO);
        
        // Check if this is a status change that requires notification
        final PromotionStatus oldStatus;
        if (demandePromotionDTO.getId() != null) {
            DemandePromotion existingRequest = demandePromotionRepository.findById(demandePromotionDTO.getId()).orElse(null);
            oldStatus = existingRequest != null ? existingRequest.getStatus() : null;
        } else {
            oldStatus = null;
        }
        
        // Ensure status is always set for updates
        if (demandePromotionDTO.getStatus() == null) {
            demandePromotionDTO.setStatus(PromotionStatus.Waiting);
            LOG.debug("Setting default status to Waiting for update of DemandePromotion");
        }
        
        // Get current user and associate with employee (same as save method)
        String currentUserLogin = SecurityUtils.getCurrentUserLogin()
            .orElseThrow(() -> new RuntimeException("Current user login not found"));
        
        LOG.debug("Updating DemandePromotion for user: {}", currentUserLogin);
        
        return userService.getUserWithAuthorities()
            .map(user -> {
                LOG.debug("Current user ID: {}", user.getId());
                
                DemandePromotion demandePromotion = demandePromotionMapper.toEntity(demandePromotionDTO);
                
                // IMPORTANT: For updates, preserve the original employee (requester)
                // Do NOT change the employee to the current user (validator)
                if (demandePromotionDTO.getId() != null) {
                    DemandePromotion existingRequest = demandePromotionRepository.findById(demandePromotionDTO.getId()).orElse(null);
                    if (existingRequest != null && existingRequest.getEmployee() != null) {
                        demandePromotion.setEmployee(existingRequest.getEmployee());
                        LOG.debug("Preserved original requester employee: {} for promotion request: {}", 
                                 existingRequest.getEmployee().getId(), demandePromotion.getId());
                    } else {
                        LOG.warn("No existing employee found for promotion request: {}", demandePromotionDTO.getId());
                    }
                } else {
                    // This is a new request, find or create employee for current user
                    Employee employee = employeeRepository.findByUserId(user.getId())
                        .orElseGet(() -> {
                            LOG.info("No employee found for user: {}. Creating a basic employee record.", user.getLogin());
                            Employee newEmployee = new Employee();
                            newEmployee.setUser(user);
                            newEmployee.setFullname(user.getFirstName() + " " + user.getLastName());
                            newEmployee.setMail(user.getEmail());
                            newEmployee.setActive("Y");
                            newEmployee = employeeRepository.save(newEmployee);
                            LOG.info("Created employee ID: {} for user: {}", newEmployee.getId(), user.getLogin());
                            return newEmployee;
                        });
                    demandePromotion.setEmployee(employee);
                    LOG.debug("Set employee for new promotion request: {}", employee.getId());
                }
                
                demandePromotion = demandePromotionRepository.save(demandePromotion);
                LOG.debug("DemandePromotion updated with ID: {}", demandePromotion.getId());
                
                // Check for status changes that require Camunda task completion
                PromotionStatus newStatus = demandePromotion.getStatus();
                if (oldStatus != null && !oldStatus.equals(newStatus) && 
                    (newStatus == PromotionStatus.Approved || newStatus == PromotionStatus.Rejected)) {
                    
                    // Complete the Camunda user task if it exists
                    // The BPMN process will handle the notifications via service tasks
                    try {
                        completePromotionReviewTask(demandePromotion, newStatus);
                        LOG.debug("Camunda user task completed for request ID: {} with status: {}", demandePromotion.getId(), newStatus);
                    } catch (Exception e) {
                        LOG.warn("Failed to complete Camunda user task for request ID: {}", demandePromotion.getId(), e);
                        // Send fallback notification only if Camunda is not available
                        try {
                            String gmsComments = "";
                            if (demandePromotion.getPromotionDetails() != null && !demandePromotion.getPromotionDetails().isEmpty()) {
                                gmsComments = demandePromotion.getPromotionDetails().iterator().next().getApproManagerGMS();
                                if (gmsComments != null && gmsComments.startsWith("REJECTED:")) {
                                    gmsComments = gmsComments.substring("REJECTED:".length()).trim();
                                }
                            }
                            
                            promotionNotificationService.notifyRequesterOnStatusChange(demandePromotion, newStatus, gmsComments);
                            LOG.debug("Fallback notification sent for request ID: {} with status: {}", demandePromotion.getId(), newStatus);
                        } catch (Exception notificationException) {
                            LOG.warn("Failed to send fallback notification for request ID: {}", demandePromotion.getId(), notificationException);
                        }
                    }
                }
                
                return demandePromotionMapper.toDto(demandePromotion);
            })
            .orElseThrow(() -> new RuntimeException("Current user not found: " + currentUserLogin));
    }

    @Override
    public Optional<DemandePromotionDTO> partialUpdate(DemandePromotionDTO demandePromotionDTO) {
        LOG.debug("Request to partially update DemandePromotion : {}", demandePromotionDTO);

        return demandePromotionRepository
            .findById(demandePromotionDTO.getId())
            .map(existingDemandePromotion -> {
                demandePromotionMapper.partialUpdate(existingDemandePromotion, demandePromotionDTO);

                return existingDemandePromotion;
            })
            .map(demandePromotionRepository::save)
            .map(demandePromotionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<DemandePromotionDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all DemandePromotions");
        return demandePromotionRepository.findAll(pageable).map(demandePromotionMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<DemandePromotionDTO> findAllForCurrentUser(Pageable pageable) {
        LOG.debug("Request to get all DemandePromotions for current user");
        
        // Get current user ID
        String currentUserLogin = SecurityUtils.getCurrentUserLogin()
            .orElseThrow(() -> new RuntimeException("Current user login not found"));
        
        LOG.debug("Finding DemandePromotions for user: {}", currentUserLogin);
        
        // Get current user
        return userService.getUserWithAuthorities()
            .map(user -> {
                LOG.debug("Current user ID: {}", user.getId());
                
                // Use repository method to filter by user at database level
                Page<DemandePromotion> userRequests = demandePromotionRepository.findByEmployeeUserId(user.getId(), pageable);
                
                LOG.debug("Found {} requests for current user (total elements: {})", 
                    userRequests.getNumberOfElements(), 
                    userRequests.getTotalElements());
                
                return userRequests.map(demandePromotionMapper::toDto);
            })
            .orElseThrow(() -> new RuntimeException("Current user not found"));
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<DemandePromotionDTO> findOne(Long id) {
        LOG.debug("Request to get DemandePromotion : {}", id);
        return demandePromotionRepository.findById(id).map(demandePromotionMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete DemandePromotion : {}", id);
        demandePromotionRepository.deleteById(id);
    }
    
}
