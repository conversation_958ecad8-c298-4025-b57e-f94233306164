package com.dq.lilas.service.impl;

import com.dq.lilas.domain.ProductsList;
import com.dq.lilas.repository.ProductsListRepository;
import com.dq.lilas.service.ProductsListService;
import com.dq.lilas.service.dto.ProductsListDTO;
import com.dq.lilas.service.mapper.ProductsListMapper;
import java.util.Optional;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

/**
 * Service Implementation for managing {@link com.dq.lilas.domain.ProductsList}.
 */
@Service
@Transactional
public class ProductsListServiceImpl implements ProductsListService {

    private static final Logger LOG = LoggerFactory.getLogger(ProductsListServiceImpl.class);

    private final ProductsListRepository productsListRepository;

    private final ProductsListMapper productsListMapper;

    public ProductsListServiceImpl(ProductsListRepository productsListRepository, ProductsListMapper productsListMapper) {
        this.productsListRepository = productsListRepository;
        this.productsListMapper = productsListMapper;
    }

    @Override
    public ProductsListDTO save(ProductsListDTO productsListDTO) {
        LOG.debug("Request to save ProductsList : {}", productsListDTO);
        ProductsList productsList = productsListMapper.toEntity(productsListDTO);
        productsList = productsListRepository.save(productsList);
        return productsListMapper.toDto(productsList);
    }

    @Override
    public ProductsListDTO update(ProductsListDTO productsListDTO) {
        LOG.debug("Request to update ProductsList : {}", productsListDTO);
        ProductsList productsList = productsListMapper.toEntity(productsListDTO);
        productsList = productsListRepository.save(productsList);
        return productsListMapper.toDto(productsList);
    }

    @Override
    public Optional<ProductsListDTO> partialUpdate(ProductsListDTO productsListDTO) {
        LOG.debug("Request to partially update ProductsList : {}", productsListDTO);

        return productsListRepository
            .findById(productsListDTO.getId())
            .map(existingProductsList -> {
                productsListMapper.partialUpdate(existingProductsList, productsListDTO);

                return existingProductsList;
            })
            .map(productsListRepository::save)
            .map(productsListMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Page<ProductsListDTO> findAll(Pageable pageable) {
        LOG.debug("Request to get all ProductsLists");
        return productsListRepository.findAll(pageable).map(productsListMapper::toDto);
    }

    @Override
    @Transactional(readOnly = true)
    public Optional<ProductsListDTO> findOne(Long id) {
        LOG.debug("Request to get ProductsList : {}", id);
        return productsListRepository.findById(id).map(productsListMapper::toDto);
    }

    @Override
    public void delete(Long id) {
        LOG.debug("Request to delete ProductsList : {}", id);
        productsListRepository.deleteById(id);
    }
}
