package com.dq.lilas.service.mapper;

import com.dq.lilas.domain.Employee;
import com.dq.lilas.domain.Job;
import com.dq.lilas.domain.Unit;
import com.dq.lilas.domain.User;
import com.dq.lilas.service.dto.EmployeeDTO;
import com.dq.lilas.service.dto.JobDTO;
import com.dq.lilas.service.dto.UnitDTO;
import com.dq.lilas.service.dto.UserDTO;
import org.mapstruct.*;

/**
 * Mapper for the entity {@link Employee} and its DTO {@link EmployeeDTO}.
 */
@Mapper(componentModel = "spring")
public interface EmployeeMapper extends EntityMapper<EmployeeDTO, Employee> {
    @Mapping(target = "user", source = "user", qualifiedByName = "userLogin")
    @Mapping(target = "job", source = "job", qualifiedByName = "jobId")
    @Mapping(target = "unit", source = "unit", qualifiedByName = "unitId")
    EmployeeDTO toDto(Employee s);

    @Named("userLogin")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    @Mapping(target = "login", source = "login")
    UserDTO toDtoUserLogin(User user);

    @Named("jobId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    JobDTO toDtoJobId(Job job);

    @Named("unitId")
    @BeanMapping(ignoreByDefault = true)
    @Mapping(target = "id", source = "id")
    UnitDTO toDtoUnitId(Unit unit);
}
