package com.dq.lilas.service.camunda;

import com.dq.lilas.domain.DemandePromotion;
import com.dq.lilas.domain.enumeration.PromotionStatus;
import com.dq.lilas.repository.DemandePromotionRepository;
import com.dq.lilas.service.PromotionNotificationService;
import org.camunda.bpm.engine.RuntimeService;
import org.camunda.bpm.engine.TaskService;
import org.camunda.bpm.engine.task.Task;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.HashMap;
import java.util.Map;

/**
 * Service for integrating promotion requests with Camunda workflow
 */
@Service
public class PromotionWorkflowService {

    private static final Logger LOG = LoggerFactory.getLogger(PromotionWorkflowService.class);
    
    private static final String PROCESS_KEY = "Process_1";
    private static final String MESSAGE_NAME = "Message_0gu6vpn";
    
    private final RuntimeService runtimeService;
    private final TaskService taskService;
    private final DemandePromotionRepository demandePromotionRepository;
    private final PromotionNotificationService promotionNotificationService;
    
    public PromotionWorkflowService(
        RuntimeService runtimeService,
        TaskService taskService,
        DemandePromotionRepository demandePromotionRepository,
        PromotionNotificationService promotionNotificationService
    ) {
        this.runtimeService = runtimeService;
        this.taskService = taskService;
        this.demandePromotionRepository = demandePromotionRepository;
        this.promotionNotificationService = promotionNotificationService;
    }
    
    /**
     * Start a new promotion workflow process
     * @param demandePromotion the promotion request
     * @return the process instance ID
     */
    public String startPromotionProcess(DemandePromotion demandePromotion) {
        LOG.info("Starting Camunda process for promotion request ID: {}", demandePromotion.getId());
        
        try {
            // Prepare process variables
            Map<String, Object> variables = new HashMap<>();
            variables.put("demandePromotionId", demandePromotion.getId());
            variables.put("requesterId", demandePromotion.getEmployee() != null ? demandePromotion.getEmployee().getId() : null);
            variables.put("requesterName", demandePromotion.getEmployee() != null ? demandePromotion.getEmployee().getFullname() : "Unknown");
            variables.put("clientCode", demandePromotion.getCodeClient());
            variables.put("brand", demandePromotion.getEnseigne());
            variables.put("action", demandePromotion.getAction());
            variables.put("approved", false); // Default value for gateway decision
            
            // Start process instance with message
            var processInstance = runtimeService.createMessageCorrelation(MESSAGE_NAME)
                .setVariables(variables)
                .correlateStartMessage();
            
            String processInstanceId = processInstance.getProcessInstanceId();
            LOG.info("Started Camunda process instance: {} for promotion request: {}", processInstanceId, demandePromotion.getId());
            
            // Send notification to Controle_gestion users about new request
            try {
                promotionNotificationService.notifyControleGestionOnNewRequest(demandePromotion);
                LOG.debug("Notification sent for new promotion request ID: {}", demandePromotion.getId());
            } catch (Exception e) {
                LOG.warn("Failed to send notification for new promotion request ID: {}", demandePromotion.getId(), e);
                // Don't fail the process if email notification fails
            }
            
            return processInstanceId;
            
        } catch (Exception e) {
            LOG.error("Failed to start Camunda process for promotion request ID: {}", demandePromotion.getId(), e);
            throw new RuntimeException("Failed to start workflow process", e);
        }
    }
    
    /**
     * Complete the review task with approval or rejection
     * @param demandePromotionId the promotion request ID
     * @param approved whether the request is approved
     * @param comments GMS manager comments
     * @param reviewerId the ID of the user who reviewed the request
     */
    @Transactional
    public void completeReviewTask(Long demandePromotionId, boolean approved, String comments, String reviewerId) {
        LOG.info("Completing review task for promotion request ID: {} with decision: {}", demandePromotionId, approved);
        
        try {
            // Find the task for this promotion request
            Task reviewTask = taskService.createTaskQuery()
                .processVariableValueEquals("demandePromotionId", demandePromotionId)
                .taskDefinitionKey("UserTask_1") // "Revue de la demande"
                .singleResult();
            
            if (reviewTask == null) {
                LOG.warn("No review task found for promotion request ID: {}", demandePromotionId);
                return;
            }
            
            // Update the database entity with the decision
            DemandePromotion demandePromotion = demandePromotionRepository.findById(demandePromotionId)
                .orElseThrow(() -> new RuntimeException("Promotion request not found: " + demandePromotionId));
            
            PromotionStatus newStatus = approved ? PromotionStatus.Approved : PromotionStatus.Rejected;
            demandePromotion.setStatus(newStatus);
            
            // Update promotion details with comments if they exist
            if (demandePromotion.getPromotionDetails() != null && !demandePromotion.getPromotionDetails().isEmpty()) {
                var promotionDetail = demandePromotion.getPromotionDetails().iterator().next();
                promotionDetail.setApproManagerGMS(comments);
                promotionDetail.setStatus(newStatus);
            }
            
            demandePromotionRepository.save(demandePromotion);
            LOG.debug("Updated promotion request status to: {}", newStatus);
            
            // Prepare task completion variables
            Map<String, Object> variables = new HashMap<>();
            variables.put("approved", approved);
            variables.put("reviewerId", reviewerId);
            variables.put("reviewComments", comments);
            variables.put("reviewDate", java.time.Instant.now());
            
            // Complete the task
            taskService.complete(reviewTask.getId(), variables);
            LOG.info("Completed review task: {} for promotion request: {}", reviewTask.getId(), demandePromotionId);
            
            // Send notification to requester
            try {
                promotionNotificationService.notifyRequesterOnStatusChange(demandePromotion, newStatus, comments);
                LOG.debug("Status change notification sent for promotion request ID: {}", demandePromotionId);
            } catch (Exception e) {
                LOG.warn("Failed to send status change notification for promotion request ID: {}", demandePromotionId, e);
                // Don't fail the process if email notification fails
            }
            
        } catch (Exception e) {
            LOG.error("Failed to complete review task for promotion request ID: {}", demandePromotionId, e);
            throw new RuntimeException("Failed to complete review task", e);
        }
    }
    
    /**
     * Get the current task for a promotion request
     * @param demandePromotionId the promotion request ID
     * @return the current task or null if no active task
     */
    public Task getCurrentTask(Long demandePromotionId) {
        return taskService.createTaskQuery()
            .processVariableValueEquals("demandePromotionId", demandePromotionId)
            .singleResult();
    }
    
    /**
     * Check if a promotion request has an active workflow process
     * @param demandePromotionId the promotion request ID
     * @return true if there's an active process
     */
    public boolean hasActiveProcess(Long demandePromotionId) {
        long count = runtimeService.createProcessInstanceQuery()
            .variableValueEquals("demandePromotionId", demandePromotionId)
            .count();
        return count > 0;
    }
}
