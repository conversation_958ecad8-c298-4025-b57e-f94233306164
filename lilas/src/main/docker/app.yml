# This configuration is intended for development purpose, it's **your** responsibility to harden it for production
name: project
services:
  app:
    image: project
    environment:
      - _JAVA_OPTIONS=-Xmx512m -Xms256m
      - SPRING_PROFILES_ACTIVE=prod,api-docs
      - MANAGEMENT_PROMETHEUS_METRICS_EXPORT_ENABLED=true
      - SPRING_DATASOURCE_URL=*****************************************
      - SPRING_LIQUIBASE_URL=*****************************************
    ports:
      - 127.0.0.1:8080:8080
    healthcheck:
      test:
        - CMD
        - curl
        - -f
        - http://localhost:8080/management/health
      interval: 5s
      timeout: 5s
      retries: 40
    depends_on:
      postgresql:
        condition: service_healthy
  postgresql:
    extends:
      file: ./postgresql.yml
      service: postgresql
