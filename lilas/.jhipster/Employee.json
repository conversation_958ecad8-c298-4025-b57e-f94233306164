{"annotations": {"changelogDate": "20250619211021"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "tel", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "fax", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "mail", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "<PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "numidentity", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "empnumber", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "fullname", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "address", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "matricule", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "upscale", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "active", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "statut", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "gender", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "avatar", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "filenameparaf", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "filenamesign", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "coursier", "fieldType": "Boolean"}], "name": "Employee", "pagination": "pagination", "relationships": [{"otherEntityField": "login", "otherEntityName": "user", "relationshipName": "user", "relationshipSide": "left", "relationshipType": "one-to-one", "relationshipWithBuiltInEntity": true}, {"otherEntityName": "employeelang", "relationshipName": "employeelangs", "relationshipSide": "left", "relationshipType": "one-to-many"}, {"otherEntityName": "job", "relationshipName": "job", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}