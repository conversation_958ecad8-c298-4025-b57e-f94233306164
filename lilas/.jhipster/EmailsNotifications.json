{"annotations": {"changelogDate": "20250619211039"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "subject", "fieldType": "String"}, {"fieldName": "notificationDate", "fieldType": "Instant"}, {"fieldName": "body", "fieldType": "Blob"}, {"fieldName": "recipient", "fieldType": "<PERSON>"}, {"fieldName": "sender", "fieldType": "String"}], "name": "EmailsNotifications", "pagination": "pagination", "relationships": [{"otherEntityName": "orderDetails", "otherEntityRelationshipName": "emailsNotifications", "relationshipName": "orderDetails", "relationshipSide": "right", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}