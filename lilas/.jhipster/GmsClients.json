{"annotations": {"changelogDate": "20250619211042"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "clientName", "fieldType": "String"}, {"fieldName": "code", "fieldType": "String", "fieldValidateRules": ["unique"]}, {"fieldName": "classe", "fieldType": "String"}, {"fieldName": "ville", "fieldType": "String"}, {"fieldName": "creationDate", "fieldType": "Instant"}, {"fieldName": "updateDate", "fieldType": "Instant"}], "name": "GmsClients", "pagination": "pagination", "relationships": [{"otherEntityName": "cadencier", "relationshipName": "cadencier", "relationshipSide": "left", "relationshipType": "one-to-one"}], "searchEngine": "no", "service": "serviceImpl"}