{"annotations": {"changelogDate": "20250619211030"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "statut", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "orderpos", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}], "name": "Deliverymode", "pagination": "pagination", "relationships": [{"otherEntityName": "deliverymodelang", "relationshipName": "deleverymodelang", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "no", "service": "serviceImpl"}