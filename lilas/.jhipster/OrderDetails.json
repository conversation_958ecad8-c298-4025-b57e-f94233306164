{"annotations": {"changelogDate": "20250619211038"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "quantity", "fieldType": "BigDecimal"}, {"fieldName": "updatedQty", "fieldType": "BigDecimal"}, {"fieldName": "discount", "fieldType": "Integer"}, {"fieldName": "updatedDiscount", "fieldType": "Integer"}, {"fieldName": "unitPrice", "fieldType": "Double"}, {"fieldName": "updatedUnitPrice", "fieldType": "Double"}, {"fieldName": "orderLineJson", "fieldType": "TextBlob"}, {"fieldName": "availability", "fieldType": "Boolean"}, {"fieldName": "validConditions", "fieldType": "Boolean"}, {"fieldName": "injected", "fieldType": "Boolean"}, {"fieldName": "barcode", "fieldType": "String"}, {"fieldName": "updatedBarcode", "fieldType": "String"}, {"fieldName": "internalCode", "fieldType": "String"}, {"fieldName": "updatedInternalCode", "fieldType": "String"}, {"fieldName": "discountStatus", "fieldType": "Boolean"}, {"fieldName": "priceStatus", "fieldType": "Boolean"}, {"fieldName": "quantityStatus", "fieldType": "Boolean"}, {"fieldName": "productStatus", "fieldType": "Boolean"}], "name": "OrderDetails", "pagination": "pagination", "relationships": [{"otherEntityName": "emailsNotifications", "otherEntityRelationshipName": "orderDetails", "relationshipName": "emailsNotifications", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "no", "service": "serviceImpl"}