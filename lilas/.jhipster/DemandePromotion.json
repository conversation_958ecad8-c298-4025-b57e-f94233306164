{"annotations": {"changelogDate": "20250619211047"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "codeClient", "fieldType": "String"}, {"fieldName": "enseigne", "fieldType": "String"}, {"fieldName": "action", "fieldType": "String"}, {"fieldName": "periodPromotionStart", "fieldType": "Instant"}, {"fieldName": "periodPromotionEnd", "fieldType": "Instant"}, {"fieldName": "periodFacturationStart", "fieldType": "Instant"}, {"fieldName": "periodFacturationEnd", "fieldType": "Instant"}], "name": "DemandePromotion", "pagination": "pagination", "relationships": [{"otherEntityName": "employee", "relationshipName": "employee", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "promotionDetails", "relationshipName": "articles", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}