{"annotations": {"changelogDate": "20250619211051"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "internalCode", "fieldType": "String"}, {"fieldName": "barcode", "fieldType": "String"}, {"fieldName": "productName", "fieldType": "String"}, {"fieldName": "stockQty", "fieldType": "<PERSON>"}, {"fieldName": "stockDate", "fieldType": "LocalDate"}, {"fieldName": "remainingQuantity", "fieldType": "<PERSON>"}], "name": "DailyStock", "pagination": "pagination", "relationships": [{"otherEntityName": "productsList", "relationshipName": "productsList", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}