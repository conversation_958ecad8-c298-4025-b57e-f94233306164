{"annotations": {"changelogDate": "20250619211040"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "maildate", "fieldType": "Instant"}, {"fieldName": "subject", "fieldType": "String"}, {"fieldName": "mailbody", "fieldType": "TextBlob"}, {"fieldName": "recipient", "fieldType": "String"}, {"fieldName": "sender", "fieldType": "String"}], "name": "Mails", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}