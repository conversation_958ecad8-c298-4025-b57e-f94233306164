{"annotations": {"changelogDate": "20250619211045"}, "applications": "*", "dto": "mapstruct", "entityTableName": "jhi_order", "fields": [{"fieldName": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "TextBlob"}, {"fieldName": "rank", "fieldType": "Integer"}, {"fieldName": "status", "fieldType": "Integer"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "TextBlob"}], "name": "Order", "pagination": "pagination", "relationships": [{"otherEntityName": "orderDetails", "relationshipName": "orderDetails", "relationshipSide": "left", "relationshipType": "one-to-many"}, {"otherEntityName": "dailyBatches", "relationshipName": "dailyBatches", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "gmsClients", "relationshipName": "gmsClients", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "templateConditions", "relationshipName": "templateConditions", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "mails", "relationshipName": "mails", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}