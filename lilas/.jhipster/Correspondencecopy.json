{"annotations": {"changelogDate": "20250619211036"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "numcopy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "comments", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "4000"}, {"fieldName": "datejccreate", "fieldType": "Instant"}, {"fieldName": "datehjrcreate", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "typereceive", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "typecopy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "userreceive", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "date<PERSON><PERSON><PERSON>", "fieldType": "Instant"}, {"fieldName": "datehjrreceive", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "datejcaction", "fieldType": "Instant"}, {"fieldName": "datehj<PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "actiontype", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "savecorrespcpy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "ordernbr", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"fieldName": "pagenbr", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "200"}, {"fieldName": "expno", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "200"}, {"fieldName": "expyear", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "docy<PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "categorycorresp", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "heureaction", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "statusdeniedcopy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "pagenbrpaper", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "taskscan", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "pathtransto", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "pathtranscc", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "pathtranstolib", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "pathtranscclib", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "flggroup", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "Instant"}, {"fieldName": "date<PERSON><PERSON><PERSON>", "fieldType": "Instant"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "Instant"}], "name": "Correspondencecopy", "pagination": "pagination", "relationships": [{"otherEntityName": "correspondence", "relationshipName": "correspondence", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "employee", "relationshipName": "employee", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "relationshipName": "unit", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "action", "relationshipName": "action", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "employee", "relationshipName": "useraction", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "employee", "relationshipName": "userrevoke", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "employee", "relationshipName": "<PERSON><PERSON><PERSON>", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}