{"annotations": {"changelogDate": "20250619211026"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "address", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"fieldName": "nbr", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "500"}, {"fieldName": "lang", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "abrv", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}], "name": "Unitlang", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}