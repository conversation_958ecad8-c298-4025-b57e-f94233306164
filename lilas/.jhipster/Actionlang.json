{"annotations": {"changelogDate": "20250619211029"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "appType", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "lang", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "abrv", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}], "name": "Actionlang", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}