{"annotations": {"changelogDate": "20250619211025"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "parentid", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "tel", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "fax", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "order", "fieldType": "Integer"}, {"fieldName": "<PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "address", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "nbr", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "name", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "lang", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "abbreviated", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "mail", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "checkenabled", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "activate", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "active", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "level", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "grp", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "compid", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "branch", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "regoffice", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "unitgroup", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "grdparent", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "status", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "category", "fieldType": "String"}, {"fieldName": "functionUnit", "fieldType": "String"}, {"fieldName": "position", "fieldType": "String"}, {"fieldName": "section", "fieldType": "String"}, {"fieldName": "categdir", "fieldType": "String"}, {"fieldName": "categUnit", "fieldType": "String"}, {"fieldName": "function", "fieldType": "String"}, {"fieldName": "responsible", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}], "name": "Unit", "pagination": "pagination", "relationships": [{"otherEntityName": "unitlang", "relationshipName": "unitlangs", "relationshipSide": "left", "relationshipType": "one-to-many"}, {"otherEntityName": "employee", "relationshipName": "employees", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "no", "service": "serviceImpl"}