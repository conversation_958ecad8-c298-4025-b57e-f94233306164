{"annotations": {"changelogDate": "20250619211028"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "regulation", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "circular", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "appOrder", "fieldType": "Integer"}, {"fieldName": "statut", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}], "name": "Action", "pagination": "pagination", "relationships": [{"otherEntityName": "actionlang", "relationshipName": "actionlangs", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "no", "service": "serviceImpl"}