{"annotations": {"changelogDate": "20250619211032"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "typecorresp", "fieldType": "String"}, {"fieldName": "typedecision", "fieldType": "String"}, {"fieldName": "hosp", "fieldType": "String"}, {"fieldName": "lbl", "fieldType": "String"}, {"fieldName": "statut", "fieldType": "String"}, {"fieldName": "abbreviated", "fieldType": "String"}, {"fieldName": "speech", "fieldType": "String"}, {"fieldName": "nbrnotifbeforerec", "fieldType": "<PERSON>"}, {"fieldName": "nbrnotifafterrec", "fieldType": "<PERSON>"}, {"fieldName": "archiveSubjectsId", "fieldType": "String"}], "name": "Typecorrespondence", "pagination": "pagination", "relationships": [{"otherEntityName": "typecorrespondencelang", "relationshipName": "typecorrespondence", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "no", "service": "serviceImpl"}