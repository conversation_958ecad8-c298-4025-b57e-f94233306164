{"annotations": {"changelogDate": "20250619211035"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "docy<PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "texttransfer", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "4000"}, {"fieldName": "datejctransfer", "fieldType": "Instant"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "30"}, {"fieldName": "statustransfer", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "datesendjctransfer", "fieldType": "Instant"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "30"}, {"fieldName": "savetransfer", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "numcopy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "25"}, {"fieldName": "highlevel", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "<PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "priority", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "timeaction", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "deadline", "fieldType": "Instant"}, {"fieldName": "rappelnum", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "rappeltype", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "readrequest", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "typereceive", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "date<PERSON><PERSON><PERSON>", "fieldType": "Instant"}, {"fieldName": "datehjrreceive", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "actiontype", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "comments", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "datearch", "fieldType": "Instant"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "30"}, {"fieldName": "lasttransserial", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "statusreceiveto", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "<PERSON><PERSON><PERSON>to", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "Instant"}, {"fieldName": "<PERSON>ate<PERSON><PERSON><PERSON>ert<PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "30"}, {"fieldName": "typetransfer", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "transrecserial", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "attach", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "300"}, {"fieldName": "transtype", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "ordernbr", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"fieldName": "heureaction", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "datejcaction", "fieldType": "Instant"}, {"fieldName": "datehj<PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "statusdenied", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "subjectcorresp", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "Instant"}, {"fieldName": "date<PERSON><PERSON><PERSON><PERSON>resp", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "oldstatus", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "step", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "typeprocess", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "codetask", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"fieldName": "refusetext", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "statusrefused", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "bidadrsbook", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "pagenbrpaper", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "flagprint", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "dateprint", "fieldType": "Instant"}, {"fieldName": "datehjrprint", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "Instant"}, {"fieldName": "date<PERSON><PERSON><PERSON>", "fieldType": "Instant"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "gabaritcontext", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "4000"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "datejcapprovedspeech", "fieldType": "Instant"}, {"fieldName": "datehjrapprovedspeech", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "conformitytask", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"fieldName": "useradrsbook", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"fieldName": "stepmaxwf", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "incidenttransfer", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "qualificationincident", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "categorieincident", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "statutincident", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "criticiteincident", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "voiceId", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "255"}, {"fieldName": "favoris", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "checkinboxfavorite", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "checkclosefavorite", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "checkfavorite", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "taskcategId", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "pinrequired", "fieldType": "Boolean"}, {"fieldName": "codePin", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "6"}, {"fieldName": "sendwithmail", "fieldType": "Boolean"}], "name": "Transfer", "pagination": "pagination", "relationships": [{"otherEntityName": "transfer", "relationshipName": "transmoth", "relationshipSide": "left", "relationshipType": "one-to-one"}, {"otherEntityName": "correspondencecopy", "relationshipName": "correspondencecopy", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "correspondence", "relationshipName": "correspondence", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "deliverymode", "relationshipName": "deliverymode", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "employee", "relationshipName": "employee", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "relationshipName": "unit", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "action", "relationshipName": "action", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "employee", "relationshipName": "userreceive", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "employee", "relationshipName": "usertrans", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "employee", "relationshipName": "usertransto", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "relationshipName": "unittransto", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "employee", "relationshipName": "userrevoke", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "employee", "relationshipName": "userreceiveto", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "employee", "relationshipName": "useraction", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "relationshipName": "fromdept", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "transfer", "relationshipName": "transprincip", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "typecorrespondence", "relationshipName": "typecorrespondence", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}