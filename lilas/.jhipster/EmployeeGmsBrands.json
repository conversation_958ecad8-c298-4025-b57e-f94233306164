{"annotations": {"changelogDate": "20250619211044"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "assignmentDate", "fieldType": "Instant"}, {"fieldName": "status", "fieldType": "Boolean"}], "name": "EmployeeGmsBrands", "pagination": "pagination", "relationships": [{"otherEntityName": "employee", "relationshipName": "employee", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "gmsBrands", "relationshipName": "gmsBrands", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}