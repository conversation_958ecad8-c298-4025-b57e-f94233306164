{"annotations": {"changelogDate": "20250619211046"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "name", "fieldType": "String"}], "name": "TemplateConditions", "pagination": "pagination", "relationships": [{"otherEntityName": "employee", "relationshipName": "employee", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}