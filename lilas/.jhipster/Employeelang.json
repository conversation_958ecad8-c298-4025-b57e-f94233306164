{"annotations": {"changelogDate": "20250619211024"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "fullname", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"fieldName": "address", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"fieldName": "lang", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "matricule", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}], "name": "Employeelang", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}