{"annotations": {"changelogDate": "20250619211037"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "copyId", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "30"}, {"fieldName": "lblAttachment", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "150"}, {"fieldName": "idDocAttachment", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"fieldName": "sizeAttachement", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "filenameattachment", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"fieldName": "userattachment", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "iddecision", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "idtemplate", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "datej<PERSON><PERSON><PERSON><PERSON>", "fieldType": "Instant"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "Instant"}, {"fieldName": "idtransfer", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "idcorresp", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "ordering", "fieldType": "Double"}, {"fieldName": "levelattachement", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "idreq", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "orderingscan", "fieldType": "Double"}, {"fieldName": "iddocext", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "idleave", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "20"}, {"fieldName": "configLevel", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "typeAtach", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "idDirectOrder", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "pathFile", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "100"}, {"fieldName": "version", "fieldType": "Integer"}], "name": "Attachement", "pagination": "pagination", "relationships": [{"otherEntityName": "order", "relationshipName": "order", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}