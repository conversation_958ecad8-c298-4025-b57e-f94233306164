{"annotations": {"changelogDate": "20250619211027"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "comapanyName", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "255"}], "name": "Company", "pagination": "pagination", "relationships": [{"otherEntityName": "unit", "relationshipName": "units", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "no", "service": "serviceImpl"}