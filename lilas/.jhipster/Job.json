{"annotations": {"changelogDate": "20250619211022"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "status", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}], "name": "Job", "pagination": "pagination", "relationships": [{"otherEntityName": "joblang", "relationshipName": "joblangs", "relationshipSide": "left", "relationshipType": "one-to-many"}], "searchEngine": "no", "service": "serviceImpl"}