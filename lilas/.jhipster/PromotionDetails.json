{"annotations": {"changelogDate": "20250619211048"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "codeProduit", "fieldType": "String"}, {"fieldName": "description", "fieldType": "String"}, {"fieldName": "priceHT", "fieldType": "Double"}, {"fieldName": "remiseFixe", "fieldType": "Double"}, {"fieldName": "remiseDePro", "fieldType": "Double"}, {"fieldName": "gratuitEnNat", "fieldType": "Double"}, {"fieldName": "approManager<PERSON>u<PERSON><PERSON><PERSON>", "fieldType": "String"}, {"fieldName": "approManagerGMS", "fieldType": "String"}], "name": "PromotionDetails", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}