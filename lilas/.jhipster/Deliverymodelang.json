{"annotations": {"changelogDate": "20250619211031"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "lbl", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "250"}, {"fieldName": "lang", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "abrv", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}], "name": "Deliverymodelang", "pagination": "pagination", "relationships": [], "searchEngine": "no", "service": "serviceImpl"}