{"annotations": {"changelogDate": "20250717134643"}, "dto": "mapstruct", "fields": [], "jpaMetamodelFiltering": false, "name": "EmployeeCompanyPermission", "pagination": "no", "readOnly": false, "relationships": [{"otherEntityField": "login", "otherEntityName": "employee", "relationshipName": "employee", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityField": "id", "otherEntityName": "company", "relationshipName": "company", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}