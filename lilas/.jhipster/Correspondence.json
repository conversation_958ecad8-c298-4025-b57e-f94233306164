{"annotations": {"changelogDate": "20250619211034"}, "applications": "*", "dto": "mapstruct", "fields": [{"fieldName": "ordernbr", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"fieldName": "numcopy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "doclink", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"fieldName": "pagenbr", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "200"}, {"fieldName": "highlevel", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "<PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "priority", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "obs", "fieldType": "String"}, {"fieldName": "category", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "status", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "datejc", "fieldType": "ZonedDateTime"}, {"fieldName": "datej<PERSON><PERSON>", "fieldType": "ZonedDateTime"}, {"fieldName": "typereceive", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "typecopy", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "refsnd", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"fieldName": "text", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "4000"}, {"fieldName": "docy<PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "4"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "10"}, {"fieldName": "<PERSON><PERSON><PERSON><PERSON>", "fieldType": "ZonedDateTime"}, {"fieldName": "oldOrderNumber", "fieldType": "String"}, {"fieldName": "subject", "fieldType": "String", "fieldValidateRules": ["required", "maxlength"], "fieldValidateRulesMaxlength": "1000"}, {"fieldName": "attach", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "200"}, {"fieldName": "ordernbradrsbook", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "50"}, {"fieldName": "seqkeyadrsbook", "fieldType": "Integer"}, {"fieldName": "useradrsbook", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "500"}, {"fieldName": "cityzenncard", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "cityzenphone", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "15"}, {"fieldName": "sms", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "incident", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "5"}, {"fieldName": "checkFavorite", "fieldType": "String", "fieldValidateRules": ["maxlength"], "fieldValidateRulesMaxlength": "1"}, {"fieldName": "companyId", "fieldType": "String"}], "name": "Correspondence", "pagination": "pagination", "relationships": [{"otherEntityName": "employee", "relationshipName": "employee", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "unit", "relationshipName": "unit", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "deliverymode", "relationshipName": "deliverymode", "relationshipSide": "left", "relationshipType": "many-to-one"}, {"otherEntityName": "typecorrespondence", "relationshipName": "typecorrespondence", "relationshipSide": "left", "relationshipType": "many-to-one"}], "searchEngine": "no", "service": "serviceImpl"}