# Order Update Services Guide

## Overview

This guide explains the comprehensive order update services implemented in the system, including their differences, use cases, and integration patterns. The services provide atomic operations for updating orders with their associated order details while handling circular references and ensuring data consistency.

## Available Services

### 1. **updateWithDetails()** - Comprehensive Order Update
**Purpose**: Updates an order and its order details atomically in a single transaction.

**Service Layer**:
```java
OrderDTO updateWithDetails(OrderDTO orderDTO);
```

**REST Endpoint**:
```http
PUT /api/orders/{id}/with-details
Content-Type: application/json

{
  "id": 1,
  "orderNumber": "ORD-001",
  "company": "Updated Company",
  "orderDetails": [
    {"id": 1, "quantity": 10, "unitPrice": 25.50, "productName": "Product 1"},
    {"id": 2, "quantity": 5, "unitPrice": 15.75, "productName": "Product 2"}
  ]
}
```

### 2. **bulkUpdateOrderDetails()** - Bulk Details Update
**Purpose**: Updates only the order details for an existing order, leaving the order information unchanged.

**Service Layer**:
```java
OrderDTO bulkUpdateOrderDetails(Long orderId, Set<OrderDetailsDTO> orderDetailsSet);
```

**REST Endpoint**:
```http
PUT /api/orders/{id}/details
Content-Type: application/json

[
  {"id": 1, "quantity": 15, "unitPrice": 30.00},
  {"id": 2, "quantity": 8, "unitPrice": 20.00}
]
```

### 3. **findOneWithDetails()** - Retrieve Order with Details
**Purpose**: Retrieves an order with its complete order details, handling circular references properly.

**Service Layer**:
```java
Optional<OrderDTO> findOneWithDetails(Long id);
```

**REST Endpoint**:
```http
GET /api/orders/{id}/with-details
```

## Key Differences

| Feature | **updateWithDetails()** | **bulkUpdateOrderDetails()** |
|---------|-------------------------|------------------------------|
| **Scope** | Order + Order Details | Order Details Only |
| **Order Info** | ✅ Updates order fields | ❌ Leaves order unchanged |
| **Details Operations** | ✅ Add/Update/Remove | ✅ Add/Update/Remove |
| **Use Case** | Complete order modification | Details-only updates |
| **Performance** | Moderate (full update) | Optimized (details only) |
| **Transaction** | Single atomic operation | Single atomic operation |

## Technical Implementation

### Circular Reference Handling
Both services handle the circular reference between Order and OrderDetails entities:

```java
// Clear order details to avoid circular reference during mapping
orderDTO.setOrderDetails(new HashSet<>());

// Update the order first
orderMapper.partialUpdate(existingOrder, orderDTO);
Order updatedOrder = orderRepository.save(existingOrder);

// Handle order details updates with proper parent reference
updateOrderDetailsForOrder(updatedOrder, newDetailsDTOs);
```

### CRUD Operations on Order Details
The services support comprehensive CRUD operations:

- **Create**: New details without ID are added
- **Update**: Existing details (with ID) are updated
- **Delete**: Details not in the new set are removed

```java
for (OrderDetailsDTO newDetailDTO : newDetailsDTOs) {
    if (newDetailDTO.getId() != null && existingDetailsMap.containsKey(newDetailDTO.getId())) {
        // Update existing detail
        updateOrderDetailEntity(existingDetail, newDetailDTO);
    } else {
        // Add new detail
        OrderDetails newDetail = createOrderDetailEntity(newDetailDTO);
    }
}

// Remove details not in the new set
for (OrderDetails existingDetail : existingDetails) {
    if (!newDetailsMap.containsKey(existingDetail.getId())) {
        orderDetailsRepository.deleteById(existingDetail.getId());
    }
}
```

## Usage Scenarios

### Scenario 1: Complete Order Modification
**Use `updateWithDetails()`** when you need to update both order information and details:

```javascript
// Frontend example
const updateCompleteOrder = async (orderId, orderData) => {
    const response = await fetch(`/api/orders/${orderId}/with-details`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
            id: orderId,
            company: "New Company Name",
            orderDate: "2025-01-15",
            orderDetails: [
                {id: 1, quantity: 20, unitPrice: 35.00},
                {quantity: 10, unitPrice: 25.00, productName: "New Product"}
            ]
        })
    });
    return response.json();
};
```

### Scenario 2: Details-Only Updates
**Use `bulkUpdateOrderDetails()`** when only order details need modification:

```javascript
// Frontend example
const updateOrderDetails = async (orderId, details) => {
    const response = await fetch(`/api/orders/${orderId}/details`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(details)
    });
    return response.json();
};
```

### Scenario 3: Retrieve Complete Order
**Use `findOneWithDetails()`** to get order with all details:

```javascript
// Frontend example
const getOrderWithDetails = async (orderId) => {
    const response = await fetch(`/api/orders/${orderId}/with-details`);
    return response.json();
};
```

## Error Handling

All services include comprehensive error handling:

- **Validation Errors**: Invalid IDs, missing required fields
- **Not Found Errors**: Order doesn't exist
- **Constraint Violations**: Database constraint failures
- **Circular Reference Issues**: Automatically handled

```java
try {
    OrderDTO result = orderService.updateWithDetails(orderDTO);
    return ResponseEntity.ok().body(result);
} catch (IllegalArgumentException e) {
    throw new BadRequestAlertException(e.getMessage(), ENTITY_NAME, "invalidargument");
} catch (RuntimeException e) {
    throw new BadRequestAlertException("Failed to update: " + e.getMessage(), ENTITY_NAME, "updatefailed");
}
```

## Performance Considerations

- **updateWithDetails()**: Moderate performance, suitable for complete updates
- **bulkUpdateOrderDetails()**: Optimized for details-only updates
- **Batch Operations**: Both services use efficient batch operations for multiple details
- **Transaction Management**: Single transaction ensures data consistency

## Best Practices

1. **Use the Right Service**: Choose based on what needs updating
2. **Include IDs**: Always include IDs for existing details to update them
3. **Omit IDs**: Omit IDs for new details to create them
4. **Handle Errors**: Implement proper error handling in client code
5. **Test Thoroughly**: Test all CRUD scenarios (Create, Update, Delete details)

## Integration with WebhookController

The services integrate seamlessly with the existing WebhookController for BC processing:

```java
// In WebhookController
private void updateOrderAfterBCProcessing(OrderDTO order, List<ProcessedAttachment> attachments) {
    // Add new order details based on processed attachments
    Set<OrderDetailsDTO> newDetails = createOrderDetailsFromAttachments(attachments);
    order.getOrderDetails().addAll(newDetails);
    
    // Update atomically
    OrderDTO updatedOrder = orderService.updateWithDetails(order);
    log.info("Updated order {} with {} new details", updatedOrder.getId(), newDetails.size());
}
```

This comprehensive implementation provides a robust, scalable solution for managing orders with their associated details while maintaining data integrity and optimal performance.
